{"analysis": {"precise_analysis": {"code_lines": ["CodeLineInfo(line_number=2, content='float3 worldPos = transform * localPos;', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['worldPos', 'transform', 'localPos'], conversion_details=[])", "CodeLineInfo(line_number=3, content='half4 baseColor = texture.sample(sampler, uv);', variables=4, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['baseColor', 'sampler', 'texture.sample', 'uv'], conversion_details=[])", "CodeLineInfo(line_number=4, content='float dotNL = dot(normal, lightDir);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['dotNL', 'normal', 'lightDir'], conversion_details=[])"], "syntax_trees": ["assignment(=:float3)", "assignment(=:half4)", "assignment(=:float)"], "precise_analyses": ["<Process.Analysis.shader_type_analyzer.TypeAnalysisResult object at 0x000001911302D2B0>", "<Process.Analysis.shader_type_analyzer.TypeAnalysisResult object at 0x000001911302D790>", "<Process.Analysis.shader_type_analyzer.TypeAnalysisResult object at 0x000001911302DFA0>"], "global_type_table": {"worldPos": "DataType.FLOAT3", "baseColor": "DataType.HALF4", "dotNL": "DataType.FLOAT"}, "overall_statistics": {"total_lines": 3, "total_nodes": 13, "total_variables": 3, "total_intermediate_results": 2, "total_type_conversions": 0, "total_precision_issues": 0, "type_distribution": {"float3": 1, "unknown": 9, "half4": 1, "float": 2}, "precision_accuracy_score": 23.076923076923077}}}, "files": {}, "analysis_method": "precise_tree_based"}