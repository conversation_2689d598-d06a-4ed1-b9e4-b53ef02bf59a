#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一的API接口
"""

import sys
import os
import webbrowser
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_unified_api():
    """测试统一的API接口"""
    print("🔄 测试统一的API接口")
    print("=" * 50)
    
    try:
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        processor = ShaderAnalysisProcessor()
        print("✅ ShaderAnalysisProcessor 导入成功")
        
        # 测试着色器代码
        test_shader = """
float3 worldPos = transform * localPos;
half4 baseColor = texture.sample(sampler, uv);
float3 normal = normalize(normalMatrix * localNormal);
float dotNL = dot(normal, lightDir);
half3 diffuse = baseColor.rgb * half(max(dotNL, 0.0));
float3 specular = pow(max(dot(reflect(-lightDir, normal), viewDir), 0.0), shininess);
half3 finalColor = diffuse + half3(specular);
"""
        
        print(f"📝 测试着色器代码: {len(test_shader)} 字符")
        
        # 测试统一的analyze_shader方法
        print("\n🔍 测试 analyze_shader 方法...")
        result = processor.analyze_shader(
            test_shader,
            save_reports=True,
            base_filename="unified_api_test"
        )
        
        print("✅ analyze_shader 方法调用成功")
        
        # 验证返回结果结构
        expected_keys = ['analysis', 'files', 'analysis_method', 'shader_content']
        for key in expected_keys:
            if key in result:
                print(f"   ✅ 包含键: {key}")
            else:
                print(f"   ❌ 缺少键: {key}")
        
        # 验证分析方法标识
        if result.get('analysis_method') == 'precise_tree_based':
            print("   ✅ 分析方法标识正确: precise_tree_based")
        else:
            print(f"   ❌ 分析方法标识错误: {result.get('analysis_method')}")
        
        # 验证精确分析数据
        if 'precise_analysis' in result['analysis']:
            precise_data = result['analysis']['precise_analysis']
            print("   ✅ 包含精确分析数据")
            
            if 'overall_statistics' in precise_data:
                stats = precise_data['overall_statistics']
                print(f"      总节点数: {stats.get('total_nodes', 0)}")
                print(f"      类型转换: {stats.get('total_type_conversions', 0)}")
                print(f"      准确性评分: {stats.get('precision_accuracy_score', 0):.1f}%")
        else:
            print("   ❌ 缺少精确分析数据")
        
        # 测试其他API方法
        print("\n🔧 测试其他API方法...")
        
        # 测试获取关键指标
        try:
            metrics = processor.get_key_metrics(result)
            print("   ✅ get_key_metrics 方法正常")
            print(f"      关键指标: {list(metrics.keys())}")
        except Exception as e:
            print(f"   ❌ get_key_metrics 方法失败: {str(e)}")
        
        # 测试获取分析摘要
        try:
            summary = processor.get_analysis_summary(result)
            print("   ✅ get_analysis_summary 方法正常")
            print(f"      摘要长度: {len(summary)} 字符")
        except Exception as e:
            print(f"   ❌ get_analysis_summary 方法失败: {str(e)}")
        
        # 测试获取优化建议
        try:
            suggestions = processor.get_optimization_suggestions(result)
            print("   ✅ get_optimization_suggestions 方法正常")
            print(f"      建议数量: {len(suggestions)}")
            for i, suggestion in enumerate(suggestions[:3], 1):
                print(f"      {i}. {suggestion}")
        except Exception as e:
            print(f"   ❌ get_optimization_suggestions 方法失败: {str(e)}")
        
        # 测试HTML报告生成
        try:
            html_content = processor.generate_html_report(result, test_shader)
            print("   ✅ generate_html_report 方法正常")
            print(f"      HTML长度: {len(html_content)} 字符")
        except Exception as e:
            print(f"   ❌ generate_html_report 方法失败: {str(e)}")
        
        # 检查生成的文件
        if 'files' in result:
            print(f"\n📄 生成的文件:")
            for file_type, file_path in result['files'].items():
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"   ✅ {file_type}: {file_path} ({file_size:,} 字节)")
                else:
                    print(f"   ❌ {file_type}: {file_path} (文件不存在)")
            
            # 打开HTML报告
            if 'html' in result['files'] and os.path.exists(result['files']['html']):
                html_file = result['files']['html']
                abs_path = os.path.abspath(html_file)
                file_url = f'file:///{abs_path.replace(os.sep, "/")}'
                print(f"\n🌐 打开HTML报告: {abs_path}")
                webbrowser.open(file_url)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_metal_shader_analysis():
    """测试Metal着色器分析"""
    print(f"\n🎯 测试Metal着色器分析")
    print("=" * 50)
    
    # 读取metal_shader_ps文件
    shader_file = "metal_shader_ps"
    if not os.path.exists(shader_file):
        print("❌ 未找到metal_shader_ps文件")
        return False
    
    try:
        with open(shader_file, 'r', encoding='utf-8') as f:
            shader_content = f.read()
        print(f"✅ 读取Metal着色器文件成功 ({len(shader_content)} 字符)")
    except Exception as e:
        print(f"❌ 读取文件失败: {str(e)}")
        return False
    
    try:
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        processor = ShaderAnalysisProcessor()
        
        print("开始分析Metal着色器...")
        result = processor.analyze_shader(
            shader_content,
            save_reports=True,
            base_filename="metal_shader_unified_api"
        )
        
        print("✅ Metal着色器分析完成")
        
        # 显示关键指标
        metrics = processor.get_key_metrics(result)
        print(f"\n📊 关键指标:")
        for key, value in metrics.items():
            if isinstance(value, float):
                print(f"   {key}: {value:.2f}")
            else:
                print(f"   {key}: {value:,}")
        
        # 显示优化建议
        suggestions = processor.get_optimization_suggestions(result)
        print(f"\n💡 优化建议:")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"   {i}. {suggestion}")
        
        return True
        
    except Exception as e:
        print(f"❌ Metal着色器分析失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🎯 统一API接口测试")
    print("=" * 60)
    print("改进内容:")
    print("• 🔄 移除原始分析方法，统一使用基于AST的精确分析")
    print("• 📝 更新API接口，analyze_shader 现在就是精确分析")
    print("• 🎯 简化方法调用，所有组件使用统一接口")
    print("• 📊 更新关键指标和优化建议以匹配新的数据结构")
    print()
    
    # 测试统一API
    api_test = test_unified_api()
    
    # 测试Metal着色器分析
    metal_test = test_metal_shader_analysis()
    
    print(f"\n🎉 测试结果总结")
    print("=" * 60)
    
    if api_test:
        print("✅ 统一API接口测试通过")
        print("   • analyze_shader 方法正常工作")
        print("   • 返回基于AST的精确分析结果")
        print("   • 所有辅助方法正常工作")
    else:
        print("❌ 统一API接口测试失败")
    
    if metal_test:
        print("✅ Metal着色器分析测试通过")
        print("   • 大型着色器文件分析正常")
        print("   • 关键指标计算正确")
        print("   • 优化建议生成正常")
    else:
        print("❌ Metal着色器分析测试失败")
    
    if api_test and metal_test:
        print(f"\n🎊 所有测试通过！")
        print("现在系统统一使用基于AST的精确类型分析:")
        print("• 🔄 单一的 analyze_shader 方法")
        print("• 🎯 基于语法树的精确类型推断")
        print("• 📊 完整的运算过程模拟")
        print("• 🌐 交互式HTML报告")
        print("• 💡 智能优化建议")
    else:
        print(f"\n❌ 部分测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
