#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终系统验证测试
"""

import sys
import os
import webbrowser
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_complete_system():
    """测试完整系统"""
    print("🎯 完整系统测试")
    print("=" * 60)
    
    try:
        # 1. 测试分析处理器
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        processor = ShaderAnalysisProcessor()
        print("✅ ShaderAnalysisProcessor 创建成功")
        
        # 2. 测试简单着色器分析
        test_shader = """
float3 worldPos = transform * localPos;
half4 baseColor = texture.sample(sampler, uv);
float3 normal = normalize(normalMatrix * localNormal);
float dotNL = dot(normal, lightDir);
half3 diffuse = baseColor.rgb * half(max(dotNL, 0.0));
float3 specular = pow(max(dot(reflect(-lightDir, normal), viewDir), 0.0), shininess);
half3 finalColor = diffuse + half3(specular);
"""
        
        print("开始分析测试着色器...")
        result = processor.analyze_shader(
            test_shader,
            save_reports=True,
            base_filename="final_system_test"
        )
        
        print("✅ 着色器分析完成")
        
        # 3. 验证结果结构
        if 'analysis' in result and 'precise_analysis' in result['analysis']:
            precise_data = result['analysis']['precise_analysis']
            overall_stats = precise_data['overall_statistics']
            
            print(f"📊 分析结果:")
            print(f"   总节点数: {overall_stats['total_nodes']}")
            print(f"   类型转换: {overall_stats['total_type_conversions']}")
            print(f"   准确性评分: {overall_stats['precision_accuracy_score']:.1f}%")
        else:
            print("❌ 分析结果结构错误")
            return False
        
        # 4. 测试辅助方法
        metrics = processor.get_key_metrics(result)
        print(f"✅ 关键指标获取成功: {len(metrics)} 个指标")
        
        summary = processor.get_analysis_summary(result)
        print(f"✅ 分析摘要生成成功: {len(summary)} 字符")
        
        suggestions = processor.get_optimization_suggestions(result)
        print(f"✅ 优化建议生成成功: {len(suggestions)} 条建议")
        
        html_content = processor.generate_html_report(result, test_shader)
        print(f"✅ HTML报告生成成功: {len(html_content):,} 字符")
        
        # 5. 检查生成的文件
        if 'files' in result:
            print(f"📄 生成的文件:")
            for file_type, file_path in result['files'].items():
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"   ✅ {file_type}: {file_path} ({file_size:,} 字节)")
                else:
                    print(f"   ❌ {file_type}: {file_path} (文件不存在)")
            
            # 打开HTML报告
            if 'html' in result['files'] and os.path.exists(result['files']['html']):
                html_file = result['files']['html']
                abs_path = os.path.abspath(html_file)
                file_url = f'file:///{abs_path.replace(os.sep, "/")}'
                print(f"🌐 打开HTML报告: {abs_path}")
                webbrowser.open(file_url)
        
        return True
        
    except Exception as e:
        print(f"❌ 系统测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_compatibility():
    """测试UI兼容性"""
    print("\n🎨 UI兼容性测试")
    print("=" * 60)
    
    try:
        # 测试主窗口导入
        from ui.main_window import MainWindow
        print("✅ MainWindow 导入成功")
        
        # 测试精确分析组件导入
        from ui.precise_type_analysis_widget import PreciseTypeAnalysisWidget, PreciseAnalysisWorker
        print("✅ PreciseTypeAnalysisWidget 导入成功")
        print("✅ PreciseAnalysisWorker 导入成功")
        
        # 验证API兼容性
        required_methods = [
            'start_analysis',
            'update_display',
            'clear_results',
            'set_loading_state',
            'on_analysis_completed',
            'on_analysis_failed'
        ]
        
        for method in required_methods:
            if hasattr(PreciseTypeAnalysisWidget, method):
                print(f"✅ {method} 方法存在")
            else:
                print(f"❌ {method} 方法缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ UI兼容性测试失败: {str(e)}")
        return False

def test_performance():
    """测试性能"""
    print("\n⚡ 性能测试")
    print("=" * 60)
    
    # 读取大型着色器文件
    shader_file = "metal_shader_ps"
    if not os.path.exists(shader_file):
        print("❌ 未找到大型测试文件")
        return True  # 不影响整体测试结果
    
    try:
        with open(shader_file, 'r', encoding='utf-8') as f:
            shader_content = f.read()
        
        print(f"测试大型着色器: {len(shader_content):,} 字符")
        
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        import time
        
        processor = ShaderAnalysisProcessor()
        
        start_time = time.time()
        result = processor.analyze_shader(shader_content, save_reports=False)
        end_time = time.time()
        
        analysis_time = end_time - start_time
        print(f"✅ 分析完成时间: {analysis_time:.2f} 秒")
        
        # 显示统计
        precise_data = result['analysis']['precise_analysis']
        overall_stats = precise_data['overall_statistics']
        
        print(f"📊 大型文件分析结果:")
        print(f"   总节点数: {overall_stats['total_nodes']:,}")
        print(f"   类型转换: {overall_stats['total_type_conversions']:,}")
        print(f"   准确性评分: {overall_stats['precision_accuracy_score']:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🎊 最终系统验证")
    print("=" * 80)
    print("验证内容:")
    print("• 🎯 完整系统功能测试")
    print("• 🎨 UI兼容性测试")
    print("• ⚡ 性能测试")
    print()
    
    # 执行测试
    system_ok = test_complete_system()
    ui_ok = test_ui_compatibility()
    perf_ok = test_performance()
    
    print(f"\n🎉 最终验证结果")
    print("=" * 80)
    
    if system_ok:
        print("✅ 完整系统功能验证通过")
        print("   • 统一的 analyze_shader 方法正常工作")
        print("   • 基于AST的精确类型分析正常")
        print("   • 所有辅助方法正常工作")
        print("   • HTML报告生成正常")
    else:
        print("❌ 完整系统功能验证失败")
    
    if ui_ok:
        print("✅ UI兼容性验证通过")
        print("   • 主窗口导入正常")
        print("   • 精确分析组件API完整")
        print("   • 与旧API完全兼容")
    else:
        print("❌ UI兼容性验证失败")
    
    if perf_ok:
        print("✅ 性能验证通过")
        print("   • 大型文件分析正常")
        print("   • 分析速度满足要求")
    else:
        print("❌ 性能验证失败")
    
    if all([system_ok, ui_ok, perf_ok]):
        print(f"\n🎊 系统统一完成！")
        print("整个着色器分析系统现在完全统一:")
        print()
        print("🔄 API统一:")
        print("   • 单一的 analyze_shader 方法")
        print("   • 统一的数据结构")
        print("   • 兼容的UI接口")
        print()
        print("🎯 分析引擎:")
        print("   • 基于AST的精确类型推断")
        print("   • 完整的运算过程模拟")
        print("   • 中间结果类型跟踪")
        print()
        print("📊 报告系统:")
        print("   • 交互式HTML报告")
        print("   • 智能优化建议")
        print("   • 完整的统计信息")
        print()
        print("🎨 用户界面:")
        print("   • 统一的分析窗口")
        print("   • 一致的用户体验")
        print("   • 简化的操作流程")
        print()
        print("🚀 系统已准备就绪，可以投入使用！")
    else:
        print(f"\n❌ 系统统一未完全完成，请检查失败的验证项")

if __name__ == "__main__":
    main()
