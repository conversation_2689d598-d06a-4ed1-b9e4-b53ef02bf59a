#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试HTML生成问题
"""

import sys
import os
import traceback
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_template_import():
    """测试模板导入"""
    print("🔍 测试模板导入")
    print("=" * 50)
    
    try:
        from Process.Analysis.templates.html_template import HTML_TEMPLATE_HEADER, HTML_TEMPLATE_FOOTER
        print("✅ 模板导入成功")
        print(f"   HTML_TEMPLATE_HEADER 长度: {len(HTML_TEMPLATE_HEADER):,} 字符")
        print(f"   HTML_TEMPLATE_FOOTER 长度: {len(HTML_TEMPLATE_FOOTER):,} 字符")
        return True
    except Exception as e:
        print(f"❌ 模板导入失败: {str(e)}")
        traceback.print_exc()
        return False

def test_report_generator_import():
    """测试报告生成器导入"""
    print("\n🔍 测试报告生成器导入")
    print("=" * 50)
    
    try:
        from Process.Analysis.analysis_report_generator import AnalysisReportGenerator, CodeLineData
        print("✅ 报告生成器导入成功")
        
        generator = AnalysisReportGenerator()
        print("✅ 报告生成器实例化成功")
        return generator
    except Exception as e:
        print(f"❌ 报告生成器导入失败: {str(e)}")
        traceback.print_exc()
        return None

def test_html_generation():
    """测试HTML生成"""
    print("\n🔍 测试HTML生成")
    print("=" * 50)
    
    generator = test_report_generator_import()
    if not generator:
        return False
    
    try:
        # 创建测试数据
        from Process.Analysis.analysis_report_generator import CodeLineData
        
        # 模拟精确分析数据
        precise_data = {
            'overall_statistics': {
                'total_nodes': 100,
                'total_type_conversions': 5,
                'precision_accuracy_score': 85.5,
                'total_operations': 50
            }
        }
        
        # 创建测试代码行数据
        code_lines_data = [
            CodeLineData(
                line_number=1,
                content="float3 worldPos = transform * localPos;",
                operation_count=2,
                conversion_count=0,
                precision_issues=0,
                node_details=["BinaryOp: *", "Assignment: ="],
                operation_types=["multiply", "assign"],
                has_analysis=True,
                css_classes=[],
                performance_level="normal"
            ),
            CodeLineData(
                line_number=2,
                content="half4 baseColor = texture.sample(sampler, uv);",
                operation_count=1,
                conversion_count=1,
                precision_issues=0,
                node_details=["FunctionCall: sample"],
                operation_types=["function_call"],
                has_analysis=True,
                css_classes=[],
                performance_level="conversion"
            ),
            CodeLineData(
                line_number=3,
                content="// 注释行",
                operation_count=0,
                conversion_count=0,
                precision_issues=0,
                node_details=[],
                operation_types=[],
                has_analysis=False,
                css_classes=[],
                performance_level="normal"
            )
        ]
        
        print("✅ 测试数据创建成功")
        print(f"   精确分析数据: {len(precise_data)} 个统计项")
        print(f"   代码行数据: {len(code_lines_data)} 行")
        
        # 生成HTML报告
        print("\n🔄 开始生成HTML报告...")
        html_content = generator.generate_precise_html_report(precise_data, code_lines_data)
        
        if html_content:
            print(f"✅ HTML报告生成成功: {len(html_content):,} 字符")
            
            # 检查HTML内容
            if "<!DOCTYPE html>" in html_content:
                print("✅ HTML文档类型声明正确")
            else:
                print("❌ 缺少HTML文档类型声明")
            
            if "<title>🎯 精确类型分析报告</title>" in html_content:
                print("✅ HTML标题正确")
            else:
                print("❌ HTML标题不正确")
            
            if "float3 worldPos = transform * localPos;" in html_content:
                print("✅ 代码内容包含在HTML中")
            else:
                print("❌ 代码内容未包含在HTML中")
            
            # 保存测试HTML文件
            test_html_file = "test_html_generation.html"
            with open(test_html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"✅ 测试HTML文件已保存: {test_html_file}")
            
            # 打开HTML文件
            import webbrowser
            abs_path = os.path.abspath(test_html_file)
            file_url = f'file:///{abs_path.replace(os.sep, "/")}'
            print(f"🌐 打开测试HTML: {abs_path}")
            webbrowser.open(file_url)
            
            return True
        else:
            print("❌ HTML报告生成失败: 返回空内容")
            return False
            
    except Exception as e:
        print(f"❌ HTML生成测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_shader_analysis_processor():
    """测试着色器分析处理器的HTML生成"""
    print("\n🔍 测试着色器分析处理器")
    print("=" * 50)
    
    try:
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        processor = ShaderAnalysisProcessor()
        print("✅ ShaderAnalysisProcessor 创建成功")
        
        # 测试简单着色器
        test_shader = """
float3 worldPos = transform * localPos;
half4 baseColor = texture.sample(sampler, uv);
float dotNL = dot(normal, lightDir);
"""
        
        print("🔄 开始分析测试着色器...")
        result = processor.analyze_shader(
            test_shader,
            save_reports=True,
            base_filename="html_generation_test"
        )
        
        print("✅ 着色器分析完成")
        
        # 检查结果中是否包含HTML文件
        if 'files' in result and 'html' in result['files']:
            html_file = result['files']['html']
            if os.path.exists(html_file):
                print(f"✅ HTML文件生成成功: {html_file}")
                
                # 检查文件大小
                file_size = os.path.getsize(html_file)
                print(f"   文件大小: {file_size:,} 字节")
                
                if file_size > 1000:  # 至少1KB
                    print("✅ HTML文件大小正常")
                    
                    # 打开HTML文件
                    import webbrowser
                    abs_path = os.path.abspath(html_file)
                    file_url = f'file:///{abs_path.replace(os.sep, "/")}'
                    print(f"🌐 打开HTML报告: {abs_path}")
                    webbrowser.open(file_url)
                    
                    return True
                else:
                    print("❌ HTML文件太小，可能生成失败")
                    return False
            else:
                print(f"❌ HTML文件不存在: {html_file}")
                return False
        else:
            print("❌ 结果中没有HTML文件信息")
            print(f"   结果键: {list(result.keys())}")
            if 'files' in result:
                print(f"   文件键: {list(result['files'].keys())}")
            return False
            
    except Exception as e:
        print(f"❌ 着色器分析处理器测试失败: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 HTML生成问题诊断")
    print("=" * 80)
    
    # 执行测试
    template_ok = test_template_import()
    html_ok = test_html_generation()
    processor_ok = test_shader_analysis_processor()
    
    print(f"\n🎉 诊断结果总结")
    print("=" * 80)
    
    if template_ok:
        print("✅ 模板导入正常")
    else:
        print("❌ 模板导入失败")
    
    if html_ok:
        print("✅ HTML生成正常")
    else:
        print("❌ HTML生成失败")
    
    if processor_ok:
        print("✅ 着色器分析处理器HTML生成正常")
    else:
        print("❌ 着色器分析处理器HTML生成失败")
    
    if all([template_ok, html_ok, processor_ok]):
        print(f"\n🎊 所有测试通过！HTML生成功能正常")
    else:
        print(f"\n❌ 部分测试失败，请检查失败的项目")
        
        # 提供解决建议
        print(f"\n💡 解决建议:")
        if not template_ok:
            print("   • 检查模板文件路径和内容")
            print("   • 确保 Process/Analysis/templates/html_template.py 存在")
        if not html_ok:
            print("   • 检查报告生成器的HTML生成逻辑")
            print("   • 确保 CodeLineData 类的方法正常工作")
        if not processor_ok:
            print("   • 检查着色器分析处理器的文件保存逻辑")
            print("   • 确保 save_reports=True 时能正确保存HTML文件")

if __name__ == "__main__":
    main()
