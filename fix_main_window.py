#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复主窗口文件
"""

import os
import re

def fix_main_window():
    """修复主窗口文件"""
    main_window_path = "UI/main_window.py"
    
    if not os.path.exists(main_window_path):
        print(f"❌ 文件不存在: {main_window_path}")
        return False
    
    try:
        # 读取文件内容
        with open(main_window_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("🔧 修复主窗口文件...")
        
        # 1. 更新导入，移除对shader_analysis_widget的导入
        content = re.sub(
            r'from ui\.shader_analysis_widget import ShaderAnalysisWidget\n',
            '',
            content
        )
        
        # 2. 删除旧的AST分析action
        content = re.sub(
            r'\s*# 基于语法树的着色器分析\s*\n\s*shader_AST_analysis_action = QAction.*?\n\s*shader_AST_analysis_action\.triggered\.connect.*?\n\s*shader_AST_analysis_action\.setShortcut.*?\n\s*process_menu\.addAction\(shader_AST_analysis_action\)\s*\n',
            '',
            content,
            flags=re.DOTALL
        )
        
        # 3. 更新on_shader_analysis函数
        old_function = r'def on_shader_analysis\(self\):.*?self\.analysis_dialog\.show\(\)  # 非模态显示，不阻塞主窗口'
        new_function = '''def on_shader_analysis(self):
        """着色器分析 - 基于AST的精确类型分析"""
        # 获取代码内容
        shader_content=self.code_area_widget.get_current_text()
        if not shader_content or not shader_content.strip() or len(shader_content) == 0 :
            QMessageBox.warning(self, '分析失败', '无代码内容')
            return

        # 创建精确类型分析弹窗
        self.analysis_dialog = QDialog(self)
        self.analysis_dialog.setWindowTitle("🎯 着色器分析")
        self.analysis_dialog.setModal(False)  # 非模态
        self.analysis_dialog.setMinimumSize(800, 600)
        self.analysis_dialog.setMaximumSize(1400, 1000)
        self.analysis_dialog.resize(1000, 700)

        # 居中显示
        if self.geometry().isValid():
            parent_center = self.geometry().center()
            self.analysis_dialog.move(parent_center.x() - 500, parent_center.y() - 350)

        # 设置弹窗样式
        self.analysis_dialog.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
        """)

        # 布局
        layout = QVBoxLayout(self.analysis_dialog)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 添加精确分析组件
        analysis_widget = PreciseTypeAnalysisWidget()
        layout.addWidget(analysis_widget)

        # 显示弹窗并开始分析
        analysis_widget.start_analysis(shader_content)
        self.analysis_dialog.show()  # 非模态显示，不阻塞主窗口'''
        
        content = re.sub(old_function, new_function, content, flags=re.DOTALL)
        
        # 4. 删除on_shader_AST_analysis函数
        content = re.sub(
            r'\s*def on_shader_AST_analysis\(self\):.*?self\.precise_analysis_dialog\.show\(\)  # 非模态显示，不阻塞主窗口',
            '',
            content,
            flags=re.DOTALL
        )
        
        # 写回文件
        with open(main_window_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 主窗口文件修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 修复主窗口文件")
    print("=" * 50)
    
    if fix_main_window():
        print("✅ 修复完成")
    else:
        print("❌ 修复失败")

if __name__ == "__main__":
    main()
