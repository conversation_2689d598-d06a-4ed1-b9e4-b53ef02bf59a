#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试集成的精确类型分析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_core_analyzer_precise_method():
    """测试核心分析器的精确分析方法"""
    print("🔧 核心分析器精确分析测试")
    print("=" * 60)
    
    from Process.Analysis.shader_analyzer_core import ShaderAnalyzerCore
    
    # 测试着色器代码
    test_shader = """
float a = b + c;
half d = half(intensity);
float3 result = normalize(pos) * scale;
"""
    
    analyzer = ShaderAnalyzerCore()
    
    print("1. 原始分析方法:")
    print("-" * 30)
    original_result = analyzer.analyze_shader_content(test_shader)
    print(f"总操作数: {original_result['statistics']['total_operations']}")
    print(f"操作类型: {list(original_result['statistics']['operation_types'].keys())}")
    
    print("\n2. 精确分析方法:")
    print("-" * 30)
    precise_result = analyzer.analyze_shader_with_precise_types(test_shader)
    
    if 'precise_analysis' in precise_result:
        precise_stats = precise_result['precise_analysis']['overall_statistics']
        print(f"总节点数: {precise_stats['total_nodes']}")
        print(f"中间结果: {precise_stats['total_intermediate_results']}")
        print(f"变量声明: {precise_stats['total_variables']}")
        print(f"准确性评分: {precise_stats['precision_accuracy_score']:.1f}%")
        
        # 显示对比结果
        if 'comparison' in precise_result:
            comparison = precise_result['comparison']
            improvement = comparison['operation_count_improvement']
            print(f"\n📈 改进对比:")
            print(f"  节点数提升: +{improvement['improvement']} ({improvement['improvement_percentage']:.1f}%)")
    
    return precise_result

def test_processor_precise_method():
    """测试处理器的精确分析方法"""
    print("\n" + "=" * 60)
    print("🏭 处理器精确分析测试")
    print("=" * 60)
    
    from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
    
    # 复杂测试用例
    complex_shader = """
float3 worldPos = transform * localPos;
half4 baseColor = texture.sample(sampler, uv);
float3 normal = normalize(normalMatrix * localNormal);
float dotNL = dot(normal, lightDir);
half3 diffuse = baseColor.rgb * half(max(dotNL, 0.0));
float3 finalColor = diffuse + specular;
"""
    
    processor = ShaderAnalysisProcessor()
    
    print("1. 标准分析:")
    print("-" * 30)
    standard_result = processor.analyze_shader(complex_shader, save_reports=False)
    standard_stats = standard_result['analysis']['statistics']
    print(f"总操作数: {standard_stats['total_operations']}")
    print(f"类型转换: {standard_stats.get('precision_conversions', 0)}")
    print(f"混合精度: {standard_stats.get('mixed_precision_ops', 0)}")
    
    print("\n2. 精确分析:")
    print("-" * 30)
    precise_result = processor.analyze_shader_with_precise_types(complex_shader, save_reports=False)
    
    if 'precise_analysis' in precise_result['analysis']:
        precise_stats = precise_result['analysis']['precise_analysis']['overall_statistics']
        print(f"总节点数: {precise_stats['total_nodes']}")
        print(f"中间结果: {precise_stats['total_intermediate_results']}")
        print(f"类型转换: {precise_stats['total_type_conversions']}")
        print(f"精度问题: {precise_stats['total_precision_issues']}")
        print(f"准确性评分: {precise_stats['precision_accuracy_score']:.1f}%")
    
    print("\n3. 摘要报告:")
    print("-" * 30)
    print(precise_result['summary'])
    
    return precise_result

def test_detailed_node_analysis():
    """测试详细节点分析"""
    print("\n" + "=" * 60)
    print("🔍 详细节点分析测试")
    print("=" * 60)
    
    from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
    
    # 包含各种节点类型的测试用例
    test_shader = """
float a = b + c;
half result = half(max(a, d) * intensity);
"""
    
    processor = ShaderAnalysisProcessor()
    result = processor.analyze_shader_with_precise_types(test_shader, save_reports=False)
    
    if 'precise_analysis' in result['analysis']:
        precise_data = result['analysis']['precise_analysis']
        
        print("逐行节点分析:")
        for i, (code_line, analysis) in enumerate(zip(precise_data['code_lines'], precise_data['precise_analyses'])):
            print(f"\n第{i+1}行: {code_line.content}")
            print("节点详情:")
            
            for typed_node in analysis.typed_nodes:
                node = typed_node.node
                node_type = node.node_type.value
                node_value = node.value
                inferred_type = typed_node.inferred_type.value
                is_intermediate = "中间结果" if typed_node.is_intermediate else "直接节点"
                confidence = typed_node.type_confidence
                
                print(f"  • {node_type}({node_value}) → {inferred_type} [{is_intermediate}] (置信度: {confidence:.1f})")
            
            print(f"统计: 节点{analysis.statistics['total_nodes']}, 中间结果{analysis.statistics['intermediate_results']}")

def test_report_generation():
    """测试报告生成"""
    print("\n" + "=" * 60)
    print("📄 报告生成测试")
    print("=" * 60)
    
    from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
    
    test_shader = """
float3 pos = worldMatrix * localPos;
half4 color = texture.sample(sampler, uv);
float intensity = dot(normal, lightDir);
half3 result = color.rgb * half(intensity);
"""
    
    processor = ShaderAnalysisProcessor()
    
    print("生成精确分析报告...")
    result = processor.analyze_shader_with_precise_types(test_shader, save_reports=True, base_filename="test_precise")
    
    if 'files' in result:
        print("生成的报告文件:")
        for report_type, file_path in result['files'].items():
            print(f"  {report_type}: {file_path}")
        
        # 检查HTML报告内容
        if 'html' in result['files']:
            html_file = result['files']['html']
            if os.path.exists(html_file):
                with open(html_file, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 检查关键内容
                checks = [
                    ('精确分析标题', '精确着色器类型分析报告' in html_content),
                    ('节点信息', '🔸' in html_content or '🔹' in html_content),
                    ('统计信息', '总节点数' in html_content),
                    ('代码行分析', 'code-line' in html_content)
                ]
                
                print(f"\nHTML报告内容检查:")
                for check_name, passed in checks:
                    print(f"  {check_name}: {'✅' if passed else '❌'}")
    
    return result

def test_performance_comparison():
    """测试性能对比"""
    print("\n" + "=" * 60)
    print("⚡ 性能对比测试")
    print("=" * 60)
    
    import time
    from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
    
    # 大型测试用例
    large_shader = """
float3 worldPos = transform * localPos;
half4 baseColor = texture.sample(sampler, uv);
float3 normal = normalize(normalMatrix * localNormal);
float3 lightDir = normalize(lightPos - worldPos);
float dotNL = dot(normal, lightDir);
half3 diffuse = baseColor.rgb * half(max(dotNL, 0.0));
float3 viewDir = normalize(cameraPos - worldPos);
float3 halfDir = normalize(lightDir + viewDir);
float dotNH = dot(normal, halfDir);
half specular = half(pow(max(dotNH, 0.0), shininess));
half3 finalColor = diffuse + half3(specular);
""" * 3  # 重复3次
    
    processor = ShaderAnalysisProcessor()
    
    # 测试标准方法
    print("测试标准分析方法...")
    start_time = time.time()
    for _ in range(5):
        standard_result = processor.analyze_shader(large_shader, save_reports=False)
    standard_time = time.time() - start_time
    
    # 测试精确方法
    print("测试精确分析方法...")
    start_time = time.time()
    for _ in range(5):
        precise_result = processor.analyze_shader_with_precise_types(large_shader, save_reports=False)
    precise_time = time.time() - start_time
    
    print(f"\n性能对比结果:")
    print(f"标准方法耗时: {standard_time:.3f}s")
    print(f"精确方法耗时: {precise_time:.3f}s")
    print(f"性能比率: {precise_time/standard_time:.2f}x")
    
    # 分析质量对比
    standard_ops = standard_result['analysis']['statistics']['total_operations']
    if 'precise_analysis' in precise_result['analysis']:
        precise_nodes = precise_result['analysis']['precise_analysis']['overall_statistics']['total_nodes']
        print(f"\n分析质量对比:")
        print(f"标准方法识别: {standard_ops} 个操作")
        print(f"精确方法识别: {precise_nodes} 个节点")
        print(f"精度提升: {(precise_nodes - standard_ops) / standard_ops * 100:.1f}%")

def main():
    """主函数"""
    print("🚀 集成精确类型分析功能测试")
    print("=" * 70)
    print("新功能特点:")
    print("• 🎯 在核心分析器中集成精确类型分析")
    print("• 🏭 在处理器中提供精确分析接口")
    print("• 📊 对比原有方法和精确方法的结果")
    print("• 📄 生成精确分析的专用报告")
    print("• 🔍 提供详细的节点级别分析")
    print()
    
    try:
        test_core_analyzer_precise_method()
        test_processor_precise_method()
        test_detailed_node_analysis()
        test_report_generation()
        test_performance_comparison()
        
        print("\n" + "=" * 70)
        print("🎉 所有测试完成！")
        print("精确类型分析功能已成功集成到着色器分析系统中。")
        print("\n主要改进:")
        print("✅ 基于语法树的精确节点识别")
        print("✅ 中间结果节点的准确统计")
        print("✅ 智能类型推断和置信度评估")
        print("✅ 详细的性能问题检测")
        print("✅ 专用的精确分析报告")
        print("✅ 与原有系统的无缝集成")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
