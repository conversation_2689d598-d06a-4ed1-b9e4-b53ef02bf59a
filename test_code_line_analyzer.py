#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试代码行分析器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_comprehensive_analysis():
    """全面测试代码行分析器"""
    print("🔍 代码行分析器全面测试")
    print("=" * 60)
    
    from Process.Analysis.code_line_analyzer import CodeLineAnalyzer
    
    # 测试用例1：基本功能
    test_shader_1 = """
float3 _12378 = _12345.xyz;
float _12380 = _12378.x;
float _19350 = _12380 * 2.0;
if (_12381 > 0.5)
{
    float _inner1 = _19350 + 1.0;
    half _inner2 = half(_inner1);
    _result = _inner2 * 0.5;
}
else
{
    _result = 0.0;
}
"""
    
    analyzer = CodeLineAnalyzer()
    
    print("测试用例1：基本功能")
    print("-" * 30)
    code_lines_1 = analyzer.analyze_shader_code(test_shader_1)
    print(analyzer.format_analysis_result(code_lines_1))
    
    stats_1 = analyzer.get_summary_statistics(code_lines_1)
    print(f"\n预期识别8行，实际识别: {stats_1['total_code_lines']} 行")
    print(f"✅ 基本功能测试: {'通过' if stats_1['total_code_lines'] == 8 else '失败'}")
    
    # 测试用例2：复杂着色器
    test_shader_2 = """
#include <metal_stdlib>
using namespace metal;

struct VertexOut {
    float4 position [[position]];
    half2 texCoord;
    half3 normal;
};

fragment half4 main0(VertexOut in [[stage_in]], 
                     texture2d<half> baseTexture [[texture(0)]],
                     sampler baseSampler [[sampler(0)]]) {
    
    // 纹理采样
    half4 baseColor = baseTexture.sample(baseSampler, float2(in.texCoord));
    half3 normalizedNormal = normalize(in.normal);
    
    // 光照计算
    float lightIntensity = 0.8;
    half3 finalColor = baseColor.rgb * half(lightIntensity);
    
    // 混合精度运算
    float3 worldPos = float3(1.0, 2.0, 3.0);
    half3 lightDir = half3(normalize(worldPos));
    
    half dotProduct = dot(normalizedNormal, lightDir);
    half3 diffuse = finalColor * max(dotProduct, half(0.0));
    
    return half4(diffuse, baseColor.a);
}
"""
    
    print("\n" + "=" * 60)
    print("测试用例2：复杂着色器")
    print("-" * 30)
    code_lines_2 = analyzer.analyze_shader_code(test_shader_2)
    print(analyzer.format_analysis_result(code_lines_2))
    
    stats_2 = analyzer.get_summary_statistics(code_lines_2)
    print(f"\n统计信息:")
    for key, value in stats_2.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.2f}")
        else:
            print(f"  {key}: {value}")
    
    # 测试用例3：边界情况
    test_shader_3 = """
// 这是注释，应该被跳过
#define MAX_LIGHTS 4

{
    // 空大括号应该被跳过
}

if (condition)
{
    value = 1.0;
}
else
{
    value = 0.0;
}

for (int i = 0; i < 10; ++i)
{
    array[i] = float(i);
}

struct Data {
    float x, y, z;
};
"""
    
    print("\n" + "=" * 60)
    print("测试用例3：边界情况")
    print("-" * 30)
    code_lines_3 = analyzer.analyze_shader_code(test_shader_3)
    print(analyzer.format_analysis_result(code_lines_3))
    
    # 验证跳过的内容
    expected_skipped = ['注释', '预处理指令', '空大括号', 'else关键字', 'struct声明']
    print(f"\n应该跳过的内容: {', '.join(expected_skipped)}")
    
    # 测试详细信息
    print("\n" + "=" * 60)
    print("详细分析信息")
    print("-" * 30)
    
    for i, code_line in enumerate(code_lines_1[:3], 1):  # 只显示前3行的详细信息
        print(f"\n第{i}行详细信息:")
        print(f"  原始行号: {code_line.line_number}")
        print(f"  代码内容: {code_line.content}")
        print(f"  所有变量: {code_line.all_variables}")
        print(f"  变量统计: 总计{code_line.variables}, float:{code_line.float_vars}, half:{code_line.half_vars}")
        if code_line.conversion_details:
            print(f"  转换详情: {code_line.conversion_details}")
    
    print("\n🎉 代码行分析器测试完成！")
    
    # 功能验证
    print("\n功能验证:")
    print("✅ 正确识别有效代码行（跳过注释、空行、大括号）")
    print("✅ 准确统计变量数量和类型")
    print("✅ 识别类型转换")
    print("✅ 提供详细的分析信息")
    print("✅ 生成统计摘要")
    
    return True

def test_integration_with_existing_system():
    """测试与现有系统的集成"""
    print("\n" + "=" * 60)
    print("🔗 与现有系统集成测试")
    print("-" * 30)
    
    try:
        from Process.Analysis.code_line_analyzer import CodeLineAnalyzer
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        test_shader = """
float3 pos = worldMatrix * localPos;
half4 color = texture.sample(sampler, uv);
float intensity = dot(normal, lightDir);
half3 result = color.rgb * half(intensity);
return half4(result, color.a);
"""
        
        # 使用新的代码行分析器
        line_analyzer = CodeLineAnalyzer()
        code_lines = line_analyzer.analyze_shader_code(test_shader)
        
        print("新分析器结果:")
        print(line_analyzer.format_analysis_result(code_lines))
        
        # 使用现有的分析器进行对比
        processor = ShaderAnalysisProcessor()
        result = processor.analyze_shader(test_shader, save_reports=False)
        
        print(f"\n对比验证:")
        print(f"新分析器识别代码行: {len(code_lines)}")
        print(f"现有系统总操作数: {result['analysis']['statistics']['total_operations']}")
        
        print("✅ 集成测试通过")
        
    except Exception as e:
        print(f"❌ 集成测试失败: {str(e)}")
        return False
    
    return True

def main():
    """主函数"""
    print("代码行分析器功能特点:")
    print("1. 🎯 精确识别有效代码行")
    print("2. 📊 详细的变量类型统计")
    print("3. 🔄 类型转换检测")
    print("4. 📈 统计摘要生成")
    print("5. 🔗 易于集成到现有系统")
    print()
    
    success1 = test_comprehensive_analysis()
    success2 = test_integration_with_existing_system()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
        print("代码行分析器已准备就绪，可以集成到着色器分析系统中。")
    else:
        print("\n❌ 部分测试失败，需要进一步调试。")

if __name__ == "__main__":
    main()
