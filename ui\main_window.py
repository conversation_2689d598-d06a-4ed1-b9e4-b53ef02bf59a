"""
代码对比显示组件使用示例（单区域组件实现对比）
"""
import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QSplitter, QMenuBar, QAction, QFileDialog, QMenu, QShortcut
from PyQt5.QtCore import Qt, QSettings, QTimer
from PyQt5.QtGui import QKeySequence
import os
from PyQt5.QtWidgets import QMessageBox, QDialog, QVBoxLayout 

from ui.text_area_widget import TextAreaWidget
from ui.compiler_explorer_panel import CompilerExplorerPanel
from ui.process_widget import ProcessWidget
from ui.file_tree_widget import FileTreeWidget

from ui.precise_type_analysis_widget import PreciseTypeAnalysisWidget

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()

        self.setWindowTitle(f'代码对比显示示例')
        self.setGeometry(100, 100, 1200, 800)
        self.settings = QSettings('shaderProfile', 'shaderProcess')
        self.recent_files = self._load_recent_files()
        # 菜单栏
        self.menu_bar = self.menuBar()
        self._create_menus()
        # 主体区域：左侧为文件树，中间为code文本区，右侧为compile文本区
        from PyQt5.QtWidgets import QSplitter
        self.splitter = QSplitter(Qt.Horizontal)
        self.splitter.setStyleSheet("""
            QSplitter::handle {
                background: #e0e0e0;
                border: none;
                width: 1px;
            }
            QSplitter::handle:hover {
                background: #0078d4;
            }
        """)

        # 左侧文件树
        self.file_tree_widget = FileTreeWidget()
        self.file_tree_widget.file_double_clicked.connect(self.on_file_tree_double_clicked)
        self.file_tree_widget.setMinimumWidth(150)  # 减少最小宽度，但保持可用性
        self.file_tree_widget.setMaximumWidth(500)
        # 设置文件树的背景样式
        self.file_tree_widget.setStyleSheet("""
            FileTreeWidget {
                background-color: #f8f9fa;
                border-right: 1px solid #e0e0e0;
            }
        """)
        self.splitter.addWidget(self.file_tree_widget)

        # 中间代码区域
        self.code_area_widget = TextAreaWidget()
        self.code_area_widget.setMinimumWidth(300)  # 减少最小宽度，提高灵活性
        self.splitter.addWidget(self.code_area_widget)

        # 右侧编译区域
        self.compile_area_widget = TextAreaWidget(is_compile=True)
        self.compile_area_widget.setMinimumWidth(250)  # 减少最小宽度，提高灵活性
        self.splitter.addWidget(self.compile_area_widget)

        self.splitter.setCollapsible(0, False)  # 文件树不可折叠，防止消失
        self.splitter.setCollapsible(1, False)  # 代码区域不可折叠
        self.splitter.setCollapsible(2, False)  # 编译区域不可折叠
        self.splitter.setSizes([200, 500, 350])  # 文件树:代码区域:编译区域 (调整默认比例)
        self.setCentralWidget(self.splitter)

        # 设置窗口最小尺寸，确保所有区域都能正常显示
        self.setMinimumSize(800, 600)  # 最小宽度800px，高度600px
        # 算法工具箱dock
        self.process_panel = ProcessWidget(self)
        self.addDockWidget(Qt.RightDockWidgetArea, self.process_panel)
        self.process_panel.hide()

        # 设置全局快捷键
        self._setup_global_shortcuts()


    def _load_recent_files(self):
        files = self.settings.value('recent_files', [])
        if isinstance(files, str):
            # QSettings有时会返回str
            import ast
            try:
                files = ast.literal_eval(files)
            except Exception:
                files = []
        return files if isinstance(files, list) else []

    def _save_recent_files(self):
        self.settings.setValue('recent_files', self.recent_files)

    def _create_menus(self):
        # 文件菜单
        file_menu = self.menu_bar.addMenu('文件')
        import_file_action = QAction('导入文件', self)
        import_file_action.triggered.connect(self.on_import_file)
        file_menu.addAction(import_file_action)

        import_folder_action = QAction('导入文件夹', self)
        import_folder_action.triggered.connect(self.on_import_folder)
        file_menu.addAction(import_folder_action)

        file_menu.addSeparator()

        save_action = QAction('保存 (Ctrl+S)', self)
        save_action.triggered.connect(self.on_save)
        file_menu.addAction(save_action)

        save_as_action = QAction('另存为 (Ctrl+Shift+S)', self)
        save_as_action.triggered.connect(self.on_save_as)
        file_menu.addAction(save_as_action)
        # 最近文件子菜单
        self.recent_menu = QMenu('最近文件', self)
        file_menu.addMenu(self.recent_menu)
        self._update_recent_files_menu()
        # 编辑菜单
        edit_menu = self.menu_bar.addMenu('编辑')

        # 撤销和重做
        undo_action = QAction('撤销 (Ctrl+Z)', self)
        undo_action.triggered.connect(self.on_undo)
        undo_action.setShortcut(QKeySequence.Undo)
        edit_menu.addAction(undo_action)

        redo_action = QAction('重做 (Ctrl+Y)', self)
        redo_action.triggered.connect(self.on_redo)
        redo_action.setShortcut(QKeySequence.Redo)
        edit_menu.addAction(redo_action)

        edit_menu.addSeparator()

        # 代码格式化
        format_action = QAction('格式化代码 (Ctrl+Shift+F)', self)
        format_action.triggered.connect(self.on_format_code)
        format_action.setShortcut(QKeySequence("Ctrl+Shift+F"))
        edit_menu.addAction(format_action)

        # 处理菜单
        process_menu = self.menu_bar.addMenu('处理')

        # 优化器组子菜单
        optimizers_menu = QMenu('HLSL优化器', self)
        process_menu.addMenu(optimizers_menu)

        # 基础优化器组
        basic_optimizers_action = QAction('基础优化器', self)
        basic_optimizers_action.triggered.connect(self.on_basic_optimizers)
        optimizers_menu.addAction(basic_optimizers_action)

        # 外部工具优化器组
        external_tools_action = QAction('外部工具优化器', self)
        external_tools_action.triggered.connect(self.on_external_tools)
        optimizers_menu.addAction(external_tools_action)

        # 传统优化器组
        legacy_optimizers_action = QAction('传统优化器', self)
        legacy_optimizers_action.triggered.connect(self.on_legacy_optimizers)
        optimizers_menu.addAction(legacy_optimizers_action)

        # 视图菜单
        view_menu = self.menu_bar.addMenu('视图')

        # 文件树视图
        self.file_tree_action = QAction('文件树', self)
        self.file_tree_action.setCheckable(True)
        self.file_tree_action.setChecked(True)  # 默认显示
        self.file_tree_action.triggered.connect(self.on_toggle_file_tree)
        view_menu.addAction(self.file_tree_action)

        # 代码区域视图
        self.code_area_action = QAction('代码区域', self)
        self.code_area_action.setCheckable(True)
        self.code_area_action.setChecked(True)  # 默认显示
        self.code_area_action.triggered.connect(self.on_toggle_code_area)
        view_menu.addAction(self.code_area_action)

        # 编译区域视图
        self.compile_area_action = QAction('编译区域', self)
        self.compile_area_action.setCheckable(True)
        self.compile_area_action.setChecked(True)  # 默认显示
        self.compile_area_action.triggered.connect(self.on_toggle_compile_area)
        view_menu.addAction(self.compile_area_action)

        view_menu.addSeparator()

        # 重置布局
        reset_layout_action = QAction('重置布局', self)
        reset_layout_action.triggered.connect(self.on_reset_layout)
        view_menu.addAction(reset_layout_action)


        process_menu.addSeparator()

        # 着色器分析
        shader_analysis_action = QAction('🎯 着色器精确类型分析', self)
        shader_analysis_action.triggered.connect(self.on_shader_analysis)
        shader_analysis_action.setShortcut("Ctrl+Alt+A")
        process_menu.addAction(shader_analysis_action)

        process_menu.addSeparator()

        # 新建编译区
        new_compile_action = QAction('新建编译区', self)
        new_compile_action.triggered.connect(self.on_new_compile_tab)
        process_menu.addAction(new_compile_action)

    def _setup_global_shortcuts(self):
        """设置全局快捷键"""
        # 全局Ctrl+S快捷键
        self.global_save_shortcut = QShortcut(QKeySequence.Save, self)
        self.global_save_shortcut.activated.connect(lambda: self._on_global_save())

        # 全局Ctrl+Shift+S快捷键
        self.global_save_as_shortcut = QShortcut(QKeySequence("Ctrl+Shift+S"), self)
        self.global_save_as_shortcut.activated.connect(lambda: self._on_global_save_as())

        # 全局Ctrl+Z快捷键（撤销）
        self.global_undo_shortcut = QShortcut(QKeySequence.Undo, self)
        self.global_undo_shortcut.activated.connect(lambda: self._on_global_undo())

        # 全局Ctrl+Y快捷键（重做）
        self.global_redo_shortcut = QShortcut(QKeySequence.Redo, self)
        self.global_redo_shortcut.activated.connect(lambda: self._on_global_redo())

        # 全局Ctrl+Shift+F快捷键（格式化）
        self.global_format_shortcut = QShortcut(QKeySequence("Ctrl+Shift+F"), self)
        self.global_format_shortcut.activated.connect(lambda: self._on_global_format())

    def _on_global_save(self):
        """全局Ctrl+S快捷键处理"""
        # 检查当前焦点是否在可编辑的代码区域
        focused_widget = QApplication.focusWidget()

        # 检查焦点是否在左侧代码区域（非编译区域）
        if self._is_focus_in_editable_code_area(focused_widget):
            self.on_save()

    def _on_global_save_as(self):
        """全局Ctrl+Shift+S快捷键处理"""
        # 检查当前焦点是否在可编辑的代码区域
        focused_widget = QApplication.focusWidget()

        # 检查焦点是否在左侧代码区域（非编译区域）
        if self._is_focus_in_editable_code_area(focused_widget):
            self.on_save_as()

    def _is_focus_in_editable_code_area(self, focused_widget):
        """检查焦点是否在可编辑的代码区域（左侧代码区域，非编译区域）"""
        if not focused_widget:
            return False

        # 检查焦点widget是否属于左侧代码区域
        # 方法1：检查是否是左侧TextAreaWidget的子组件
        parent = focused_widget
        while parent:
            if parent == self.code_area_widget:
                return True
            elif parent == self.compile_area_widget:
                return False
            parent = parent.parent()

        # 方法2：检查widget类型和可编辑性
        # 如果是QsciScintilla或QTextEdit，检查是否只读
        if hasattr(focused_widget, 'isReadOnly'):
            is_readonly = focused_widget.isReadOnly()
            if not is_readonly:
                # 进一步检查是否属于左侧代码区域
                return self._widget_belongs_to_code_area(focused_widget)

        return False

    def _widget_belongs_to_code_area(self, widget):
        """检查widget是否属于左侧代码区域"""
        # 遍历左侧代码区域的所有tab
        for i in range(self.code_area_widget.tab_widget.count()):
            tab_widget = self.code_area_widget.tab_widget.widget(i)
            if self._is_widget_descendant(widget, tab_widget):
                return True
        return False

    def _is_widget_descendant(self, child, ancestor):
        """检查child是否是ancestor的后代widget"""
        parent = child
        while parent:
            if parent == ancestor:
                return True
            parent = parent.parent()
        return False

    def _on_global_undo(self):
        """全局Ctrl+Z快捷键处理"""
        focused_widget = QApplication.focusWidget()
        if self._is_focus_in_editable_code_area(focused_widget):
            self.on_undo()

    def _on_global_redo(self):
        """全局Ctrl+Y快捷键处理"""
        focused_widget = QApplication.focusWidget()
        if self._is_focus_in_editable_code_area(focused_widget):
            self.on_redo()

    def _on_global_format(self):
        """全局Ctrl+Shift+F快捷键处理"""
        focused_widget = QApplication.focusWidget()
        if self._is_focus_in_editable_code_area(focused_widget):
            self.on_format_code()

    def _update_recent_files_menu(self):
        self.recent_menu.clear()
        # 过滤掉不存在的文件/文件夹，并更新列表
        existing_files = []
        for i, f in enumerate(self.recent_files):
            if os.path.exists(f):
                existing_files.append(f)
                # 区分文件和文件夹的显示
                if os.path.isdir(f):
                    display_name = f"📁 {os.path.basename(f)}"
                else:
                    display_name = f"📄 {os.path.basename(f)}"
                act = QAction(display_name, self)
                act.setToolTip(f)  # 显示完整路径作为提示
                # 修复lambda闭包问题
                act.triggered.connect(self._create_recent_file_handler(f))
                self.recent_menu.addAction(act)

        # 如果有文件被删除，更新列表
        if len(existing_files) != len(self.recent_files):
            self.recent_files = existing_files
            self._save_recent_files()

    def _create_recent_file_handler(self, file_path):
        """创建最近文件的处理器，避免lambda闭包问题"""
        return lambda: self.on_open_recent_file(file_path)

    def _add_to_recent_files(self, path):
        if path in self.recent_files:
            self.recent_files.remove(path)
        self.recent_files.insert(0, path)
        self.recent_files = self.recent_files[:10]
        self._save_recent_files()
        self._update_recent_files_menu()

    def get_default_model(self, file_path):
        return 'QScintilla'

    def add_file_tab(self, file_path, content, add_to_recent=True):
        model = self.get_default_model(file_path)
        file_name = os.path.basename(file_path)
        widget = self.code_area_widget.add_code_page(file_name, content, source=file_path, model=model)
        if add_to_recent:
            self._add_to_recent_files(file_path)
        return widget

    def on_import_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, '选择要导入的文件', '', '所有文件 (*);;文本文件 (*.txt);;HLSL (*.hlsl);;GLSL (*.glsl);;C++ (*.cpp *.h)')
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except Exception:
                try:
                    with open(file_path, 'r', encoding='gbk') as f:
                        content = f.read()
                except Exception as e:       
                    QMessageBox.warning(self, '打开失败', f'无法读取文件:\n{file_path}\n{e}')
                    return
            self.add_file_tab(file_path, content)
            # 添加文件到文件树
            self.file_tree_widget.add_root_path(file_path)

    def on_import_folder(self):
        """导入文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, '选择要导入的文件夹')
        if folder_path:
            # 添加文件夹到文件树
            self.file_tree_widget.add_root_path(folder_path)
            # 添加文件夹到最近路径
            self._add_to_recent_files(folder_path)

    def on_file_tree_double_clicked(self, file_path):
        """处理文件树中文件的双击事件"""
        if self._is_file_already_open(file_path):
            self._switch_to_file_tab(file_path)
        else:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except Exception:
                try:
                    with open(file_path, 'r', encoding='gbk') as f:
                        content = f.read()
                except Exception as e:
         
                    QMessageBox.warning(self, '打开失败', f'无法读取文件:\n{file_path}\n{e}')
                    return
            # 从文件树双击打开的文件，不自动添加到最近文件（因为文件夹已经在最近文件中）
            self.add_file_tab(file_path, content, add_to_recent=False)

    def _is_file_already_open(self, file_path):
        """检查文件是否已经在代码区域中打开"""
        abs_file_path = os.path.abspath(file_path)
        
        for i in range(self.code_area_widget.tab_widget.count()):
            widget = self.code_area_widget.tab_widget.widget(i)
            actual_widget = self.code_area_widget._get_actual_code_widget(widget)
            if actual_widget and hasattr(actual_widget, 'source') and actual_widget.source:
                # 标准化已打开文件的路径
                abs_source_path = os.path.abspath(actual_widget.source)
                if abs_source_path == abs_file_path:
                    return True
        return False

    def _switch_to_file_tab(self, file_path):
        """切换到指定文件的tab"""
        abs_file_path = os.path.abspath(file_path)
        
        for i in range(self.code_area_widget.tab_widget.count()):
            widget = self.code_area_widget.tab_widget.widget(i)
            actual_widget = self.code_area_widget._get_actual_code_widget(widget)
            if actual_widget and hasattr(actual_widget, 'source') and actual_widget.source:
                # 标准化已打开文件的路径
                abs_source_path = os.path.abspath(actual_widget.source)
                if abs_source_path == abs_file_path:
                    self.code_area_widget.tab_widget.setCurrentIndex(i)
                    return True
        return False

    def on_save(self):
        """保存当前文件"""
        # 获取当前选中的tab
        current_widget = self.code_area_widget.get_current_widget()
        if current_widget is None:
 
            QMessageBox.warning(self, '保存失败', '没有可保存的内容')
            return

        # 获取实际的代码显示组件
        actual_widget = self.code_area_widget._get_actual_code_widget(current_widget)
        if actual_widget:
            actual_widget.save_file()
        else:
            QMessageBox.warning(self, '保存失败', '无法获取当前窗口的代码组件')

    def on_save_as(self):
        # 获取当前选中的tab
        current_widget = self.code_area_widget.get_current_widget()
        if current_widget is None:
            QMessageBox.warning(self, '保存失败', '没有可保存的内容')
            return

        # 设置文件过滤器，支持HLSL和GLSL格式
        file_filters = (
            'HLSL文件 (*.hlsl);;'
            'GLSL文件 (*.glsl *.vert *.frag *.geom *.tesc *.tese *.comp);;'
            'Shader文件 (*.shader);;'
            'C++文件 (*.cpp *.h *.hpp);;'
            '文本文件 (*.txt);;'
            '所有文件 (*)'
        )

        file_path, _ = QFileDialog.getSaveFileName(self, '另存为', 'default.hlsl', file_filters)
        if file_path:
            try:
                # 获取当前widget的文本内容
                content = current_widget.get_text()
                if content is None:
                    QMessageBox.warning(self, '保存失败', '无法获取当前窗口的文本内容')
                    return
                # 保存文件时不添加额外的换行符
                with open(file_path, 'w', encoding='utf-8', newline='') as f:
                    f.write(content)
                self._add_to_recent_files(file_path)
                QMessageBox.information(self, '保存成功', f'文件已保存到:\n{file_path}')
            except Exception as e:
                QMessageBox.warning(self, '保存失败', f'保存文件失败:\n{file_path}\n{e}')

    def on_open_recent_file(self, path):
        if os.path.isdir(path):
            # 如果是文件夹，直接添加到文件树，并更新最近文件顺序
            self.file_tree_widget.add_root_path(path)
            self._add_to_recent_files(path)  # 更新最近文件顺序
        else:
            # 如果是文件，读取内容并添加到代码区域
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except Exception:
                try:
                    with open(path, 'r', encoding='gbk') as f:
                        content = f.read()
                except Exception as e:       
                    QMessageBox.warning(self, '打开失败', f'无法读取文件:\n{path}\n{e}')
                    return
            self.add_file_tab(path, content, add_to_recent=False)
            self.file_tree_widget.add_root_path(path)
            self._add_to_recent_files(path)

    def on_basic_optimizers(self):
        """显示基础优化器组"""
        self.process_panel.show_algorithm('basic_optimizers')

    def on_external_tools(self):
        """显示外部工具优化器组"""
        self.process_panel.show_algorithm('external_tools')

    def on_legacy_optimizers(self):
        """显示传统优化器组"""
        self.process_panel.show_algorithm('legacy_optimizers')

    def on_new_compile_tab(self):
        # 统计当前已有多少个compile tab
        idx = self.compile_area_widget.tab_widget.count()+ 1
        self.compile_area_widget.add_code_page(f'编译({idx})', '请点击编译按钮')

    # 视图控制方法
    def on_toggle_file_tree(self, checked):
        """切换文件树显示/隐藏"""
        try:
            if checked:
                self.file_tree_widget.show()
            else:
                self.file_tree_widget.hide()
            # 延迟更新尺寸，避免立即调用时的问题
            QTimer.singleShot(100, self._update_splitter_sizes)
        except Exception as e:
            print(f"切换文件树时出错: {e}")

    def on_toggle_code_area(self, checked):
        """切换代码区域显示/隐藏"""
        try:
            if checked:
                self.code_area_widget.show()
            else:
                self.code_area_widget.hide()
            # 延迟更新尺寸，避免立即调用时的问题
            QTimer.singleShot(100, self._update_splitter_sizes)
        except Exception as e:
            print(f"切换代码区域时出错: {e}")

    def on_toggle_compile_area(self, checked):
        """切换编译区域显示/隐藏"""
        try:
            if checked:
                self.compile_area_widget.show()
            else:
                self.compile_area_widget.hide()
            # 延迟更新尺寸，避免立即调用时的问题
            QTimer.singleShot(100, self._update_splitter_sizes)
        except Exception as e:
            print(f"切换编译区域时出错: {e}")

    def on_reset_layout(self):
        """重置布局到默认状态"""
        # 显示所有区域
        self.file_tree_widget.show()
        self.code_area_widget.show()
        self.compile_area_widget.show()

        # 更新菜单状态
        self.file_tree_action.setChecked(True)
        self.code_area_action.setChecked(True)
        self.compile_area_action.setChecked(True)

        # 重置分割器尺寸
        self.splitter.setSizes([200, 500, 350])

    def _update_splitter_sizes(self):
        """根据当前显示的区域更新分割器尺寸"""
        try:
            visible_widgets = []
            if self.file_tree_widget.isVisible():
                visible_widgets.append(0)
            if self.code_area_widget.isVisible():
                visible_widgets.append(1)
            if self.compile_area_widget.isVisible():
                visible_widgets.append(2)

            # 获取当前splitter宽度，如果为0则使用默认值
            total_width = self.splitter.width()
            if total_width <= 0:
                total_width = 1000  # 默认宽度

            # 如果只有一个区域可见，给它全部空间
            if len(visible_widgets) == 1:
                sizes = [0, 0, 0]
                sizes[visible_widgets[0]] = total_width
                self.splitter.setSizes(sizes)
            # 如果有两个区域可见，平分空间
            elif len(visible_widgets) == 2:
                sizes = [0, 0, 0]
                half_width = total_width // 2
                sizes[visible_widgets[0]] = half_width
                sizes[visible_widgets[1]] = half_width
                self.splitter.setSizes(sizes)
            # 如果三个区域都可见，使用默认比例
            elif len(visible_widgets) == 3:
                self.splitter.setSizes([200, 500, 350])
        except Exception as e:
            print(f"更新分割器尺寸时出错: {e}")
            # 发生错误时，恢复默认布局
            self.splitter.setSizes([200, 500, 350])

    def on_undo(self):
        """撤销操作"""
        current_widget = self.code_area_widget.get_current_widget()
        if current_widget is None:
            QMessageBox.warning(self, '撤销失败', '没有可撤销的内容')
            return

        actual_widget = self.code_area_widget._get_actual_code_widget(current_widget)
        if actual_widget and hasattr(actual_widget, 'undo'):
            success = actual_widget.undo()
            if not success:
     
                QMessageBox.information(self, '撤销', '没有可撤销的操作')
        else:
 
            QMessageBox.warning(self, '撤销失败', '无法获取当前窗口的代码组件')

    def on_redo(self):
        """重做操作"""
        current_widget = self.code_area_widget.get_current_widget()
        if current_widget is None:
            QMessageBox.warning(self, '重做失败', '没有可重做的内容')
            return

        actual_widget = self.code_area_widget._get_actual_code_widget(current_widget)
        if actual_widget and hasattr(actual_widget, 'redo'):
            success = actual_widget.redo()
            if not success:     
                QMessageBox.information(self, '重做', '没有可重做的操作')
        else:
            QMessageBox.warning(self, '重做失败', '无法获取当前窗口的代码组件')

    def on_format_code(self):
        """格式化代码"""
        current_widget = self.code_area_widget.get_current_widget()
        if current_widget is None:
            QMessageBox.warning(self, '格式化失败', '没有可格式化的内容')
            return

        actual_widget = self.code_area_widget._get_actual_code_widget(current_widget)
        if actual_widget and hasattr(actual_widget, 'format_code'):
            actual_widget.format_code()
        else:
            QMessageBox.warning(self, '格式化失败', '无法获取当前窗口的代码组件')

    def on_shader_analysis(self):
        """着色器精确类型分析"""
        # 获取代码内容
        shader_content = self.code_area_widget.get_current_text()
        if not shader_content or not shader_content.strip() or len(shader_content) == 0:
            QMessageBox.warning(self, '分析失败', '无代码内容')
            return

        # 创建精确类型分析弹窗
        self.analysis_dialog = QDialog(self)
        self.analysis_dialog.setWindowTitle("🎯 着色器精确类型分析")
        self.analysis_dialog.setModal(False)  # 非模态
        self.analysis_dialog.setMinimumSize(800, 600)
        self.analysis_dialog.setMaximumSize(1400, 1000)
        self.analysis_dialog.resize(1000, 700)

        # 居中显示
        if self.geometry().isValid():
            parent_center = self.geometry().center()
            self.analysis_dialog.move(parent_center.x() - 500, parent_center.y() - 350)

        # 设置弹窗样式
        self.analysis_dialog.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
        """)

        # 布局
        layout = QVBoxLayout(self.analysis_dialog)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 添加精确分析组件
        analysis_widget = PreciseTypeAnalysisWidget()
        layout.addWidget(analysis_widget)

        # 显示弹窗并开始分析
        analysis_widget.start_analysis(shader_content)
        self.analysis_dialog.show()  # 非模态显示，不阻塞主窗口
