#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试清理后的系统
"""

import sys
import os
import webbrowser
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_removed_components():
    """测试已移除的组件"""
    print("🗑️ 测试已移除的组件")
    print("=" * 50)
    
    # 检查已删除的文件
    removed_files = [
        'Process/Analysis/shader_analyzer_core.py'
    ]
    
    for file_path in removed_files:
        if not os.path.exists(file_path):
            print(f"   ✅ 已删除: {file_path}")
        else:
            print(f"   ❌ 仍存在: {file_path}")
    
    # 检查是否有对已删除组件的导入
    try:
        from Process.Analysis.shader_analyzer_core import ShaderAnalyzerCore
        print("   ❌ shader_analyzer_core 仍可导入")
        return False
    except ImportError:
        print("   ✅ shader_analyzer_core 已无法导入")
    
    return True

def test_new_report_generator():
    """测试新的报告生成器"""
    print("\n📊 测试新的报告生成器")
    print("=" * 50)
    
    try:
        from Process.Analysis.report_generator import ReportGenerator, CodeLineData
        
        print("   ✅ 新的报告生成器导入成功")
        
        # 测试CodeLineData
        line_data = CodeLineData(
            line_number=1,
            content="float a = b + c;",
            operation_count=2,
            conversion_count=1,
            has_analysis=True,
            performance_level="conversion"
        )
        
        stats_text = line_data.get_stats_text(True)
        color_style = line_data.get_color_style(True)
        
        print(f"   ✅ CodeLineData 工作正常")
        print(f"      统计文本: {stats_text}")
        print(f"      颜色样式: {color_style}")
        
        # 测试报告生成器
        generator = ReportGenerator()
        print(f"   ✅ ReportGenerator 创建成功")
        
        # 检查方法
        if hasattr(generator, 'generate_precise_html_report'):
            print(f"   ✅ generate_precise_html_report 方法存在")
        else:
            print(f"   ❌ generate_precise_html_report 方法缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        return False

def test_unified_system():
    """测试统一系统"""
    print("\n🎯 测试统一系统")
    print("=" * 50)
    
    try:
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        processor = ShaderAnalysisProcessor()
        
        # 测试简单代码
        test_code = """
float3 worldPos = transform * localPos;
half4 baseColor = texture.sample(sampler, uv);
float dotNL = dot(normal, lightDir);
"""
        
        print("   开始分析测试代码...")
        result = processor.analyze_shader(test_code, save_reports=True, base_filename="cleaned_system_test")
        
        print("   ✅ 分析完成")
        
        # 验证结果结构
        if 'analysis' in result and 'precise_analysis' in result['analysis']:
            print("   ✅ 包含精确分析数据")
            
            precise_data = result['analysis']['precise_analysis']
            if 'overall_statistics' in precise_data:
                stats = precise_data['overall_statistics']
                print(f"      总节点数: {stats.get('total_nodes', 0)}")
                print(f"      类型转换: {stats.get('total_type_conversions', 0)}")
        else:
            print("   ❌ 缺少精确分析数据")
            return False
        
        # 测试其他方法
        try:
            metrics = processor.get_key_metrics(result)
            print("   ✅ get_key_metrics 正常")
        except Exception as e:
            print(f"   ❌ get_key_metrics 失败: {str(e)}")
            return False
        
        try:
            summary = processor.get_analysis_summary(result)
            print("   ✅ get_analysis_summary 正常")
        except Exception as e:
            print(f"   ❌ get_analysis_summary 失败: {str(e)}")
            return False
        
        try:
            html_content = processor.generate_html_report(result, test_code)
            print("   ✅ generate_html_report 正常")
            print(f"      HTML长度: {len(html_content):,} 字符")
        except Exception as e:
            print(f"   ❌ generate_html_report 失败: {str(e)}")
            return False
        
        # 检查生成的文件
        if 'files' in result and 'html' in result['files']:
            html_file = result['files']['html']
            if os.path.exists(html_file):
                print(f"   ✅ HTML文件生成成功: {html_file}")
                
                # 打开HTML文件
                abs_path = os.path.abspath(html_file)
                file_url = f'file:///{abs_path.replace(os.sep, "/")}'
                print(f"   🌐 打开HTML报告: {abs_path}")
                webbrowser.open(file_url)
            else:
                print(f"   ❌ HTML文件不存在: {html_file}")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_performance():
    """测试性能"""
    print("\n⚡ 测试性能")
    print("=" * 50)
    
    # 读取大型着色器文件
    shader_file = "metal_shader_ps"
    if not os.path.exists(shader_file):
        print("   ❌ 未找到测试文件")
        return False
    
    try:
        with open(shader_file, 'r', encoding='utf-8') as f:
            shader_content = f.read()
        
        print(f"   测试大型着色器: {len(shader_content)} 字符")
        
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        import time
        
        processor = ShaderAnalysisProcessor()
        
        start_time = time.time()
        result = processor.analyze_shader(shader_content, save_reports=False)
        end_time = time.time()
        
        analysis_time = end_time - start_time
        print(f"   ✅ 分析完成时间: {analysis_time:.2f} 秒")
        
        # 显示统计
        precise_data = result['analysis']['precise_analysis']
        overall_stats = precise_data['overall_statistics']
        
        print(f"   📊 分析结果:")
        print(f"      总节点数: {overall_stats['total_nodes']:,}")
        print(f"      类型转换: {overall_stats['total_type_conversions']:,}")
        print(f"      准确性评分: {overall_stats['precision_accuracy_score']:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 性能测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🧹 清理后的系统测试")
    print("=" * 60)
    print("测试内容:")
    print("• 🗑️ 验证已移除的组件 (shader_analyzer_core)")
    print("• 📊 测试新的报告生成器 (只保留精确分析方法)")
    print("• 🎯 测试统一的分析系统")
    print("• ⚡ 测试性能表现")
    print()
    
    # 执行测试
    removed_ok = test_removed_components()
    generator_ok = test_new_report_generator()
    system_ok = test_unified_system()
    perf_ok = test_performance()
    
    print(f"\n🎉 测试结果总结")
    print("=" * 60)
    
    if removed_ok:
        print("✅ 组件清理验证通过")
        print("   • shader_analyzer_core 已完全移除")
        print("   • 无残留导入或依赖")
    else:
        print("❌ 组件清理验证失败")
    
    if generator_ok:
        print("✅ 新报告生成器验证通过")
        print("   • CodeLineData 数据结构正常")
        print("   • generate_precise_html_report 方法正常")
    else:
        print("❌ 新报告生成器验证失败")
    
    if system_ok:
        print("✅ 统一分析系统验证通过")
        print("   • analyze_shader 方法正常")
        print("   • 所有辅助方法正常")
        print("   • HTML报告生成正常")
    else:
        print("❌ 统一分析系统验证失败")
    
    if perf_ok:
        print("✅ 性能测试通过")
        print("   • 大型文件分析正常")
        print("   • 分析速度满足要求")
    else:
        print("❌ 性能测试失败")
    
    if all([removed_ok, generator_ok, system_ok, perf_ok]):
        print(f"\n🎊 系统清理完成！")
        print("现在系统更加简洁高效:")
        print("• 🗑️ 移除了不需要的 shader_analyzer_core")
        print("• 📊 report_generator 只保留精确分析方法")
        print("• 🎯 统一使用基于AST的精确类型分析")
        print("• ⚡ 性能和功能完全正常")
        print("• 🔄 API接口更加简洁统一")
    else:
        print(f"\n❌ 系统清理未完全完成，请检查失败的测试项")

if __name__ == "__main__":
    main()
