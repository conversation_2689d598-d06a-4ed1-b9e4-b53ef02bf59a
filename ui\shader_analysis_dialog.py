#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
着色器分析弹窗
"""

from PyQt5.QtWidgets import QDialog, QVBoxLayout
from PyQt5.QtCore import Qt

from .shader_analysis_widget import ShaderAnalysisWidget


class ShaderAnalysisDialog(QDialog):
    """着色器分析弹窗"""

    def __init__(self, parent=None, shader_content=""):
        super().__init__(parent)
        self.shader_content = shader_content
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("🔍 Metal着色器分析")
        self.setModal(True)  # 模态对话框
        self.resize(800, 600)  # 设置合适的大小

        # 主布局 - 直接放入分析组件
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)  # 无边距
        layout.setSpacing(0)  # 无间距

        # 直接添加分析组件
        self.analysis_widget = ShaderAnalysisWidget()
        layout.addWidget(self.analysis_widget)

        # 设置对话框样式
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
        """)

    def showEvent(self, event):
        """显示事件"""
        super().showEvent(event)
        # 窗口显示后自动开始分析
        if self.shader_content and self.analysis_widget:
            self.analysis_widget.start_analysis(self.shader_content)


# 测试代码
if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication

    app = QApplication(sys.argv)

    test_shader = """
#include <metal_stdlib>
using namespace metal;

fragment half4 main0(VertexOut in [[stage_in]]) {
    half4 color = half4(1.0, 0.5, 0.0, 1.0);
    float intensity = 0.8;
    return color * half(intensity);
}
"""

    dialog = ShaderAnalysisDialog(None, test_shader)
    dialog.show()

    sys.exit(app.exec_())
