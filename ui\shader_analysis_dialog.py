#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
着色器分析弹窗
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QLabel, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon, QFont

from .shader_analysis_widget import ShaderAnalysisWidget


class ShaderAnalysisDialog(QDialog):
    """着色器分析弹窗"""
    
    # 信号
    analysis_completed = pyqtSignal(dict)  # 分析完成信号
    
    def __init__(self, parent=None, shader_content=""):
        super().__init__(parent)
        self.shader_content = shader_content
        self.analysis_widget = None
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("🔍 Metal着色器分析")
        self.setModal(True)  # 模态对话框
        self.resize(800, 600)  # 设置合适的大小
        
        # 设置窗口图标（如果有的话）
        try:
            self.setWindowIcon(QIcon("resources/icons/analysis.png"))
        except:
            pass
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 标题区域
        title_layout = QHBoxLayout()
        
        title_label = QLabel("🔍 Metal着色器Half/Float运算分析")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #333333; margin-bottom: 10px;")
        
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        layout.addLayout(title_layout)
        
        # 分析组件
        self.analysis_widget = ShaderAnalysisWidget()
        self.analysis_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        layout.addWidget(self.analysis_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 重新分析按钮
        self.reanalyze_btn = QPushButton("🔄 重新分析")
        self.reanalyze_btn.setFixedHeight(35)
        self.reanalyze_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        
        # 关闭按钮
        self.close_btn = QPushButton("❌ 关闭")
        self.close_btn.setFixedHeight(35)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        
        button_layout.addStretch()
        button_layout.addWidget(self.reanalyze_btn)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        
        # 设置对话框样式
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)
        
    def setup_connections(self):
        """设置信号连接"""
        self.close_btn.clicked.connect(self.accept)
        self.reanalyze_btn.clicked.connect(self.reanalyze)
        
        # 连接分析组件的信号
        if self.analysis_widget:
            # 如果分析组件有完成信号，可以连接
            pass
    
    def start_analysis(self, shader_content=None):
        """开始分析"""
        if shader_content:
            self.shader_content = shader_content
            
        if self.analysis_widget and self.shader_content:
            self.analysis_widget.start_analysis(self.shader_content)
    
    def reanalyze(self):
        """重新分析"""
        if self.analysis_widget and self.shader_content:
            self.analysis_widget.start_analysis(self.shader_content)
    
    def get_analysis_results(self):
        """获取分析结果"""
        if self.analysis_widget:
            return self.analysis_widget.get_analysis_results()
        return None
    
    def showEvent(self, event):
        """显示事件"""
        super().showEvent(event)
        # 窗口显示后自动开始分析
        if self.shader_content:
            self.start_analysis()
    
    def closeEvent(self, event):
        """关闭事件"""
        # 可以在这里保存分析结果或执行清理操作
        super().closeEvent(event)


def show_shader_analysis_dialog(parent, shader_content):
    """显示着色器分析对话框的便捷函数"""
    dialog = ShaderAnalysisDialog(parent, shader_content)
    result = dialog.exec_()
    
    if result == QDialog.Accepted:
        return dialog.get_analysis_results()
    return None


# 测试代码
if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    test_shader = """
#include <metal_stdlib>
using namespace metal;

fragment half4 main0(VertexOut in [[stage_in]]) {
    half4 color = half4(1.0, 0.5, 0.0, 1.0);
    float intensity = 0.8;
    return color * half(intensity);
}
"""
    
    dialog = ShaderAnalysisDialog(None, test_shader)
    dialog.show()
    
    sys.exit(app.exec_())
