#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示统一的着色器分析功能
"""

import sys
import os
import webbrowser
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QHBoxLayout, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class DemoMainWindow(QMainWindow):
    """演示主窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("🎯 统一着色器分析演示")
        self.setGeometry(100, 100, 1000, 700)
        
        # 中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("🎯 统一着色器精确类型分析演示")
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
        """)
        layout.addWidget(title_label)
        
        # 说明文本
        info_label = QLabel("""
这是统一的着色器分析系统演示。系统特点：
• 🎯 基于AST的精确类型分析
• 📊 完整的运算过程模拟
• 🌐 交互式HTML报告
• 💡 智能优化建议
• ⚡ 异步分析处理

请在下方输入着色器代码，然后点击"开始分析"按钮。
        """)
        info_label.setStyleSheet("""
            font-size: 14px;
            color: #666;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4fc3f7;
        """)
        layout.addWidget(info_label)
        
        # 代码输入区域
        code_label = QLabel("着色器代码:")
        code_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        layout.addWidget(code_label)
        
        self.code_input = QTextEdit()
        self.code_input.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', monospace;
                font-size: 12px;
                background-color: #f8f9fa;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 10px;
            }
        """)
        
        # 默认着色器代码
        default_shader = """float3 worldPos = transform * localPos;
half4 baseColor = texture.sample(sampler, uv);
float3 normal = normalize(normalMatrix * localNormal);
float dotNL = dot(normal, lightDir);
half3 diffuse = baseColor.rgb * half(max(dotNL, 0.0));
float3 specular = pow(max(dot(reflect(-lightDir, normal), viewDir), 0.0), shininess);
half3 finalColor = diffuse + half3(specular);"""
        
        self.code_input.setPlainText(default_shader)
        layout.addWidget(self.code_input)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.analyze_btn = QPushButton("🎯 开始精确分析")
        self.analyze_btn.setStyleSheet("""
            QPushButton {
                background-color: #4fc3f7;
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #29b6f6;
            }
            QPushButton:pressed {
                background-color: #0288d1;
            }
        """)
        self.analyze_btn.clicked.connect(self.start_analysis)
        
        self.clear_btn = QPushButton("🗑️ 清空代码")
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #e53935;
            }
        """)
        self.clear_btn.clicked.connect(self.clear_code)
        
        self.load_sample_btn = QPushButton("📝 加载示例")
        self.load_sample_btn.setStyleSheet("""
            QPushButton {
                background-color: #66bb6a;
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #4caf50;
            }
        """)
        self.load_sample_btn.clicked.connect(self.load_sample)
        
        button_layout.addWidget(self.analyze_btn)
        button_layout.addWidget(self.clear_btn)
        button_layout.addWidget(self.load_sample_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: white;
            }
        """)
    
    def start_analysis(self):
        """开始分析"""
        shader_content = self.code_input.toPlainText().strip()
        if not shader_content:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "警告", "请输入着色器代码")
            return
        
        try:
            # 导入统一分析组件
            from ui.unified_shader_analysis_widget import UnifiedShaderAnalysisWidget
            from PyQt5.QtWidgets import QDialog, QVBoxLayout
            
            # 创建分析对话框
            self.analysis_dialog = QDialog(self)
            self.analysis_dialog.setWindowTitle("🎯 着色器精确类型分析")
            self.analysis_dialog.setModal(False)
            self.analysis_dialog.setMinimumSize(1000, 800)
            self.analysis_dialog.resize(1200, 900)
            
            # 居中显示
            if self.geometry().isValid():
                parent_center = self.geometry().center()
                self.analysis_dialog.move(parent_center.x() - 600, parent_center.y() - 450)
            
            # 布局
            layout = QVBoxLayout(self.analysis_dialog)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.setSpacing(0)
            
            # 添加分析组件
            analysis_widget = UnifiedShaderAnalysisWidget()
            layout.addWidget(analysis_widget)
            
            # 显示对话框并开始分析
            self.analysis_dialog.show()
            analysis_widget.start_analysis(shader_content)
            
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"启动分析失败:\n{str(e)}")
    
    def clear_code(self):
        """清空代码"""
        self.code_input.clear()
    
    def load_sample(self):
        """加载示例代码"""
        sample_shaders = [
            # 示例1：基本光照
            """// 基本光照着色器
float3 worldPos = transform * localPos;
half4 baseColor = texture.sample(sampler, uv);
float3 normal = normalize(normalMatrix * localNormal);
float dotNL = dot(normal, lightDir);
half3 diffuse = baseColor.rgb * half(max(dotNL, 0.0));
float3 specular = pow(max(dot(reflect(-lightDir, normal), viewDir), 0.0), shininess);
half3 finalColor = diffuse + half3(specular);""",
            
            # 示例2：复杂材质
            """// 复杂材质着色器
float4 albedo = albedoTexture.sample(sampler, uv);
float3 normal = normalTexture.sample(sampler, uv).rgb * 2.0 - 1.0;
float roughness = roughnessTexture.sample(sampler, uv).r;
float metallic = metallicTexture.sample(sampler, uv).r;

float3 worldNormal = normalize(normalMatrix * normal);
float3 viewDir = normalize(cameraPos - worldPos);
float3 lightDir = normalize(lightPos - worldPos);

float NdotL = max(dot(worldNormal, lightDir), 0.0);
float NdotV = max(dot(worldNormal, viewDir), 0.0);
float3 halfVector = normalize(lightDir + viewDir);
float NdotH = max(dot(worldNormal, halfVector), 0.0);

float3 F0 = mix(float3(0.04), albedo.rgb, metallic);
float3 fresnel = F0 + (1.0 - F0) * pow(1.0 - NdotV, 5.0);

float3 diffuse = albedo.rgb * (1.0 - metallic) * NdotL;
float3 specular = fresnel * NdotL * NdotH;

float3 finalColor = diffuse + specular;""",
            
            # 示例3：类型转换密集
            """// 类型转换测试
half a = 1.0;
float b = half(2.0);
half2 c = float2(a, b);
float3 d = half3(c.xy, 1.0);
half4 e = float4(d, a);
float result = half(e.x + e.y) * float(c.x);"""
        ]
        
        import random
        selected_shader = random.choice(sample_shaders)
        self.code_input.setPlainText(selected_shader)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建并显示主窗口
    window = DemoMainWindow()
    window.show()
    
    print("🎯 统一着色器分析演示启动成功！")
    print("=" * 50)
    print("功能特点:")
    print("• 🎯 基于AST的精确类型分析")
    print("• 📊 完整的运算过程模拟")
    print("• 🌐 交互式HTML报告")
    print("• 💡 智能优化建议")
    print("• ⚡ 异步分析处理")
    print()
    print("使用方法:")
    print("1. 在文本框中输入或编辑着色器代码")
    print("2. 点击'开始精确分析'按钮")
    print("3. 在弹出的分析窗口中查看结果")
    print("4. 可以保存报告或打开HTML报告")
    print()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
