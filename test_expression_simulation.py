#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试表达式模拟功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_temp_variable_manager():
    """测试临时变量管理器"""
    print("🔍 测试临时变量管理器")
    print("=" * 50)
    
    try:
        from Process.Analysis.temp_variable_manager import TempVariableManager, SimulationStep
        
        manager = TempVariableManager()
        print("✅ TempVariableManager 创建成功")
        
        # 测试临时变量生成
        temp1 = manager.get_next_temp_var()
        temp2 = manager.get_next_temp_var()
        temp3 = manager.get_next_temp_var()
        
        print(f"   生成的临时变量: {temp1}, {temp2}, {temp3}")
        
        if temp1 == "tmp_0" and temp2 == "tmp_1" and temp3 == "tmp_2":
            print("✅ 临时变量命名正确")
        else:
            print("❌ 临时变量命名错误")
            return False
        
        # 测试模拟步骤
        step = SimulationStep(
            step_index=0,
            operation="add",
            target_var=temp1,
            operands=["a", "b"],
            expression="a + b",
            node_type="BinOp",
            line_number=1
        )
        
        manager.add_simulation_step(step)
        print("✅ 模拟步骤添加成功")
        
        # 测试获取步骤
        line_steps = manager.get_line_simulation_steps(1)
        if len(line_steps) == 1:
            print("✅ 按行获取模拟步骤正确")
        else:
            print("❌ 按行获取模拟步骤失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_expression_simulator():
    """测试表达式模拟器"""
    print("\n🔍 测试表达式模拟器")
    print("=" * 50)
    
    try:
        from Process.Analysis.temp_variable_manager import TempVariableManager
        from Process.Analysis.expression_simulator import ExpressionSimulator
        import ast
        
        manager = TempVariableManager()
        simulator = ExpressionSimulator(manager)
        print("✅ ExpressionSimulator 创建成功")
        
        # 测试简单二元操作
        expr = "a + b"
        tree = ast.parse(expr, mode='eval')
        result_var = simulator.simulate_expression(tree.body, 1, "test")
        
        print(f"   表达式 '{expr}' 模拟结果: {result_var}")
        
        steps = manager.get_line_simulation_steps(1)
        if len(steps) == 1:
            step = steps[0]
            print(f"   生成步骤: {step.expression}")
            print("✅ 简单表达式模拟成功")
        else:
            print("❌ 简单表达式模拟失败")
            return False
        
        # 测试复杂表达式
        manager.reset_counter()
        simulator.step_counter = 0
        
        expr2 = "a + b * c"
        tree2 = ast.parse(expr2, mode='eval')
        result_var2 = simulator.simulate_expression(tree2.body, 2, "test")
        
        print(f"   表达式 '{expr2}' 模拟结果: {result_var2}")
        
        steps2 = manager.get_line_simulation_steps(2)
        print(f"   生成了 {len(steps2)} 个步骤:")
        for step in steps2:
            print(f"     {step.expression}")
        
        if len(steps2) >= 2:
            print("✅ 复杂表达式模拟成功")
        else:
            print("❌ 复杂表达式模拟失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_syntax_tree_simulation():
    """测试语法树模拟功能"""
    print("\n🔍 测试语法树模拟功能")
    print("=" * 50)
    
    try:
        from Process.Analysis.syntax_tree_builder import SyntaxTreeBuilder
        
        builder = SyntaxTreeBuilder()
        print("✅ SyntaxTreeBuilder 创建成功")
        
        # 测试简单赋值
        test_cases = [
            "a = b + c",
            "result = x * y + z",
            "final = a + b + c + d",
            "value = func(x, y)",
            "temp = obj.property"
        ]
        
        for i, test_code in enumerate(test_cases, 1):
            print(f"\n   测试用例 {i}: {test_code}")
            
            simulation = builder.simulate_line_execution(test_code, i)
            
            if simulation['has_simulation']:
                print(f"   ✅ 生成了 {len(simulation['simulation_steps'])} 个模拟步骤:")
                for step in simulation['simulation_steps']:
                    print(f"     {step['expression']}")
                
                if simulation['temp_variables_used']:
                    print(f"   使用的临时变量: {', '.join(simulation['temp_variables_used'])}")
            else:
                print("   ⚠️  没有生成模拟步骤")
        
        # 测试模拟摘要
        summary = builder.get_simulation_summary()
        print(f"\n   模拟摘要:")
        print(f"     总步骤数: {summary['total_steps']}")
        print(f"     有模拟的行数: {summary['lines_with_simulation']}")
        print(f"     使用的临时变量数: {summary['temp_variables_used']}")
        print(f"     操作类型统计: {summary['operation_counts']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_integrated_analysis():
    """测试集成分析功能"""
    print("\n🔍 测试集成分析功能")
    print("=" * 50)
    
    try:
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        processor = ShaderAnalysisProcessor()
        print("✅ ShaderAnalysisProcessor 创建成功")
        
        # 测试着色器代码
        test_shader = """
float3 worldPos = transform * localPos;
half4 baseColor = texture.sample(sampler, uv);
float dotNL = dot(normal, lightDir);
float3 result = worldPos + baseColor.xyz * dotNL;
"""
        
        print("🔄 开始分析测试着色器...")
        result = processor.analyze_shader(test_shader, save_reports=False)
        
        if 'analysis' in result:
            analysis_data = result['analysis']
            
            # 检查是否包含模拟数据
            if 'line_simulations' in analysis_data:
                simulations = analysis_data['line_simulations']
                print(f"✅ 找到 {len(simulations)} 行的模拟数据")
                
                for line_num, simulation in simulations.items():
                    if simulation['has_simulation']:
                        print(f"   第 {line_num} 行: {simulation['original_code'].strip()}")
                        print(f"     模拟步骤数: {len(simulation['simulation_steps'])}")
                        for step in simulation['simulation_steps']:
                            print(f"       {step['expression']}")
                        print()
            else:
                print("❌ 没有找到模拟数据")
                return False
            
            # 检查模拟摘要
            if 'simulation_summary' in analysis_data:
                summary = analysis_data['simulation_summary']
                print(f"   模拟摘要:")
                print(f"     总步骤数: {summary['total_steps']}")
                print(f"     使用的临时变量数: {summary['temp_variables_used']}")
                print("✅ 集成分析包含模拟功能")
            else:
                print("❌ 没有找到模拟摘要")
                return False
        else:
            print("❌ 分析结果中没有analysis数据")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎊 表达式模拟功能测试")
    print("=" * 80)
    print("测试内容:")
    print("• 🔧 临时变量管理器")
    print("• 🎯 表达式模拟器")
    print("• 🌳 语法树模拟功能")
    print("• 🔗 集成分析功能")
    print()
    
    # 执行测试
    temp_manager_ok = test_temp_variable_manager()
    expression_sim_ok = test_expression_simulator()
    syntax_tree_ok = test_syntax_tree_simulation()
    integrated_ok = test_integrated_analysis()
    
    print(f"\n🎉 测试结果总结")
    print("=" * 80)
    
    if temp_manager_ok:
        print("✅ 临时变量管理器测试通过")
    else:
        print("❌ 临时变量管理器测试失败")
    
    if expression_sim_ok:
        print("✅ 表达式模拟器测试通过")
    else:
        print("❌ 表达式模拟器测试失败")
    
    if syntax_tree_ok:
        print("✅ 语法树模拟功能测试通过")
    else:
        print("❌ 语法树模拟功能测试失败")
    
    if integrated_ok:
        print("✅ 集成分析功能测试通过")
    else:
        print("❌ 集成分析功能测试失败")
    
    if all([temp_manager_ok, expression_sim_ok, syntax_tree_ok, integrated_ok]):
        print(f"\n🎊 所有测试通过！表达式模拟功能正常工作")
        print("\n🎯 功能特点:")
        print("   • 全局递增的临时变量命名 (tmp_0, tmp_1, tmp_2...)")
        print("   • 复杂表达式自动拆分为多个步骤")
        print("   • 支持二元操作、函数调用、属性访问等")
        print("   • 完整的模拟过程记录和统计")
        print("   • 与现有分析系统无缝集成")
    else:
        print(f"\n❌ 部分测试失败，请检查失败的测试项")

if __name__ == "__main__":
    main()
