line_number,has_half,has_float,precision_level,performance_impact,operation_types,num_operations,optimization_suggestions,code_snippet
1,False,False,none,low,,0,,#include <metal_stdlib>
2,False,False,none,low,,0,,#include <simd/simd.h>
3,False,False,none,low,,0,,
4,False,False,none,low,,0,,using namespace metal;
5,False,False,none,low,,0,,
6,False,False,none,low,,0,,struct _Block1T
7,False,False,none,low,,0,,{
8,False,True,float,low,declaration,1,,float4 AerialPerspectiveExt;
9,False,True,float,low,declaration,1,,float4 AerialPerspectiveMie;
10,False,True,float,low,declaration,1,,float4 AerialPerspectiveRay;
11,False,True,float,low,declaration,1,,float4 AmbientColor;
12,False,True,float,low,declaration,1,,float4 CSMCacheIndexs;
13,False,True,float,low,declaration,1,,float4 CSMShadowBiases;
14,False,True,float,low,declaration,1,,float4 CameraInfo;
15,False,True,float,low,declaration,1,,float4 CameraPos;
16,False,True,float,low,declaration,1,,float4 DiyLightingInfo;
17,False,True,float,low,declaration,1,,float4 EnvInfo;
18,False,True,float,low,declaration,1,,float4 FogColor;
19,False,True,float,low,declaration,1,,float4 FogInfo;
20,False,True,float,low,declaration,1,,float4 GIInfo;
21,False,True,float,low,declaration,1,,float4 HexRenderOptionData[4];
22,False,True,float,low,declaration,1,,float4 LightDataBuffer[65];
23,False,False,none,low,,0,,float3x4 Local;
24,False,True,float,low,declaration,1,,float4 OriginSunDir;
25,False,True,float,low,declaration,1,,float4 PlayerPos;
26,False,True,float,low,declaration,1,,float4 ReflectionProbeBBMin;
27,False,True,float,low,declaration,1,,float4 SHAOParam;
28,False,True,float,low,declaration,1,,float4 SHGIParam;
29,False,True,float,low,declaration,1,,float4 SHGIParam2;
30,False,True,float,low,declaration,1,,float4 ScreenInfo;
31,False,True,float,low,declaration,1,,float4 ScreenMotionGray;
32,False,False,none,low,,0,,float4x4 ShadowViewProjTexs0;
33,False,False,none,low,,0,,float4x4 ShadowViewProjTexs1;
34,False,False,none,low,,0,,float4x4 ShadowViewProjTexs2;
35,False,True,float,low,declaration,1,,float4 SunColor;
36,False,True,float,low,declaration,1,,float4 SunDirection;
37,False,True,float,low,declaration,1,,float4 SunFogColor;
38,False,True,float,low,declaration,1,,float4 TimeOfDayInfos;
39,False,False,none,low,,0,,float3x4 World;
40,False,True,float,low,declaration,1,,float4 WorldProbeInfo;
41,False,True,float,low,declaration,1,,float4 cCIFadeTime;
42,False,True,float,low,declaration,1,,float4 cCISnowData;
43,False,True,float,low,declaration,1,,float4 cCISwitchData;
44,False,True,float,low,declaration,1,,float4 cLocalVirtualLitColor;
45,False,True,float,low,declaration,1,,float4 cLocalVirtualLitCustom;
46,False,True,float,low,declaration,1,,float4 cLocalVirtualLitPos;
47,False,True,float,low,declaration,1,,float4 cSHCoefficients[7];
48,False,True,float,low,declaration,1,,float4 cShadowBias;
49,False,True,float,low,declaration,1,,float4 cVirtualLitColor;
50,False,True,float,low,declaration,1,,float4 cVirtualLitParam;
51,False,True,float,low,declaration,1,,float4 cVisibilitySH[2];
52,False,False,none,low,,0,,packed_float3 cBaseColor;
53,False,True,float,low,declaration,1,,float eIsPlayerOverride;
54,False,False,none,low,,0,,packed_float3 cCIMudBuff;
55,False,True,float,low,declaration,1,,float eFresnelPower;
56,False,False,none,low,,0,,packed_float3 cEmissionColor;
57,False,True,float,low,declaration,1,,float eFresnelMinIntensity;
58,False,False,none,low,,0,,packed_float3 eFresnelColor;
59,False,True,float,low,declaration,1,,float eFresnelIntensity;
60,False,True,float,low,declaration,1,,float cAOoffset;
61,False,True,float,low,declaration,1,,float cBiasFarAwayShadow;
62,False,True,float,low,declaration,1,,float cEmissionScale;
63,False,True,float,low,declaration,1,,float cFurFadeInt;
64,False,True,float,low,declaration,1,,float cMicroShadow;
65,False,True,float,low,declaration,1,,float cNoise1Scale;
66,False,True,float,low,declaration,1,,float cNoise2Bias;
67,False,True,float,low,declaration,1,,float cNoise2Scale;
68,False,True,float,low,declaration,1,,float cNormalMapStrength;
69,False,True,float,low,declaration,1,,float cSaturation;
70,False,True,float,low,declaration,1,,float eDynamicFresnelIntensity;
71,False,True,float,low,declaration,1,,float eFresnelAlphaAdd;
72,False,False,none,low,,0,,};
73,False,False,none,low,,0,,
74,True,False,half,low,declaration,1,,constant half3 _18526 = {};
75,False,True,float,low,declaration,1,,constant float3 _19185 = {};
76,True,False,half,low,declaration,1,,constant half _19493 = {};
77,False,True,float,low,declaration,1,,constant float _19585 = {};
78,False,False,none,low,,0,,constant int _19621 = {};
79,False,True,float,low,declaration,1,,constant float3 _21234 = {};
80,False,True,float,low,declaration,1,,constant float3 _21295 = {};
81,True,False,half,low,declaration,1,,constant half4 _21296 = {};
82,True,False,half,low,declaration,1,,constant half3 _21297 = {};
83,False,False,none,low,,0,,
84,False,False,none,low,,0,,struct main0_out
85,False,False,none,low,,0,,{
86,False,True,float,low,declaration,1,,float4 _Ret [[color(0)]];
87,False,False,none,low,,0,,};
88,False,False,none,low,,0,,
89,False,False,none,low,,0,,struct main0_in
90,False,False,none,low,,0,,{
91,False,True,float,low,declaration,1,,float4 IN_TexCoord [[user(locn0)]];
92,False,True,float,low,declaration,1,,float4 IN_WorldPosition [[user(locn1)]];
93,True,False,half,low,declaration,1,,half4 IN_WorldNormal [[user(locn2)]];
94,True,False,half,low,declaration,1,,half4 IN_WorldTangent [[user(locn3)]];
95,True,False,half,low,declaration,1,,half4 IN_WorldBinormal [[user(locn4)]];
96,True,False,half,low,declaration,1,,half4 IN_TintColor [[user(locn5)]];
97,False,True,float,low,declaration,1,,float IN_LinearZ [[user(locn6)]];
98,True,False,half,low,declaration,1,,half3 IN_LocalPosition [[user(locn7)]];
99,True,False,half,low,declaration,1,,half4 IN_StaticWorldNormal [[user(locn8)]];
100,False,False,none,low,,0,,};
101,False,False,none,low,,0,,
102,True,True,mixed,high,,0,考虑统一使用单一精度类型,"fragment main0_out main0(main0_in in [[stage_in]], constant _Block1T& _Block1 [[buffer(0)]], texture"
103,False,False,none,low,,0,,{
104,False,False,none,low,,0,,"constexpr sampler sShadowMapArraySamplerSmplr(filter::linear, mip_filter::linear, compare_func::grea"
105,False,False,none,low,,0,,main0_out out = {};
106,True,False,half,medium,declaration|conversion,2,,half _9176 = half(0);
107,True,False,half,medium,declaration|conversion,2,,half3 _9179 = half3(_9176);
108,True,False,half,medium,declaration|conversion,2,,half _9199 = half(1);
109,False,True,float,low,arithmetic|declaration,2,,float3 _8921 = fast::normalize(_Block1.CameraPos.xyz - in.IN_WorldPosition.xyz);
110,False,True,float,low,declaration|conversion,2,,float3 _8925 = float3(in.IN_WorldNormal.xyz);
111,True,False,half,medium,declaration|conversion,2,,"half _8929 = half(fast::clamp(dot(_8925, _8921), 0.0, 1.0));"
112,True,False,half,high,texture_sample|declaration,2,考虑优化纹理访问模式,"half4 _8939 = sBaseSampler.sample(sBaseSamplerSmplr, in.IN_TexCoord.xy);"
113,True,False,half,low,declaration,1,,half3 _8941 = _8939.xyz;
114,True,True,mixed,medium,arithmetic|declaration|conversion,5,考虑统一使用单一精度类型,half3 _8949 = half3(float3(_8941 * _8941) * float3(_Block1.cBaseColor));
115,False,True,float,high,arithmetic|texture_sample|declaration,3,考虑优化纹理访问模式,"float4 _8973 = sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy * _Block1.cNoise1Scale));"
116,True,False,half,medium,declaration|conversion,2,,half4 _8974 = half4(_8973);
117,False,True,float,high,arithmetic|texture_sample|declaration,3,考虑优化纹理访问模式,"float4 _8982 = sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy * _Block1.cNoise2Scale));"
118,True,False,half,medium,declaration|conversion,2,,half4 _8983 = half4(_8982);
119,True,True,mixed,medium,arithmetic|declaration|conversion,4,考虑统一使用单一精度类型,"float _8991 = fast::clamp(powr(float(max(in.IN_TintColor.w, half(9.9956989288330078125e-05))), _Bloc"
120,True,False,half,low,declaration,1,,half _8994 = _8974.x;
121,True,False,half,low,declaration,1,,half _8996 = _8983.x;
122,False,True,float,low,arithmetic|declaration,2,,float _9014 = 1.0 - _8991;
123,True,False,half,medium,declaration|conversion,2,,half _9019 = half(1.0);
124,True,True,mixed,medium,arithmetic|conversion,4,考虑统一使用单一精度类型,"if ((float((_8994 * _8996) - half(mix(0.0, 0.5, powr(float(max(_9199 - _8929, half(9.995698928833007"
125,False,False,none,low,,0,,{
126,False,False,none,low,,0,,discard_fragment();
127,False,False,none,low,,0,,}
128,True,False,half,high,texture_sample|declaration,2,考虑优化纹理访问模式,"half4 _9248 = sNormalSampler.sample(sNormalSamplerSmplr, in.IN_TexCoord.xy);"
129,True,False,half,medium,declaration|conversion,2,,half _9251 = half(2);
130,True,False,half,medium,declaration|conversion,2,,half3 _9254 = half3(_9199);
131,True,False,half,low,arithmetic|declaration,2,,half3 _9255 = (_9248.xyz * _9251) - _9254;
132,True,False,half,low,declaration,1,,half _9257 = _9255.x;
133,True,False,half,low,declaration,1,,half _9263 = _9255.y;
134,True,False,half,low,declaration,1,,half _9270 = _9255.z;
135,False,True,float,low,arithmetic|declaration|conversion,3,,float3 _9279 = float3(((in.IN_WorldTangent.xyz * _9257) + (in.IN_WorldBinormal.xyz * _9263)) + (in.I
136,False,True,float,low,declaration|conversion,2,,float3 _9286 = float3(in.IN_StaticWorldNormal.xyz);
137,False,True,float,low,arithmetic|declaration|conversion,7,,"float3 _9331 = (((float4(float3(in.IN_WorldTangent.xyz), 0.0) * _Block1.Local) * float(_9257)) + ((f"
138,True,False,half,medium,arithmetic|declaration|conversion,3,,"half _9334 = half((_9331 * rsqrt(fast::max(dot(_9331, _9331), 9.9999997473787516355514526367188e-06)"
139,True,False,half,medium,function_call|arithmetic|declaration|conversion,5,,"half3 _9064 = mix(in.IN_WorldNormal.xyz, half3(_9279 * rsqrt(fast::max(dot(_9279, _9279), 9.99999974"
140,True,False,half,high,texture_sample|declaration,2,考虑优化纹理访问模式,"half4 _9074 = sMixSampler.sample(sMixSamplerSmplr, in.IN_TexCoord.xy);"
141,True,False,half,low,declaration,1,,half _9079 = _9074.y;
142,True,True,mixed,medium,arithmetic|declaration|conversion,6,考虑统一使用单一精度类型,"half _9096 = half(mix(1.0, float(mix(half3(_9019), half3(half(fast::clamp(_9014 + _Block1.cAOoffset,"
143,False,True,float,low,declaration|conversion,2,,float _9100 = float(_9096);
144,False,True,float,low,declaration|conversion,2,,float3 _9109 = float3(_9064);
145,True,False,half,high,texture_sample|declaration,2,考虑优化纹理访问模式,"half4 _9130 = sEmissionMapSampler.sample(sEmissionMapSamplerSmplr, in.IN_TexCoord.xy);"
146,True,True,mixed,medium,arithmetic|declaration|conversion,5,考虑统一使用单一精度类型,half3 _9145 = _9130.xyz * half3((float3(_Block1.cEmissionColor) * _Block1.cEmissionScale) * float3(_
147,True,False,half,low,declaration,1,,half _18217;
148,False,False,none,low,,0,,if (!gl_FrontFacing)
149,False,False,none,low,,0,,{
150,True,False,half,medium,arithmetic|conversion,2,,_18217 = _9334 * half(-1);
151,False,False,none,low,,0,,}
152,False,False,none,low,,0,,else
153,False,False,none,low,,0,,{
154,False,False,none,low,,0,,_18217 = _9334;
155,False,False,none,low,,0,,}
156,False,True,float,low,declaration|conversion,2,,float3 _9698 = float3(_9179);
157,False,True,float,low,function_call|arithmetic|declaration,3,,"float _9408 = mix(_Block1.cCIFadeTime.y, _Block1.cCIFadeTime.w, fast::clamp((_Block1.CameraPos.w - _"
158,True,True,mixed,medium,declaration|conversion,3,考虑统一使用单一精度类型,"float _9721 = fast::clamp(dot(_9286, float3(half3(_9176, _9176, _9199))), 0.0, 1.0);"
159,True,True,mixed,medium,arithmetic|declaration|conversion,4,考虑统一使用单一精度类型,float _9734 = (_9721 * _9721) * float(half(fast::clamp((_Block1.cCIMudBuff[2u] - 1.0) * 0.6999999880
160,True,False,half,medium,function_call|arithmetic|declaration|conversion,7,,"half3 _9761 = mix(_8949, half3(half(0.20700000226497650146484375), half(0.18400000035762786865234375"
161,True,True,mixed,medium,arithmetic|declaration|conversion,6,考虑统一使用单一精度类型,"half _9772 = half(mix(float(_9199 - _9074.x), 0.89999997615814208984375, _9734 * float(half(mix(_940"
162,True,True,mixed,medium,arithmetic|declaration|conversion,4,考虑统一使用单一精度类型,"float _9429 = (float(half(_9014)) * _9408) * fast::clamp(1.0 + _Block1.EnvInfo.y, 0.0, 1.0);"
163,False,True,float,high,arithmetic|texture_sample|declaration,3,考虑优化纹理访问模式,"float4 _9443 = sCharInteractionSampler.sample(sCharInteractionSamplerSmplr, ((in.IN_TexCoord.xy * _B"
164,True,False,half,low,declaration,1,,half3 _18225;
165,True,False,half,low,declaration,1,,half _18234;
166,True,False,half,low,declaration,1,,half _18236;
167,True,False,half,low,declaration,1,,half3 _18268;
168,True,False,half,low,declaration,1,,half _18272;
169,False,False,none,low,,0,,if (_Block1.cCISwitchData.x > 0.0)
170,False,False,none,low,,0,,{
171,True,True,mixed,medium,declaration|conversion,3,考虑统一使用单一精度类型,"float _9460 = float(half(fast::max(0.0, _Block1.EnvInfo.y)));"
172,False,True,float,low,arithmetic|declaration,2,,"float _9462 = fast::min(2.0, fast::clamp(_Block1.cCISnowData.z, 0.0, 1.0) + _9460);"
173,False,True,float,low,arithmetic|declaration,2,,float _9499 = 1.0 - _9429;
174,False,True,float,low,declaration,1,,float _9505 = _9443.y;
175,True,True,mixed,medium,function_call|arithmetic|declaration|conversion,9,考虑统一使用单一精度类型,"float _9510 = float(half(fast::clamp(((float2(0.800000011920928955078125, 0.5) * _9429).x * (1.0 - p"
176,True,True,mixed,medium,arithmetic|declaration|conversion,4,考虑统一使用单一精度类型,float _9519 = float(half(9.9956989288330078125e-05));
177,True,True,mixed,medium,arithmetic|declaration|conversion,4,考虑统一使用单一精度类型,half _9535 = half(1.0 - fast::clamp((float(in.IN_LocalPosition.y) * (2.0 - _9460)) * _Block1.cCISnow
178,True,True,mixed,medium,arithmetic|declaration|conversion,5,考虑统一使用单一精度类型,"float _9556 = float(half((float(_9535) * fast::clamp(float(_9535 + _18217) + 0.5, 0.0, 1.0)) * _9429"
179,False,True,float,low,arithmetic|declaration,2,,float _9557 = 1.0 - _9556;
180,True,True,mixed,medium,arithmetic|declaration|conversion,6,考虑统一使用单一精度类型,"half _9585 = half((_Block1.cCISwitchData.x * fast::clamp(1.0 - _Block1.cCISnowData.w, 0.0, 1.0)) * f"
181,True,False,half,low,arithmetic|declaration,2,,half _9588 = _9199 - _9585;
182,False,True,float,low,declaration|conversion,2,,float _9603 = float(_9585);
183,True,True,mixed,medium,conversion,2,考虑统一使用单一精度类型,"_18272 = half(mix(float(_9772), 1.0, _9603));"
184,False,False,none,low,,0,,_18268 = _9145 * _9588;
185,True,False,half,medium,conversion,1,,"_18236 = half(mix(_9100, 1.0, _9603));"
186,False,False,none,low,,0,,_18234 = _9079 * _9588;
187,True,False,half,medium,function_call|conversion,5,,"_18225 = mix(_9761, half3(half(0.61000001430511474609375), half(0.660000026226043701171875), half(0."
188,False,False,none,low,,0,,}
189,False,False,none,low,,0,,else
190,False,False,none,low,,0,,{
191,False,False,none,low,,0,,_18272 = _9772;
192,False,False,none,low,,0,,_18268 = _9145;
193,False,False,none,low,,0,,_18236 = _9096;
194,False,False,none,low,,0,,_18234 = _9079;
195,False,False,none,low,,0,,_18225 = _9761;
196,False,False,none,low,,0,,}
197,True,False,half,medium,arithmetic|declaration|conversion,3,,"half _8295 = half(fast::clamp(1.0 - _Block1.HexRenderOptionData[0].x, 0.0, 1.0));"
198,True,True,mixed,medium,function_call|arithmetic|declaration|conversion,7,考虑统一使用单一精度类型,"float _8298 = float(_8939.w * half((_8991 * float(min(_8994, _8996))) * powr(float(max(in.IN_TintCol"
199,True,False,half,medium,declaration|conversion,4,,"half3 _8303 = half3(half(0.2125999927520751953125), half(0.715200006961822509765625), half(0.0722000"
200,True,False,half,medium,function_call|declaration|conversion,4,,"half3 _8315 = mix(half3(dot(_18225, _8303)), _18225, half3(half(_Block1.cSaturation)));"
201,True,False,half,medium,declaration|conversion,2,,half _9787 = half(_8298);
202,True,False,half,low,declaration,1,,half _18230;
203,True,False,half,low,declaration,1,,half3 _18255;
204,False,False,none,low,,0,,if (_Block1.eDynamicFresnelIntensity > 0.0)
205,False,False,none,low,,0,,{
206,False,True,float,low,function_call|arithmetic|declaration,3,,"float _9806 = abs(dot(_9109, -_8921));"
207,False,True,float,low,function_call|declaration,2,,float _9813 = abs(_Block1.eFresnelPower);
208,True,True,mixed,medium,function_call|arithmetic|declaration|conversion,6,考虑统一使用单一精度类型,"float _9831 = fast::max(_Block1.eFresnelMinIntensity, (_Block1.eFresnelPower < 0.0) ? powr(_9806, _9"
209,True,True,mixed,medium,arithmetic|declaration|conversion,4,考虑统一使用单一精度类型,float _9846 = float(_9787 * half(_9831));
210,True,True,mixed,medium,arithmetic|conversion,3,考虑统一使用单一精度类型,_18255 = _9179 + half3((((float3(_Block1.eFresnelColor) * _9831) * _Block1.eFresnelIntensity) * 1.0)
211,True,False,half,medium,function_call|arithmetic|conversion,5,,"_18230 = clamp(half(fast::max(fast::clamp(_Block1.eFresnelAlphaAdd, 0.0, 1.0) * _8298, _9846)), half"
212,False,False,none,low,,0,,}
213,False,False,none,low,,0,,else
214,False,False,none,low,,0,,{
215,False,False,none,low,,0,,_18255 = _9179;
216,False,False,none,low,,0,,_18230 = _9787;
217,False,False,none,low,,0,,}
218,False,True,float,low,declaration|conversion,2,,float _9868 = float(_18230);
219,True,False,half,medium,arithmetic|declaration|conversion,3,,half _8346 = _18236 * half(_Block1.SHAOParam.w);
220,False,True,float,low,arithmetic|declaration|conversion,3,,float4 _9926 = float4((in.IN_WorldPosition.xyz + (_8925 * _Block1.cBiasFarAwayShadow)) - _Block1.Cam
221,False,True,float,low,declaration,1,,float4 _17899 = _9926;
222,False,False,none,low,,0,,_17899.z = _9926.z - _Block1.CSMShadowBiases.x;
223,False,True,float,low,declaration|conversion,2,,"float4 _9942 = float4(in.IN_WorldPosition.xyz, 1.0);"
224,False,True,float,low,arithmetic|declaration,2,,float4 _9945 = _9942 * _Block1.ShadowViewProjTexs1;
225,False,True,float,low,declaration,1,,float4 _17902 = _9945;
226,False,False,none,low,,0,,_17902.z = _9945.z - _Block1.CSMShadowBiases.y;
227,False,True,float,low,declaration,1,,float4 _18237;
228,False,False,none,low,,0,,if (_Block1.CSMCacheIndexs.z > 0.0)
229,False,False,none,low,,0,,{
230,False,True,float,low,arithmetic|declaration,2,,float4 _9971 = _9942 * _Block1.ShadowViewProjTexs2;
231,False,False,none,low,,0,,_9971.z = _9971.z - _Block1.CSMShadowBiases.z;
232,False,False,none,low,,0,,_18237 = _9971;
233,False,False,none,low,,0,,}
234,False,False,none,low,,0,,else
235,False,False,none,low,,0,,{
236,False,True,float,low,conversion,1,,"_18237 = float4(0.0, 0.0, 0.0, 1.0);"
237,False,False,none,low,,0,,}
238,False,True,float,low,arithmetic|declaration|conversion,3,,float3 _10033 = _17902.xyz / float3(_9945.w);
239,False,True,float,low,arithmetic|declaration|conversion,3,,float3 _10040 = _18237.xyz / float3(_18237.w);
240,False,True,float,low,arithmetic|declaration|conversion,4,,"float3 _10077 = _10040 * (step(-0.100000001490116119384765625, _Block1.CSMShadowBiases.z) * float(al"
241,False,True,float,low,arithmetic|declaration|conversion,4,,"float _21135 = step(-0.100000001490116119384765625, _Block1.CSMShadowBiases.y) * float(all(_10033 > "
242,False,True,float,low,arithmetic|declaration,2,,float3 _21138 = _10077 + ((_10033 - _10077) * _21135);
243,False,True,float,low,declaration,1,,float _10113 = _21138.z;
244,False,True,float,low,declaration|conversion,2,,float2 _10120 = float2(_Block1.cShadowBias.w);
245,False,True,float,low,arithmetic|declaration|conversion,3,,float2 _10167 = (_21138.xy / _10120) - float2(0.5);
246,False,True,float,low,function_call|declaration,2,,float2 _10169 = fract(_10167);
247,False,True,float,low,function_call|declaration,2,,float2 _10171 = floor(_10167);
248,False,True,float,low,arithmetic|declaration|conversion,3,,float2 _10177 = float2(2.0) - _10169;
249,False,True,float,low,arithmetic|declaration|conversion,3,,float2 _10181 = _10169 + float2(1.0);
250,False,True,float,low,arithmetic|declaration|conversion,3,,float2 _10184 = float2(1.0) / _10177;
251,False,True,float,low,arithmetic|declaration,2,,float2 _10187 = _10169 / _10181;
252,False,True,float,low,arithmetic|declaration|conversion,3,,float _10205 = float(int(_Block1.CSMCacheIndexs[int(2.0 + ((-1.0) * _21135))]));
253,False,True,float,low,arithmetic|declaration|conversion,3,,"float3 _10208 = float3(((_10171 + float2(-0.5)) + _10184) * _10120, _10205);"
254,False,True,float,low,arithmetic|declaration|conversion,4,,"float3 _10231 = float3(((_10171 + float2(1.5, -0.5)) + float2(_10187.x, _10184.y)) * _10120, _10205)"
255,False,True,float,low,arithmetic|declaration|conversion,4,,"float3 _10254 = float3(((_10171 + float2(-0.5, 1.5)) + float2(_10184.x, _10187.y)) * _10120, _10205)"
256,False,True,float,low,arithmetic|declaration|conversion,3,,"float3 _10276 = float3(((_10171 + float2(1.5)) + _10187) * _10120, _10205);"
257,False,True,float,low,declaration,1,,float _10282 = _10177.x;
258,False,True,float,low,declaration,1,,float _10289 = _10181.x;
259,False,True,float,low,declaration,1,,float _10300 = _10181.y;
260,False,True,float,low,arithmetic|declaration|conversion,3,,float3 _9997 = _17899.xyz / float3(_9926.w);
261,True,True,mixed,medium,arithmetic|declaration|conversion,5,考虑统一使用单一精度类型,"float _10004 = fast::min(1.0 - float(half(10) * half(9.9956989288330078125e-05)), _9997.z);"
262,False,True,float,low,declaration,1,,float3 _17928 = _9997;
263,False,False,none,low,,0,,_17928.z = _10004;
264,False,True,float,low,arithmetic|declaration|conversion,3,,float2 _10378 = (_17928.xy / _10120) - float2(0.5);
265,False,True,float,low,function_call|declaration,2,,float2 _10380 = fract(_10378);
266,False,True,float,low,function_call|declaration,2,,float2 _10382 = floor(_10378);
267,False,True,float,low,arithmetic|declaration|conversion,3,,float2 _10388 = float2(2.0) - _10380;
268,False,True,float,low,arithmetic|declaration|conversion,3,,float2 _10392 = _10380 + float2(1.0);
269,False,True,float,low,arithmetic|declaration|conversion,3,,float2 _10395 = float2(1.0) / _10388;
270,False,True,float,low,arithmetic|declaration,2,,float2 _10398 = _10380 / _10392;
271,False,True,float,low,declaration|conversion,2,,float _10416 = float(int(_Block1.CSMCacheIndexs.x));
272,False,True,float,low,arithmetic|declaration|conversion,3,,"float3 _10419 = float3(((_10382 + float2(-0.5)) + _10395) * _10120, _10416);"
273,False,True,float,low,arithmetic|declaration|conversion,4,,"float3 _10442 = float3(((_10382 + float2(1.5, -0.5)) + float2(_10398.x, _10395.y)) * _10120, _10416)"
274,False,True,float,low,arithmetic|declaration|conversion,4,,"float3 _10465 = float3(((_10382 + float2(-0.5, 1.5)) + float2(_10395.x, _10398.y)) * _10120, _10416)"
275,False,True,float,low,arithmetic|declaration|conversion,3,,"float3 _10487 = float3(((_10382 + float2(1.5)) + _10398) * _10120, _10416);"
276,False,True,float,low,declaration,1,,float _10493 = _10388.x;
277,False,True,float,low,declaration,1,,float _10500 = _10392.x;
278,False,True,float,low,declaration,1,,float _10511 = _10392.y;
279,True,True,mixed,medium,function_call|arithmetic|declaration|conversion,12,考虑统一使用单一精度类型,"half _8351 = max(half(mix(0.0, 1.0 - fast::clamp((abs(dot(_9109, _Block1.SunDirection.xyz)) + ((2.0 "
280,False,True,float,low,arithmetic|declaration,2,,float3 _8370 = in.IN_WorldPosition.xyz - _Block1.CameraPos.xyz;
281,False,True,float,low,arithmetic|declaration,2,,float3 _8373 = fast::normalize(-_8370);
282,False,True,float,low,function_call|declaration,2,,"float _8378 = dot(_9109, _8373);"
283,True,False,half,medium,function_call|declaration|conversion,4,,"half3 _10557 = mix(half3(half(0.039999999105930328369140625)), _8315, half3(_18234));"
284,True,True,mixed,medium,arithmetic|declaration|conversion,5,考虑统一使用单一精度类型,half3 _10569 = half3(float3(_8315 - (_8315 * _18234)) * float3(0.3183098733425140380859375));
285,False,True,float,low,declaration|conversion,2,,float3 _10588 = float3(_Block1.EnvInfo.z);
286,True,False,half,medium,declaration|conversion,2,,half3 _8393 = half3(half(0.0));
287,False,False,none,low,,0,,uint _8397 = as_type<uint>(_Block1.SHGIParam.w);
288,False,False,none,low,,0,,bool _8401 = (_8397 & 63u) > 0u;
289,True,False,half,low,declaration,1,,half _18329;
290,False,False,none,low,,0,,if (_8401)
291,False,False,none,low,,0,,{
292,False,True,float,low,declaration,1,,"float3 _8435 = select(_Block1.PlayerPos.xyz, _Block1.CameraPos.xyz, bool3((_8397 & 524288u) > 0u));"
293,True,False,half,low,declaration,1,,half _18306;
294,False,False,none,low,,0,,if (_8401)
295,False,False,none,low,,0,,{
296,True,False,half,low,declaration,1,,half _18304;
297,False,False,none,low,,0,,if ((_8397 & 8u) != 0u)
298,False,False,none,low,,0,,{
299,False,True,float,low,arithmetic|declaration|conversion,3,,float3 _10686 = (in.IN_WorldPosition.xyz + (_8373 * 0.100000001490116119384765625)) * float3(0.00416
300,False,True,float,low,function_call|arithmetic|declaration,3,,float3 _10762 = _10686 - floor(_10686);
301,False,True,float,low,arithmetic|declaration|conversion,3,,float3 _10789 = _8435 * float3(0.125);
302,False,True,float,low,declaration,1,,float _10797 = _10789.x;
303,False,True,float,low,function_call|declaration,2,,float _10799 = floor(_10797);
304,False,True,float,low,declaration,1,,float3 _21191;
305,False,False,none,low,,0,,_21191.x = _10799 - 15.0;
306,False,True,float,low,declaration,1,,float3 _21235;
307,False,False,none,low,,0,,if ((_10797 - _10799) > 0.5)
308,False,False,none,low,,0,,{
309,False,True,float,low,declaration,1,,float3 _21194 = _21191;
310,False,False,none,low,,0,,_21194.x = _10799 + (-14.0);
311,False,False,none,low,,0,,_21235 = _21194;
312,False,False,none,low,,0,,}
313,False,False,none,low,,0,,else
314,False,False,none,low,,0,,{
315,False,False,none,low,,0,,_21235 = _21191;
316,False,False,none,low,,0,,}
317,False,True,float,low,declaration,1,,float _21078 = _10789.y;
318,False,True,float,low,function_call|declaration,2,,float _21079 = floor(_21078);
319,False,True,float,low,declaration,1,,float3 _21198 = _21235;
320,False,False,none,low,,0,,_21198.y = _21079 - 8.0;
321,False,True,float,low,declaration,1,,float3 _21236;
322,False,False,none,low,,0,,if ((_21078 - _21079) > 0.5)
323,False,False,none,low,,0,,{
324,False,True,float,low,declaration,1,,float3 _21201 = _21198;
325,False,False,none,low,,0,,_21201.y = _21079 + (-7.0);
326,False,False,none,low,,0,,_21236 = _21201;
327,False,False,none,low,,0,,}
328,False,False,none,low,,0,,else
329,False,False,none,low,,0,,{
330,False,False,none,low,,0,,_21236 = _21198;
331,False,False,none,low,,0,,}
332,False,True,float,low,declaration,1,,float _21100 = _10789.z;
333,False,True,float,low,function_call|declaration,2,,float _21101 = floor(_21100);
334,False,True,float,low,declaration,1,,float3 _21205 = _21236;
335,False,False,none,low,,0,,_21205.z = _21101 - 15.0;
336,False,True,float,low,declaration,1,,float3 _21237;
337,False,False,none,low,,0,,if ((_21100 - _21101) > 0.5)
338,False,False,none,low,,0,,{
339,False,True,float,low,declaration,1,,float3 _21208 = _21205;
340,False,False,none,low,,0,,_21208.z = _21101 + (-14.0);
341,False,False,none,low,,0,,_21237 = _21208;
342,False,False,none,low,,0,,}
343,False,False,none,low,,0,,else
344,False,False,none,low,,0,,{
345,False,False,none,low,,0,,_21237 = _21205;
346,False,False,none,low,,0,,}
347,False,True,float,low,arithmetic|declaration,2,,float3 _10822 = _21237 * 8.0;
348,True,False,half,low,declaration,1,,half _18305;
349,False,True,float,low,arithmetic|conversion,2,,"if (all(in.IN_WorldPosition.xyz >= _10822) && all(in.IN_WorldPosition.xyz < (_10822 + float3(240.0, "
350,False,False,none,low,,0,,{
351,False,False,none,low,,0,,uint _10704 = (_8397 & 251658240u) >> 24u;
352,False,True,float,low,arithmetic|declaration|conversion,3,,float _10887 = 3.0 - float((_8397 & 458752u) >> 16u);
353,True,False,half,low,declaration,1,,half _18297;
354,False,False,none,low,,0,,if (_10704 <= 3u)
355,False,False,none,low,,0,,{
356,False,True,float,low,arithmetic|declaration|conversion,3,,float _10900 = 3.0 - float(_10704);
357,False,True,float,low,arithmetic|declaration|conversion,4,,float2 _10994 = ((_10762.xz - float2(0.5)) * 0.9375) + float2(0.5);
358,False,True,float,low,arithmetic|declaration,2,,float _11001 = _10822.x * 0.0041666668839752674102783203125;
359,False,True,float,low,function_call|arithmetic|declaration,3,,float _11005 = ((_11001 - floor(_11001)) - 0.5) * 0.9375;
360,False,True,float,low,arithmetic|declaration,2,,float _11011 = _10822.z * 0.0041666668839752674102783203125;
361,False,True,float,low,function_call|arithmetic|declaration,3,,float _11015 = ((_11011 - floor(_11011)) - 0.5) * 0.9375;
362,False,True,float,low,declaration,1,,float _11020 = _10994.x;
363,False,True,float,low,declaration,1,,float3 _17954;
364,False,False,none,low,,0,,"_17954.x = (_11020 < (_11005 + 0.5)) ? fast::min(_11020, _11005 + 0.49609375) : fast::max(_11020, _1"
365,False,True,float,low,declaration,1,,float _11038 = _10994.y;
366,False,False,none,low,,0,,"_17954.z = (_11038 < (_11015 + 0.5)) ? fast::min(_11038, _11015 + 0.49609375) : fast::max(_11038, _1"
367,False,True,float,low,arithmetic|declaration,2,,float _11059 = (_10762.y * 64.0) - 0.5;
368,False,True,float,low,function_call|declaration,2,,float _11064 = floor(_11059);
369,False,False,none,low,,0,,uint _11067 = (_11059 < 0.0) ? 63u : uint(_11064);
370,False,False,none,low,,0,,uint _11070 = _11067 + 1u;
371,False,False,none,low,,0,,uint _21301 = (_11070 >= 64u) ? 0u : _11070;
372,False,True,float,low,arithmetic|declaration|conversion,4,,"float2 _11097 = (float2(float(_11067 & 7u), float(_11067 >> 3u)) + _17954.xz) * 0.125;"
373,False,True,float,low,declaration,1,,float _11100 = _11097.x;
374,False,True,float,low,declaration|conversion,2,,"float3 _11102 = float3(_11100, _11097.y, _10887);"
375,True,False,half,low,declaration,1,,half4 _17962;
376,True,False,half,high,texture_sample|conversion,2,考虑优化纹理访问模式,"_17962.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11102.xy, uint(rint(_11102.z))"
377,False,True,float,low,declaration|conversion,2,,"float3 _11113 = float3(_11100, _11097.y, _10900);"
378,True,False,half,high,texture_sample|declaration|conversion,3,考虑优化纹理访问模式,"half3 _11118 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11113.xy, uint(rint(_11113.z)"
379,False,True,float,low,arithmetic|declaration|conversion,4,,"float2 _11135 = (float2(float(_21301 & 7u), float(_21301 >> 3u)) + _17954.xz) * 0.125;"
380,False,True,float,low,declaration,1,,float _11138 = _11135.x;
381,False,True,float,low,declaration|conversion,2,,"float3 _11140 = float3(_11138, _11135.y, _10887);"
382,True,False,half,low,declaration,1,,half4 _17964;
383,True,False,half,high,texture_sample|conversion,2,考虑优化纹理访问模式,"_17964.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11140.xy, uint(rint(_11140.z))"
384,False,True,float,low,declaration|conversion,2,,"float3 _11151 = float3(_11138, _11135.y, _10900);"
385,True,False,half,high,texture_sample|declaration|conversion,3,考虑优化纹理访问模式,"half3 _11156 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11151.xy, uint(rint(_11151.z)"
386,True,False,half,medium,function_call|arithmetic|declaration|conversion,6,,"half4 _11163 = mix(half4(_11118.x, _11118.y, _11118.z, _17962.w), half4(_11156.x, _11156.y, _11156.z"
387,True,True,mixed,medium,function_call|arithmetic|conversion,11,考虑统一使用单一精度类型,"_18297 = clamp((_11163.w * half(32.0)) + half(float(dot(half3(_8373), half3((float3(_11163.xyz) * fl"
388,False,False,none,low,,0,,}
389,False,False,none,low,,0,,else
390,False,False,none,low,,0,,{
391,False,True,float,low,arithmetic|declaration|conversion,4,,float2 _11233 = ((_10762.xz - float2(0.5)) * 0.9375) + float2(0.5);
392,False,True,float,low,arithmetic|declaration,2,,float _11240 = _10822.x * 0.0041666668839752674102783203125;
393,False,True,float,low,function_call|arithmetic|declaration,3,,float _11244 = ((_11240 - floor(_11240)) - 0.5) * 0.9375;
394,False,True,float,low,arithmetic|declaration,2,,float _11250 = _10822.z * 0.0041666668839752674102783203125;
395,False,True,float,low,function_call|arithmetic|declaration,3,,float _11254 = ((_11250 - floor(_11250)) - 0.5) * 0.9375;
396,False,True,float,low,declaration,1,,float _11259 = _11233.x;
397,False,True,float,low,declaration,1,,float3 _17977;
398,False,False,none,low,,0,,"_17977.x = (_11259 < (_11244 + 0.5)) ? fast::min(_11259, _11244 + 0.49609375) : fast::max(_11259, _1"
399,False,True,float,low,declaration,1,,float _11277 = _11233.y;
400,False,False,none,low,,0,,"_17977.z = (_11277 < (_11254 + 0.5)) ? fast::min(_11277, _11254 + 0.49609375) : fast::max(_11277, _1"
401,False,True,float,low,arithmetic|declaration,2,,float _11298 = (_10762.y * 64.0) - 0.5;
402,False,True,float,low,function_call|declaration,2,,float _11303 = floor(_11298);
403,False,False,none,low,,0,,uint _11306 = (_11298 < 0.0) ? 63u : uint(_11303);
404,False,False,none,low,,0,,uint _11309 = _11306 + 1u;
405,False,False,none,low,,0,,uint _21300 = (_11309 >= 64u) ? 0u : _11309;
406,False,True,float,low,arithmetic|declaration|conversion,4,,"float3 _11340 = float3((float2(float(_11306 & 7u), float(_11306 >> 3u)) + _17977.xz) * 0.125, _10887"
407,False,True,float,low,arithmetic|declaration|conversion,4,,"float3 _11365 = float3((float2(float(_21300 & 7u), float(_21300 >> 3u)) + _17977.xz) * 0.125, _10887"
408,True,True,mixed,high,arithmetic|texture_sample|conversion,7,考虑统一使用单一精度类型|考虑优化纹理访问模式,"_18297 = half(mix(float(half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11340.xy, uint(ri"
409,False,False,none,low,,0,,}
410,False,True,float,low,arithmetic|declaration|conversion,4,,"float3 _11404 = (((in.IN_WorldPosition.xyz - _10822) * float3(0.0041666668839752674102783203125, 0.0"
411,False,True,float,low,arithmetic|declaration,2,,float3 _11407 = _11404 * _11404;
412,False,True,float,low,arithmetic|declaration,2,,float3 _11410 = _11407 * _11407;
413,True,False,half,low,declaration,1,,half _18303;
414,False,False,none,low,,0,,if ((!((_8397 & 4u) != 0u)) && ((_8397 & 32768u) > 0u))
415,False,False,none,low,,0,,{
416,True,True,mixed,medium,conversion,2,考虑统一使用单一精度类型,"_18303 = half(mix(float(_18297), 1.0, fast::clamp(fast::max(_11410.x, fast::max(_11410.y, _11410.z))"
417,False,False,none,low,,0,,}
418,False,False,none,low,,0,,else
419,False,False,none,low,,0,,{
420,False,False,none,low,,0,,_18303 = _18297;
421,False,False,none,low,,0,,}
422,False,False,none,low,,0,,_18305 = _18303;
423,False,False,none,low,,0,,}
424,False,False,none,low,,0,,else
425,False,False,none,low,,0,,{
426,False,False,none,low,,0,,_18305 = _9019;
427,False,False,none,low,,0,,}
428,False,False,none,low,,0,,_18304 = _18305;
429,False,False,none,low,,0,,}
430,False,False,none,low,,0,,else
431,False,False,none,low,,0,,{
432,False,False,none,low,,0,,_18304 = _9019;
433,False,False,none,low,,0,,}
434,False,True,float,low,arithmetic|declaration,2,,float _11467 = _Block1.SHAOParam.z * _Block1.SHAOParam.z;
435,False,True,float,low,arithmetic|declaration,2,,float3 _11470 = in.IN_WorldPosition.xyz - _8435;
436,False,True,float,low,arithmetic|declaration,2,,"float _11479 = fast::clamp((_11467 - dot(_11470, _11470)) / _11467, 0.0, 1.0);"
437,True,True,mixed,medium,arithmetic|conversion,5,考虑统一使用单一精度类型,_18306 = half(1.0 - fast::clamp((1.0 - float(half(fast::clamp(1.0 - float(half(powr(float(half(fast:
438,False,False,none,low,,0,,}
439,False,False,none,low,,0,,else
440,False,False,none,low,,0,,{
441,False,False,none,low,,0,,_18306 = _9019;
442,False,False,none,low,,0,,}
443,True,False,half,low,declaration,1,,half _18330;
444,False,False,none,low,,0,,if (!((_8397 & 64u) > 0u))
445,False,False,none,low,,0,,{
446,False,False,none,low,,0,,_18330 = _8346 * _18306;
447,False,False,none,low,,0,,}
448,False,False,none,low,,0,,else
449,False,False,none,low,,0,,{
450,False,False,none,low,,0,,_18330 = _8346;
451,False,False,none,low,,0,,}
452,False,False,none,low,,0,,_18329 = _18330;
453,False,False,none,low,,0,,}
454,False,False,none,low,,0,,else
455,False,False,none,low,,0,,{
456,False,False,none,low,,0,,_18329 = _8346;
457,False,False,none,low,,0,,}
458,True,True,mixed,medium,declaration|conversion,3,考虑统一使用单一精度类型,float3 _11517 = float3(half3(_8373));
459,False,True,float,low,arithmetic|declaration|conversion,3,,"float3 _11600 = _Block1.CameraPos.xyz + (fast::normalize(float3(_Block1.SunDirection.x, fast::min(_B"
460,False,True,float,low,arithmetic|declaration,2,,"float3 _11604 = reflect(-_11517, _9109);"
461,False,True,float,low,function_call|arithmetic|declaration,3,,"float3 _11611 = (_11604 * dot(_11600, _11604)) - _11600;"
462,False,True,float,low,arithmetic|declaration,2,,"float3 _11622 = fast::normalize(_11600 + (_11611 * fast::clamp(4500.0 / length(_11611), 0.0, 1.0)));"
463,True,False,half,medium,function_call|declaration|conversion,5,,"half _11536 = clamp(half(dot(_9109, _11622)), half(0.0), half(1.0));"
464,True,True,mixed,medium,declaration|conversion,3,考虑统一使用单一精度类型,"float _11560 = float(half(fast::max(0.119999997317790985107421875, float(_18272))));"
465,False,True,float,low,arithmetic|declaration,2,,"float _11629 = fast::max(0.00999999977648258209228515625, fast::clamp((4.125 * _11560) - 0.319999992"
466,False,True,float,low,declaration|conversion,2,,float _11919 = float(_11536);
467,False,True,float,low,function_call|declaration,2,,"float _11925 = dot(_9109, _11517);"
468,False,True,float,low,arithmetic|declaration,2,,float3 _11940 = fast::normalize(_11517 + _11622);
469,False,True,float,low,declaration,1,,"float _11945 = fast::clamp(dot(_9109, _11940), 0.0, 1.0);"
470,False,True,float,low,declaration,1,,"float _11951 = fast::clamp(dot(_11517, _11940), 0.0, 1.0);"
471,False,True,float,low,arithmetic|declaration,2,,"float _11736 = fast::clamp(abs(fast::clamp(_11925, 0.0, 1.0)) + 9.9999997473787516355514526367188e-0"
472,True,False,half,medium,declaration|conversion,2,,half4 _11959 = half4(half(0.60000002384185791015625));
473,True,False,half,low,arithmetic|declaration,2,,half4 _11967 = _11959 * _11959;
474,True,False,half,medium,arithmetic|declaration|conversion,3,,half4 _11970 = half4(half(1.0)) - _11967;
475,True,False,half,medium,arithmetic|declaration|conversion,3,,half4 _11973 = half4(half(1.0)) + _11967;
476,True,False,half,medium,arithmetic|declaration|conversion,3,,half4 _11975 = _11959 * half(2.0);
477,True,False,half,medium,declaration|conversion,2,,half4 _11982 = half4(half(1.5));
478,False,True,float,low,arithmetic|declaration,2,,float _11756 = exp2((((-5.554729938507080078125) * _11951) - 6.9831600189208984375) * _11951);
479,False,True,float,low,arithmetic|declaration,2,,float _11763 = _11756 + ((1.0 - _11756) * 0.039999999105930328369140625);
480,True,False,half,medium,declaration|conversion,2,,half _11764 = half(0.699999988079071044921875);
481,True,True,mixed,medium,arithmetic|declaration|conversion,4,考虑统一使用单一精度类型,half _11768 = half(float(_11764) + 0.100000001490116119384765625);
482,True,False,half,low,arithmetic|declaration,2,,half _11772 = _9199 - _8351;
483,True,False,half,low,arithmetic|declaration,2,,half _11777 = _9199 + _11768;
484,True,False,half,low,arithmetic|declaration,2,,half _11790 = _9199 + _11764;
485,True,True,mixed,medium,function_call|arithmetic|declaration|conversion,10,考虑统一使用单一精度类型,half3 _11812 = half3((float3(half3(_Block1.SunColor.xyz)) * _11919) * float(clamp(((_11772 + _11768)
486,True,False,half,medium,declaration|conversion,2,,half _11815 = half(10.0);
487,True,False,half,medium,function_call|arithmetic|declaration|conversion,6,,"half _11827 = clamp(dot(half3(fast::normalize(_11622 + _11517)), _9064), half(0.0), half(1.0));"
488,False,True,float,low,arithmetic|declaration|conversion,3,,float _11858 = float(_11815) * 0.5;
489,False,True,float,low,arithmetic|declaration|conversion,3,,float _11862 = float(_9251 + _11815);
490,False,True,float,low,arithmetic|declaration,2,,float _12049 = _11560 * _11560;
491,False,True,float,low,arithmetic|declaration,2,,float _12062 = _12049 / (((((_11945 * _12049) * _12049) - _11945) * _11945) + 1.0);
492,False,True,float,low,arithmetic|declaration,2,,float _12080 = _12049 * _12049;
493,True,True,mixed,medium,arithmetic|declaration|conversion,4,考虑统一使用单一精度类型,float _12108 = float(half(9.9956989288330078125e-05));
494,False,True,float,low,declaration|conversion,2,,float3 _12025 = float3(_10557);
495,False,True,float,low,arithmetic|declaration|conversion,3,,"float3 _12120 = float3(fast::clamp(50.0 * _12025.y, 0.0, 1.0)) - _12025;"
496,True,True,mixed,medium,function_call|arithmetic|declaration|conversion,14,考虑统一使用单一精度类型,"float3 _8521 = ((_9698 + (float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9"
497,False,False,none,low,,0,,uint _12171 = uint(_Block1.LightDataBuffer[0].x);
498,True,False,half,low,declaration,1,,half3 _18429;
499,False,True,float,low,declaration,1,,float3 _18431;
500,False,True,float,low,declaration,1,,float3 _19685;
501,False,True,float,low,declaration,1,,float3 _19720;
502,False,True,float,low,declaration,1,,float3 _19755;
503,True,False,half,low,declaration,1,,half3 _19825;
504,False,False,none,low,,0,,_19825 = _18526;
505,False,False,none,low,,0,,_19755 = _19185;
506,False,False,none,low,,0,,_19720 = _19185;
507,False,False,none,low,,0,,_19685 = _19185;
508,False,False,none,low,,0,,_18431 = _9698;
509,False,False,none,low,,0,,_18429 = _9179;
510,True,False,half,low,declaration,1,,half3 _19923;
511,False,True,float,low,declaration,1,,float3 _19965;
512,True,False,half,low,declaration,1,,half _20833;
513,False,True,float,low,declaration,1,,float _20848;
514,False,False,none,low,,0,,int _20863;
515,False,True,float,low,declaration,1,,float3 _20893;
516,False,True,float,low,declaration,1,,float3 _20908;
517,False,True,float,low,declaration,1,,float3 _20923;
518,False,True,float,low,declaration,1,,float _20938;
519,True,False,half,low,declaration,1,,half3 _20953;
520,True,False,half,low,declaration,1,,half _19485;
521,False,True,float,low,declaration,1,,float _19577;
522,False,False,none,low,,0,,int _19613;
523,False,True,float,low,declaration,1,,float _19790;
524,False,False,none,low,,0,,"for (uint _18428 = 0u; _18428 < _12171; _19825 = _20953, _19790 = _20938, _19755 = _20923, _19720 = "
525,False,False,none,low,,0,,{
526,False,False,none,low,,0,,uint _12181 = _18428 * 4u;
527,False,False,none,low,,0,,int _12188 = int(_12181 + 1u);
528,False,False,none,low,,0,,int _12195 = int(_12181 + 2u);
529,False,False,none,low,,0,,int _12202 = int(_12181 + 3u);
530,False,False,none,low,,0,,int _12209 = int(_12181 + 4u);
531,False,False,none,low,,0,,uint _12298 = as_type<uint>(_Block1.LightDataBuffer[_12209].x);
532,False,False,none,low,,0,,if (!((_12298 & 2097152u) == 2097152u))
533,False,False,none,low,,0,,{
534,False,False,none,low,,0,,_20953 = _19825;
535,False,False,none,low,,0,,_20938 = _19790;
536,False,False,none,low,,0,,_20923 = _19755;
537,False,False,none,low,,0,,_20908 = _19720;
538,False,False,none,low,,0,,_20893 = _19685;
539,False,False,none,low,,0,,_20863 = _19613;
540,False,False,none,low,,0,,_20848 = _19577;
541,False,False,none,low,,0,,_20833 = _19485;
542,False,False,none,low,,0,,_19965 = _18431;
543,False,False,none,low,,0,,_19923 = _18429;
544,False,False,none,low,,0,,continue;
545,False,False,none,low,,0,,}
546,False,False,none,low,,0,,uint _12309 = _12298 & 196608u;
547,True,False,half,low,declaration,1,,half _19481;
548,False,True,float,low,declaration,1,,float _19573;
549,False,False,none,low,,0,,int _19609;
550,False,True,float,low,declaration,1,,float3 _19681;
551,False,True,float,low,declaration,1,,float3 _19716;
552,False,True,float,low,declaration,1,,float3 _19751;
553,False,True,float,low,declaration,1,,float _19786;
554,True,False,half,low,declaration,1,,half3 _19821;
555,False,False,none,low,,0,,if (_12309 == 196608u)
556,False,False,none,low,,0,,{
557,False,True,float,low,arithmetic|declaration,2,,float3 _12360 = -_Block1.LightDataBuffer[_12202].xyz;
558,False,True,float,low,function_call|arithmetic|declaration,4,,float3 _12378 = in.IN_WorldPosition.xyz - (_Block1.LightDataBuffer[_12188].xyz + (_12360 * (dot(in.I
559,False,True,float,low,function_call|declaration,2,,"float _12381 = dot(_12378, _12378);"
560,False,True,float,low,declaration,1,,float _19350;
561,False,False,none,low,,0,,if (_12381 > (_Block1.LightDataBuffer[_12209].y * _Block1.LightDataBuffer[_12209].y))
562,False,False,none,low,,0,,{
563,False,True,float,low,function_call|arithmetic|declaration,3,,float _12392 = sqrt(_12381) - _Block1.LightDataBuffer[_12209].y;
564,False,True,float,low,arithmetic|declaration,2,,float _12395 = _12392 * _12392;
565,False,True,float,low,function_call|arithmetic|declaration,3,,float _12398 = _12395 * abs(_Block1.LightDataBuffer[_12188].w);
566,False,True,float,low,arithmetic|declaration,2,,"float _12404 = fast::clamp(1.0 - (_12398 * _12398), 0.0, 1.0);"
567,False,False,none,low,,0,,"_19350 = fast::min(100.0, (_12404 * _12404) / (_12395 + 1.0));"
568,False,False,none,low,,0,,}
569,False,False,none,low,,0,,else
570,False,False,none,low,,0,,{
571,False,False,none,low,,0,,_19350 = 1.0;
572,False,False,none,low,,0,,}
573,True,False,half,medium,conversion,1,,_19821 = half3(_Block1.LightDataBuffer[_12195].xyz);
574,False,False,none,low,,0,,_19786 = _19350;
575,False,False,none,low,,0,,_19751 = _12360;
576,False,False,none,low,,0,,_19716 = _11517;
577,False,False,none,low,,0,,_19681 = _9109;
578,False,False,none,low,,0,,_19609 = 0;
579,False,False,none,low,function_call,1,,_19573 = abs(_Block1.LightDataBuffer[_12195].w);
580,True,False,half,medium,function_call|conversion,4,,"_19481 = clamp(half(dot(_9109, _12360)), half(0.0), half(1.0));"
581,False,False,none,low,,0,,}
582,False,False,none,low,,0,,else
583,False,False,none,low,,0,,{
584,True,False,half,low,declaration,1,,half _19482;
585,False,True,float,low,declaration,1,,float _19574;
586,False,False,none,low,,0,,int _19610;
587,False,True,float,low,declaration,1,,float3 _19682;
588,False,True,float,low,declaration,1,,float3 _19717;
589,False,True,float,low,declaration,1,,float3 _19752;
590,False,True,float,low,declaration,1,,float _19787;
591,True,False,half,low,declaration,1,,half3 _19822;
592,False,False,none,low,,0,,if (_12309 == 0u)
593,False,False,none,low,,0,,{
594,False,False,none,low,,0,,uint _12741 = as_type<uint>(_Block1.LightDataBuffer[_12195].w);
595,False,True,float,low,arithmetic|declaration|conversion,3,,float _12858 = float((_12741 >> 0u) & 65535u) * 0.0001525902189314365386962890625;
596,False,True,float,low,arithmetic|declaration,2,,float3 _12599 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;
597,False,True,float,low,function_call|declaration,2,,"float _12602 = dot(_12599, _12599);"
598,False,True,float,low,function_call|arithmetic|declaration,3,,float3 _12606 = _12599 * rsqrt(_12602);
599,False,True,float,low,function_call|arithmetic|declaration,3,,float _12613 = _12602 * abs(_Block1.LightDataBuffer[_12188].w);
600,False,True,float,low,arithmetic|declaration,2,,"float _12620 = fast::clamp(1.0 - (_12613 * _12613), 0.0, 1.0);"
601,False,True,float,low,arithmetic|declaration,2,,float _12631 = _12620 * _12620;
602,False,True,float,low,declaration,1,,float _19213;
603,False,False,none,low,,0,,if ((_12298 & 16777216u) == 16777216u)
604,False,False,none,low,,0,,{
605,False,True,float,low,arithmetic|declaration,2,,float _12641 = _12631 / ((_12602 * _Block1.LightDataBuffer[_12202].w) + 9.99999974737875163555145263
606,False,True,float,low,declaration,1,,float _19211;
607,False,False,none,low,,0,,if (_12858 > 0.00999999977648258209228515625)
608,False,False,none,low,,0,,{
609,False,False,none,low,,0,,"_19211 = fast::min(_12641, _12858);"
610,False,False,none,low,,0,,}
611,False,False,none,low,,0,,else
612,False,False,none,low,,0,,{
613,False,False,none,low,,0,,_19211 = _12641;
614,False,False,none,low,,0,,}
615,False,False,none,low,,0,,"_19213 = fast::min(100.0, _19211);"
616,False,False,none,low,,0,,}
617,False,False,none,low,,0,,else
618,False,False,none,low,,0,,{
619,False,False,none,low,,0,,_19213 = _12631 * 0.100000001490116119384765625;
620,False,False,none,low,,0,,}
621,False,True,float,low,arithmetic|declaration,2,,"float _12677 = fast::clamp((dot(_Block1.LightDataBuffer[_12202].xyz, -_12606) - _Block1.LightDataBuf"
622,True,True,mixed,medium,arithmetic|conversion,3,考虑统一使用单一精度类型,"_19822 = half3(fast::min(float3(3000.0), _Block1.LightDataBuffer[_12195].xyz * _19213));"
623,False,False,none,low,,0,,_19787 = _12677 * _12677;
624,False,False,none,low,,0,,_19752 = _12606;
625,False,False,none,low,,0,,_19717 = _11517;
626,False,False,none,low,,0,,_19682 = _9109;
627,False,False,none,low,,0,,_19610 = 0;
628,False,True,float,low,arithmetic|conversion,2,,_19574 = float((_12741 >> 16u) & 65535u) * 0.001525902189314365386962890625;
629,True,False,half,medium,function_call|conversion,4,,"_19482 = clamp(half(dot(_9109, _12606)), half(0.0), half(1.0));"
630,False,False,none,low,,0,,}
631,False,False,none,low,,0,,else
632,False,False,none,low,,0,,{
633,True,False,half,low,declaration,1,,half _19483;
634,False,True,float,low,declaration,1,,float _19575;
635,False,False,none,low,,0,,int _19611;
636,False,True,float,low,declaration,1,,float3 _19683;
637,False,True,float,low,declaration,1,,float3 _19718;
638,False,True,float,low,declaration,1,,float3 _19753;
639,False,True,float,low,declaration,1,,float _19788;
640,True,False,half,low,declaration,1,,half3 _19823;
641,False,False,none,low,,0,,if (_12309 == 65536u)
642,False,False,none,low,,0,,{
643,False,False,none,low,,0,,uint _13098 = as_type<uint>(_Block1.LightDataBuffer[_12195].w);
644,False,True,float,low,arithmetic|declaration,2,,float3 _12933 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;
645,False,True,float,low,function_call|declaration,2,,"float _12936 = dot(_12933, _12933);"
646,False,True,float,low,arithmetic|declaration|conversion,3,,float3 _12942 = _12933 / float3(sqrt(_12936));
647,False,True,float,low,function_call|arithmetic|declaration,3,,float _12950 = _12936 * abs(_Block1.LightDataBuffer[_12188].w);
648,False,True,float,low,arithmetic|declaration,2,,"float _12956 = fast::clamp(1.0 - (_12950 * _12950), 0.0, 1.0);"
649,False,True,float,low,arithmetic|declaration,2,,"float _12972 = fast::min(100.0, (_12956 * _12956) / ((_12936 * _Block1.LightDataBuffer[_12209].w) + "
650,False,True,float,low,arithmetic|declaration|conversion,3,,float _13202 = float((_13098 >> 0u) & 65535u) * 0.0001525902189314365386962890625;
651,False,True,float,low,declaration,1,,float _19070;
652,False,False,none,low,,0,,if (_13202 > 0.00999999977648258209228515625)
653,False,False,none,low,,0,,{
654,False,False,none,low,,0,,"_19070 = fast::min(_12972, _13202);"
655,False,False,none,low,,0,,}
656,False,False,none,low,,0,,else
657,False,False,none,low,,0,,{
658,False,False,none,low,,0,,_19070 = _12972;
659,False,False,none,low,,0,,}
660,True,True,mixed,medium,arithmetic|conversion,3,考虑统一使用单一精度类型,"_19823 = half3(fast::min(float3(3000.0), _Block1.LightDataBuffer[_12195].xyz * _19070) * (((_12298 &"
661,False,False,none,low,,0,,_19788 = 1.0;
662,False,False,none,low,,0,,_19753 = _12942;
663,False,False,none,low,,0,,_19718 = _11517;
664,False,False,none,low,,0,,_19683 = _9109;
665,False,False,none,low,,0,,_19611 = 0;
666,False,True,float,low,arithmetic|conversion,2,,_19575 = float((_13098 >> 16u) & 65535u) * 0.001525902189314365386962890625;
667,True,False,half,medium,conversion,1,,"_19483 = ((_12298 & 262144u) == 262144u) ? _9019 : half(fast::clamp(dot(_9109, _12942), 0.0, 1.0));"
668,False,False,none,low,,0,,}
669,False,False,none,low,,0,,else
670,False,False,none,low,,0,,{
671,False,False,none,low,,0,,bool _13270 = _12309 == 131072u;
672,True,False,half,low,declaration,1,,half _19484;
673,False,True,float,low,declaration,1,,float3 _19684;
674,False,True,float,low,declaration,1,,float3 _19754;
675,False,True,float,low,declaration,1,,float _19789;
676,True,False,half,low,declaration,1,,half3 _19824;
677,False,False,none,low,,0,,if (_13270)
678,False,False,none,low,,0,,{
679,False,True,float,low,arithmetic|declaration,2,,float3 _13339 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;
680,False,True,float,low,function_call|declaration,2,,"float _13342 = dot(_13339, _13339);"
681,False,True,float,low,function_call|arithmetic|declaration,3,,float _13348 = _13342 * abs(_Block1.LightDataBuffer[_12188].w);
682,False,True,float,low,arithmetic|declaration,2,,"float _13356 = fast::clamp(1.0 - (_13348 * _13348), 0.0, 1.0);"
683,False,True,float,low,arithmetic|declaration,2,,float3 _13433 = fast::normalize(_11517 - (_9109 * _11925));
684,False,False,none,low,,0,,"float3x3 _13459 = float3x3(_13433, cross(_9109, _13433), _9109);"
685,False,True,float,low,arithmetic|declaration|conversion,3,,float3 _13466 = float3(_Block1.LightDataBuffer[_12202].xyz) * _Block1.LightDataBuffer[_12195].w;
686,False,True,float,low,arithmetic|declaration,2,,float3 _13467 = _13339 - _13466;
687,False,True,float,low,arithmetic|declaration|conversion,3,,float3 _13472 = float3(_Block1.LightDataBuffer[_12209].yzw) * _Block1.LightDataBuffer[_12202].w;
688,False,True,float,low,arithmetic|declaration,2,,float3 _13484 = _13339 + _13466;
689,False,True,float,low,arithmetic|declaration,2,,float3 _13657 = fast::normalize((_13467 - _13472) * _13459);
690,False,True,float,low,arithmetic|declaration,2,,float3 _13660 = fast::normalize((_13484 - _13472) * _13459);
691,False,True,float,low,arithmetic|declaration,2,,float3 _13663 = fast::normalize((_13484 + _13472) * _13459);
692,False,True,float,low,arithmetic|declaration,2,,float3 _13666 = fast::normalize((_13467 + _13472) * _13459);
693,False,True,float,low,function_call|declaration,2,,"float _13712 = dot(_13657, _13660);"
694,False,True,float,low,function_call|declaration,2,,float _13714 = abs(_13712);
695,False,True,float,low,arithmetic|declaration,2,,float _13728 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.0145206004381179809570312
696,False,True,float,low,function_call|declaration,2,,"float _13753 = dot(_13660, _13663);"
697,False,True,float,low,function_call|declaration,2,,float _13755 = abs(_13753);
698,False,True,float,low,arithmetic|declaration,2,,float _13769 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.0145206004381179809570312
699,False,True,float,low,function_call|declaration,2,,"float _13794 = dot(_13663, _13666);"
700,False,True,float,low,function_call|declaration,2,,float _13796 = abs(_13794);
701,False,True,float,low,arithmetic|declaration,2,,float _13810 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.0145206004381179809570312
702,False,True,float,low,function_call|declaration,2,,"float _13835 = dot(_13666, _13657);"
703,False,True,float,low,function_call|declaration,2,,float _13837 = abs(_13835);
704,False,True,float,low,arithmetic|declaration,2,,float _13851 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.0145206004381179809570312
705,False,True,float,low,function_call|arithmetic|declaration,8,,"float3 _13700 = cross(_13660, (_13657 * (-((_13712 > 0.0) ? _13728 : ((0.5 * rsqrt(fast::max(1.0 - ("
706,False,True,float,low,declaration,1,,float _13531 = length(_13700);
707,False,True,float,low,declaration,1,,"float _13539 = step(0.0, dot(cross(_Block1.LightDataBuffer[_12202].xyz, _Block1.LightDataBuffer[_122"
708,True,False,half,medium,arithmetic|conversion,2,,_19824 = half3(_Block1.LightDataBuffer[_12195].xyz * (_13356 * _13356));
709,False,False,none,low,,0,,_19789 = ((!((_12298 & 67108864u) == 67108864u)) && (_13539 > 0.0)) ? 0.0 : _13531;
710,False,False,none,low,function_call,1,,_19754 = _13339 * rsqrt(_13342);
711,False,False,none,low,,0,,_19684 = _9109;
712,True,True,mixed,medium,arithmetic|conversion,3,考虑统一使用单一精度类型,_19484 = half(fast::max(((_13531 * _13531) + ((_13700 / float3(_13531)).z * ((_13539 * 2.0) - 1.0)))
713,False,False,none,low,,0,,}
714,False,False,none,low,,0,,else
715,False,False,none,low,,0,,{
716,False,False,none,low,,0,,_19824 = _19825;
717,False,False,none,low,,0,,_19789 = _19790;
718,False,False,none,low,,0,,_19754 = _19755;
719,False,False,none,low,,0,,_19684 = _19685;
720,False,False,none,low,,0,,_19484 = _19485;
721,False,False,none,low,,0,,}
722,False,False,none,low,,0,,_19823 = _19824;
723,False,False,none,low,,0,,_19788 = _19789;
724,False,False,none,low,,0,,_19753 = _19754;
725,False,False,none,low,,0,,"_19718 = select(_19720, _11517, bool3(_13270));"
726,False,False,none,low,,0,,_19683 = _19684;
727,False,False,none,low,,0,,_19611 = _13270 ? 0 : _19613;
728,False,False,none,low,,0,,_19575 = _13270 ? 1.0 : _19577;
729,False,False,none,low,,0,,_19483 = _19484;
730,False,False,none,low,,0,,}
731,False,False,none,low,,0,,_19822 = _19823;
732,False,False,none,low,,0,,_19787 = _19788;
733,False,False,none,low,,0,,_19752 = _19753;
734,False,False,none,low,,0,,_19717 = _19718;
735,False,False,none,low,,0,,_19682 = _19683;
736,False,False,none,low,,0,,_19610 = _19611;
737,False,False,none,low,,0,,_19574 = _19575;
738,False,False,none,low,,0,,_19482 = _19483;
739,False,False,none,low,,0,,}
740,False,False,none,low,,0,,_19821 = _19822;
741,False,False,none,low,,0,,_19786 = _19787;
742,False,False,none,low,,0,,_19751 = _19752;
743,False,False,none,low,,0,,_19716 = _19717;
744,False,False,none,low,,0,,_19681 = _19682;
745,False,False,none,low,,0,,_19609 = _19610;
746,False,False,none,low,,0,,_19573 = _19574;
747,False,False,none,low,,0,,_19481 = _19482;
748,False,False,none,low,,0,,}
749,False,True,float,low,declaration|conversion,2,,float _14176 = float(_19481);
750,False,True,float,low,arithmetic|declaration,2,,float3 _14197 = fast::normalize(_19716 + _19751);
751,False,True,float,low,declaration,1,,"float _14202 = fast::clamp(dot(_19681, _14197), 0.0, 1.0);"
752,False,True,float,low,declaration,1,,"float _14208 = fast::clamp(dot(_19716, _14197), 0.0, 1.0);"
753,False,True,float,low,arithmetic|declaration,2,,"float _13993 = fast::clamp(abs(fast::clamp(dot(_19681, _19716), 0.0, 1.0)) + 9.999999747378751635551"
754,False,True,float,low,arithmetic|declaration,2,,float _14013 = exp2((((-5.554729938507080078125) * _14208) - 6.9831600189208984375) * _14208);
755,False,True,float,low,arithmetic|declaration,2,,float _14020 = _14013 + ((1.0 - _14013) * 0.039999999105930328369140625);
756,True,False,half,low,arithmetic|declaration,2,,half _14029 = _9199 - (_9199 - _19481);
757,True,True,mixed,medium,function_call|arithmetic|declaration|conversion,11,考虑统一使用单一精度类型,half3 _14069 = half3(((float3(_19821) * float3(_19786)) * _14176) * float(clamp(((_14029 + _11768) /
758,True,False,half,medium,function_call|arithmetic|declaration|conversion,6,,"half _14084 = clamp(dot(half3(fast::normalize(_19751 + _19716)), _9064), half(0.0), half(1.0));"
759,False,True,float,low,declaration,1,,float3 _19883;
760,False,False,none,low,,0,,do
761,False,False,none,low,,0,,{
762,False,False,none,low,,0,,if (_19609 >= 1)
763,False,False,none,low,,0,,{
764,False,True,float,low,arithmetic|declaration,2,,float _14319 = _12049 / (((((_14202 * _12049) * _12049) - _14202) * _14202) + 1.0);
765,False,False,none,low,function_call,1,,"_19883 = (_12025 + (_12120 * _14013)) * (fast::min(1000.0, (_14319 * _14319) * 0.3183098733425140380"
766,False,False,none,low,,0,,break;
767,False,False,none,low,,0,,}
768,False,False,none,low,,0,,else
769,False,False,none,low,,0,,{
770,False,True,float,low,conversion,1,,_19883 = float3(0.0);
771,False,False,none,low,,0,,break;
772,False,False,none,low,,0,,}
773,False,False,none,low,,0,,break; // unreachable workaround
774,False,False,none,low,,0,,} while(false);
775,False,False,none,low,,0,,_20953 = _19821;
776,False,False,none,low,,0,,_20938 = _19786;
777,False,False,none,low,,0,,_20923 = _19751;
778,False,False,none,low,,0,,_20908 = _19716;
779,False,False,none,low,,0,,_20893 = _19681;
780,False,False,none,low,,0,,_20863 = _19609;
781,False,False,none,low,,0,,_20848 = _19573;
782,False,False,none,low,,0,,_20833 = _19481;
783,True,True,mixed,medium,arithmetic|conversion,12,考虑统一使用单一精度类型,"_19965 = (_18431 + (float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - "
784,False,False,none,low,,0,,_19923 = _18429 + (_14069 * _14029);
785,False,False,none,low,,0,,}
786,True,False,half,medium,arithmetic|declaration|conversion,3,,half3 _8560 = (_9179 + (((_9179 + (_11812 * _11772)) * _9254) * half3(half(_Block1.EnvInfo.z)))) + _
787,False,True,float,low,arithmetic|declaration,2,,float3 _8565 = (_9698 + _8521) + _18431;
788,False,False,none,low,,0,,bool _8573 = (_8397 & 16128u) > 0u;
789,False,True,float,low,declaration,1,,float3 _18989;
790,True,False,half,low,declaration,1,,half3 _19028;
791,False,False,none,low,,0,,if (_8573)
792,False,False,none,low,,0,,{
793,False,False,none,low,,0,,bool _8590 = (_8397 & 16384u) > 0u;
794,True,False,half,low,declaration,1,,half3 _18832;
795,True,False,half,low,declaration,1,,half3 _18859;
796,False,False,none,low,,0,,if (_8573)
797,False,False,none,low,,0,,{
798,False,False,none,low,,0,,uint _14451 = (_8397 & 458752u) >> 16u;
799,False,False,none,low,,0,,uint _14469 = (_8397 & 4026531840u) >> 28u;
800,False,True,float,low,arithmetic|declaration,2,,"float _14482 = fast::clamp((_Block1.CameraInfo.y * in.IN_LinearZ) / fast::max(_Block1.SHGIParam2.x, "
801,True,False,half,low,declaration,1,,half3 _18824;
802,True,False,half,low,declaration,1,,half3 _18826;
803,False,False,none,low,,0,,if ((_8397 & 2048u) != 0u)
804,False,False,none,low,,0,,{
805,False,True,float,low,arithmetic|declaration|conversion,3,,"float3 _14684 = select(_Block1.PlayerPos.xyz, _Block1.CameraPos.xyz, bool3((_8397 & 524288u) > 0u)) "
806,False,True,float,low,declaration,1,,float _14692 = _14684.x;
807,False,True,float,low,function_call|declaration,2,,float _14694 = floor(_14692);
808,False,True,float,low,declaration,1,,float3 _21212;
809,False,False,none,low,,0,,_21212.x = _14694 - 15.0;
810,False,True,float,low,declaration,1,,float3 _21268;
811,False,False,none,low,,0,,if ((_14692 - _14694) > 0.5)
812,False,False,none,low,,0,,{
813,False,True,float,low,declaration,1,,float3 _21215 = _21212;
814,False,False,none,low,,0,,_21215.x = _14694 + (-14.0);
815,False,False,none,low,,0,,_21268 = _21215;
816,False,False,none,low,,0,,}
817,False,False,none,low,,0,,else
818,False,False,none,low,,0,,{
819,False,False,none,low,,0,,_21268 = _21212;
820,False,False,none,low,,0,,}
821,False,True,float,low,declaration,1,,float _21034 = _14684.y;
822,False,True,float,low,function_call|declaration,2,,float _21035 = floor(_21034);
823,False,True,float,low,declaration,1,,float3 _21219 = _21268;
824,False,False,none,low,,0,,_21219.y = _21035 - 8.0;
825,False,True,float,low,declaration,1,,float3 _21269;
826,False,False,none,low,,0,,if ((_21034 - _21035) > 0.5)
827,False,False,none,low,,0,,{
828,False,True,float,low,declaration,1,,float3 _21222 = _21219;
829,False,False,none,low,,0,,_21222.y = _21035 + (-7.0);
830,False,False,none,low,,0,,_21269 = _21222;
831,False,False,none,low,,0,,}
832,False,False,none,low,,0,,else
833,False,False,none,low,,0,,{
834,False,False,none,low,,0,,_21269 = _21219;
835,False,False,none,low,,0,,}
836,False,True,float,low,declaration,1,,float _21056 = _14684.z;
837,False,True,float,low,function_call|declaration,2,,float _21057 = floor(_21056);
838,False,True,float,low,declaration,1,,float3 _21226 = _21269;
839,False,False,none,low,,0,,_21226.z = _21057 - 15.0;
840,False,True,float,low,declaration,1,,float3 _21270;
841,False,False,none,low,,0,,if ((_21056 - _21057) > 0.5)
842,False,False,none,low,,0,,{
843,False,True,float,low,declaration,1,,float3 _21229 = _21226;
844,False,False,none,low,,0,,_21229.z = _21057 + (-14.0);
845,False,False,none,low,,0,,_21270 = _21229;
846,False,False,none,low,,0,,}
847,False,False,none,low,,0,,else
848,False,False,none,low,,0,,{
849,False,False,none,low,,0,,_21270 = _21226;
850,False,False,none,low,,0,,}
851,False,True,float,low,arithmetic|declaration,2,,float3 _14717 = _21270 * 8.0;
852,True,False,half,low,declaration,1,,half3 _18825;
853,True,False,half,low,declaration,1,,half3 _18827;
854,False,True,float,low,arithmetic|conversion,2,,"if (all(in.IN_WorldPosition.xyz >= _14717) && all(in.IN_WorldPosition.xyz < (_14717 + float3(240.0, "
855,False,False,none,low,,0,,{
856,False,True,float,low,arithmetic|declaration|conversion,3,,float3 _14534 = (in.IN_WorldPosition.xyz + (_9109 * 0.100000001490116119384765625)) * float3(0.00416
857,False,True,float,low,function_call|arithmetic|declaration,3,,float3 _14747 = _14534 - floor(_14534);
858,True,False,half,medium,declaration|conversion,2,,half3 _14556 = half3(_9109);
859,False,True,float,low,declaration|conversion,2,,float _14788 = float((_8397 & 15728640u) >> 20u);
860,False,True,float,low,declaration|conversion,2,,float _14797 = float(_14451);
861,False,True,float,low,arithmetic|declaration,2,,float _14827 = (_14788 - _14797) + ((3.0 - _14797) * 3.0);
862,False,True,float,low,arithmetic|declaration,2,,float _14831 = _14827 + 1.0;
863,False,True,float,low,arithmetic|declaration,2,,float _14833 = _14827 + 2.0;
864,True,False,half,low,declaration,1,,half3 _18806;
865,True,False,half,low,declaration,1,,half3 _18818;
866,False,False,none,low,,0,,if (3 >= int(_14469))
867,False,False,none,low,,0,,{
868,False,True,float,low,arithmetic|declaration|conversion,4,,float _14850 = (_14788 - float((_8397 & 251658240u) >> 24u)) + ((3.0 - float(_14469)) * 3.0);
869,False,True,float,low,arithmetic|declaration,2,,float _14854 = _14850 + 1.0;
870,False,True,float,low,arithmetic|declaration,2,,float _14856 = _14850 + 2.0;
871,False,True,float,low,arithmetic|declaration|conversion,4,,float2 _14956 = ((_14747.xz - float2(0.5)) * 0.9375) + float2(0.5);
872,False,True,float,low,arithmetic|declaration,2,,float _14963 = _14717.x * 0.0041666668839752674102783203125;
873,False,True,float,low,function_call|arithmetic|declaration,3,,float _14967 = ((_14963 - floor(_14963)) - 0.5) * 0.9375;
874,False,True,float,low,arithmetic|declaration,2,,float _14973 = _14717.z * 0.0041666668839752674102783203125;
875,False,True,float,low,function_call|arithmetic|declaration,3,,float _14977 = ((_14973 - floor(_14973)) - 0.5) * 0.9375;
876,False,True,float,low,declaration,1,,float _14982 = _14956.x;
877,False,True,float,low,declaration,1,,float3 _18095;
878,False,False,none,low,,0,,"_18095.x = (_14982 < (_14967 + 0.5)) ? fast::min(_14982, _14967 + 0.49609375) : fast::max(_14982, _1"
879,False,True,float,low,declaration,1,,float _15000 = _14956.y;
880,False,False,none,low,,0,,"_18095.z = (_15000 < (_14977 + 0.5)) ? fast::min(_15000, _14977 + 0.49609375) : fast::max(_15000, _1"
881,False,True,float,low,arithmetic|declaration,2,,float _15021 = (_14747.y * 64.0) - 0.5;
882,False,True,float,low,function_call|declaration,2,,float _15026 = floor(_15021);
883,False,False,none,low,,0,,uint _15029 = (_15021 < 0.0) ? 63u : uint(_15026);
884,False,False,none,low,,0,,uint _15032 = _15029 + 1u;
885,False,False,none,low,,0,,uint _21309 = (_15032 >= 64u) ? 0u : _15032;
886,False,True,float,low,arithmetic|declaration|conversion,4,,"float2 _15059 = (float2(float(_15029 & 7u), float(_15029 >> 3u)) + _18095.xz) * 0.125;"
887,False,True,float,low,arithmetic|declaration|conversion,4,,"float2 _15074 = (float2(float(_21309 & 7u), float(_21309 >> 3u)) + _18095.xz) * 0.125;"
888,False,True,float,low,declaration,1,,float _15078 = _15059.x;
889,False,True,float,low,declaration|conversion,2,,"float3 _15080 = float3(_15078, _15059.y, _14827);"
890,True,False,half,low,declaration,1,,half4 _18103;
891,True,False,half,high,texture_sample|conversion,2,考虑优化纹理访问模式,"_18103.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15080.xy, uint(rint(_15080.z))"
892,False,True,float,low,declaration|conversion,2,,"float3 _15092 = float3(_15078, _15059.y, _14850);"
893,True,False,half,high,texture_sample|declaration|conversion,3,考虑优化纹理访问模式,"half3 _15097 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15092.xy, uint(rint(_15092.z)"
894,False,True,float,low,declaration,1,,float _15104 = _15074.x;
895,False,True,float,low,declaration|conversion,2,,"float3 _15106 = float3(_15104, _15074.y, _14827);"
896,True,False,half,low,declaration,1,,half4 _18105;
897,True,False,half,high,texture_sample|conversion,2,考虑优化纹理访问模式,"_18105.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15106.xy, uint(rint(_15106.z))"
898,False,True,float,low,declaration|conversion,2,,"float3 _15118 = float3(_15104, _15074.y, _14850);"
899,True,False,half,high,texture_sample|declaration|conversion,3,考虑优化纹理访问模式,"half3 _15123 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15118.xy, uint(rint(_15118.z)"
900,True,False,half,medium,arithmetic|declaration|conversion,3,,"half4 _15132 = half4(half(fast::clamp(_15021 - _15026, 0.0, 1.0)));"
901,True,False,half,medium,function_call|declaration|conversion,4,,"half4 _15133 = mix(half4(_15097.x, _15097.y, _15097.z, _18103.w), half4(_15123.x, _15123.y, _15123.z"
902,False,True,float,low,declaration|conversion,2,,"float3 _15140 = float3(_15078, _15059.y, _14831);"
903,True,False,half,low,declaration,1,,half4 _18107;
904,True,False,half,high,texture_sample|conversion,2,考虑优化纹理访问模式,"_18107.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15140.xy, uint(rint(_15140.z))"
905,False,True,float,low,declaration|conversion,2,,"float3 _15152 = float3(_15078, _15059.y, _14854);"
906,True,False,half,high,texture_sample|declaration|conversion,3,考虑优化纹理访问模式,"half3 _15157 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15152.xy, uint(rint(_15152.z)"
907,False,True,float,low,declaration|conversion,2,,"float3 _15166 = float3(_15104, _15074.y, _14831);"
908,True,False,half,low,declaration,1,,half4 _18109;
909,True,False,half,high,texture_sample|conversion,2,考虑优化纹理访问模式,"_18109.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15166.xy, uint(rint(_15166.z))"
910,False,True,float,low,declaration|conversion,2,,"float3 _15178 = float3(_15104, _15074.y, _14854);"
911,True,False,half,high,texture_sample|declaration|conversion,3,考虑优化纹理访问模式,"half3 _15183 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15178.xy, uint(rint(_15178.z)"
912,True,False,half,medium,function_call|declaration|conversion,4,,"half4 _15193 = mix(half4(_15157.x, _15157.y, _15157.z, _18107.w), half4(_15183.x, _15183.y, _15183.z"
913,False,True,float,low,declaration|conversion,2,,"float3 _15200 = float3(_15078, _15059.y, _14833);"
914,True,False,half,low,declaration,1,,half4 _18111;
915,True,False,half,high,texture_sample|conversion,2,考虑优化纹理访问模式,"_18111.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15200.xy, uint(rint(_15200.z))"
916,False,True,float,low,declaration|conversion,2,,"float3 _15212 = float3(_15078, _15059.y, _14856);"
917,True,False,half,high,texture_sample|declaration|conversion,3,考虑优化纹理访问模式,"half3 _15217 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15212.xy, uint(rint(_15212.z)"
918,False,True,float,low,declaration|conversion,2,,"float3 _15226 = float3(_15104, _15074.y, _14833);"
919,True,False,half,low,declaration,1,,half4 _18113;
920,True,False,half,high,texture_sample|conversion,2,考虑优化纹理访问模式,"_18113.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15226.xy, uint(rint(_15226.z))"
921,False,True,float,low,declaration|conversion,2,,"float3 _15238 = float3(_15104, _15074.y, _14856);"
922,True,False,half,high,texture_sample|declaration|conversion,3,考虑优化纹理访问模式,"half3 _15243 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15238.xy, uint(rint(_15238.z)"
923,True,False,half,medium,function_call|declaration|conversion,4,,"half4 _15253 = mix(half4(_15217.x, _15217.y, _15217.z, _18111.w), half4(_15243.x, _15243.y, _15243.z"
924,True,False,half,medium,declaration|conversion,2,,half _15255 = half(32.0);
925,True,False,half,low,arithmetic|declaration,2,,half _15258 = _15133.w * _15255;
926,True,False,half,low,arithmetic|declaration,2,,half _15262 = _15193.w * _15255;
927,True,False,half,low,arithmetic|declaration,2,,half _15266 = _15253.w * _15255;
928,True,True,mixed,medium,arithmetic|declaration|conversion,7,考虑统一使用单一精度类型,half3 _15343 = half3(((float3(_15133.xyz) * float3(2.0)) - float3(1.0)) * float(_15258)).xyz;
929,True,False,half,low,declaration,1,,half3 _18130;
930,True,True,mixed,medium,arithmetic|conversion,3,考虑统一使用单一精度类型,"_18130.x = half(float(dot(_14556, _15343)) * 2.0);"
931,True,True,mixed,medium,arithmetic|declaration|conversion,7,考虑统一使用单一精度类型,half3 _15352 = half3(((float3(_15193.xyz) * float3(2.0)) - float3(1.0)) * float(_15262)).xyz;
932,True,True,mixed,medium,arithmetic|conversion,3,考虑统一使用单一精度类型,"_18130.y = half(float(dot(_14556, _15352)) * 2.0);"
933,True,True,mixed,medium,arithmetic|declaration|conversion,7,考虑统一使用单一精度类型,half3 _15361 = half3(((float3(_15253.xyz) * float3(2.0)) - float3(1.0)) * float(_15266)).xyz;
934,True,True,mixed,medium,arithmetic|conversion,3,考虑统一使用单一精度类型,"_18130.z = half(float(dot(_14556, _15361)) * 2.0);"
935,True,False,half,low,declaration,1,,half3 _18819;
936,False,False,none,low,,0,,if (_8590)
937,False,False,none,low,,0,,{
938,True,True,mixed,medium,arithmetic|conversion,8,考虑统一使用单一精度类型,_18819 = half3(((float3(_15343) * float3(0.21199999749660491943359375)) + (float3(_15352) * float3(0
939,False,False,none,low,,0,,}
940,False,False,none,low,,0,,else
941,False,False,none,low,,0,,{
942,False,False,none,low,,0,,_18819 = _8393;
943,False,False,none,low,,0,,}
944,False,False,none,low,,0,,_18818 = _18819;
945,True,False,half,medium,function_call|arithmetic|conversion,4,,"_18806 = max(half3(_15258, _15262, _15266) + (_18130 * half(mix(_Block1.SHGIParam2.z, 1.0, _14482)))"
946,False,False,none,low,,0,,}
947,False,False,none,low,,0,,else
948,False,False,none,low,,0,,{
949,False,True,float,low,arithmetic|declaration|conversion,4,,float2 _15427 = ((_14747.xz - float2(0.5)) * 0.9375) + float2(0.5);
950,False,True,float,low,arithmetic|declaration,2,,float _15434 = _14717.x * 0.0041666668839752674102783203125;
951,False,True,float,low,function_call|arithmetic|declaration,3,,float _15438 = ((_15434 - floor(_15434)) - 0.5) * 0.9375;
952,False,True,float,low,arithmetic|declaration,2,,float _15444 = _14717.z * 0.0041666668839752674102783203125;
953,False,True,float,low,function_call|arithmetic|declaration,3,,float _15448 = ((_15444 - floor(_15444)) - 0.5) * 0.9375;
954,False,True,float,low,declaration,1,,float _15453 = _15427.x;
955,False,True,float,low,declaration,1,,float3 _18143;
956,False,False,none,low,,0,,"_18143.x = (_15453 < (_15438 + 0.5)) ? fast::min(_15453, _15438 + 0.49609375) : fast::max(_15453, _1"
957,False,True,float,low,declaration,1,,float _15471 = _15427.y;
958,False,False,none,low,,0,,"_18143.z = (_15471 < (_15448 + 0.5)) ? fast::min(_15471, _15448 + 0.49609375) : fast::max(_15471, _1"
959,False,True,float,low,arithmetic|declaration,2,,float _15492 = (_14747.y * 64.0) - 0.5;
960,False,True,float,low,function_call|declaration,2,,float _15497 = floor(_15492);
961,False,False,none,low,,0,,uint _15500 = (_15492 < 0.0) ? 63u : uint(_15497);
962,False,False,none,low,,0,,uint _15503 = _15500 + 1u;
963,False,False,none,low,,0,,uint _21308 = (_15503 >= 64u) ? 0u : _15503;
964,False,True,float,low,arithmetic|declaration|conversion,4,,"float2 _15530 = (float2(float(_15500 & 7u), float(_15500 >> 3u)) + _18143.xz) * 0.125;"
965,False,True,float,low,arithmetic|declaration|conversion,4,,"float2 _15545 = (float2(float(_21308 & 7u), float(_21308 >> 3u)) + _18143.xz) * 0.125;"
966,False,True,float,low,declaration,1,,float _15549 = _15530.x;
967,False,True,float,low,declaration|conversion,2,,"float3 _15551 = float3(_15549, _15530.y, _14827);"
968,True,False,half,low,declaration,1,,half3 _18151;
969,True,False,half,high,texture_sample|conversion,2,考虑优化纹理访问模式,"_18151.x = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15551.xy, uint(rint(_15551.z))"
970,False,True,float,low,declaration,1,,float _15561 = _15545.x;
971,False,True,float,low,declaration|conversion,2,,"float3 _15563 = float3(_15561, _15545.y, _14827);"
972,True,False,half,low,declaration,1,,half3 _18153;
973,True,False,half,high,texture_sample|conversion,2,考虑优化纹理访问模式,"_18153.x = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15563.xy, uint(rint(_15563.z))"
974,False,True,float,low,declaration|conversion,2,,"float3 _15575 = float3(_15549, _15530.y, _14831);"
975,True,False,half,high,texture_sample|conversion,2,考虑优化纹理访问模式,"_18151.y = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15575.xy, uint(rint(_15575.z))"
976,False,True,float,low,declaration|conversion,2,,"float3 _15587 = float3(_15561, _15545.y, _14831);"
977,True,False,half,high,texture_sample|conversion,2,考虑优化纹理访问模式,"_18153.y = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15587.xy, uint(rint(_15587.z))"
978,False,True,float,low,declaration|conversion,2,,"float3 _15599 = float3(_15549, _15530.y, _14833);"
979,True,False,half,high,texture_sample|conversion,2,考虑优化纹理访问模式,"_18151.z = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15599.xy, uint(rint(_15599.z))"
980,False,True,float,low,declaration|conversion,2,,"float3 _15611 = float3(_15561, _15545.y, _14833);"
981,True,False,half,high,texture_sample|conversion,2,考虑优化纹理访问模式,"_18153.z = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15611.xy, uint(rint(_15611.z))"
982,True,False,half,medium,function_call|arithmetic|declaration|conversion,4,,"half3 _15622 = mix(_18151, _18153, half3(half(fast::clamp(_15492 - _15497, 0.0, 1.0))));"
983,True,False,half,medium,declaration|conversion,2,,half _15623 = half(32.0);
984,True,False,half,low,declaration,1,,half3 _18164;
985,False,False,none,low,,0,,_18164.x = _15622.x * _15623;
986,False,False,none,low,,0,,_18164.y = _15622.y * _15623;
987,False,False,none,low,,0,,_18164.z = _15622.z * _15623;
988,False,False,none,low,,0,,_18818 = _8393;
989,False,False,none,low,,0,,_18806 = _18164;
990,False,False,none,low,,0,,}
991,False,True,float,low,arithmetic|declaration|conversion,4,,"float3 _15658 = (((in.IN_WorldPosition.xyz - _14717) * float3(0.0041666668839752674102783203125, 0.0"
992,False,True,float,low,arithmetic|declaration,2,,float3 _15661 = _15658 * _15658;
993,False,True,float,low,arithmetic|declaration,2,,float3 _15664 = _15661 * _15661;
994,True,False,half,low,declaration,1,,half3 _18822;
995,True,False,half,low,declaration,1,,half3 _18823;
996,False,False,none,low,,0,,"if ((max(int(_14451), 2) == 3) && ((_8397 & 32768u) > 0u))"
997,False,False,none,low,,0,,{
998,True,False,half,medium,arithmetic|declaration|conversion,3,,"half3 _14921 = half3(half(1.0 - fast::clamp(fast::max(_15664.x, fast::max(_15664.y, _15664.z)), 0.0,"
999,False,False,none,low,,0,,_18823 = _18818 * _14921;
1000,False,False,none,low,,0,,_18822 = _18806 * _14921;
1001,False,False,none,low,,0,,}
1002,False,False,none,low,,0,,else
1003,False,False,none,low,,0,,{
1004,False,False,none,low,,0,,_18823 = _18818;
1005,False,False,none,low,,0,,_18822 = _18806;
1006,False,False,none,low,,0,,}
1007,False,False,none,low,,0,,_18827 = _18823;
1008,False,False,none,low,,0,,_18825 = _18822;
1009,False,False,none,low,,0,,}
1010,False,False,none,low,,0,,else
1011,False,False,none,low,,0,,{
1012,False,False,none,low,,0,,_18827 = _8393;
1013,False,False,none,low,,0,,_18825 = _8393;
1014,False,False,none,low,,0,,}
1015,False,False,none,low,,0,,_18826 = _18827;
1016,False,False,none,low,,0,,_18824 = _18825;
1017,False,False,none,low,,0,,}
1018,False,False,none,low,,0,,else
1019,False,False,none,low,,0,,{
1020,False,False,none,low,,0,,_18826 = _8393;
1021,False,False,none,low,,0,,_18824 = _8393;
1022,False,False,none,low,,0,,}
1023,True,True,mixed,medium,declaration|conversion,3,考虑统一使用单一精度类型,half3 _14565 = half3(float3(0.0));
1024,True,False,half,low,declaration,1,,half3 _18830;
1025,False,False,none,low,,0,,if (_8590)
1026,False,False,none,low,,0,,{
1027,False,True,float,low,declaration|conversion,2,,float3 _14569 = float3(_18824);
1028,False,True,float,low,declaration|conversion,2,,"float _14575 = float(dot(_18826, _18826));"
1029,True,False,half,low,declaration,1,,half3 _18831;
1030,True,True,mixed,medium,arithmetic|conversion,3,考虑统一使用单一精度类型,if ((float(half(((0.21267099678516387939453125 * _14569.x) + (0.71516001224517822265625 * _14569.y))
1031,False,False,none,low,,0,,{
1032,False,True,float,low,arithmetic|declaration,2,,"float _14592 = fast::clamp(_9109.y, 0.0, 1.0) * 0.75;"
1033,False,True,float,low,arithmetic|declaration|conversion,5,,float3 _14600 = (float3(_18826) / float3(sqrt(_14575))) * float3(fast::clamp(1.0 - (_14592 * _14592)
1034,False,True,float,low,function_call|declaration,2,,"float _14604 = mix(_11560, 1.0, 0.25);"
1035,False,True,float,low,arithmetic|declaration,2,,"float _14612 = fast::clamp(dot(_9109, fast::normalize(_8373 + _14600)), 0.0, 1.0);"
1036,False,True,float,low,arithmetic|declaration,2,,float _15697 = _14604 * _14604;
1037,False,True,float,low,arithmetic|declaration,2,,float _15710 = _15697 / (((((_14612 * _15697) * _15697) - _14612) * _14612) + 1.0);
1038,True,True,mixed,medium,arithmetic|conversion,4,考虑统一使用单一精度类型,"_18831 = half3(float3(_10557 * _18824) * float3((fast::min(1000.0, (_15710 * _15710) * 0.31830987334"
1039,False,False,none,low,,0,,}
1040,False,False,none,low,,0,,else
1041,False,False,none,low,,0,,{
1042,False,False,none,low,,0,,_18831 = _14565;
1043,False,False,none,low,,0,,}
1044,False,False,none,low,,0,,_18830 = _18831;
1045,False,False,none,low,,0,,}
1046,False,False,none,low,,0,,else
1047,False,False,none,low,,0,,{
1048,False,False,none,low,,0,,_18830 = _14565;
1049,False,False,none,low,,0,,}
1050,True,True,mixed,medium,declaration|conversion,3,考虑统一使用单一精度类型,"float _14641 = float(half(mix(_Block1.SHGIParam2.y, 1.0, _14482)));"
1051,True,False,half,medium,arithmetic|conversion,2,,_18859 = _18830 * half3(half(_Block1.SHGIParam.y * _14641));
1052,True,False,half,medium,arithmetic|conversion,2,,_18832 = _18824 * half3(half(_Block1.SHGIParam.x * _14641));
1053,False,False,none,low,,0,,}
1054,False,False,none,low,,0,,else
1055,False,False,none,low,,0,,{
1056,False,False,none,low,,0,,_18859 = _8393;
1057,False,False,none,low,,0,,_18832 = _8393;
1058,False,False,none,low,,0,,}
1059,False,False,none,low,,0,,_19028 = _8560 + (_18832 * _18329);
1060,False,True,float,low,arithmetic|conversion,2,,_18989 = _8565 + float3(_18859 * _18329);
1061,False,False,none,low,,0,,}
1062,False,False,none,low,,0,,else
1063,False,False,none,low,,0,,{
1064,False,False,none,low,,0,,_19028 = _8560;
1065,False,False,none,low,,0,,_18989 = _8565;
1066,False,False,none,low,,0,,}
1067,False,True,float,low,arithmetic|declaration,2,,"float _15792 = fast::clamp(dot(_9109, fast::normalize(_8373 + _8373)), 0.0, 1.0);"
1068,False,True,float,low,arithmetic|declaration,2,,float _15839 = _12049 / (((((_15792 * _12049) * _12049) - _15792) * _15792) + 1.0);
1069,True,True,mixed,medium,declaration|conversion,3,考虑统一使用单一精度类型,"float _15805 = float(half(fast::clamp(_8378, 0.0, 1.0)));"
1070,False,True,float,low,arithmetic|declaration,2,,float _15854 = _12049 * 0.5;
1071,False,True,float,low,arithmetic|declaration,2,,float _15858 = 1.0 - _15854;
1072,False,True,float,low,arithmetic|declaration,2,,float _15861 = (_15805 * _15858) + _15854;
1073,True,False,half,medium,declaration|conversion,2,,half3 _15813 = half3(_12025);
1074,False,True,float,low,declaration|conversion,2,,float3 _15762 = float3(_10569);
1075,False,True,float,low,arithmetic|declaration,2,,float3 _15920 = _Block1.cLocalVirtualLitPos.xyz + _Block1.cVirtualLitParam.xyz;
1076,False,True,float,low,arithmetic|declaration|conversion,5,,"float3 _15960 = ((((fast::normalize(cross(_8373, float3(0.0, 1.0, 0.0))) * _15920.x) + float3(0.0, _"
1077,False,True,float,low,function_call|declaration|conversion,3,,"float3 _15970 = mix(_15960, fast::normalize(_15960), float3(step(0.0, _Block1.cLocalVirtualLitColor."
1078,True,False,half,medium,declaration|conversion,2,,"half _15974 = half(dot(_15970, _15970));"
1079,False,True,float,low,declaration,1,,float3 _15976 = fast::normalize(_15970);
1080,True,False,half,medium,arithmetic|declaration|conversion,3,,half _15979 = _15974 * half(1.0 / (_Block1.cLocalVirtualLitCustom.x * _Block1.cLocalVirtualLitCustom
1081,False,True,float,low,arithmetic|declaration|conversion,3,,"float _15986 = fast::clamp(1.0 - float(_15979 * _15979), 0.0, 1.0);"
1082,True,False,half,medium,arithmetic|declaration|conversion,3,,"half _16031 = half((fast::clamp(dot(_9109, _15976), 0.0, 1.0) * _Block1.cLocalVirtualLitCustom.z) + "
1083,False,True,float,low,arithmetic|declaration,2,,"float _16112 = fast::clamp(dot(_9109, fast::normalize(_8373 + _15976)), 0.0, 1.0);"
1084,False,True,float,low,arithmetic|declaration,2,,float _16159 = _12049 / (((((_16112 * _12049) * _12049) - _16112) * _16112) + 1.0);
1085,False,True,float,low,declaration|conversion,2,,float _8657 = float(_18329);
1086,True,False,half,medium,declaration|conversion,2,,"half _8696 = half(fast::min(_8657, mix(abs(_Block1.cVisibilitySH[0].w), 1.0, _Block1.WorldProbeInfo."
1087,True,False,half,medium,arithmetic|declaration|conversion,3,,half _16335 = half(-1.023326873779296875);
1088,True,False,half,medium,declaration|conversion,2,,half _16336 = half(1.023326873779296875);
1089,True,False,half,low,declaration,1,,half _16342 = _9064.y;
1090,True,False,half,medium,arithmetic|declaration|conversion,3,,half _16351 = half(-0.858085691928863525390625);
1091,True,False,half,medium,declaration|conversion,3,,"half4 _16356 = half4(_16351, half(0.7431240081787109375), _16351, half(0.4290428459644317626953125))"
1092,True,False,half,low,declaration,1,,half _16361 = _9064.z;
1093,True,False,half,low,declaration,1,,half _16369 = _9064.x;
1094,True,False,half,medium,arithmetic|declaration|conversion,3,,"half4 _16385 = _16356 * half4(_16342 * _16361, _16361 * _16361, _16369 * _16361, (_16369 * _16369) -"
1095,True,False,half,medium,arithmetic|declaration|conversion,3,,half _16387 = half(-0.2477079927921295166015625);
1096,False,False,none,low,,0,,_16385.y = _16385.y + _16387;
1097,True,True,mixed,medium,arithmetic|declaration|conversion,4,考虑统一使用单一精度类型,half3 _16279 = half3(_Block1.cSHCoefficients[0].xyz * float3(half3(half(0.886226952075958251953125))
1098,True,True,mixed,medium,arithmetic|declaration|conversion,4,考虑统一使用单一精度类型,"float4 _16284 = float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16342)"
1099,False,True,float,low,declaration|conversion,2,,float4 _16306 = float4(_16385);
1100,True,True,mixed,medium,arithmetic|declaration|conversion,4,考虑统一使用单一精度类型,"half3 _16397 = half3(float3(0.081409998238086700439453125, 0.74361002445220947265625, -0.66364002227"
1101,True,False,half,low,declaration,1,,half _16509 = _16397.y;
1102,True,False,half,low,declaration,1,,half _16528 = _16397.z;
1103,True,False,half,low,declaration,1,,half _16536 = _16397.x;
1104,True,False,half,medium,arithmetic|declaration|conversion,3,,"half4 _16552 = _16356 * half4(_16509 * _16528, _16528 * _16528, _16536 * _16528, (_16536 * _16536) -"
1105,False,False,none,low,,0,,_16552.y = _16552.y + _16387;
1106,True,True,mixed,medium,arithmetic|declaration|conversion,4,考虑统一使用单一精度类型,"float4 _16451 = float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16509)"
1107,False,True,float,low,declaration|conversion,2,,float4 _16473 = float4(_16552);
1108,True,True,mixed,medium,function_call|arithmetic|declaration|conversion,28,考虑统一使用单一精度类型,"half3 _16258 = ((((max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16284)), half(dot(_Bloc"
1109,True,True,mixed,medium,function_call|arithmetic|declaration|conversion,19,考虑统一使用单一精度类型,"half3 _8776 = ((_18255 + (half3((float3(_15813 * (half(fast::min(1000.0, (_15839 * _15839) * 0.31830"
1110,False,True,float,low,declaration,1,,float _16622 = length(_8370);
1111,False,True,float,low,declaration,1,,float _16645 = _8370.y;
1112,True,True,mixed,medium,arithmetic|declaration|conversion,5,考虑统一使用单一精度类型,float _16657 = (_16645 + _12108) + float((_9251 * half(9.9956989288330078125e-05)) * half(int(sign(_
1113,False,True,float,low,arithmetic|declaration|conversion,8,,"float2 _16682 = fast::max(float2(0.0), float2(_Block1.FogInfo.w, exp((-_Block1.AerialPerspectiveMie."
1114,False,True,float,low,declaration|conversion,2,,"float3 _16688 = fast::max(float3(_12108), _Block1.AerialPerspectiveExt.xyz);"
1115,False,True,float,low,declaration|conversion,2,,float3 _16698 = float3(_8303);
1116,False,True,float,low,function_call|arithmetic|declaration,3,,float3 _16715 = exp(-(_16688 * ((_16622 * (_Block1.FogColor.w + ((1.0 - _Block1.FogColor.w) * fast::
1117,False,True,float,low,declaration,1,,float3 _16602 = fast::normalize(_8370);
1118,False,True,float,low,declaration,1,,"float _16756 = fast::clamp(dot(_16602, _Block1.OriginSunDir.xyz), 0.0, 1.0);"
1119,False,True,float,low,declaration,1,,"float _16759 = fast::max(0.0, _16602.y);"
1120,False,True,float,low,arithmetic|declaration,2,,"float _16820 = fast::clamp((_16622 - 80.0) / fast::max(_12108, 520.0), 0.0, 1.0);"
1121,False,True,float,low,arithmetic|declaration,2,,float _16778 = 1.0 - (_16759 * _16759);
1122,False,True,float,low,function_call|arithmetic|declaration|conversion,4,,float3 _16785 = float3(((1.0 - (_Block1.AerialPerspectiveExt.w * _Block1.AerialPerspectiveExt.w)) / 
1123,True,False,half,medium,declaration|conversion,2,,half _16793 = half(_16756);
1124,True,True,mixed,medium,arithmetic|declaration|conversion,4,考虑统一使用单一精度类型,float3 _16805 = ((_Block1.AerialPerspectiveRay.xyz * float(half(1.0) + (_16793 * _16793))) + (_16785
1125,True,True,mixed,medium,function_call|arithmetic|declaration|conversion,11,考虑统一使用单一精度类型,float3 _16862 = (((((((((((_Block1.cVisibilitySH[0].xyz * _8657) * _15762) * _Block1.AmbientColor.w)
1126,False,True,float,low,arithmetic|declaration,2,,float3 _8804 = (_16862 * _9868).xyz;
1127,False,True,float,low,declaration,1,,float3 _19031;
1128,False,False,none,low,,0,,if (_Block1.eIsPlayerOverride < 0.5)
1129,False,False,none,low,,0,,{
1130,False,True,float,low,declaration,1,,float3 _19032;
1131,False,False,none,low,,0,,if ((_Block1.ScreenMotionGray.x * _Block1.ScreenMotionGray.x) > _12108)
1132,False,False,none,low,,0,,{
1133,False,True,float,low,arithmetic|declaration,2,,"float _16911 = fast::clamp((_Block1.CameraPos.w - _Block1.ScreenMotionGray.w) / fast::max(_12108, (_"
1134,False,True,float,low,declaration,1,,float _19029;
1135,False,False,none,low,,0,,if (_Block1.ScreenMotionGray.x > 0.001000000047497451305389404296875)
1136,False,False,none,low,,0,,{
1137,False,False,none,low,,0,,_19029 = 1.0 - _16911;
1138,False,False,none,low,,0,,}
1139,False,False,none,low,,0,,else
1140,False,False,none,low,,0,,{
1141,False,False,none,low,,0,,_19029 = _16911;
1142,False,False,none,low,,0,,}
1143,False,True,float,low,function_call|arithmetic|conversion,5,,"_19032 = mix(_8804, float3(dot(_8804, _16698) * (0.00999999977648258209228515625 * floor(_Block1.Scr"
1144,False,False,none,low,,0,,}
1145,False,False,none,low,,0,,else
1146,False,False,none,low,,0,,{
1147,False,False,none,low,,0,,_19032 = _8804;
1148,False,False,none,low,,0,,}
1149,False,False,none,low,,0,,_19031 = _19032;
1150,False,False,none,low,,0,,}
1151,False,False,none,low,,0,,else
1152,False,False,none,low,,0,,{
1153,False,False,none,low,,0,,_19031 = _8804;
1154,False,False,none,low,,0,,}
1155,False,True,float,low,declaration|conversion,2,,"float4 _8808 = float4(_19031.x, _19031.y, _19031.z, float4(0.0).w);"
1156,False,False,none,low,,0,,_8808.w = _9868;
1157,False,True,float,low,declaration|conversion,2,,"float3 _8816 = fast::min(_8808.xyz, float3(10000.0));"
1158,False,True,float,low,conversion,1,,"out._Ret = float4(_8816.x, _8816.y, _8816.z, _8808.w);"
1159,False,False,none,low,,0,,return out;
1160,False,False,none,low,,0,,}
1161,False,False,none,low,,0,,
