#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试HTML报告生成修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_html_report_generation():
    """测试HTML报告生成"""
    print("🔧 HTML报告生成修复测试")
    print("=" * 50)
    
    try:
        # 测试导入
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        print("✅ 成功导入ShaderAnalysisProcessor")
        
        # 创建处理器
        processor = ShaderAnalysisProcessor()
        print("✅ 成功创建处理器实例")
        
        # 测试着色器代码
        test_shader = """
#include <metal_stdlib>
using namespace metal;

struct VertexOut {
    float4 position [[position]];
    half2 texCoord;
    half3 normal;
};

fragment half4 main0(VertexOut in [[stage_in]], 
                     texture2d<half> baseTexture [[texture(0)]],
                     sampler baseSampler [[sampler(0)]]) {
    
    // 变量声明和类型转换测试
    half4 baseColor = baseTexture.sample(baseSampler, float2(in.texCoord));
    half3 normalizedNormal = normalize(in.normal);
    float lightIntensity = 0.8;
    half3 finalColor = baseColor.rgb * half(lightIntensity);
    
    // 混合精度运算
    float3 worldPos = float3(1.0, 2.0, 3.0);
    half3 lightDir = half3(normalize(worldPos));
    
    half dotProduct = dot(normalizedNormal, lightDir);
    half3 diffuse = finalColor * max(dotProduct, half(0.0));
    
    return half4(diffuse, baseColor.a);
}
"""
        
        print("✅ 准备测试着色器代码")
        
        # 执行分析
        print("🔍 开始分析着色器...")
        result = processor.analyze_shader(test_shader, save_reports=False)
        print("✅ 着色器分析完成")
        
        # 测试HTML报告生成
        print("📄 开始生成HTML报告...")
        html_content = processor.generate_html_report(
            result['analysis'], 
            test_shader, 
            max_lines=50
        )
        print("✅ HTML报告生成成功")
        
        # 检查HTML内容
        if html_content and len(html_content) > 1000:
            print(f"✅ HTML报告内容长度: {len(html_content)} 字符")
            
            # 检查是否包含颜色标记
            if '<span style="color:' in html_content:
                print("✅ HTML报告包含变量类型颜色标记")
            else:
                print("⚠️  HTML报告未包含变量类型颜色标记")
                
            # 检查是否包含变量统计信息
            if '变量:' in html_content:
                print("✅ HTML报告包含变量统计信息")
            else:
                print("⚠️  HTML报告未包含变量统计信息")
                
            print("✅ 所有测试通过！")
            
            # 保存测试报告
            test_html_path = "test_report.html"
            with open(test_html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"✅ 测试报告已保存到: {test_html_path}")
            
        else:
            print("❌ HTML报告内容过短或为空")
            return False
            
    except ImportError as e:
        print(f"❌ 导入错误: {str(e)}")
        return False
    except NameError as e:
        print(f"❌ 名称错误: {str(e)}")
        print("这表明仍有未导入的模块")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def main():
    """主函数"""
    print("修复内容:")
    print("- 在report_generator.py中添加了 import re")
    print("- 修复了_colorize_variables_in_line方法中的NameError")
    print()
    
    success = test_html_report_generation()
    
    if success:
        print("\n🎉 修复验证成功！")
        print("现在可以正常生成带有类型颜色显示的HTML报告了。")
    else:
        print("\n❌ 修复验证失败，需要进一步检查。")

if __name__ == "__main__":
    main()
