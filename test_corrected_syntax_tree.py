#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修正后的语法树结构
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_correct_tree_structure():
    """测试正确的语法树结构"""
    print("🌳 正确语法树结构测试")
    print("=" * 60)
    
    from Process.Analysis.syntax_tree_builder import SyntaxTreeBuilder
    
    builder = SyntaxTreeBuilder()
    
    # 测试用例：a = b + c
    test_cases = [
        ("float a = b + c", "变量声明和赋值"),
        ("result = x * y", "简单赋值"),
        ("half d = half(intensity)", "带类型转换的赋值"),
    ]
    
    print("语法树结构验证:")
    print("-" * 40)
    
    for i, (code, description) in enumerate(test_cases, 1):
        print(f"\n测试{i}: {description}")
        print(f"代码: {code}")
        
        try:
            tree = builder.parse_statement(code, i)
            print(f"语法树结构:")
            print(builder.print_tree(tree))
            
            # 验证树结构
            if tree.node_type.value == 'assignment':
                print("✅ 根节点是赋值运算符 (=)")
                
                if len(tree.children) >= 2:
                    left_child = tree.children[0]
                    right_child = tree.children[1]
                    
                    print(f"✅ 左子节点: {left_child.node_type.value}({left_child.value})")
                    print(f"✅ 右子节点: {right_child.node_type.value}({right_child.value})")
                    
                    # 对于 a = b + c，右子节点应该是运算符
                    if "+" in code and right_child.node_type.value == 'operator':
                        print("✅ 右子节点正确识别为运算符")
                        if len(right_child.children) == 2:
                            print(f"✅ 运算符的操作数: {[child.value for child in right_child.children]}")
                else:
                    print("❌ 赋值节点缺少子节点")
            else:
                print(f"❌ 根节点不是赋值运算符，而是: {tree.node_type.value}")
                
        except Exception as e:
            print(f"❌ 解析错误: {str(e)}")

def test_assignment_type_conversion():
    """测试赋值运算的类型转换"""
    print("\n" + "=" * 60)
    print("🔄 赋值类型转换测试")
    print("=" * 60)
    
    from Process.Analysis.tree_based_type_analyzer import TreeBasedTypeAnalyzer
    
    analyzer = TreeBasedTypeAnalyzer()
    
    # 测试包含隐式类型转换的赋值
    test_shader = """
float a = 1.0;
half b = a;           // float → half 隐式转换
float c = b + 2.0;    // half → float 隐式转换
half d = half(c);     // float → half 显式转换
"""
    
    result = analyzer.analyze_shader_with_precise_types(test_shader)
    
    print("赋值类型转换分析:")
    print("-" * 40)
    
    for i, (code_line, analysis) in enumerate(zip(result['code_lines'], result['precise_analyses'])):
        print(f"\n第{i+1}行: {code_line.content}")
        
        # 显示语法树结构
        syntax_trees = result['syntax_trees']
        if i < len(syntax_trees):
            tree = syntax_trees[i]
            print("语法树:")
            # 获取语法树构建器来打印树结构
            tree_builder = analyzer.syntax_analyzer.tree_builder
            print(tree_builder.print_tree(tree))
        
        # 显示类型转换
        if analysis.type_conversions:
            print("类型转换:")
            for conv in analysis.type_conversions:
                conversion_type = conv.get('conversion_type', 'explicit')
                print(f"  {conv['from_type'].value} → {conv['to_type'].value} ({conversion_type})")
        
        # 显示精度问题
        if analysis.precision_issues:
            print("精度问题:")
            for issue in analysis.precision_issues:
                print(f"  {issue['left_type'].value} {issue['operation']} {issue['right_type'].value}")

def test_node_counting_accuracy():
    """测试节点计数准确性"""
    print("\n" + "=" * 60)
    print("📊 节点计数准确性测试")
    print("=" * 60)
    
    from Process.Analysis.tree_based_type_analyzer import TreeBasedTypeAnalyzer
    
    analyzer = TreeBasedTypeAnalyzer()
    
    # 测试用例：验证 a = b + c 确实有4个节点
    test_cases = [
        {
            'code': 'float a = b + c;',
            'expected_nodes': 5,
            'description': 'float a=b+c应该有5个节点: =, declaration(a), +, b, c'
        },
        {
            'code': 'half result = half(max(x, y));',
            'expected_nodes': 6,
            'description': 'result=half(max(x,y))应该有6个节点: =, result, half(), max(), x, y'
        },
        {
            'code': 'pos = normalize(worldPos) * scale;',
            'expected_nodes': 6,
            'description': 'pos=normalize(worldPos)*scale应该有6个节点: =, pos, *, normalize(), worldPos, scale'
        }
    ]
    
    print("节点计数验证:")
    print("-" * 40)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试{i}: {test_case['description']}")
        print(f"代码: {test_case['code']}")
        
        result = analyzer.analyze_shader_with_precise_types(test_case['code'])
        
        if result['precise_analyses']:
            analysis = result['precise_analyses'][0]
            actual_nodes = analysis.statistics['total_nodes']
            
            print(f"期望节点数: {test_case['expected_nodes']}")
            print(f"实际节点数: {actual_nodes}")
            
            if actual_nodes == test_case['expected_nodes']:
                print("✅ 节点计数正确")
            else:
                print("❌ 节点计数不正确")
            
            # 显示所有节点
            print("所有节点:")
            for j, typed_node in enumerate(analysis.typed_nodes, 1):
                node = typed_node.node
                marker = "🔸" if typed_node.is_intermediate else "🔹"
                print(f"  {j}. {marker} {node.node_type.value}({node.value}) → {typed_node.inferred_type.value}")

def test_tree_visualization():
    """测试语法树可视化"""
    print("\n" + "=" * 60)
    print("🎨 语法树可视化测试")
    print("=" * 60)
    
    from Process.Analysis.syntax_tree_builder import SyntaxTreeBuilder
    
    builder = SyntaxTreeBuilder()
    
    # 复杂表达式测试
    complex_expressions = [
        "float a = b + c",
        "half result = half(max(x, y) * intensity)",
        "pos = normalize(worldPos) * transform"
    ]
    
    print("复杂表达式语法树:")
    print("-" * 40)
    
    for i, expr in enumerate(complex_expressions, 1):
        print(f"\n表达式{i}: {expr}")
        print("=" * 50)
        
        try:
            tree = builder.parse_statement(expr, i)
            print("语法树结构:")
            tree_str = builder.print_tree(tree)
            print(tree_str)
            
            # 分析树的深度和广度
            def analyze_tree_structure(node, depth=0):
                max_depth = depth
                node_count = 1
                
                for child in node.children:
                    child_depth, child_count = analyze_tree_structure(child, depth + 1)
                    max_depth = max(max_depth, child_depth)
                    node_count += child_count
                
                return max_depth, node_count
            
            max_depth, total_nodes = analyze_tree_structure(tree)
            print(f"树深度: {max_depth}")
            print(f"总节点数: {total_nodes}")
            print(f"子节点数: {len(tree.children)}")
            
        except Exception as e:
            print(f"❌ 解析失败: {str(e)}")

def test_assignment_semantics():
    """测试赋值语义"""
    print("\n" + "=" * 60)
    print("🎯 赋值语义测试")
    print("=" * 60)
    
    from Process.Analysis.tree_based_type_analyzer import TreeBasedTypeAnalyzer
    
    analyzer = TreeBasedTypeAnalyzer()
    
    # 测试不同类型的赋值语义
    test_shader = """
float a = 1.0;        // 声明并初始化
a = 2.0;              // 重新赋值
half b = a;           // 隐式类型转换赋值
float c = b + a;      // 表达式赋值
"""
    
    result = analyzer.analyze_shader_with_precise_types(test_shader)
    
    print("赋值语义分析:")
    print("-" * 40)
    
    for i, (code_line, analysis) in enumerate(zip(result['code_lines'], result['precise_analyses'])):
        print(f"\n第{i+1}行: {code_line.content}")
        
        # 找到赋值节点
        assignment_nodes = [tn for tn in analysis.typed_nodes if tn.node.node_type.value == 'assignment']
        
        if assignment_nodes:
            assign_node = assignment_nodes[0]
            print(f"赋值操作: {assign_node.node.value}")
            print(f"结果类型: {assign_node.inferred_type.value}")
            
            # 分析赋值的左右操作数
            if len(assign_node.node.children) >= 2:
                left_child = assign_node.node.children[0]
                right_child = assign_node.node.children[1]
                
                print(f"左操作数: {left_child.node_type.value}({left_child.value})")
                print(f"右操作数: {right_child.node_type.value}({right_child.value})")
                
                # 检查是否需要类型转换
                left_type = None
                right_type = None
                
                for tn in analysis.typed_nodes:
                    if tn.node == left_child:
                        left_type = tn.inferred_type
                    elif tn.node == right_child:
                        right_type = tn.inferred_type
                
                if left_type and right_type:
                    if left_type != right_type:
                        print(f"⚠️  类型转换: {right_type.value} → {left_type.value}")
                    else:
                        print("✅ 类型匹配，无需转换")

def main():
    """主函数"""
    print("🚀 修正后的语法树结构测试")
    print("=" * 70)
    print("修正内容:")
    print("• 🌳 赋值运算符(=)作为根节点")
    print("• 🔄 正确处理赋值的隐式类型转换")
    print("• 📊 准确统计所有语法节点")
    print("• 🎯 区分显式和隐式类型转换")
    print("• 🎨 改进语法树可视化")
    print()
    
    try:
        test_correct_tree_structure()
        test_assignment_type_conversion()
        test_node_counting_accuracy()
        test_tree_visualization()
        test_assignment_semantics()
        
        print("\n" + "=" * 70)
        print("🎉 所有测试完成！")
        print("语法树结构已修正，现在能够:")
        print("✅ 正确构建以运算符为根节点的语法树")
        print("✅ 准确识别赋值运算的隐式类型转换")
        print("✅ 精确统计所有语法节点（包括中间结果）")
        print("✅ 提供详细的类型转换分析")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
