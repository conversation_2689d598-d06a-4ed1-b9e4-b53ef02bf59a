#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于语法树的精确类型分析器
"""

from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass
from enum import Enum

from .syntax_tree_builder import ASTNode, NodeType, DataType, SyntaxTreeAnalyzer

@dataclass
class TypedNode:
    """带类型信息的节点"""
    node: ASTNode
    inferred_type: DataType
    is_intermediate: bool = False  # 是否是中间结果
    type_confidence: float = 1.0   # 类型推断置信度
    
class TypeAnalysisResult:
    """类型分析结果"""
    
    def __init__(self):
        self.typed_nodes: List[TypedNode] = []
        self.variable_types: Dict[str, DataType] = {}
        self.intermediate_results: List[TypedNode] = []
        self.type_conversions: List[Dict] = []
        self.precision_issues: List[Dict] = []
        self.statistics: Dict = {}

class ShaderTypeAnalyzer:
    """基于语法树的类型分析器"""
    
    def __init__(self):
        self.syntax_analyzer = SyntaxTreeAnalyzer()
        
        # 运算符类型推断规则
        self.operator_type_rules = {
            '+': self._infer_arithmetic_type,
            '-': self._infer_arithmetic_type,
            '*': self._infer_arithmetic_type,
            '/': self._infer_arithmetic_type,
            '==': lambda l, r: DataType.BOOL,
            '!=': lambda l, r: DataType.BOOL,
            '<': lambda l, r: DataType.BOOL,
            '>': lambda l, r: DataType.BOOL,
            '<=': lambda l, r: DataType.BOOL,
            '>=': lambda l, r: DataType.BOOL,
            '&&': lambda l, r: DataType.BOOL,
            '||': lambda l, r: DataType.BOOL,
        }
        
        # 函数返回类型推断规则
        self.function_return_rules = {
            'normalize': self._infer_normalize_return,
            'dot': lambda args: DataType.FLOAT,
            'cross': self._infer_cross_return,
            'max': self._infer_max_min_return,
            'min': self._infer_max_min_return,
            'clamp': self._infer_clamp_return,
            'mix': self._infer_mix_return,
            'sqrt': lambda args: args[0] if args else DataType.UNKNOWN,
            'pow': lambda args: args[0] if args else DataType.UNKNOWN,
            'sin': lambda args: args[0] if args else DataType.UNKNOWN,
            'cos': lambda args: args[0] if args else DataType.UNKNOWN,
        }
    
    def analyze_tree_types(self, tree: ASTNode, type_table: Dict[str, DataType] = None) -> TypeAnalysisResult:
        """分析语法树中所有节点的类型"""
        if type_table is None:
            type_table = {}
        
        result = TypeAnalysisResult()
        
        # 递归分析所有节点
        self._analyze_node_recursive(tree, type_table, result)
        
        # 生成统计信息
        result.statistics = self._generate_statistics(result)
        
        return result
    
    def _analyze_node_recursive(self, node: ASTNode, type_table: Dict[str, DataType], result: TypeAnalysisResult):
        """递归分析节点类型"""
        # 先分析子节点
        child_types = []
        for child in node.children:
            self._analyze_node_recursive(child, type_table, result)
            child_types.append(self._get_node_inferred_type(child, type_table, result))
        
        # 推断当前节点类型
        inferred_type = self._infer_node_type(node, child_types, type_table)
        
        # 创建类型化节点
        typed_node = TypedNode(
            node=node,
            inferred_type=inferred_type,
            is_intermediate=self._is_intermediate_result(node),
            type_confidence=self._calculate_confidence(node, inferred_type)
        )
        
        result.typed_nodes.append(typed_node)
        
        # 如果是中间结果，添加到中间结果列表
        if typed_node.is_intermediate:
            result.intermediate_results.append(typed_node)
        
        # 如果是变量声明，更新类型表
        if node.node_type == NodeType.DECLARATION:
            var_name = self._extract_variable_name(node.value)
            if var_name:
                type_table[var_name] = inferred_type
                result.variable_types[var_name] = inferred_type

        # 如果是赋值运算，更新左侧变量的类型
        elif node.node_type == NodeType.ASSIGNMENT and len(node.children) >= 2:
            left_child = node.children[0]
            if left_child.node_type == NodeType.VARIABLE:
                var_name = left_child.value
                type_table[var_name] = inferred_type
                result.variable_types[var_name] = inferred_type
        
        # 检查类型转换
        if node.node_type == NodeType.TYPE_CAST and child_types:
            source_type = child_types[0]
            target_type = inferred_type
            if source_type != target_type and source_type != DataType.UNKNOWN:
                result.type_conversions.append({
                    'node': node,
                    'from_type': source_type,
                    'to_type': target_type,
                    'line': node.line_number
                })

        # 检查赋值运算的隐式类型转换
        if node.node_type == NodeType.ASSIGNMENT and len(child_types) >= 2:
            left_type = child_types[0]   # 左侧变量类型
            right_type = child_types[1]  # 右侧表达式类型

            # 如果左右类型不同，说明发生了隐式类型转换
            if (left_type != right_type and
                left_type != DataType.UNKNOWN and
                right_type != DataType.UNKNOWN):
                result.type_conversions.append({
                    'node': node,
                    'from_type': right_type,
                    'to_type': left_type,
                    'line': node.line_number,
                    'conversion_type': 'implicit_assignment'  # 标记为隐式赋值转换
                })
        
        # 检查混合精度问题
        if node.node_type == NodeType.OPERATOR and len(child_types) >= 2:
            if self._is_mixed_precision_operation(child_types[0], child_types[1]):
                result.precision_issues.append({
                    'node': node,
                    'left_type': child_types[0],
                    'right_type': child_types[1],
                    'operation': node.value,
                    'line': node.line_number
                })
    
    def _infer_node_type(self, node: ASTNode, child_types: List[DataType], type_table: Dict[str, DataType]) -> DataType:
        """推断节点类型"""
        if node.node_type == NodeType.VARIABLE:
            return type_table.get(node.value, DataType.UNKNOWN)
        
        elif node.node_type == NodeType.LITERAL:
            return self._infer_literal_type(node.value)
        
        elif node.node_type == NodeType.OPERATOR:
            if node.value in self.operator_type_rules and len(child_types) >= 2:
                result_type = self.operator_type_rules[node.value](child_types[0], child_types[1])
                return result_type
            return DataType.UNKNOWN
        
        elif node.node_type == NodeType.FUNCTION:
            if node.value in self.function_return_rules:
                return self.function_return_rules[node.value](child_types)
            return DataType.UNKNOWN
        
        elif node.node_type == NodeType.TYPE_CAST:
            return node.data_type
        
        elif node.node_type == NodeType.DECLARATION:
            return node.data_type
        
        elif node.node_type == NodeType.ASSIGNMENT:
            var_name = self._extract_variable_name(node.value)
            return type_table.get(var_name, DataType.UNKNOWN)
        
        elif node.node_type == NodeType.MEMBER_ACCESS:
            return self._infer_member_access_type(node.value, type_table)
        
        return DataType.UNKNOWN
    
    def _infer_arithmetic_type(self, left_type: DataType, right_type: DataType) -> DataType:
        """推断算术运算结果类型"""
        # 如果有未知类型，返回另一个类型
        if left_type == DataType.UNKNOWN:
            return right_type
        if right_type == DataType.UNKNOWN:
            return left_type
        
        # 类型提升规则：float > half > int
        type_priority = {
            DataType.FLOAT: 3, DataType.FLOAT2: 3, DataType.FLOAT3: 3, DataType.FLOAT4: 3,
            DataType.HALF: 2, DataType.HALF2: 2, DataType.HALF3: 2, DataType.HALF4: 2,
            DataType.INT: 1, DataType.UINT: 1,
            DataType.BOOL: 0
        }
        
        left_priority = type_priority.get(left_type, 0)
        right_priority = type_priority.get(right_type, 0)
        
        # 返回优先级更高的类型
        if left_priority >= right_priority:
            return left_type
        else:
            return right_type
    
    def _infer_normalize_return(self, arg_types: List[DataType]) -> DataType:
        """推断normalize函数返回类型"""
        if not arg_types:
            return DataType.UNKNOWN
        return arg_types[0]  # normalize返回与输入相同的向量类型
    
    def _infer_cross_return(self, arg_types: List[DataType]) -> DataType:
        """推断cross函数返回类型"""
        if len(arg_types) >= 2:
            # cross总是返回3D向量
            if arg_types[0].value.startswith('float'):
                return DataType.FLOAT3
            elif arg_types[0].value.startswith('half'):
                return DataType.HALF3
        return DataType.FLOAT3
    
    def _infer_max_min_return(self, arg_types: List[DataType]) -> DataType:
        """推断max/min函数返回类型"""
        if len(arg_types) >= 2:
            return self._infer_arithmetic_type(arg_types[0], arg_types[1])
        return DataType.UNKNOWN
    
    def _infer_clamp_return(self, arg_types: List[DataType]) -> DataType:
        """推断clamp函数返回类型"""
        if arg_types:
            return arg_types[0]  # clamp返回第一个参数的类型
        return DataType.UNKNOWN
    
    def _infer_mix_return(self, arg_types: List[DataType]) -> DataType:
        """推断mix函数返回类型"""
        if len(arg_types) >= 2:
            return self._infer_arithmetic_type(arg_types[0], arg_types[1])
        return DataType.UNKNOWN
    
    def _infer_literal_type(self, value: str) -> DataType:
        """推断字面量类型"""
        import re
        if re.match(r'^\d+\.\d*f?$', value):
            return DataType.FLOAT
        elif re.match(r'^\d+$', value):
            return DataType.INT
        elif value.lower() in ['true', 'false']:
            return DataType.BOOL
        return DataType.UNKNOWN
    
    def _infer_member_access_type(self, member_expr: str, type_table: Dict[str, DataType]) -> DataType:
        """推断成员访问类型"""
        parts = member_expr.split('.')
        if len(parts) != 2:
            return DataType.UNKNOWN
        
        obj_name, member = parts
        obj_type = type_table.get(obj_name, DataType.UNKNOWN)
        
        # 根据成员名推断类型
        if member in ['x', 'y', 'z', 'w', 'r', 'g', 'b', 'a']:
            if obj_type.value.startswith('float'):
                return DataType.FLOAT
            elif obj_type.value.startswith('half'):
                return DataType.HALF
        elif member in ['xy', 'rg']:
            if obj_type.value.startswith('float'):
                return DataType.FLOAT2
            elif obj_type.value.startswith('half'):
                return DataType.HALF2
        elif member in ['xyz', 'rgb']:
            if obj_type.value.startswith('float'):
                return DataType.FLOAT3
            elif obj_type.value.startswith('half'):
                return DataType.HALF3
        
        return DataType.UNKNOWN
    
    def _is_intermediate_result(self, node: ASTNode) -> bool:
        """判断是否是中间结果"""
        return node.node_type in [NodeType.OPERATOR, NodeType.FUNCTION, NodeType.TYPE_CAST]
    
    def _calculate_confidence(self, node: ASTNode, inferred_type: DataType) -> float:
        """计算类型推断置信度"""
        if inferred_type == DataType.UNKNOWN:
            return 0.0
        
        if node.node_type in [NodeType.DECLARATION, NodeType.TYPE_CAST]:
            return 1.0  # 显式类型声明，置信度最高
        
        if node.node_type == NodeType.LITERAL:
            return 0.9  # 字面量类型推断，置信度很高
        
        if node.node_type == NodeType.VARIABLE:
            return 0.8  # 变量类型查询，置信度较高
        
        return 0.6  # 其他情况，置信度中等
    
    def _extract_variable_name(self, declaration_or_assignment: str) -> Optional[str]:
        """从声明或赋值语句中提取变量名"""
        import re
        
        # 匹配声明: "type varname" 或 赋值: "varname ="
        decl_match = re.match(r'^(?:\w+\s+)?([a-zA-Z_][a-zA-Z0-9_]*)', declaration_or_assignment)
        if decl_match:
            return decl_match.group(1)
        
        return None
    
    def _is_mixed_precision_operation(self, type1: DataType, type2: DataType) -> bool:
        """检查是否是混合精度运算"""
        float_types = {DataType.FLOAT, DataType.FLOAT2, DataType.FLOAT3, DataType.FLOAT4}
        half_types = {DataType.HALF, DataType.HALF2, DataType.HALF3, DataType.HALF4}
        
        return (type1 in float_types and type2 in half_types) or \
               (type1 in half_types and type2 in float_types)
    
    def _get_node_inferred_type(self, node: ASTNode, type_table: Dict[str, DataType], result: TypeAnalysisResult) -> DataType:
        """获取节点的推断类型"""
        # 在已分析的节点中查找
        for typed_node in result.typed_nodes:
            if typed_node.node == node:
                return typed_node.inferred_type
        
        # 如果没找到，进行简单推断
        return self._infer_node_type(node, [], type_table)
    
    def _generate_statistics(self, result: TypeAnalysisResult) -> Dict:
        """生成统计信息"""
        stats = {
            'total_nodes': len(result.typed_nodes),
            'intermediate_results': len(result.intermediate_results),
            'variable_declarations': len(result.variable_types),
            'type_conversions': len(result.type_conversions),
            'precision_issues': len(result.precision_issues),
            'type_distribution': {},
            'confidence_distribution': {}
        }
        
        # 统计类型分布
        for typed_node in result.typed_nodes:
            type_name = typed_node.inferred_type.value
            stats['type_distribution'][type_name] = stats['type_distribution'].get(type_name, 0) + 1
        
        # 统计置信度分布
        high_confidence = sum(1 for tn in result.typed_nodes if tn.type_confidence >= 0.8)
        medium_confidence = sum(1 for tn in result.typed_nodes if 0.5 <= tn.type_confidence < 0.8)
        low_confidence = sum(1 for tn in result.typed_nodes if tn.type_confidence < 0.5)
        
        stats['confidence_distribution'] = {
            'high': high_confidence,
            'medium': medium_confidence,
            'low': low_confidence
        }
        
        return stats

    def analyze_shader_with_precise_types(self, shader_content: str) -> Dict:
        """分析着色器代码并进行精确类型分析"""
        # 首先使用语法树分析器
        syntax_result = self.syntax_analyzer.analyze_shader_with_syntax_trees(shader_content)

        # 对每个语法树进行精确类型分析
        precise_analyses = []
        global_type_table = {}

        for tree in syntax_result['syntax_trees']:
            analysis = self.analyze_tree_types(tree, global_type_table.copy())
            precise_analyses.append(analysis)

            # 更新全局类型表
            global_type_table.update(analysis.variable_types)

        # 整合结果
        result = {
            'code_lines': syntax_result['code_lines'],
            'syntax_trees': syntax_result['syntax_trees'],
            'precise_analyses': precise_analyses,
            'global_type_table': global_type_table,
            'overall_statistics': self._generate_overall_statistics(precise_analyses)
        }

        return result

    def _generate_overall_statistics(self, analyses: List[TypeAnalysisResult]) -> Dict:
        """生成整体统计信息"""
        overall_stats = {
            'total_lines': len(analyses),
            'total_nodes': 0,
            'total_variables': 0,
            'total_intermediate_results': 0,
            'total_type_conversions': 0,
            'total_precision_issues': 0,
            'type_distribution': {},
            'precision_accuracy_score': 0.0
        }

        for analysis in analyses:
            stats = analysis.statistics
            overall_stats['total_nodes'] += stats['total_nodes']
            overall_stats['total_variables'] += stats['variable_declarations']
            overall_stats['total_intermediate_results'] += stats['intermediate_results']
            overall_stats['total_type_conversions'] += stats['type_conversions']
            overall_stats['total_precision_issues'] += stats['precision_issues']

            # 合并类型分布
            for type_name, count in stats['type_distribution'].items():
                overall_stats['type_distribution'][type_name] = \
                    overall_stats['type_distribution'].get(type_name, 0) + count

        # 计算精度准确性评分
        if overall_stats['total_nodes'] > 0:
            high_confidence_nodes = sum(
                analysis.statistics['confidence_distribution']['high']
                for analysis in analyses
            )
            overall_stats['precision_accuracy_score'] = \
                high_confidence_nodes / overall_stats['total_nodes'] * 100

        return overall_stats

    def format_precise_analysis(self, result: Dict) -> str:
        """格式化精确类型分析结果"""
        output = []
        output.append("🎯 基于语法树的精确类型分析")
        output.append("=" * 60)

        code_lines = result['code_lines']
        precise_analyses = result['precise_analyses']
        overall_stats = result['overall_statistics']

        # 逐行显示精确分析结果
        for i, (code_line, analysis) in enumerate(zip(code_lines, precise_analyses)):
            output.append(f"\n第{i+1}行: {code_line.content}")
            output.append("-" * 40)

            # 显示所有类型化节点
            output.append("类型化节点:")
            for j, typed_node in enumerate(analysis.typed_nodes):
                node = typed_node.node
                node_desc = f"{node.node_type.value}({node.value})"
                type_desc = f"{typed_node.inferred_type.value}"
                confidence = f"{typed_node.type_confidence:.1f}"

                marker = "🔸" if typed_node.is_intermediate else "🔹"
                output.append(f"  {marker} {node_desc} → {type_desc} (置信度: {confidence})")

            # 显示统计信息
            stats = analysis.statistics
            output.append(f"统计: 节点{stats['total_nodes']} | 中间结果{stats['intermediate_results']} | 转换{stats['type_conversions']} | 精度问题{stats['precision_issues']}")

            # 显示类型转换详情
            if analysis.type_conversions:
                output.append("类型转换:")
                for conv in analysis.type_conversions:
                    output.append(f"  🔄 {conv['from_type'].value} → {conv['to_type'].value}")

            # 显示精度问题
            if analysis.precision_issues:
                output.append("精度问题:")
                for issue in analysis.precision_issues:
                    output.append(f"  ⚠️  {issue['left_type'].value} {issue['operation']} {issue['right_type'].value}")

        # 显示整体统计
        output.append(f"\n📊 整体统计信息")
        output.append("=" * 40)
        output.append(f"总代码行: {overall_stats['total_lines']}")
        output.append(f"总节点数: {overall_stats['total_nodes']}")
        output.append(f"变量声明: {overall_stats['total_variables']}")
        output.append(f"中间结果: {overall_stats['total_intermediate_results']}")
        output.append(f"类型转换: {overall_stats['total_type_conversions']}")
        output.append(f"精度问题: {overall_stats['total_precision_issues']}")
        output.append(f"精度准确性: {overall_stats['precision_accuracy_score']:.1f}%")

        # 显示类型分布
        if overall_stats['type_distribution']:
            output.append(f"\n🎨 类型分布:")
            for type_name, count in sorted(overall_stats['type_distribution'].items()):
                percentage = count / overall_stats['total_nodes'] * 100
                output.append(f"  {type_name}: {count} ({percentage:.1f}%)")

        # 显示全局类型表
        if result['global_type_table']:
            output.append(f"\n📋 全局变量类型表:")
            for var_name, var_type in sorted(result['global_type_table'].items()):
                output.append(f"  {var_name}: {var_type.value}")

        return "\n".join(output)

    def get_detailed_node_analysis(self, result: Dict, line_index: int) -> Dict:
        """获取指定行的详细节点分析"""
        if line_index >= len(result['precise_analyses']):
            return {}

        analysis = result['precise_analyses'][line_index]
        code_line = result['code_lines'][line_index]

        detailed_analysis = {
            'line_info': {
                'number': code_line.line_number,
                'content': code_line.content,
                'original_stats': {
                    'variables': code_line.variables,
                    'float_vars': code_line.float_vars,
                    'half_vars': code_line.half_vars,
                    'conversions': code_line.conversions
                }
            },
            'precise_stats': {
                'total_nodes': analysis.statistics['total_nodes'],
                'intermediate_results': analysis.statistics['intermediate_results'],
                'type_conversions': analysis.statistics['type_conversions'],
                'precision_issues': analysis.statistics['precision_issues']
            },
            'typed_nodes': [],
            'intermediate_results': [],
            'type_conversions': analysis.type_conversions,
            'precision_issues': analysis.precision_issues
        }

        # 详细节点信息
        for typed_node in analysis.typed_nodes:
            node_info = {
                'type': typed_node.node.node_type.value,
                'value': typed_node.node.value,
                'inferred_type': typed_node.inferred_type.value,
                'is_intermediate': typed_node.is_intermediate,
                'confidence': typed_node.type_confidence
            }

            detailed_analysis['typed_nodes'].append(node_info)

            if typed_node.is_intermediate:
                detailed_analysis['intermediate_results'].append(node_info)

        return detailed_analysis
