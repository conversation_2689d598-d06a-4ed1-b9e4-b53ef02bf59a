#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复precise_type_analysis_widget的API
"""

import os

def fix_precise_widget():
    """修复precise_type_analysis_widget的API"""
    file_path = 'ui/precise_type_analysis_widget.py'
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    try:
        # 读取文件内容
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read()
        
        # 替换方法名
        old_method = 'def start_precise_analysis(self, shader_content: str):'
        new_method = 'def start_analysis(self, shader_content: str):'
        
        if old_method in content:
            content = content.replace(old_method, new_method)
            print(f"✅ 替换方法名: start_precise_analysis -> start_analysis")
        else:
            print(f"❌ 未找到方法: {old_method}")
            return False
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 文件修复完成: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 修复precise_type_analysis_widget的API")
    print("=" * 50)
    
    success = fix_precise_widget()
    
    if success:
        print("\n🎉 修复完成！")
        print("现在precise_type_analysis_widget使用start_analysis方法")
    else:
        print("\n❌ 修复失败")
