#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确类型分析功能使用示例
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def example_basic_usage():
    """基本使用示例"""
    print("📖 基本使用示例")
    print("=" * 50)
    
    from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
    
    # 示例着色器代码
    shader_code = """
float3 worldPos = transform * localPos;
half4 baseColor = texture.sample(sampler, uv);
float intensity = dot(normal, lightDir);
half3 result = baseColor.rgb * half(intensity);
"""
    
    # 创建处理器
    processor = ShaderAnalysisProcessor()
    
    # 使用精确类型分析
    result = processor.analyze_shader_with_precise_types(
        shader_code, 
        save_reports=True, 
        base_filename="example_precise"
    )
    
    print("分析完成！")
    print(f"分析方法: {result['analysis_method']}")
    
    if 'precise_analysis' in result['analysis']:
        stats = result['analysis']['precise_analysis']['overall_statistics']
        print(f"总节点数: {stats['total_nodes']}")
        print(f"中间结果: {stats['total_intermediate_results']}")
        print(f"准确性评分: {stats['precision_accuracy_score']:.1f}%")
    
    # 显示生成的报告文件
    if 'files' in result:
        print(f"\n生成的报告文件:")
        for report_type, file_path in result['files'].items():
            print(f"  {report_type}: {file_path}")
    
    return result

def example_comparison_analysis():
    """对比分析示例"""
    print("\n" + "=" * 50)
    print("📊 对比分析示例")
    print("=" * 50)
    
    from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
    
    # 复杂着色器示例
    complex_shader = """
float a = b + c;
float d = (a * 2.0) + (b / c);
half result = half(max(a, d) * intensity);
float3 pos = normalize(worldPos) * scale;
half4 color = texture.sample(sampler, uv);
"""
    
    processor = ShaderAnalysisProcessor()
    
    print("1. 标准分析:")
    print("-" * 30)
    standard_result = processor.analyze_shader(complex_shader, save_reports=False)
    standard_stats = standard_result['analysis']['statistics']
    print(f"总操作数: {standard_stats['total_operations']}")
    print(f"操作类型数: {len(standard_stats['operation_types'])}")
    
    print("\n2. 精确分析:")
    print("-" * 30)
    precise_result = processor.analyze_shader_with_precise_types(complex_shader, save_reports=False)
    
    if 'precise_analysis' in precise_result['analysis']:
        precise_stats = precise_result['analysis']['precise_analysis']['overall_statistics']
        print(f"总节点数: {precise_stats['total_nodes']}")
        print(f"中间结果: {precise_stats['total_intermediate_results']}")
        print(f"变量声明: {precise_stats['total_variables']}")
        
        # 显示对比结果
        if 'comparison' in precise_result['analysis']:
            comparison = precise_result['analysis']['comparison']
            improvement = comparison['operation_count_improvement']
            print(f"\n📈 精度提升:")
            print(f"  节点数增加: +{improvement['improvement']} ({improvement['improvement_percentage']:.1f}%)")
            print(f"  分析质量: {comparison['analysis_quality']['confidence_level']}")

def example_detailed_node_inspection():
    """详细节点检查示例"""
    print("\n" + "=" * 50)
    print("🔍 详细节点检查示例")
    print("=" * 50)
    
    from Process.Analysis.shader_analyzer_core import ShaderAnalyzerCore
    
    # 包含各种节点类型的测试用例
    test_shader = """
float a = b + c;
half d = half(max(a, intensity));
"""
    
    analyzer = ShaderAnalyzerCore()
    result = analyzer.analyze_shader_with_precise_types(test_shader)
    
    if 'precise_analysis' in result:
        precise_data = result['precise_analysis']
        
        print("逐行节点详细检查:")
        for i, (code_line, analysis) in enumerate(zip(precise_data['code_lines'], precise_data['precise_analyses'])):
            print(f"\n第{i+1}行: {code_line.content}")
            print("=" * 40)
            
            print("所有节点:")
            for j, typed_node in enumerate(analysis.typed_nodes, 1):
                node = typed_node.node
                print(f"  {j}. {node.node_type.value}({node.value})")
                print(f"     类型: {typed_node.inferred_type.value}")
                print(f"     中间结果: {'是' if typed_node.is_intermediate else '否'}")
                print(f"     置信度: {typed_node.type_confidence:.1f}")
            
            # 显示问题
            if analysis.type_conversions:
                print("  类型转换:")
                for conv in analysis.type_conversions:
                    print(f"    {conv['from_type'].value} → {conv['to_type'].value}")
            
            if analysis.precision_issues:
                print("  精度问题:")
                for issue in analysis.precision_issues:
                    print(f"    {issue['left_type'].value} {issue['operation']} {issue['right_type'].value}")

def example_performance_optimization():
    """性能优化建议示例"""
    print("\n" + "=" * 50)
    print("🎯 性能优化建议示例")
    print("=" * 50)
    
    from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
    
    # 包含性能问题的着色器
    problematic_shader = """
float a = 1.0;
half b = 0.5;
float result1 = a + b;        // 混合精度问题
half result2 = half(a * 2.0); // 类型转换
float3 pos = float3(1.0, 2.0, 3.0);
half3 color = half3(0.5, 0.5, 0.5);
float3 mixed = pos + color;   // 混合精度问题
"""
    
    processor = ShaderAnalysisProcessor()
    result = processor.analyze_shader_with_precise_types(problematic_shader, save_reports=False)
    
    print("性能问题分析:")
    print("-" * 30)
    
    if 'precise_analysis' in result['analysis']:
        precise_stats = result['analysis']['precise_analysis']['overall_statistics']
        
        print(f"类型转换: {precise_stats['total_type_conversions']} 个")
        print(f"精度问题: {precise_stats['total_precision_issues']} 个")
        print(f"准确性评分: {precise_stats['precision_accuracy_score']:.1f}%")
        
        # 生成优化建议
        suggestions = []
        
        if precise_stats['total_type_conversions'] > 0:
            suggestions.append("减少不必要的类型转换")
        
        if precise_stats['total_precision_issues'] > 0:
            suggestions.append("统一变量精度类型，避免混合精度运算")
        
        if precise_stats['precision_accuracy_score'] < 70:
            suggestions.append("提高类型声明的明确性")
        
        print(f"\n💡 优化建议:")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"  {i}. {suggestion}")
        
        # 计算性能评分
        base_score = 100
        score_deduction = precise_stats['total_type_conversions'] * 5 + precise_stats['total_precision_issues'] * 10
        performance_score = max(0, base_score - score_deduction)
        
        print(f"\n📊 性能评分: {performance_score}/100")
        if performance_score >= 90:
            print("  等级: 优秀 ✅")
        elif performance_score >= 70:
            print("  等级: 良好 🟡")
        else:
            print("  等级: 需要优化 ⚠️")

def example_integration_with_ui():
    """与UI集成示例"""
    print("\n" + "=" * 50)
    print("🖥️ UI集成示例")
    print("=" * 50)
    
    from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
    
    def generate_ui_friendly_data(shader_content):
        """生成UI友好的数据格式"""
        processor = ShaderAnalysisProcessor()
        result = processor.analyze_shader_with_precise_types(shader_content, save_reports=False)
        
        ui_data = {
            'analysis_summary': {
                'method': result.get('analysis_method', 'unknown'),
                'success': 'precise_analysis' in result['analysis']
            },
            'code_lines': [],
            'statistics': {},
            'recommendations': []
        }
        
        if 'precise_analysis' in result['analysis']:
            precise_data = result['analysis']['precise_analysis']
            ui_data['statistics'] = precise_data['overall_statistics']
            
            # 处理代码行数据
            for code_line, analysis in zip(precise_data['code_lines'], precise_data['precise_analyses']):
                line_data = {
                    'line_number': code_line.line_number,
                    'content': code_line.content,
                    'node_count': analysis.statistics['total_nodes'],
                    'intermediate_count': analysis.statistics['intermediate_results'],
                    'has_issues': len(analysis.type_conversions) > 0 or len(analysis.precision_issues) > 0,
                    'nodes': []
                }
                
                # 处理节点数据
                for typed_node in analysis.typed_nodes:
                    node_data = {
                        'type': typed_node.node.node_type.value,
                        'value': typed_node.node.value,
                        'inferred_type': typed_node.inferred_type.value,
                        'is_intermediate': typed_node.is_intermediate,
                        'confidence': typed_node.type_confidence
                    }
                    line_data['nodes'].append(node_data)
                
                ui_data['code_lines'].append(line_data)
            
            # 生成建议
            stats = ui_data['statistics']
            if stats['total_type_conversions'] > 0:
                ui_data['recommendations'].append({
                    'type': 'warning',
                    'message': f"发现 {stats['total_type_conversions']} 个类型转换，建议优化"
                })
            
            if stats['total_precision_issues'] > 0:
                ui_data['recommendations'].append({
                    'type': 'error',
                    'message': f"发现 {stats['total_precision_issues']} 个混合精度问题，需要修复"
                })
        
        return ui_data
    
    # 测试UI数据生成
    test_shader = """
float3 pos = worldMatrix * localPos;
half4 color = texture.sample(sampler, uv);
float intensity = dot(normal, lightDir);
half3 result = color.rgb * half(intensity);
"""
    
    ui_data = generate_ui_friendly_data(test_shader)
    
    print("UI数据格式示例:")
    print(f"分析方法: {ui_data['analysis_summary']['method']}")
    print(f"分析成功: {ui_data['analysis_summary']['success']}")
    print(f"代码行数: {len(ui_data['code_lines'])}")
    print(f"建议数量: {len(ui_data['recommendations'])}")
    
    if ui_data['statistics']:
        stats = ui_data['statistics']
        print(f"总节点数: {stats['total_nodes']}")
        print(f"中间结果: {stats['total_intermediate_results']}")
    
    print(f"\n代码行概览:")
    for line_data in ui_data['code_lines']:
        status = "⚠️" if line_data['has_issues'] else "✅"
        print(f"  {status} 行{line_data['line_number']}: {line_data['node_count']}节点 ({line_data['intermediate_count']}中间)")
    
    if ui_data['recommendations']:
        print(f"\n建议:")
        for rec in ui_data['recommendations']:
            icon = "⚠️" if rec['type'] == 'warning' else "❌"
            print(f"  {icon} {rec['message']}")

def main():
    """主函数"""
    print("🚀 精确类型分析功能使用指南")
    print("=" * 60)
    print("核心功能:")
    print("• 🎯 基于语法树的精确节点类型分析")
    print("• 📊 详细的中间结果识别和统计")
    print("• 🔍 智能类型推断和置信度评估")
    print("• ⚠️  混合精度问题和类型转换检测")
    print("• 📄 专用的精确分析报告生成")
    print("• 🖥️  UI友好的数据格式支持")
    print()
    
    example_basic_usage()
    example_comparison_analysis()
    example_detailed_node_inspection()
    example_performance_optimization()
    example_integration_with_ui()
    
    print("\n" + "=" * 60)
    print("🎯 使用建议")
    print("=" * 60)
    print("1. 对于简单分析，使用标准的 analyze_shader() 方法")
    print("2. 对于详细分析，使用 analyze_shader_with_precise_types() 方法")
    print("3. 利用对比结果评估分析质量的提升")
    print("4. 基于精确分析结果生成性能优化建议")
    print("5. 将精确分析数据集成到UI界面中")
    print("6. 使用生成的HTML报告进行详细的代码审查")

if __name__ == "__main__":
    main()
