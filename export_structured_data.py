#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出结构化的着色器分析数据，专门用于识别每行代码的运算类型
"""

import json
import csv
import re
from typing import Dict, List, Tuple

class ShaderLineAnalyzer:
    """着色器行级分析器"""
    
    def __init__(self):
        self.half_float_patterns = {
            'half_declaration': r'\bhalf\d*\s+\w+',
            'float_declaration': r'\bfloat\d*\s+\w+',
            'half_conversion': r'\bhalf\d*\s*\(',
            'float_conversion': r'\bfloat\d*\s*\(',
            'half_literal': r'\bhalf\d*\s*\(',
            'float_literal': r'\bfloat\d*\s*\(',
            'mixed_operation': r'(half\d*.*float\d*|float\d*.*half\d*)',
        }
    
    def analyze_line_operations(self, line: str, line_num: int) -> Dict:
        """分析单行的详细运算信息"""
        line_clean = line.strip()
        
        analysis = {
            'line_number': line_num,
            'original_code': line_clean,
            'has_half': False,
            'has_float': False,
            'operation_types': [],
            'data_type_operations': [],
            'precision_level': 'none',  # none, half, float, mixed
            'performance_impact': 'low',
            'optimization_suggestions': []
        }
        
        if not line_clean or line_clean.startswith('//') or line_clean.startswith('#'):
            return analysis
        
        # 检测half和float的存在
        has_half = bool(re.search(r'\bhalf\d*\b', line_clean))
        has_float = bool(re.search(r'\bfloat\d*\b', line_clean))
        
        analysis['has_half'] = has_half
        analysis['has_float'] = has_float
        
        # 确定精度级别
        if has_half and has_float:
            analysis['precision_level'] = 'mixed'
            analysis['performance_impact'] = 'high'
            analysis['optimization_suggestions'].append('考虑统一使用单一精度类型')
        elif has_half:
            analysis['precision_level'] = 'half'
        elif has_float:
            analysis['precision_level'] = 'float'
        
        # 检测具体的数据类型操作
        operations = []
        
        # 变量声明
        half_decl = re.findall(r'\b(half\d*)\s+(\w+)', line_clean)
        for dtype, var_name in half_decl:
            operations.append({
                'type': 'declaration',
                'data_type': dtype,
                'variable': var_name,
                'detail': f'声明{dtype}变量: {var_name}'
            })
        
        float_decl = re.findall(r'\b(float\d*)\s+(\w+)', line_clean)
        for dtype, var_name in float_decl:
            operations.append({
                'type': 'declaration',
                'data_type': dtype,
                'variable': var_name,
                'detail': f'声明{dtype}变量: {var_name}'
            })
        
        # 类型转换
        half_conv = re.findall(r'\b(half\d*)\s*\(([^)]+)\)', line_clean)
        for dtype, content in half_conv:
            operations.append({
                'type': 'conversion',
                'data_type': dtype,
                'source': content,
                'detail': f'转换为{dtype}: {content[:30]}...'
            })
            analysis['performance_impact'] = 'medium'
        
        float_conv = re.findall(r'\b(float\d*)\s*\(([^)]+)\)', line_clean)
        for dtype, content in float_conv:
            operations.append({
                'type': 'conversion',
                'data_type': dtype,
                'source': content,
                'detail': f'转换为{dtype}: {content[:30]}...'
            })
        
        # 算术运算
        if re.search(r'[+\-*/]', line_clean) and (has_half or has_float):
            operations.append({
                'type': 'arithmetic',
                'data_type': 'mixed' if (has_half and has_float) else ('half' if has_half else 'float'),
                'detail': '算术运算'
            })
        
        # 函数调用
        func_calls = re.findall(r'\b(\w+(?:::\w+)?)\s*\([^)]*\)', line_clean)
        for func in func_calls:
            if func in ['normalize', 'dot', 'cross', 'mix', 'clamp', 'min', 'max', 'sqrt', 'rsqrt', 'pow', 'powr', 'abs', 'floor', 'ceil', 'fract']:
                operations.append({
                    'type': 'function_call',
                    'function': func,
                    'detail': f'函数调用: {func}'
                })
        
        # 纹理采样
        if '.sample(' in line_clean:
            operations.append({
                'type': 'texture_sample',
                'detail': '纹理采样操作'
            })
            analysis['performance_impact'] = 'high'
            analysis['optimization_suggestions'].append('考虑优化纹理访问模式')
        
        analysis['data_type_operations'] = operations
        analysis['operation_types'] = list(set([op['type'] for op in operations]))
        
        return analysis

def export_to_csv(shader_file: str, output_file: str):
    """导出到CSV格式"""
    analyzer = ShaderLineAnalyzer()
    
    with open(shader_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    results = []
    for i, line in enumerate(lines, 1):
        analysis = analyzer.analyze_line_operations(line, i)
        
        # 扁平化数据用于CSV
        row = {
            'line_number': analysis['line_number'],
            'has_half': analysis['has_half'],
            'has_float': analysis['has_float'],
            'precision_level': analysis['precision_level'],
            'performance_impact': analysis['performance_impact'],
            'operation_types': '|'.join(analysis['operation_types']),
            'num_operations': len(analysis['data_type_operations']),
            'optimization_suggestions': '|'.join(analysis['optimization_suggestions']),
            'code_snippet': analysis['original_code'][:100]  # 限制长度
        }
        results.append(row)
    
    # 写入CSV
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['line_number', 'has_half', 'has_float', 'precision_level', 
                     'performance_impact', 'operation_types', 'num_operations',
                     'optimization_suggestions', 'code_snippet']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        writer.writerows(results)
    
    return results

def export_detailed_json(shader_file: str, output_file: str):
    """导出详细的JSON格式"""
    analyzer = ShaderLineAnalyzer()
    
    with open(shader_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    results = []
    stats = {
        'total_lines': len(lines),
        'lines_with_half': 0,
        'lines_with_float': 0,
        'mixed_precision_lines': 0,
        'high_impact_lines': 0,
        'operation_type_counts': {},
        'precision_level_counts': {'none': 0, 'half': 0, 'float': 0, 'mixed': 0}
    }
    
    for i, line in enumerate(lines, 1):
        analysis = analyzer.analyze_line_operations(line, i)
        results.append(analysis)
        
        # 更新统计
        if analysis['has_half']:
            stats['lines_with_half'] += 1
        if analysis['has_float']:
            stats['lines_with_float'] += 1
        if analysis['precision_level'] == 'mixed':
            stats['mixed_precision_lines'] += 1
        if analysis['performance_impact'] == 'high':
            stats['high_impact_lines'] += 1
        
        stats['precision_level_counts'][analysis['precision_level']] += 1
        
        for op_type in analysis['operation_types']:
            stats['operation_type_counts'][op_type] = stats['operation_type_counts'].get(op_type, 0) + 1
    
    output_data = {
        'metadata': {
            'shader_file': shader_file,
            'analysis_version': '1.0',
            'total_lines_analyzed': len(results)
        },
        'statistics': stats,
        'line_analysis': results
    }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)
    
    return output_data

def generate_summary_report(data: Dict) -> str:
    """生成摘要报告"""
    stats = data['statistics']
    
    report = []
    report.append("=" * 60)
    report.append("Metal着色器逐行分析摘要报告")
    report.append("=" * 60)
    
    report.append(f"\n📊 基础统计:")
    report.append(f"  总代码行数: {stats['total_lines']}")
    report.append(f"  包含half的行数: {stats['lines_with_half']}")
    report.append(f"  包含float的行数: {stats['lines_with_float']}")
    report.append(f"  混合精度行数: {stats['mixed_precision_lines']}")
    report.append(f"  高性能影响行数: {stats['high_impact_lines']}")
    
    report.append(f"\n🎯 精度级别分布:")
    for level, count in stats['precision_level_counts'].items():
        percentage = (count / stats['total_lines']) * 100
        report.append(f"  {level:8}: {count:4} ({percentage:.1f}%)")
    
    report.append(f"\n🔧 运算类型分布:")
    for op_type, count in sorted(stats['operation_type_counts'].items()):
        report.append(f"  {op_type:15}: {count:4}")
    
    # 找出问题行
    problem_lines = []
    for line_data in data['line_analysis']:
        if line_data['precision_level'] == 'mixed' or line_data['performance_impact'] == 'high':
            problem_lines.append(line_data)
    
    report.append(f"\n⚠️  需要关注的代码行 (前10行):")
    for i, line_data in enumerate(problem_lines[:10], 1):
        report.append(f"  {i:2}. 行{line_data['line_number']:4}: {line_data['precision_level']:6} - {line_data['original_code'][:50]}...")
    
    report.append("\n" + "=" * 60)
    
    return "\n".join(report)

def main():
    shader_file = 'metal_shader_ps'
    
    print("开始逐行分析着色器...")
    
    # 导出CSV
    csv_results = export_to_csv(shader_file, 'shader_line_analysis.csv')
    print(f"CSV数据已导出: shader_line_analysis.csv ({len(csv_results)} 行)")
    
    # 导出详细JSON
    json_data = export_detailed_json(shader_file, 'shader_detailed_analysis.json')
    print(f"详细JSON数据已导出: shader_detailed_analysis.json")
    
    # 生成摘要报告
    summary = generate_summary_report(json_data)
    with open('shader_line_summary.txt', 'w', encoding='utf-8') as f:
        f.write(summary)
    print("摘要报告已生成: shader_line_summary.txt")
    
    print("\n分析完成！生成的文件:")
    print("  - shader_line_analysis.csv (CSV格式，适合Excel)")
    print("  - shader_detailed_analysis.json (详细JSON数据)")
    print("  - shader_line_summary.txt (摘要报告)")

if __name__ == "__main__":
    main()
