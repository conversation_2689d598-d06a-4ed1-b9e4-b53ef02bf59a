🎯 精确类型分析摘要报告
==================================================
📊 基本统计:
  总代码行: 762
  总节点数: 3704
  变量声明: 648
  中间结果: 1031

🔍 类型分析:
  类型转换: 226
  精度问题: 0
  准确性评分: 39.6%

🎨 类型分布:
  bool: 86 (2.3%)
  float: 839 (22.7%)
  float2: 157 (4.2%)
  float3: 391 (10.6%)
  float3x3: 1 (0.0%)
  float4: 42 (1.1%)
  half: 260 (7.0%)
  half3: 149 (4.0%)
  half4: 59 (1.6%)
  int: 24 (0.6%)
  uint: 47 (1.3%)
  unknown: 1649 (44.5%)

📈 精确分析特点:
  基于语法树的精确类型推断
  完整的运算过程模拟
  中间结果类型跟踪

💡 性能建议:
  ⚠️  发现 226 个类型转换，建议优化