#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的UI系统
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ui_imports():
    """测试UI组件导入"""
    print("🎨 测试UI组件导入")
    print("=" * 50)
    
    try:
        # 测试统一的分析组件
        from ui.unified_shader_analysis_widget import UnifiedShaderAnalysisWidget
        print("   ✅ UnifiedShaderAnalysisWidget 导入成功")

        # 测试主窗口
        from ui.main_window import MainWindow
        print("   ✅ MainWindow 导入成功")
        
        # 检查主窗口是否使用了统一组件
        import inspect
        main_window_source = inspect.getsource(MainWindow)
        
        if 'UnifiedShaderAnalysisWidget' in main_window_source:
            print("   ✅ MainWindow 使用了 UnifiedShaderAnalysisWidget")
        else:
            print("   ❌ MainWindow 未使用 UnifiedShaderAnalysisWidget")
            return False
        
        if 'on_unified_shader_analysis' in main_window_source:
            print("   ✅ MainWindow 包含统一分析方法")
        else:
            print("   ❌ MainWindow 缺少统一分析方法")
            return False
        
        # 检查是否移除了旧方法
        old_methods = ['ShaderAnalysisWidget', 'PreciseTypeAnalysisWidget', 'on_shader_AST_analysis']
        for method in old_methods:
            if method in main_window_source:
                print(f"   ⚠️  MainWindow 仍包含旧方法: {method}")
            else:
                print(f"   ✅ MainWindow 已移除旧方法: {method}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 导入失败: {str(e)}")
        return False

def test_unified_widget():
    """测试统一分析组件"""
    print("\n🎯 测试统一分析组件")
    print("=" * 50)
    
    try:
        from ui.unified_shader_analysis_widget import UnifiedShaderAnalysisWidget, UnifiedAnalysisWorker, MetricCard
        
        print("   ✅ 所有组件类导入成功")
        
        # 测试MetricCard
        try:
            card = MetricCard("测试指标", "100", "测试描述", "#4fc3f7")
            print("   ✅ MetricCard 创建成功")
        except Exception as e:
            print(f"   ❌ MetricCard 创建失败: {str(e)}")
            return False
        
        # 测试UnifiedAnalysisWorker
        try:
            worker = UnifiedAnalysisWorker("float a = b + c;")
            print("   ✅ UnifiedAnalysisWorker 创建成功")
        except Exception as e:
            print(f"   ❌ UnifiedAnalysisWorker 创建失败: {str(e)}")
            return False
        
        # 测试UnifiedShaderAnalysisWidget
        try:
            # 这里不能直接创建QWidget，因为需要QApplication
            print("   ✅ UnifiedShaderAnalysisWidget 类定义正常")
        except Exception as e:
            print(f"   ❌ UnifiedShaderAnalysisWidget 测试失败: {str(e)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        return False

def test_analysis_integration():
    """测试分析集成"""
    print("\n🔗 测试分析集成")
    print("=" * 50)
    
    try:
        # 测试分析处理器导入
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        print("   ✅ ShaderAnalysisProcessor 导入成功")
        
        # 测试分析功能
        processor = ShaderAnalysisProcessor()
        test_code = "float a = b + c;"
        
        print("   开始测试分析...")
        result = processor.analyze_shader(test_code, save_reports=False)
        
        print("   ✅ 分析功能正常")
        
        # 测试关键指标获取
        metrics = processor.get_key_metrics(result)
        print(f"   ✅ 关键指标获取成功: {len(metrics)} 个指标")
        
        # 测试摘要生成
        summary = processor.get_analysis_summary(result)
        print(f"   ✅ 摘要生成成功: {len(summary)} 字符")
        
        # 测试优化建议
        suggestions = processor.get_optimization_suggestions(result)
        print(f"   ✅ 优化建议生成成功: {len(suggestions)} 条建议")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 分析集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_functionality():
    """测试UI功能（模拟）"""
    print("\n🖥️ 测试UI功能")
    print("=" * 50)
    
    try:
        # 检查UI组件的方法
        from ui.unified_shader_analysis_widget import UnifiedShaderAnalysisWidget
        
        # 检查必要的方法
        required_methods = [
            'init_ui',
            'create_overview_tab',
            'create_details_tab', 
            'create_suggestions_tab',
            'start_analysis',
            'on_analysis_completed',
            'on_analysis_failed',
            'update_analysis_display',
            'save_reports',
            'open_html_report'
        ]
        
        for method in required_methods:
            if hasattr(UnifiedShaderAnalysisWidget, method):
                print(f"   ✅ {method} 方法存在")
            else:
                print(f"   ❌ {method} 方法缺失")
                return False
        
        print("   ✅ 所有必要方法都存在")
        
        # 检查UI组件的信号
        from ui.unified_shader_analysis_widget import UnifiedAnalysisWorker
        
        required_signals = ['analysis_completed', 'analysis_failed', 'progress_updated']
        for signal in required_signals:
            if hasattr(UnifiedAnalysisWorker, signal):
                print(f"   ✅ {signal} 信号存在")
            else:
                print(f"   ❌ {signal} 信号缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ UI功能测试失败: {str(e)}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n📁 测试文件结构")
    print("=" * 50)
    
    # 检查新文件是否存在
    new_files = [
        'UI/unified_shader_analysis_widget.py',
        'UI/main_window.py'
    ]
    
    for file_path in new_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path} 存在")
        else:
            print(f"   ❌ {file_path} 不存在")
            return False
    
    # 检查旧文件是否可以安全移除（如果不再被使用）
    potentially_old_files = [
        'UI/shader_analysis_widget.py',
        'UI/precise_type_analysis_widget.py'
    ]
    
    for file_path in potentially_old_files:
        if os.path.exists(file_path):
            print(f"   ⚠️  {file_path} 仍存在（可能需要检查是否还被使用）")
        else:
            print(f"   ✅ {file_path} 已移除")
    
    return True

def main():
    """主函数"""
    print("🎨 更新后的UI系统测试")
    print("=" * 60)
    print("测试内容:")
    print("• 🎨 UI组件导入测试")
    print("• 🎯 统一分析组件测试")
    print("• 🔗 分析集成测试")
    print("• 🖥️ UI功能测试")
    print("• 📁 文件结构测试")
    print()
    
    # 执行测试
    imports_ok = test_ui_imports()
    widget_ok = test_unified_widget()
    integration_ok = test_analysis_integration()
    ui_func_ok = test_ui_functionality()
    structure_ok = test_file_structure()
    
    print(f"\n🎉 测试结果总结")
    print("=" * 60)
    
    if imports_ok:
        print("✅ UI组件导入测试通过")
        print("   • 统一分析组件导入正常")
        print("   • 主窗口更新正确")
        print("   • 旧方法已移除")
    else:
        print("❌ UI组件导入测试失败")
    
    if widget_ok:
        print("✅ 统一分析组件测试通过")
        print("   • 所有组件类正常")
        print("   • MetricCard 工作正常")
        print("   • UnifiedAnalysisWorker 正常")
    else:
        print("❌ 统一分析组件测试失败")
    
    if integration_ok:
        print("✅ 分析集成测试通过")
        print("   • 分析处理器正常")
        print("   • 所有分析方法正常")
    else:
        print("❌ 分析集成测试失败")
    
    if ui_func_ok:
        print("✅ UI功能测试通过")
        print("   • 所有必要方法存在")
        print("   • 信号定义正确")
    else:
        print("❌ UI功能测试失败")
    
    if structure_ok:
        print("✅ 文件结构测试通过")
        print("   • 新文件创建成功")
        print("   • 文件结构合理")
    else:
        print("❌ 文件结构测试失败")
    
    if all([imports_ok, widget_ok, integration_ok, ui_func_ok, structure_ok]):
        print(f"\n🎊 UI系统更新完成！")
        print("现在UI系统统一使用精确类型分析:")
        print("• 🎯 单一的分析窗口: UnifiedShaderAnalysisWidget")
        print("• 📊 完整的分析展示: 概览、详情、建议三个标签页")
        print("• 🔄 统一的API调用: 只使用 analyze_shader 方法")
        print("• 🌐 集成的报告功能: 保存和打开HTML报告")
        print("• 🎨 现代化的UI设计: 卡片式指标展示")
        print("• ⚡ 异步分析处理: 不阻塞主界面")
    else:
        print(f"\n❌ UI系统更新未完全完成，请检查失败的测试项")

if __name__ == "__main__":
    main()
