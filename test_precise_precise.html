
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精确着色器类型分析报告</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #1e1e1e; color: #d4d4d4; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #2d2d30; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-card { background: #252526; padding: 15px; border-radius: 6px; border-left: 4px solid #007acc; }
        .stat-value { font-size: 24px; font-weight: bold; color: #4fc3f7; }
        .stat-label { color: #cccccc; margin-top: 5px; }
        .code-section { background: #252526; border-radius: 8px; overflow: hidden; margin-bottom: 20px; }
        .code-header { background: #2d2d30; padding: 15px; border-bottom: 1px solid #3c3c3c; }
        .code-line { display: flex; padding: 8px 0; border-bottom: 1px solid #2d2d30; }
        .line-number { width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; }
        .line-content { flex: 1; padding-left: 15px; font-family: 'Consolas', monospace; }
        .node-info { font-size: 0.85em; color: #ffc107; margin-left: 10px; }
        .intermediate { color: #ff9800; }
        .direct { color: #4caf50; }
        .type-conversion { background-color: rgba(255, 193, 7, 0.2); }
        .precision-issue { background-color: rgba(244, 67, 54, 0.2); }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 精确着色器类型分析报告</h1>
            <p>基于语法树的精确类型推断分析</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">15</div>
                <div class="stat-label">总节点数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">4</div>
                <div class="stat-label">变量声明</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">4</div>
                <div class="stat-label">中间结果</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">40.0%</div>
                <div class="stat-label">准确性评分</div>
            </div>
        </div>

        <div class="code-section">
            <div class="code-header">
                <h3>📝 逐行精确类型分析</h3>
            </div>

            <div class="code-line ">
                <div class="line-number">2</div>
                <div class="line-content">
                    float3 pos = worldMatrix * localPos;
                    <div class="node-info">🔹variable(worldMatrix)→unknown | 🔹variable(localPos)→unknown | 🔸operator(*)→unknown | 🔹declaration(float3 pos)→float3</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">3</div>
                <div class="line-content">
                    half4 color = texture.sample(sampler, uv);
                    <div class="node-info">🔹literal(texture.sample(sampler, uv))→unknown | 🔹declaration(half4 color)→half4</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">4</div>
                <div class="line-content">
                    float intensity = dot(normal, lightDir);
                    <div class="node-info">🔹variable(normal)→unknown | 🔹variable(lightDir)→unknown | 🔸function(dot)→float | 🔹declaration(float intensity)→float</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">5</div>
                <div class="line-content">
                    half3 result = color.rgb * half(intensity);
                    <div class="node-info">🔹member_access(color.rgb)→half3 | 🔹variable(intensity)→float | 🔸type_cast(half)→half | 🔸operator(*)→half3 | 🔹declaration(half3 result)→half3</div>
                </div>
            </div>

        </div>
    </div>
</body>
</html>
