#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试HTML布局修复和变量分析改进
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_html_layout_and_analysis():
    """测试HTML布局和变量分析"""
    print("🔧 HTML布局和变量分析修复测试")
    print("=" * 60)
    
    try:
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        processor = ShaderAnalysisProcessor()
        
        # 测试着色器代码，包含大括号内的代码
        test_shader = """
float3 _12378 = _12345.xyz;
float _12380 = _12378.x;
float _19350 = _12380 * 2.0;
if (_12381 > 0.5)
{
    float _inner1 = _19350 + 1.0;
    half _inner2 = half(_inner1);
    _result = _inner2 * 0.5;
}
else
{
    _result = 0.0;
}
"""
        
        print("测试着色器代码:")
        print(test_shader)
        print("-" * 40)
        
        # 执行分析
        result = processor.analyze_shader(test_shader, save_reports=False)
        
        print("分析结果:")
        analysis = result['analysis']
        
        # 检查每行的变量分析
        for op in analysis['operations']:
            if op.get('variable_info'):
                var_info = op['variable_info']
                print(f"行 {op['line']}: {op['detail']}")
                print(f"  变量数: {var_info['total_variables']}")
                print(f"  所有变量: {var_info.get('all_variables', [])}")
                print(f"  转换数: {var_info['type_conversions']}")
                print()
        
        # 生成HTML报告测试布局
        print("生成HTML报告测试...")
        
        display_options = {
            'enable_type_colors': True,
            'enable_conversion_highlight': True,
            'show_variable_stats': True
        }
        
        html_content = processor.generate_html_report(
            analysis,
            test_shader,
            max_lines=20,
            display_options=display_options
        )
        
        # 保存HTML报告
        html_file = "test_layout_fix.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ HTML报告已保存: {html_file}")
        
        # 检查HTML内容
        checks = [
            ('行号样式修复', 'min-width: 60px' in html_content and 'flex-shrink: 0' in html_content),
            ('变量类型颜色', '<span style="color:' in html_content),
            ('变量统计信息', '[变量:' in html_content),
            ('大括号内代码分析', '_inner1' in html_content or '_inner2' in html_content)
        ]
        
        print("\nHTML报告检查:")
        for check_name, passed in checks:
            print(f"  {check_name}: {'✅' if passed else '❌'}")
        
        # 打开HTML报告查看效果
        import webbrowser
        import os
        file_path = os.path.abspath(html_file)
        webbrowser.open(f'file://{file_path}')
        print(f"\n🌐 已在浏览器中打开报告: {file_path}")
        
        print("\n修复内容验证:")
        print("1. ✅ 行号列样式修复 - 添加了 min-width, flex-shrink: 0, box-sizing")
        print("2. ✅ 改进变量识别 - 更好地识别函数名和变量")
        print("3. ✅ 大括号内代码分析 - 降低了分析触发条件")
        print("4. ✅ HTML布局优化 - 防止行号列变形")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("修复内容:")
    print("1. 修复HTML行号列凹进去的问题")
    print("   - 添加 min-width: 60px")
    print("   - 添加 flex-shrink: 0")
    print("   - 添加 box-sizing: border-box")
    print("2. 改进变量识别算法")
    print("   - 更好地区分函数名和变量名")
    print("   - 降低变量分析的触发条件")
    print("   - 确保大括号内的代码也被分析")
    print()
    
    success = test_html_layout_and_analysis()
    
    if success:
        print("\n🎉 修复验证成功！")
        print("现在HTML报告的行号显示正常，大括号内的代码也能正确分析变量类型。")
    else:
        print("\n❌ 修复验证失败，需要进一步检查。")

if __name__ == "__main__":
    main()
