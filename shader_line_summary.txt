============================================================
Metal着色器逐行分析摘要报告
============================================================

📊 基础统计:
  总代码行数: 1161
  包含half的行数: 239
  包含float的行数: 497
  混合精度行数: 60
  高性能影响行数: 31

🎯 精度级别分布:
  none    :  485 (41.8%)
  half    :  179 (15.4%)
  float   :  437 (37.6%)
  mixed   :   60 (5.2%)

🔧 运算类型分布:
  arithmetic     :  274
  conversion     :  264
  declaration    :  623
  function_call  :   86
  texture_sample :   30

⚠️  需要关注的代码行 (前10行):
   1. 行 102: mixed  - fragment main0_out main0(main0_in in [[stage_in]],...
   2. 行 112: half   - half4 _8939 = sBaseSampler.sample(sBaseSamplerSmpl...
   3. 行 114: mixed  - half3 _8949 = half3(float3(_8941 * _8941) * float3...
   4. 行 115: float  - float4 _8973 = sNoiseSampler.sample(sNoiseSamplerS...
   5. 行 117: float  - float4 _8982 = sNoiseSampler.sample(sNoiseSamplerS...
   6. 行 119: mixed  - float _8991 = fast::clamp(powr(float(max(in.IN_Tin...
   7. 行 124: mixed  - if ((float((_8994 * _8996) - half(mix(0.0, 0.5, po...
   8. 行 128: half   - half4 _9248 = sNormalSampler.sample(sNormalSampler...
   9. 行 140: half   - half4 _9074 = sMixSampler.sample(sMixSamplerSmplr,...
  10. 行 142: mixed  - half _9096 = half(mix(1.0, float(mix(half3(_9019),...

============================================================