#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成Metal着色器分析的HTML可视化报告
"""

import json
import re
from typing import Dict, List

def generate_html_report(json_file: str, shader_file: str) -> str:
    """生成HTML可视化报告"""
    
    # 读取分析结果
    with open(json_file, 'r', encoding='utf-8') as f:
        result = json.load(f)
    
    # 读取原始着色器代码
    with open(shader_file, 'r', encoding='utf-8') as f:
        shader_lines = f.readlines()
    
    # 创建行号到操作的映射
    line_operations = {}
    for op in result['operations']:
        line_num = op['line']
        if line_num not in line_operations:
            line_operations[line_num] = []
        line_operations[line_num].append(op)
    
    stats = result['statistics']
    
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Metal着色器Half/Float运算分析报告</title>
    <style>
        body {{
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
            line-height: 1.6;
        }}
        
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }}
        
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        
        .stat-card {{
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007acc;
            text-align: center;
        }}
        
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #4fc3f7;
        }}
        
        .stat-label {{
            color: #cccccc;
            margin-top: 5px;
        }}
        
        .code-container {{
            background: #1e1e1e;
            border: 1px solid #3c3c3c;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 30px;
        }}
        
        .code-header {{
            background: #2d2d30;
            padding: 15px;
            border-bottom: 1px solid #3c3c3c;
            font-weight: bold;
        }}
        
        .code-content {{
            max-height: 600px;
            overflow-y: auto;
        }}
        
        .code-line {{
            display: flex;
            padding: 2px 0;
            border-bottom: 1px solid #2d2d30;
        }}
        
        .line-number {{
            width: 60px;
            text-align: right;
            padding-right: 15px;
            color: #858585;
            background: #252526;
            border-right: 1px solid #3c3c3c;
            user-select: none;
        }}
        
        .line-content {{
            flex: 1;
            padding-left: 15px;
            white-space: pre;
            font-family: 'Consolas', monospace;
        }}
        
        .highlight-mixed {{
            background-color: rgba(255, 87, 87, 0.2);
        }}
        
        .highlight-conversion {{
            background-color: rgba(255, 193, 7, 0.2);
        }}
        
        .highlight-texture {{
            background-color: rgba(76, 175, 80, 0.2);
        }}
        
        .highlight-high-impact {{
            background-color: rgba(156, 39, 176, 0.2);
        }}
        
        .operation-info {{
            font-size: 0.8em;
            color: #ffc107;
            margin-left: 10px;
        }}
        
        .legend {{
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background: #2d2d30;
            border-radius: 8px;
        }}
        
        .legend-item {{
            display: flex;
            align-items: center;
            gap: 8px;
        }}
        
        .legend-color {{
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }}
        
        .chart-container {{
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }}
        
        .bar-chart {{
            display: flex;
            flex-direction: column;
            gap: 10px;
        }}
        
        .bar-item {{
            display: flex;
            align-items: center;
            gap: 15px;
        }}
        
        .bar-label {{
            width: 150px;
            text-align: right;
        }}
        
        .bar {{
            height: 25px;
            background: linear-gradient(90deg, #4fc3f7, #29b6f6);
            border-radius: 4px;
            display: flex;
            align-items: center;
            padding: 0 10px;
            color: white;
            font-weight: bold;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Metal着色器Half/Float运算分析报告</h1>
        <p>深度分析着色器中的数据类型转换和运算模式</p>
    </div>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{stats['total_operations']}</div>
            <div class="stat-label">总运算操作</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{stats['precision_conversions']}</div>
            <div class="stat-label">精度转换</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{stats['mixed_precision_ops']}</div>
            <div class="stat-label">混合精度运算</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{stats['high_impact_ops']}</div>
            <div class="stat-label">高影响操作</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{stats['texture_samples']}</div>
            <div class="stat-label">纹理采样</div>
        </div>
    </div>
    
    <div class="chart-container">
        <h3>📊 运算类型分布</h3>
        <div class="bar-chart">
"""
    
    # 添加柱状图
    for op_type, count in stats['operation_types'].items():
        percentage = (count / stats['total_operations']) * 100
        bar_width = min(percentage * 8, 400)  # 最大宽度400px
        
        html_content += f"""
            <div class="bar-item">
                <div class="bar-label">{op_type}</div>
                <div class="bar" style="width: {bar_width}px;">
                    {count} ({percentage:.1f}%)
                </div>
            </div>
"""
    
    html_content += """
        </div>
    </div>
    
    <div class="legend">
        <div class="legend-item">
            <div class="legend-color" style="background-color: rgba(255, 87, 87, 0.2);"></div>
            <span>混合精度运算</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: rgba(255, 193, 7, 0.2);"></div>
            <span>类型转换</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: rgba(76, 175, 80, 0.2);"></div>
            <span>纹理采样</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: rgba(156, 39, 176, 0.2);"></div>
            <span>高性能影响</span>
        </div>
    </div>
    
    <div class="code-container">
        <div class="code-header">
            🔍 着色器代码分析 (显示前200行)
        </div>
        <div class="code-content">
"""
    
    # 添加代码行
    for i, line in enumerate(shader_lines[:200], 1):  # 只显示前200行
        line_clean = line.rstrip()
        css_class = ""
        operation_info = ""
        
        if i in line_operations:
            ops = line_operations[i]
            op_types = [op['type'] for op in ops]
            
            if 'mixed_precision' in op_types:
                css_class = "highlight-mixed"
                operation_info += " [混合精度]"
            elif 'type_conversion' in op_types:
                css_class = "highlight-conversion"
                operation_info += " [类型转换]"
            elif 'texture_sample' in op_types:
                css_class = "highlight-texture"
                operation_info += " [纹理采样]"
            elif any(op['performance_impact'] == 'high' for op in ops):
                css_class = "highlight-high-impact"
                operation_info += " [高影响]"
        
        html_content += f"""
            <div class="code-line {css_class}">
                <div class="line-number">{i}</div>
                <div class="line-content">{line_clean}<span class="operation-info">{operation_info}</span></div>
            </div>
"""
    
    html_content += """
        </div>
    </div>
    
    <div style="text-align: center; margin-top: 30px; color: #858585;">
        <p>报告生成时间: <span id="timestamp"></span></p>
    </div>
    
    <script>
        document.getElementById('timestamp').textContent = new Date().toLocaleString('zh-CN');
    </script>
</body>
</html>
"""
    
    return html_content

def main():
    html_report = generate_html_report('shader_analysis_result.json', 'metal_shader_ps')
    
    with open('shader_analysis_report.html', 'w', encoding='utf-8') as f:
        f.write(html_report)
    
    print("HTML可视化报告已生成: shader_analysis_report.html")

if __name__ == "__main__":
    main()
