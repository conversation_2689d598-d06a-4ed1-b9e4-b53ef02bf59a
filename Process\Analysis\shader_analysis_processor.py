#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
着色器分析处理器 - 基于AST的精确类型分析
"""

from typing import Dict, Optional, List
from .tree_based_type_analyzer import TreeBasedTypeAnalyzer
from .report_generator import ReportGenerator

class ShaderAnalysisProcessor:
    """着色器分析处理器 - 基于AST的精确类型分析"""

    def __init__(self):
        self.type_analyzer = TreeBasedTypeAnalyzer()
        self.report_generator = ReportGenerator()

    def analyze_shader(self, shader_content: str, save_reports: bool = True, base_filename: str = "shader_analysis") -> Dict:
        """
        分析着色器内容 - 使用基于AST的精确类型分析

        Args:
            shader_content: 着色器源代码内容
            save_reports: 是否保存报告文件
            base_filename: 报告文件基础名称

        Returns:
            包含精确分析结果和报告路径的字典
        """
        # 执行基于AST的精确类型分析
        precise_analysis_result = self.type_analyzer.analyze_shader_with_precise_types(shader_content)

        result = {
            'analysis': {'precise_analysis': precise_analysis_result},
            'files': {},
            'analysis_method': 'precise_tree_based'
        }

        # 如果需要保存报告文件
        if save_reports:
            try:
                file_paths = self._save_reports(
                    result,
                    shader_content,
                    base_filename
                )
                result['files'] = file_paths
            except Exception as e:
                result['error'] = f"保存报告文件时出错: {str(e)}"

        # 保存着色器内容供后续使用
        result['shader_content'] = shader_content

        return result

    def _generate_summary_report(self, precise_data: Dict) -> str:
        """生成精确分析摘要报告"""
        if 'overall_statistics' in precise_data:
            # 使用精确分析结果
            overall_stats = precise_data['overall_statistics']

            summary_lines = []
            summary_lines.append("🎯 精确类型分析摘要报告")
            summary_lines.append("=" * 50)

            # 基本统计
            summary_lines.append(f"📊 基本统计:")
            summary_lines.append(f"  总代码行: {overall_stats['total_lines']}")
            summary_lines.append(f"  总节点数: {overall_stats['total_nodes']}")
            summary_lines.append(f"  变量声明: {overall_stats['total_variables']}")
            summary_lines.append(f"  中间结果: {overall_stats['total_intermediate_results']}")

            # 类型分析
            summary_lines.append(f"\n🔍 类型分析:")
            summary_lines.append(f"  类型转换: {overall_stats['total_type_conversions']}")
            summary_lines.append(f"  精度问题: {overall_stats['total_precision_issues']}")
            summary_lines.append(f"  准确性评分: {overall_stats['precision_accuracy_score']:.1f}%")

            # 类型分布
            if overall_stats['type_distribution']:
                summary_lines.append(f"\n🎨 类型分布:")
                for type_name, count in sorted(overall_stats['type_distribution'].items()):
                    percentage = count / overall_stats['total_nodes'] * 100
                    summary_lines.append(f"  {type_name}: {count} ({percentage:.1f}%)")

            # 精确分析特点
            summary_lines.append(f"\n📈 精确分析特点:")
            summary_lines.append(f"  基于语法树的精确类型推断")
            summary_lines.append(f"  完整的运算过程模拟")
            summary_lines.append(f"  中间结果类型跟踪")

            # 性能建议
            summary_lines.append(f"\n💡 性能建议:")
            if overall_stats['total_type_conversions'] > 0:
                summary_lines.append(f"  ⚠️  发现 {overall_stats['total_type_conversions']} 个类型转换，建议优化")
            if overall_stats['total_precision_issues'] > 0:
                summary_lines.append(f"  ⚠️  发现 {overall_stats['total_precision_issues']} 个混合精度问题，建议统一精度")
            if overall_stats['total_type_conversions'] == 0 and overall_stats['total_precision_issues'] == 0:
                summary_lines.append(f"  ✅ 代码类型使用良好，无明显性能问题")

            return "\n".join(summary_lines)
        else:
            # 回退到原有分析结果
            return self.report_generator.generate_summary_report(precise_result.get('original_analysis', {}))

    def _save_reports(self, result: Dict, shader_content: str, base_filename: str) -> Dict[str, str]:
        """保存分析报告文件"""
        file_paths = {}
        precise_result = result['analysis']['precise_analysis']

        try:
            # 保存JSON报告
            json_filename = f"{base_filename}.json"
            with open(json_filename, 'w', encoding='utf-8') as f:
                import json
                json.dump(result, f, indent=2, ensure_ascii=False, default=str)
            file_paths['json'] = json_filename

            # 保存HTML报告
            html_filename = f"{base_filename}.html"
            html_content = self._generate_html_report(result, shader_content)
            with open(html_filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            file_paths['html'] = html_filename

            # 保存文本摘要报告
            txt_filename = f"{base_filename}_summary.txt"
            summary_content = self._generate_summary_report(precise_result)
            with open(txt_filename, 'w', encoding='utf-8') as f:
                f.write(summary_content)
            file_paths['summary'] = txt_filename

        except Exception as e:
            raise Exception(f"保存分析报告失败: {str(e)}")

        return file_paths

    def _generate_html_report(self, result: Dict, shader_content: str) -> str:
        """生成HTML报告"""
        precise_result = result['analysis']['precise_analysis']

        # 构建代码行数据结构
        code_lines_data = self._build_code_lines_data(result, shader_content)

        # 使用报告生成器生成HTML
        return self.report_generator.generate_precise_html_report(
            precise_result,
            code_lines_data
        )

    def _build_code_lines_data(self, result: Dict, shader_content: str) -> List:
        """构建代码行数据结构"""
        from Process.Analysis.report_generator import CodeLineData

        precise_data = result['analysis']['precise_analysis']
        shader_lines = shader_content.split('\n')

        # 创建分析结果的映射（按行号）
        analysis_map = {}
        if 'code_lines' in precise_data and 'precise_analyses' in precise_data:
            for code_line, analysis in zip(precise_data['code_lines'], precise_data['precise_analyses']):
                analysis_map[code_line.line_number] = analysis

        # 构建所有代码行的数据结构
        code_lines_data = []
        for line_num, line_content in enumerate(shader_lines, 1):
            analysis = analysis_map.get(line_num)

            if analysis:
                # 有分析结果的行
                operation_count = 0
                conversion_count = len(analysis.type_conversions)
                precision_issues = len(analysis.precision_issues)

                # 计算运算次数和类型
                operation_types = []
                node_details = []

                for typed_node in analysis.typed_nodes:
                    node_type = typed_node.node.node_type.value
                    if node_type in ['operator', 'function', 'assignment']:
                        operation_count += 1
                        operation_types.append(typed_node.node.value)
                        node_details.append(f"{node_type}({typed_node.node.value})")

                # 确定性能级别
                performance_level = "normal"
                if conversion_count > 0:
                    performance_level = "conversion"
                elif precision_issues > 0:
                    performance_level = "issue"
                elif operation_count > 5:
                    performance_level = "intensive"

                # CSS类
                css_classes = []
                if analysis.type_conversions:
                    css_classes.append("type-conversion")
                if analysis.precision_issues:
                    css_classes.append("precision-issue")

                code_line_data = CodeLineData(
                    line_number=line_num,
                    content=line_content,
                    operation_count=operation_count,
                    conversion_count=conversion_count,
                    precision_issues=precision_issues,
                    node_details=node_details,
                    operation_types=operation_types,
                    has_analysis=True,
                    css_classes=css_classes,
                    performance_level=performance_level
                )
            else:
                # 没有分析结果的行
                code_line_data = CodeLineData(
                    line_number=line_num,
                    content=line_content,
                    has_analysis=False
                )

            code_lines_data.append(code_line_data)

        return code_lines_data

    def get_analysis_summary(self, result: Dict) -> str:
        """获取分析摘要"""
        precise_result = result['analysis']['precise_analysis']
        return self._generate_summary_report(precise_result)

    def generate_html_report(self, result: Dict, shader_content: str = "") -> str:
        """生成HTML报告"""
        return self._generate_html_report(result, shader_content)

    def get_key_metrics(self, result: Dict) -> Dict:
        """获取关键指标"""
        precise_data = result['analysis']['precise_analysis']
        overall_stats = precise_data['overall_statistics']

        return {
            'total_nodes': overall_stats['total_nodes'],
            'total_variables': overall_stats['total_variables'],
            'total_intermediate_results': overall_stats['total_intermediate_results'],
            'total_type_conversions': overall_stats['total_type_conversions'],
            'total_precision_issues': overall_stats['total_precision_issues'],
            'precision_accuracy_score': overall_stats['precision_accuracy_score'],
            'conversion_ratio': overall_stats['total_type_conversions'] / overall_stats['total_nodes'] if overall_stats['total_nodes'] > 0 else 0
        }
    
    def get_optimization_suggestions(self, result: Dict) -> List[str]:
        """获取优化建议列表"""
        precise_data = result['analysis']['precise_analysis']
        overall_stats = precise_data['overall_statistics']
        suggestions = []

        total_conversions = overall_stats['total_type_conversions']
        total_nodes = overall_stats['total_nodes']
        precision_issues = overall_stats['total_precision_issues']

        if total_conversions > 100:
            suggestions.append(f"发现 {total_conversions} 个类型转换，建议统一变量类型以减少转换")

        if precision_issues > 0:
            suggestions.append(f"发现 {precision_issues} 个精度问题，可能影响渲染质量")

        conversion_ratio = total_conversions / total_nodes if total_nodes > 0 else 0
        if conversion_ratio > 0.2:
            suggestions.append(f"类型转换比例过高({conversion_ratio:.1%})，建议重构数据类型设计")

        accuracy_score = overall_stats.get('precision_accuracy_score', 0)
        if accuracy_score < 60:
            suggestions.append("类型推断准确性较低，建议明确变量类型声明")

        if not suggestions:
            suggestions.append("代码质量良好，基于AST的精确分析未发现明显问题")

        return suggestions
