#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
着色器分析处理器 - 统一的分析接口
"""

from typing import Dict, Optional, List
from .shader_analyzer_core import ShaderAnalyzerCore
from .report_generator import ReportGenerator

class ShaderAnalysisProcessor:
    """着色器分析处理器 - 提供统一的分析接口"""
    
    def __init__(self):
        self.analyzer = ShaderAnalyzerCore()
        self.report_generator = ReportGenerator()
    
    def analyze_shader(self, shader_content: str, save_reports: bool = True, base_filename: str = "shader_analysis") -> Dict:
        """
        分析着色器内容
        
        Args:
            shader_content: 着色器源代码内容
            save_reports: 是否保存报告文件
            base_filename: 报告文件基础名称
            
        Returns:
            包含分析结果和报告路径的字典
        """
        # 执行分析
        analysis_result = self.analyzer.analyze_shader_content(shader_content)
        
        # 生成简要报告
        summary_report = self.report_generator.generate_summary_report(analysis_result)
        
        result = {
            'analysis': analysis_result,
            'summary': summary_report,
            'files': {}
        }
        
        # 如果需要保存报告文件
        if save_reports:
            try:
                file_paths = self.report_generator.save_reports(
                    analysis_result,
                    shader_content,
                    base_filename
                )
                result['files'] = file_paths
            except Exception as e:
                result['error'] = f"保存报告文件时出错: {str(e)}"

        # 保存着色器内容供后续使用
        result['shader_content'] = shader_content
        
        return result

    def analyze_shader_with_precise_types(self, shader_content: str, save_reports: bool = True, base_filename: str = "precise_shader_analysis") -> Dict:
        """
        使用精确类型分析方法分析着色器内容

        Args:
            shader_content: 着色器源代码内容
            save_reports: 是否保存报告文件
            base_filename: 报告文件基础名称

        Returns:
            包含精确分析结果和报告路径的字典
        """
        # 执行精确类型分析
        precise_analysis_result = self.analyzer.analyze_shader_with_precise_types(shader_content)

        # 生成精确分析报告
        precise_summary = self._generate_precise_summary_report(precise_analysis_result)

        result = {
            'analysis': precise_analysis_result,
            'summary': precise_summary,
            'files': {},
            'analysis_method': 'precise_tree_based'
        }

        # 如果需要保存报告文件
        if save_reports:
            try:
                file_paths = self._save_precise_reports(
                    precise_analysis_result,
                    shader_content,
                    base_filename
                )
                result['files'] = file_paths
            except Exception as e:
                result['error'] = f"保存精确分析报告文件时出错: {str(e)}"

        # 保存着色器内容供后续使用
        result['shader_content'] = shader_content

        return result

    def _generate_precise_summary_report(self, precise_result: Dict) -> str:
        """生成精确分析摘要报告"""
        if 'precise_analysis' in precise_result:
            # 使用精确分析结果
            precise_data = precise_result['precise_analysis']
            overall_stats = precise_data['overall_statistics']
            comparison = precise_result.get('comparison', {})

            summary_lines = []
            summary_lines.append("🎯 精确类型分析摘要报告")
            summary_lines.append("=" * 50)

            # 基本统计
            summary_lines.append(f"📊 基本统计:")
            summary_lines.append(f"  总代码行: {overall_stats['total_lines']}")
            summary_lines.append(f"  总节点数: {overall_stats['total_nodes']}")
            summary_lines.append(f"  变量声明: {overall_stats['total_variables']}")
            summary_lines.append(f"  中间结果: {overall_stats['total_intermediate_results']}")

            # 类型分析
            summary_lines.append(f"\n🔍 类型分析:")
            summary_lines.append(f"  类型转换: {overall_stats['total_type_conversions']}")
            summary_lines.append(f"  精度问题: {overall_stats['total_precision_issues']}")
            summary_lines.append(f"  准确性评分: {overall_stats['precision_accuracy_score']:.1f}%")

            # 类型分布
            if overall_stats['type_distribution']:
                summary_lines.append(f"\n🎨 类型分布:")
                for type_name, count in sorted(overall_stats['type_distribution'].items()):
                    percentage = count / overall_stats['total_nodes'] * 100
                    summary_lines.append(f"  {type_name}: {count} ({percentage:.1f}%)")

            # 分析对比
            if comparison:
                improvement = comparison.get('operation_count_improvement', {})
                if improvement:
                    summary_lines.append(f"\n📈 分析改进:")
                    summary_lines.append(f"  原始方法识别: {improvement['original']} 个操作")
                    summary_lines.append(f"  精确方法识别: {improvement['precise']} 个节点")
                    summary_lines.append(f"  精度提升: +{improvement['improvement']} 个节点 ({improvement['improvement_percentage']:.1f}%)")

            # 性能建议
            summary_lines.append(f"\n💡 性能建议:")
            if overall_stats['total_type_conversions'] > 0:
                summary_lines.append(f"  ⚠️  发现 {overall_stats['total_type_conversions']} 个类型转换，建议优化")
            if overall_stats['total_precision_issues'] > 0:
                summary_lines.append(f"  ⚠️  发现 {overall_stats['total_precision_issues']} 个混合精度问题，建议统一精度")
            if overall_stats['total_type_conversions'] == 0 and overall_stats['total_precision_issues'] == 0:
                summary_lines.append(f"  ✅ 代码类型使用良好，无明显性能问题")

            return "\n".join(summary_lines)
        else:
            # 回退到原有分析结果
            return self.report_generator.generate_summary_report(precise_result.get('original_analysis', {}))

    def _save_precise_reports(self, precise_result: Dict, shader_content: str, base_filename: str) -> Dict[str, str]:
        """保存精确分析报告文件"""
        file_paths = {}

        try:
            # 保存精确分析的JSON报告
            json_filename = f"{base_filename}_precise.json"
            with open(json_filename, 'w', encoding='utf-8') as f:
                import json
                json.dump(precise_result, f, indent=2, ensure_ascii=False, default=str)
            file_paths['json'] = json_filename

            # 保存精确分析的HTML报告
            html_filename = f"{base_filename}_precise.html"
            html_content = self._generate_precise_html_report(precise_result, shader_content)
            with open(html_filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            file_paths['html'] = html_filename

            # 保存文本摘要报告
            txt_filename = f"{base_filename}_precise_summary.txt"
            with open(txt_filename, 'w', encoding='utf-8') as f:
                f.write(self._generate_precise_summary_report(precise_result))
            file_paths['summary'] = txt_filename

        except Exception as e:
            raise Exception(f"保存精确分析报告失败: {str(e)}")

        return file_paths

    def _generate_precise_html_report(self, precise_result: Dict, shader_content: str) -> str:
        """生成精确分析的HTML报告"""
        if 'precise_analysis' not in precise_result:
            # 如果没有精确分析结果，使用原有方法
            return self.report_generator.generate_html_report(
                precise_result.get('original_analysis', {}),
                shader_content
            )

        precise_data = precise_result['precise_analysis']
        overall_stats = precise_data['overall_statistics']

        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精确着色器类型分析报告</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #1e1e1e; color: #d4d4d4; }}
        .container {{ max-width: 1200px; margin: 0 auto; }}
        .header {{ background: #2d2d30; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
        .stats-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin-bottom: 20px; }}
        .stat-card {{ background: #252526; padding: 15px; border-radius: 6px; border-left: 4px solid #007acc; }}
        .stat-value {{ font-size: 24px; font-weight: bold; color: #4fc3f7; }}
        .stat-label {{ color: #cccccc; margin-top: 5px; }}
        .code-section {{ background: #252526; border-radius: 8px; overflow: hidden; margin-bottom: 20px; }}
        .code-header {{ background: #2d2d30; padding: 15px; border-bottom: 1px solid #3c3c3c; }}
        .code-line {{ display: flex; padding: 8px 0; border-bottom: 1px solid #2d2d30; }}
        .line-number {{ width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; }}
        .line-content {{ flex: 1; padding-left: 15px; font-family: 'Consolas', monospace; }}
        .node-info {{ font-size: 0.85em; color: #ffc107; margin-left: 10px; }}
        .intermediate {{ color: #ff9800; }}
        .direct {{ color: #4caf50; }}
        .type-conversion {{ background-color: rgba(255, 193, 7, 0.2); }}
        .precision-issue {{ background-color: rgba(244, 67, 54, 0.2); }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 精确着色器类型分析报告</h1>
            <p>基于语法树的精确类型推断分析</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{overall_stats['total_nodes']}</div>
                <div class="stat-label">总节点数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{overall_stats['total_variables']}</div>
                <div class="stat-label">变量声明</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{overall_stats['total_intermediate_results']}</div>
                <div class="stat-label">中间结果</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{overall_stats['precision_accuracy_score']:.1f}%</div>
                <div class="stat-label">准确性评分</div>
            </div>
        </div>
"""

        # 添加简化的代码行分析
        if 'code_lines' in precise_data and 'precise_analyses' in precise_data:
            html_content += """
        <div class="code-section">
            <div class="code-header">
                <h3>📝 运算统计分析</h3>
            </div>
"""

            for i, (code_line, analysis) in enumerate(zip(precise_data['code_lines'], precise_data['precise_analyses'])):
                # 统计运算次数
                operation_count = 0
                conversion_count = len(analysis.type_conversions)

                # 计算运算次数（运算符节点 + 函数调用节点）
                for typed_node in analysis.typed_nodes:
                    node_type = typed_node.node.node_type.value
                    if node_type in ['operator', 'function', 'assignment']:
                        operation_count += 1

                # 构建简化的统计信息
                stats_info = f"运算: {operation_count}次"
                if conversion_count > 0:
                    stats_info += f" | 类型转换: {conversion_count}次"

                # 检查是否有问题
                css_class = ""
                if analysis.type_conversions:
                    css_class += "type-conversion "
                if analysis.precision_issues:
                    css_class += "precision-issue "

                html_content += f"""
            <div class="code-line {css_class}">
                <div class="line-number">{code_line.line_number}</div>
                <div class="line-content">
                    {code_line.content}
                    <div class="node-info">{stats_info}</div>
                </div>
            </div>
"""

        html_content += """
        </div>
    </div>
</body>
</html>
"""

        return html_content

    def get_analysis_summary(self, analysis_result: Dict) -> str:
        """获取分析摘要"""
        return self.report_generator.generate_summary_report(analysis_result)
    
    def generate_html_report(self, analysis_result: Dict, shader_content: str = "", max_lines: int = None, display_options: Dict = None) -> str:
        """生成HTML报告"""
        return self.report_generator.generate_html_report(analysis_result, shader_content, max_lines, display_options)
    
    def get_key_metrics(self, analysis_result: Dict) -> Dict:
        """获取关键指标"""
        stats = analysis_result['statistics']
        
        return {
            'total_operations': stats['total_operations'],
            'precision_conversions': stats['precision_conversions'],
            'mixed_precision_ops': stats['mixed_precision_ops'],
            'high_impact_ops': stats['high_impact_ops'],
            'texture_samples': stats['texture_samples'],
            'conversion_ratio': stats['precision_conversions'] / stats['total_operations'] if stats['total_operations'] > 0 else 0,
            'performance_score': self._calculate_performance_score(stats)
        }
    
    def _calculate_performance_score(self, stats: Dict) -> int:
        """计算性能评分 (0-100)"""
        score = 100
        
        # 混合精度运算扣分
        if stats['mixed_precision_ops'] > 0:
            score -= min(stats['mixed_precision_ops'] * 2, 30)
        
        # 类型转换扣分
        conversion_ratio = stats['precision_conversions'] / stats['total_operations'] if stats['total_operations'] > 0 else 0
        if conversion_ratio > 0.2:
            score -= min((conversion_ratio - 0.2) * 100, 25)
        
        # 高影响操作扣分
        if stats['high_impact_ops'] > 10:
            score -= min((stats['high_impact_ops'] - 10) * 1.5, 20)
        
        # 纹理采样扣分
        if stats['texture_samples'] > 30:
            score -= min((stats['texture_samples'] - 30) * 0.5, 15)
        
        return max(0, int(score))
    
    def get_optimization_suggestions(self, analysis_result: Dict) -> List[str]:
        """获取优化建议列表"""
        stats = analysis_result['statistics']
        suggestions = []
        
        if stats['mixed_precision_ops'] > 10:
            suggestions.append("发现大量混合精度运算，建议统一使用half或float")
        
        if stats['precision_conversions'] > 100:
            suggestions.append("频繁的精度转换可能影响性能，考虑减少不必要的转换")
        
        if stats['texture_samples'] > 20:
            suggestions.append("大量纹理采样操作，考虑优化纹理访问模式")
        
        conversion_ratio = stats['precision_conversions'] / stats['total_operations'] if stats['total_operations'] > 0 else 0
        if conversion_ratio > 0.3:
            suggestions.append(f"类型转换比例过高({conversion_ratio:.1%})，建议重构数据类型设计")
        
        if not suggestions:
            suggestions.append("代码质量良好，暂无明显优化建议")
        
        return suggestions
