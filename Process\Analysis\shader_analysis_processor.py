#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
着色器分析处理器 - 统一的分析接口
"""

from typing import Dict, Optional, List
from .shader_analyzer_core import ShaderAnalyzerCore
from .report_generator import ReportGenerator

class ShaderAnalysisProcessor:
    """着色器分析处理器 - 提供统一的分析接口"""
    
    def __init__(self):
        self.analyzer = ShaderAnalyzerCore()
        self.report_generator = ReportGenerator()
    
    def analyze_shader(self, shader_content: str, save_reports: bool = True, base_filename: str = "shader_analysis") -> Dict:
        """
        分析着色器内容
        
        Args:
            shader_content: 着色器源代码内容
            save_reports: 是否保存报告文件
            base_filename: 报告文件基础名称
            
        Returns:
            包含分析结果和报告路径的字典
        """
        # 执行分析
        analysis_result = self.analyzer.analyze_shader_content(shader_content)
        
        # 生成简要报告
        summary_report = self.report_generator.generate_summary_report(analysis_result)
        
        result = {
            'analysis': analysis_result,
            'summary': summary_report,
            'files': {}
        }
        
        # 如果需要保存报告文件
        if save_reports:
            try:
                file_paths = self.report_generator.save_reports(
                    analysis_result,
                    shader_content,
                    base_filename
                )
                result['files'] = file_paths
            except Exception as e:
                result['error'] = f"保存报告文件时出错: {str(e)}"

        # 保存着色器内容供后续使用
        result['shader_content'] = shader_content
        
        return result
    
    def get_analysis_summary(self, analysis_result: Dict) -> str:
        """获取分析摘要"""
        return self.report_generator.generate_summary_report(analysis_result)
    
    def generate_html_report(self, analysis_result: Dict, shader_content: str = "") -> str:
        """生成HTML报告"""
        return self.report_generator.generate_html_report(analysis_result, shader_content)
    
    def get_key_metrics(self, analysis_result: Dict) -> Dict:
        """获取关键指标"""
        stats = analysis_result['statistics']
        
        return {
            'total_operations': stats['total_operations'],
            'precision_conversions': stats['precision_conversions'],
            'mixed_precision_ops': stats['mixed_precision_ops'],
            'high_impact_ops': stats['high_impact_ops'],
            'texture_samples': stats['texture_samples'],
            'conversion_ratio': stats['precision_conversions'] / stats['total_operations'] if stats['total_operations'] > 0 else 0,
            'performance_score': self._calculate_performance_score(stats)
        }
    
    def _calculate_performance_score(self, stats: Dict) -> int:
        """计算性能评分 (0-100)"""
        score = 100
        
        # 混合精度运算扣分
        if stats['mixed_precision_ops'] > 0:
            score -= min(stats['mixed_precision_ops'] * 2, 30)
        
        # 类型转换扣分
        conversion_ratio = stats['precision_conversions'] / stats['total_operations'] if stats['total_operations'] > 0 else 0
        if conversion_ratio > 0.2:
            score -= min((conversion_ratio - 0.2) * 100, 25)
        
        # 高影响操作扣分
        if stats['high_impact_ops'] > 10:
            score -= min((stats['high_impact_ops'] - 10) * 1.5, 20)
        
        # 纹理采样扣分
        if stats['texture_samples'] > 30:
            score -= min((stats['texture_samples'] - 30) * 0.5, 15)
        
        return max(0, int(score))
    
    def get_optimization_suggestions(self, analysis_result: Dict) -> List[str]:
        """获取优化建议列表"""
        stats = analysis_result['statistics']
        suggestions = []
        
        if stats['mixed_precision_ops'] > 10:
            suggestions.append("发现大量混合精度运算，建议统一使用half或float")
        
        if stats['precision_conversions'] > 100:
            suggestions.append("频繁的精度转换可能影响性能，考虑减少不必要的转换")
        
        if stats['texture_samples'] > 20:
            suggestions.append("大量纹理采样操作，考虑优化纹理访问模式")
        
        conversion_ratio = stats['precision_conversions'] / stats['total_operations'] if stats['total_operations'] > 0 else 0
        if conversion_ratio > 0.3:
            suggestions.append(f"类型转换比例过高({conversion_ratio:.1%})，建议重构数据类型设计")
        
        if not suggestions:
            suggestions.append("代码质量良好，暂无明显优化建议")
        
        return suggestions
