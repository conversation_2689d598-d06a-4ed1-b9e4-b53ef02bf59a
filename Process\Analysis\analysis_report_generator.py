#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告生成器 - 基于AST的精确分析报告
"""

import json
import os
import re
import tempfile
from typing import Dict, List, Optional
from datetime import datetime
from dataclasses import dataclass
from templates.html_template import HTML_TEMPLATE_HEADER, HTML_TEMPLATE_FOOTER
@dataclass
class CodeLineData:
    """代码行数据结构"""
    line_number: int
    content: str
    operation_count: int = 0
    conversion_count: int = 0
    precision_issues: int = 0
    node_details: List[str] = None
    operation_types: List[str] = None
    has_analysis: bool = False
    css_classes: List[str] = None
    performance_level: str = "normal"  # normal, intensive, conversion, issue
    
    def __post_init__(self):
        if self.node_details is None:
            self.node_details = []
        if self.operation_types is None:
            self.operation_types = []
        if self.css_classes is None:
            self.css_classes = []
    
    def get_stats_text(self, show_details: bool = True) -> str:
        """获取统计信息文本"""
        if not self.has_analysis or self.operation_count == 0:
            return ""
        
        stats = f"运算: {self.operation_count}次"
        if self.conversion_count > 0:
            stats += f" | 类型转换: {self.conversion_count}次"
        
        if show_details and self.operation_types:
            unique_ops = list(set(self.operation_types))
            if unique_ops:
                stats += f" | 操作: {', '.join(unique_ops)}"
        
        return stats
    
    def get_color_style(self, use_colors: bool = True) -> str:
        """获取颜色样式"""
        if not use_colors or not self.has_analysis:
            return ""
        
        if self.conversion_count > 0:
            return "border-left: 3px solid #f44336;"  # 红色 - 类型转换
        elif self.operation_count > 5:
            return "border-left: 3px solid #ff9800;"  # 橙色 - 运算密集
        elif self.operation_count > 0:
            return "border-left: 3px solid #4caf50;"  # 绿色 - 正常运算
        
        return ""

class AnalysisReportGenerator:
    """报告生成器 - 基于AST的精确分析报告"""
    
    def __init__(self):
        self.temp_dir = tempfile.gettempdir()
    
    def generate_precise_html_report(self, precise_data: Dict, code_lines_data: List[CodeLineData]) -> str:
        """生成精确分析的HTML报告"""
        overall_stats = precise_data['overall_statistics']
        
        # 计算统计数据
        total_lines = len(code_lines_data)
        analyzed_lines = sum(1 for line in code_lines_data if line.operation_count > 0)
        total_operations = sum(line.operation_count for line in code_lines_data)
        total_conversions = sum(line.conversion_count for line in code_lines_data)
        total_precision_issues = sum(line.precision_issues for line in code_lines_data)
        total_nodes = overall_stats.get('total_nodes', 0)
        precision_score = overall_stats.get('precision_accuracy_score', 0)
        analysis_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
 
        # 使用模板生成HTML头部
        html_content = HTML_TEMPLATE_HEADER.format(
            total_lines=total_lines,
            analysis_time=analysis_time,
            total_nodes=total_nodes,
            analyzed_lines=analyzed_lines,
            total_operations=total_operations,
            total_conversions=total_conversions,
            total_precision_issues=total_precision_issues,
            precision_score=precision_score
        )
        # 生成所有代码行
        for line_data in code_lines_data:
            css_classes = []
            
            # 添加基础CSS类
            if line_data.css_classes:
                css_classes.extend(line_data.css_classes)
            
            # 添加性能级别类
            if line_data.has_analysis and line_data.operation_count > 0:
                css_classes.append(f"performance-{line_data.performance_level}")
            
            # 数据属性
            data_attrs = []
            data_attrs.append(f'data-has-analysis="{str(line_data.has_analysis).lower()}"')
            data_attrs.append(f'data-performance="{line_data.performance_level}"')
            data_attrs.append(f'data-operations="{line_data.operation_count}"')
            data_attrs.append(f'data-conversions="{line_data.conversion_count}"')
            
            css_class_str = ' '.join(css_classes)
            data_attr_str = ' '.join(data_attrs)
            
            html_content += f"""
            <div class="code-line {css_class_str}" {data_attr_str}>
                <div class="line-number">{line_data.line_number}</div>
                <div class="line-content">
                    {line_data.content}
                    <div class="node-info node-details">{line_data.get_stats_text(True)}</div>
                </div>
            </div>
            """

        # 添加HTML尾部
        html_content += HTML_TEMPLATE_FOOTER
        
        return html_content