{"original_analysis": {"operations": [{"line": 2, "type": "arithmetic", "detail": "float3 pos = worldMatrix * localPos;", "precision_conversion": null, "performance_impact": "low", "output_type": null, "variable_info": null}, {"line": 2, "type": "assignment", "detail": "声明float变量: pos", "precision_conversion": null, "performance_impact": "low", "output_type": "float3", "variable_info": null}, {"line": 2, "type": "variable_analysis", "detail": "float3 pos = worldMatrix * localPos;", "precision_conversion": null, "performance_impact": "info", "output_type": null, "variable_info": {"total_variables": 3, "float_variables": 1, "half_variables": 0, "type_conversions": 0, "variables_by_type": {"float3": ["pos"]}, "type_conversion_details": [], "all_variables": ["pos", "localPos", "worldMatrix"]}}, {"line": 3, "type": "texture_sample", "detail": "texture.sample(sampler, uv)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4", "variable_info": null}, {"line": 3, "type": "arithmetic", "detail": "half4 color = texture.sample(sampler, uv);", "precision_conversion": null, "performance_impact": "low", "output_type": null, "variable_info": null}, {"line": 3, "type": "assignment", "detail": "声明half变量: color", "precision_conversion": null, "performance_impact": "low", "output_type": "half4", "variable_info": null}, {"line": 3, "type": "variable_analysis", "detail": "half4 color = texture.sample(sampler, uv);", "precision_conversion": null, "performance_impact": "info", "output_type": null, "variable_info": {"total_variables": 4, "float_variables": 0, "half_variables": 1, "type_conversions": 0, "variables_by_type": {"half4": ["color"]}, "type_conversion_details": [], "all_variables": ["color", "uv", "sampler", "texture.sample"]}}, {"line": 4, "type": "function_call", "detail": "dot(normal, lightDir)", "precision_conversion": null, "performance_impact": "low", "output_type": null, "variable_info": null}, {"line": 4, "type": "arithmetic", "detail": "float intensity = dot(normal, lightDir);", "precision_conversion": null, "performance_impact": "low", "output_type": null, "variable_info": null}, {"line": 4, "type": "assignment", "detail": "声明float变量: intensity", "precision_conversion": null, "performance_impact": "low", "output_type": "float", "variable_info": null}, {"line": 4, "type": "variable_analysis", "detail": "float intensity = dot(normal, lightDir);", "precision_conversion": null, "performance_impact": "info", "output_type": null, "variable_info": {"total_variables": 3, "float_variables": 1, "half_variables": 0, "type_conversions": 0, "variables_by_type": {"float": ["intensity"]}, "type_conversion_details": [], "all_variables": ["intensity", "normal", "lightDir"]}}, {"line": 5, "type": "type_conversion", "detail": "half(intensity)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half", "variable_info": null}, {"line": 5, "type": "arithmetic", "detail": "half3 result = color.rgb * half(intensity);", "precision_conversion": null, "performance_impact": "low", "output_type": null, "variable_info": null}, {"line": 5, "type": "assignment", "detail": "声明half变量: result", "precision_conversion": null, "performance_impact": "low", "output_type": "half3", "variable_info": null}, {"line": 5, "type": "variable_analysis", "detail": "half3 result = color.rgb * half(intensity);", "precision_conversion": null, "performance_impact": "info", "output_type": null, "variable_info": {"total_variables": 3, "float_variables": 0, "half_variables": 1, "type_conversions": 1, "variables_by_type": {"half3": ["result"]}, "type_conversion_details": [{"target_type": "half", "source_expression": "intensity", "line_position": 27}], "all_variables": ["result", "intensity", "color.rgb"]}}], "statistics": {"total_operations": 15, "operation_types": {"arithmetic": 4, "assignment": 4, "variable_analysis": 4, "texture_sample": 1, "function_call": 1, "type_conversion": 1}, "precision_conversions": 1, "mixed_precision_ops": 0, "high_impact_ops": 1, "texture_samples": 1}, "metadata": {"analysis_time": "2025-08-01T18:11:47.376949", "total_lines": 6, "analyzer_version": "1.0"}}, "precise_analysis": {"code_lines": ["CodeLineInfo(line_number=2, content='float3 pos = worldMatrix * localPos;', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['pos', 'localPos', 'worldMatrix'], conversion_details=[])", "CodeLineInfo(line_number=3, content='half4 color = texture.sample(sampler, uv);', variables=4, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['color', 'uv', 'sampler', 'texture.sample'], conversion_details=[])", "CodeLineInfo(line_number=4, content='float intensity = dot(normal, lightDir);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['intensity', 'normal', 'lightDir'], conversion_details=[])", "CodeLineInfo(line_number=5, content='half3 result = color.rgb * half(intensity);', variables=3, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['result', 'intensity', 'color.rgb'], conversion_details=[{'target_type': 'half', 'source_expression': 'intensity', 'line_position': 27}])"], "syntax_trees": ["declaration(float3 pos:float3)", "declaration(half4 color:half4)", "declaration(float intensity:float)", "declaration(half3 result:half3)"], "precise_analyses": ["<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x0000025B8AED16A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x0000025B8AED2570>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x0000025B8AED1AC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x0000025B8AED2690>"], "global_type_table": {"pos": "DataType.FLOAT3", "color": "DataType.HALF4", "intensity": "DataType.FLOAT", "result": "DataType.HALF3"}, "overall_statistics": {"total_lines": 4, "total_nodes": 15, "total_variables": 4, "total_intermediate_results": 4, "total_type_conversions": 1, "total_precision_issues": 0, "type_distribution": {"unknown": 6, "float3": 1, "half4": 1, "float": 3, "half3": 3, "half": 1}, "precision_accuracy_score": 40.0}}, "compatible_operations": [{"line": 2, "type": "variable_analysis", "detail": "variable(worldMatrix) → unknown", "precision_conversion": null, "performance_impact": "low", "output_type": "unknown", "variable_info": null, "is_intermediate": false, "confidence": 0.0, "node_info": {"node_type": "variable", "value": "worldMatrix", "inferred_type": "unknown", "is_intermediate": false}}, {"line": 2, "type": "variable_analysis", "detail": "variable(localPos) → unknown", "precision_conversion": null, "performance_impact": "low", "output_type": "unknown", "variable_info": null, "is_intermediate": false, "confidence": 0.0, "node_info": {"node_type": "variable", "value": "localPos", "inferred_type": "unknown", "is_intermediate": false}}, {"line": 2, "type": "arithmetic", "detail": "operator(*) → unknown", "precision_conversion": null, "performance_impact": "low", "output_type": "unknown", "variable_info": null, "is_intermediate": true, "confidence": 0.0, "node_info": {"node_type": "operator", "value": "*", "inferred_type": "unknown", "is_intermediate": true}}, {"line": 2, "type": "assignment", "detail": "declaration(float3 pos) → float3", "precision_conversion": null, "performance_impact": "low", "output_type": "float3", "variable_info": null, "is_intermediate": false, "confidence": 1.0, "node_info": {"node_type": "declaration", "value": "float3 pos", "inferred_type": "float3", "is_intermediate": false}}, {"line": 3, "type": "variable_analysis", "detail": "literal(texture.sample(sampler, uv)) → unknown", "precision_conversion": null, "performance_impact": "low", "output_type": "unknown", "variable_info": null, "is_intermediate": false, "confidence": 0.0, "node_info": {"node_type": "literal", "value": "texture.sample(sampler, uv)", "inferred_type": "unknown", "is_intermediate": false}}, {"line": 3, "type": "assignment", "detail": "declaration(half4 color) → half4", "precision_conversion": null, "performance_impact": "low", "output_type": "half4", "variable_info": null, "is_intermediate": false, "confidence": 1.0, "node_info": {"node_type": "declaration", "value": "half4 color", "inferred_type": "half4", "is_intermediate": false}}, {"line": 4, "type": "variable_analysis", "detail": "variable(normal) → unknown", "precision_conversion": null, "performance_impact": "low", "output_type": "unknown", "variable_info": null, "is_intermediate": false, "confidence": 0.0, "node_info": {"node_type": "variable", "value": "normal", "inferred_type": "unknown", "is_intermediate": false}}, {"line": 4, "type": "variable_analysis", "detail": "variable(lightDir) → unknown", "precision_conversion": null, "performance_impact": "low", "output_type": "unknown", "variable_info": null, "is_intermediate": false, "confidence": 0.0, "node_info": {"node_type": "variable", "value": "lightDir", "inferred_type": "unknown", "is_intermediate": false}}, {"line": 4, "type": "function_call", "detail": "function(dot) → float", "precision_conversion": null, "performance_impact": "low", "output_type": "float", "variable_info": null, "is_intermediate": true, "confidence": 0.6, "node_info": {"node_type": "function", "value": "dot", "inferred_type": "float", "is_intermediate": true}}, {"line": 4, "type": "assignment", "detail": "declaration(float intensity) → float", "precision_conversion": null, "performance_impact": "low", "output_type": "float", "variable_info": null, "is_intermediate": false, "confidence": 1.0, "node_info": {"node_type": "declaration", "value": "float intensity", "inferred_type": "float", "is_intermediate": false}}, {"line": 5, "type": "vector_op", "detail": "member_access(color.rgb) → half3", "precision_conversion": null, "performance_impact": "low", "output_type": "half3", "variable_info": null, "is_intermediate": false, "confidence": 0.6, "node_info": {"node_type": "member_access", "value": "color.rgb", "inferred_type": "half3", "is_intermediate": false}}, {"line": 5, "type": "variable_analysis", "detail": "variable(intensity) → float", "precision_conversion": null, "performance_impact": "low", "output_type": "float", "variable_info": null, "is_intermediate": false, "confidence": 0.8, "node_info": {"node_type": "variable", "value": "intensity", "inferred_type": "float", "is_intermediate": false}}, {"line": 5, "type": "type_conversion", "detail": "type_cast(half) → half", "precision_conversion": "float → half", "performance_impact": "medium", "output_type": "half", "variable_info": null, "is_intermediate": true, "confidence": 1.0, "node_info": {"node_type": "type_cast", "value": "half", "inferred_type": "half", "is_intermediate": true}}, {"line": 5, "type": "arithmetic", "detail": "operator(*) → half3", "precision_conversion": null, "performance_impact": "low", "output_type": "half3", "variable_info": null, "is_intermediate": true, "confidence": 0.6, "node_info": {"node_type": "operator", "value": "*", "inferred_type": "half3", "is_intermediate": true}}, {"line": 5, "type": "assignment", "detail": "declaration(half3 result) → half3", "precision_conversion": null, "performance_impact": "low", "output_type": "half3", "variable_info": null, "is_intermediate": false, "confidence": 1.0, "node_info": {"node_type": "declaration", "value": "half3 result", "inferred_type": "half3", "is_intermediate": false}}], "enhanced_statistics": {"total_operations": 15, "total_nodes": 15, "total_variables": 4, "total_intermediate_results": 4, "total_type_conversions": 1, "total_precision_issues": 0, "precision_accuracy_score": 40.0, "type_distribution": {"unknown": 6, "float3": 1, "half4": 1, "float": 3, "half3": 3, "half": 1}, "operation_types": {"variable_analysis": 6, "arithmetic": 2, "assignment": 4, "function_call": 1, "vector_op": 1, "type_conversion": 1}, "precision_conversions": 1, "mixed_precision_ops": 0, "high_impact_ops": 0, "intermediate_result_ops": 4}, "comparison": {"operation_count_improvement": {"original": 15, "precise": 15, "improvement": 0, "improvement_percentage": 0.0}, "precision_analysis": {"intermediate_results_detected": 4, "type_conversions_detected": 1, "precision_issues_detected": 0, "accuracy_score": 40.0}, "analysis_quality": {"original_method": "line_based_pattern_matching", "precise_method": "syntax_tree_based_type_inference", "confidence_level": "medium"}}, "metadata": {"analysis_time": "2025-08-01T18:11:47.376949", "total_lines": 6, "analyzer_version": "2.0", "analysis_method": "precise_tree_based"}}