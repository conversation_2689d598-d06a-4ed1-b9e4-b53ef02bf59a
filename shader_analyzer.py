#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Metal着色器分析器 - 专注于half和float数据类型运算分析
"""

import re
import json
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

class OperationType(Enum):
    """运算类型枚举"""
    ASSIGNMENT = "assignment"           # 赋值
    ARITHMETIC = "arithmetic"          # 算术运算 (+, -, *, /)
    COMPARISON = "comparison"          # 比较运算 (<, >, ==, !=, <=, >=)
    FUNCTION_CALL = "function_call"    # 函数调用
    TYPE_CONVERSION = "type_conversion" # 类型转换
    VECTOR_OPERATION = "vector_op"     # 向量运算
    MATRIX_OPERATION = "matrix_op"     # 矩阵运算
    TEXTURE_SAMPLE = "texture_sample"  # 纹理采样
    CONDITIONAL = "conditional"        # 条件运算
    MIXED_PRECISION = "mixed_precision" # 混合精度运算

@dataclass
class DataTypeInfo:
    """数据类型信息"""
    base_type: str      # half, float
    dimension: int      # 1=标量, 2=vec2, 3=vec3, 4=vec4
    is_matrix: bool = False
    matrix_size: Optional[Tuple[int, int]] = None

@dataclass
class OperationInfo:
    """运算信息"""
    line_number: int
    operation_type: OperationType
    input_types: List[DataTypeInfo]
    output_type: Optional[DataTypeInfo]
    operation_detail: str
    precision_conversion: Optional[str] = None  # 精度转换信息
    performance_impact: str = "low"  # low, medium, high

class MetalShaderAnalyzer:
    """Metal着色器分析器"""
    
    def __init__(self):
        # 数据类型模式
        self.type_patterns = {
            'half': r'\bhalf\b',
            'half2': r'\bhalf2\b',
            'half3': r'\bhalf3\b', 
            'half4': r'\bhalf4\b',
            'float': r'\bfloat\b',
            'float2': r'\bfloat2\b',
            'float3': r'\bfloat3\b',
            'float4': r'\bfloat4\b',
            'float3x3': r'\bfloat3x3\b',
            'float3x4': r'\bfloat3x4\b',
            'float4x4': r'\bfloat4x4\b',
        }
        
        # 运算符模式
        self.operator_patterns = {
            'arithmetic': r'[+\-*/]',
            'comparison': r'[<>=!]=?',
            'assignment': r'=(?!=)',
        }
        
        # 函数调用模式
        self.function_patterns = {
            'math_functions': r'\b(sin|cos|tan|sqrt|rsqrt|pow|powr|exp|exp2|log|log2|abs|floor|ceil|fract|clamp|mix|dot|cross|normalize|length|distance|reflect|refract|step|smoothstep|min|max|fast::|select)\b',
            'texture_functions': r'\b\w+\.sample\(',
            'type_conversion': r'\b(half|float|half2|half3|half4|float2|float3|float4)\s*\(',
        }
        
        self.operations = []
    
    def parse_data_type(self, type_str: str) -> Optional[DataTypeInfo]:
        """解析数据类型字符串"""
        type_str = type_str.strip()
        
        # 矩阵类型
        matrix_match = re.match(r'(float|half)(\d+)x(\d+)', type_str)
        if matrix_match:
            base_type = matrix_match.group(1)
            rows = int(matrix_match.group(2))
            cols = int(matrix_match.group(3))
            return DataTypeInfo(base_type, 1, True, (rows, cols))
        
        # 向量类型
        vector_match = re.match(r'(half|float)(\d+)', type_str)
        if vector_match:
            base_type = vector_match.group(1)
            dimension = int(vector_match.group(2))
            return DataTypeInfo(base_type, dimension)
        
        # 标量类型
        if type_str in ['half', 'float']:
            return DataTypeInfo(type_str, 1)
        
        return None
    
    def analyze_line(self, line: str, line_number: int) -> List[OperationInfo]:
        """分析单行代码"""
        operations = []
        line = line.strip()
        
        if not line or line.startswith('//') or line.startswith('#'):
            return operations
        
        # 检测类型转换
        type_conversion_matches = re.finditer(r'\b(half|float|half2|half3|half4|float2|float3|float4)\s*\([^)]+\)', line)
        for match in type_conversion_matches:
            target_type = self.parse_data_type(match.group(1))
            if target_type:
                operations.append(OperationInfo(
                    line_number=line_number,
                    operation_type=OperationType.TYPE_CONVERSION,
                    input_types=[],
                    output_type=target_type,
                    operation_detail=match.group(0),
                    precision_conversion=f"转换为{target_type.base_type}",
                    performance_impact="medium" if target_type.base_type != "float" else "low"
                ))
        
        # 检测纹理采样
        texture_matches = re.finditer(r'(\w+)\.sample\([^)]+\)', line)
        for match in texture_matches:
            # 纹理采样通常返回half4或float4
            output_type = DataTypeInfo("half", 4)  # 假设为half4
            operations.append(OperationInfo(
                line_number=line_number,
                operation_type=OperationType.TEXTURE_SAMPLE,
                input_types=[],
                output_type=output_type,
                operation_detail=match.group(0),
                performance_impact="high"
            ))
        
        # 检测函数调用
        func_matches = re.finditer(r'\b(fast::)?(normalize|dot|cross|mix|clamp|min|max|sqrt|rsqrt|pow|powr|abs|floor|ceil|fract|sin|cos|tan|exp|exp2|log|log2|step|smoothstep|length|distance|reflect|refract|select)\s*\([^)]+\)', line)
        for match in func_matches:
            func_name = match.group(2) if match.group(1) else match.group(1)
            operations.append(OperationInfo(
                line_number=line_number,
                operation_type=OperationType.FUNCTION_CALL,
                input_types=[],
                output_type=None,
                operation_detail=match.group(0),
                performance_impact="medium" if match.group(1) == "fast::" else "low"
            ))
        
        # 检测算术运算
        arithmetic_matches = re.finditer(r'[+\-*/]', line)
        if arithmetic_matches:
            # 检查是否涉及half和float的混合运算
            has_half = re.search(r'\bhalf\d*\b', line)
            has_float = re.search(r'\bfloat\d*\b', line)
            
            if has_half and has_float:
                operations.append(OperationInfo(
                    line_number=line_number,
                    operation_type=OperationType.MIXED_PRECISION,
                    input_types=[],
                    output_type=None,
                    operation_detail=line,
                    precision_conversion="混合精度运算",
                    performance_impact="high"
                ))
            elif has_half or has_float:
                operations.append(OperationInfo(
                    line_number=line_number,
                    operation_type=OperationType.ARITHMETIC,
                    input_types=[],
                    output_type=None,
                    operation_detail=line,
                    performance_impact="low"
                ))
        
        # 检测变量声明和赋值
        declaration_matches = re.finditer(r'\b(half|float|half2|half3|half4|float2|float3|float4|float3x3|float3x4|float4x4)\s+(\w+)\s*=', line)
        for match in declaration_matches:
            var_type = self.parse_data_type(match.group(1))
            if var_type:
                operations.append(OperationInfo(
                    line_number=line_number,
                    operation_type=OperationType.ASSIGNMENT,
                    input_types=[],
                    output_type=var_type,
                    operation_detail=f"声明{var_type.base_type}变量: {match.group(2)}",
                    performance_impact="low"
                ))
        
        return operations
    
    def analyze_shader_file(self, file_path: str) -> Dict:
        """分析整个着色器文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
        except:
            return {"error": "无法读取文件"}
        
        all_operations = []
        
        for i, line in enumerate(lines, 1):
            line_operations = self.analyze_line(line, i)
            all_operations.extend(line_operations)
        
        # 统计信息
        stats = {
            'total_operations': len(all_operations),
            'operation_types': {},
            'precision_conversions': 0,
            'mixed_precision_ops': 0,
            'high_impact_ops': 0,
            'texture_samples': 0,
        }
        
        for op in all_operations:
            op_type = op.operation_type.value
            stats['operation_types'][op_type] = stats['operation_types'].get(op_type, 0) + 1
            
            if op.precision_conversion:
                stats['precision_conversions'] += 1
            
            if op.operation_type == OperationType.MIXED_PRECISION:
                stats['mixed_precision_ops'] += 1
            
            if op.performance_impact == "high":
                stats['high_impact_ops'] += 1
                
            if op.operation_type == OperationType.TEXTURE_SAMPLE:
                stats['texture_samples'] += 1
        
        return {
            'operations': [
                {
                    'line': op.line_number,
                    'type': op.operation_type.value,
                    'detail': op.operation_detail,
                    'precision_conversion': op.precision_conversion,
                    'performance_impact': op.performance_impact,
                    'output_type': f"{op.output_type.base_type}{op.output_type.dimension if op.output_type.dimension > 1 else ''}" if op.output_type else None
                }
                for op in all_operations
            ],
            'statistics': stats
        }

def generate_detailed_report(result: Dict) -> str:
    """生成详细的分析报告"""
    report = []
    report.append("=" * 80)
    report.append("Metal着色器Half/Float运算分析报告")
    report.append("=" * 80)

    # 统计概览
    stats = result['statistics']
    report.append(f"\n📊 统计概览:")
    report.append(f"  总运算操作数: {stats['total_operations']}")
    report.append(f"  精度转换操作: {stats['precision_conversions']}")
    report.append(f"  混合精度运算: {stats['mixed_precision_ops']}")
    report.append(f"  高性能影响操作: {stats['high_impact_ops']}")
    report.append(f"  纹理采样操作: {stats['texture_samples']}")

    # 运算类型分布
    report.append(f"\n🔍 运算类型分布:")
    for op_type, count in stats['operation_types'].items():
        percentage = (count / stats['total_operations']) * 100
        report.append(f"  {op_type:20}: {count:4} ({percentage:.1f}%)")

    # 关键运算行分析
    report.append(f"\n⚠️  关键运算行分析 (前20个高影响操作):")
    high_impact_ops = [op for op in result['operations'] if op['performance_impact'] == 'high'][:20]

    for i, op in enumerate(high_impact_ops, 1):
        report.append(f"  {i:2}. 行{op['line']:4}: {op['type']:15} - {op['detail'][:60]}...")
        if op['precision_conversion']:
            report.append(f"      转换: {op['precision_conversion']}")

    # 混合精度运算详情
    report.append(f"\n🔄 混合精度运算详情:")
    mixed_ops = [op for op in result['operations'] if op['type'] == 'mixed_precision'][:10]

    for i, op in enumerate(mixed_ops, 1):
        report.append(f"  {i:2}. 行{op['line']:4}: {op['detail'][:70]}...")

    # 类型转换热点
    report.append(f"\n🔀 类型转换热点:")
    conversion_ops = [op for op in result['operations'] if op['type'] == 'type_conversion']
    conversion_lines = {}

    for op in conversion_ops:
        line_range = (op['line'] // 10) * 10  # 按10行分组
        conversion_lines[line_range] = conversion_lines.get(line_range, 0) + 1

    sorted_ranges = sorted(conversion_lines.items(), key=lambda x: x[1], reverse=True)[:10]
    for line_range, count in sorted_ranges:
        report.append(f"  行{line_range:4}-{line_range+9:4}: {count:3}次转换")

    # 性能优化建议
    report.append(f"\n💡 性能优化建议:")
    if stats['mixed_precision_ops'] > 10:
        report.append("  ⚠️  发现大量混合精度运算，建议统一使用half或float")

    if stats['precision_conversions'] > 100:
        report.append("  ⚠️  频繁的精度转换可能影响性能，考虑减少不必要的转换")

    if stats['texture_samples'] > 20:
        report.append("  ⚠️  大量纹理采样操作，考虑优化纹理访问模式")

    conversion_ratio = stats['precision_conversions'] / stats['total_operations']
    if conversion_ratio > 0.3:
        report.append(f"  ⚠️  类型转换比例过高({conversion_ratio:.1%})，建议重构数据类型设计")

    report.append("\n" + "=" * 80)

    return "\n".join(report)

def main():
    analyzer = MetalShaderAnalyzer()
    result = analyzer.analyze_shader_file('metal_shader_ps')

    # 输出结果到JSON文件
    with open('shader_analysis_result.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)

    # 生成详细报告
    report = generate_detailed_report(result)

    # 输出报告到文件
    with open('shader_analysis_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)

    # 控制台输出简要信息
    print("着色器分析完成！")
    print(f"总共发现 {result['statistics']['total_operations']} 个运算操作")
    print(f"精度转换操作: {result['statistics']['precision_conversions']}")
    print(f"混合精度运算: {result['statistics']['mixed_precision_ops']}")
    print(f"高性能影响操作: {result['statistics']['high_impact_ops']}")
    print(f"纹理采样操作: {result['statistics']['texture_samples']}")

    print("\n详细报告已保存到 shader_analysis_report.txt")
    print("完整数据已保存到 shader_analysis_result.json")

if __name__ == "__main__":
    main()
