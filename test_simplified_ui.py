#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化后的UI系统
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_file_structure():
    """测试文件结构"""
    print("📁 测试文件结构")
    print("=" * 50)
    
    # 检查删除的文件
    removed_files = ['ui/shader_analysis_widget.py']
    for file_path in removed_files:
        if not os.path.exists(file_path):
            print(f"   ✅ 已删除: {file_path}")
        else:
            print(f"   ❌ 仍存在: {file_path}")
            return False
    
    # 检查保留的文件
    kept_files = ['ui/precise_type_analysis_widget.py', 'ui/main_window.py']
    for file_path in kept_files:
        if os.path.exists(file_path):
            print(f"   ✅ 保留: {file_path}")
        else:
            print(f"   ❌ 缺失: {file_path}")
            return False
    
    return True

def test_main_window_imports():
    """测试主窗口导入"""
    print("\n🔗 测试主窗口导入")
    print("=" * 50)
    
    try:
        # 检查主窗口文件内容
        with open('ui/main_window.py', 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 检查导入
        if 'from ui.shader_analysis_widget import ShaderAnalysisWidget' in content:
            print("   ❌ 仍包含旧的ShaderAnalysisWidget导入")
            return False
        else:
            print("   ✅ 已移除ShaderAnalysisWidget导入")
        
        if 'from ui.precise_type_analysis_widget import PreciseTypeAnalysisWidget' in content:
            print("   ✅ 保留PreciseTypeAnalysisWidget导入")
        else:
            print("   ❌ 缺少PreciseTypeAnalysisWidget导入")
            return False
        
        # 检查菜单action
        if '🎯 着色器精确类型分析' in content:
            print("   ✅ 菜单action已更新")
        else:
            print("   ❌ 菜单action未更新")
            return False
        
        # 检查方法
        if 'def on_shader_AST_analysis' in content:
            print("   ❌ 仍包含旧的on_shader_AST_analysis方法")
            return False
        else:
            print("   ✅ 已移除on_shader_AST_analysis方法")
        
        if 'PreciseTypeAnalysisWidget()' in content:
            print("   ✅ 使用PreciseTypeAnalysisWidget")
        else:
            print("   ❌ 未使用PreciseTypeAnalysisWidget")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        return False

def test_precise_widget_api():
    """测试精确分析组件API"""
    print("\n🎯 测试精确分析组件API")
    print("=" * 50)
    
    try:
        from ui.precise_type_analysis_widget import PreciseTypeAnalysisWidget
        print("   ✅ PreciseTypeAnalysisWidget 导入成功")
        
        # 检查方法是否存在
        widget = PreciseTypeAnalysisWidget()
        
        # 检查start_analysis方法（应该存在）
        if hasattr(widget, 'start_analysis'):
            print("   ✅ start_analysis 方法存在")
        else:
            print("   ❌ start_analysis 方法不存在")
            return False
        
        # 检查start_precise_analysis方法（应该不存在或已重命名）
        if hasattr(widget, 'start_precise_analysis'):
            print("   ⚠️  start_precise_analysis 方法仍存在（可能需要手动修复）")
        else:
            print("   ✅ start_precise_analysis 方法已移除/重命名")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        return False

def test_analysis_integration():
    """测试分析集成"""
    print("\n🔄 测试分析集成")
    print("=" * 50)
    
    try:
        # 测试分析处理器
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        processor = ShaderAnalysisProcessor()
        print("   ✅ ShaderAnalysisProcessor 导入成功")
        
        # 测试分析功能
        test_code = "float a = b + c;"
        result = processor.analyze_shader(test_code, save_reports=False)
        print("   ✅ 分析功能正常")
        
        # 检查结果结构
        if 'analysis' in result and 'precise_analysis' in result['analysis']:
            print("   ✅ 返回精确分析结果")
        else:
            print("   ❌ 分析结果结构异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 分析集成测试失败: {str(e)}")
        return False

def test_ui_functionality():
    """测试UI功能（模拟）"""
    print("\n🖥️ 测试UI功能")
    print("=" * 50)
    
    try:
        # 测试PyQt5导入
        from PyQt5.QtWidgets import QApplication
        app = QApplication([])
        print("   ✅ QApplication 创建成功")
        
        # 测试主窗口创建
        from ui.main_window import MainWindow
        window = MainWindow()
        print("   ✅ MainWindow 创建成功")
        
        # 测试精确分析组件创建
        from ui.precise_type_analysis_widget import PreciseTypeAnalysisWidget
        widget = PreciseTypeAnalysisWidget()
        print("   ✅ PreciseTypeAnalysisWidget 创建成功")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"   ❌ UI功能测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🎨 简化后的UI系统测试")
    print("=" * 60)
    print("测试内容:")
    print("• 📁 文件结构测试")
    print("• 🔗 主窗口导入测试")
    print("• 🎯 精确分析组件API测试")
    print("• 🔄 分析集成测试")
    print("• 🖥️ UI功能测试")
    print()
    
    # 执行测试
    structure_ok = test_file_structure()
    imports_ok = test_main_window_imports()
    api_ok = test_precise_widget_api()
    integration_ok = test_analysis_integration()
    ui_ok = test_ui_functionality()
    
    print(f"\n🎉 测试结果总结")
    print("=" * 60)
    
    if structure_ok:
        print("✅ 文件结构测试通过")
        print("   • shader_analysis_widget.py 已删除")
        print("   • 保留必要文件")
    else:
        print("❌ 文件结构测试失败")
    
    if imports_ok:
        print("✅ 主窗口导入测试通过")
        print("   • 移除旧导入和方法")
        print("   • 更新菜单和API调用")
    else:
        print("❌ 主窗口导入测试失败")
    
    if api_ok:
        print("✅ 精确分析组件API测试通过")
        print("   • start_analysis 方法可用")
    else:
        print("❌ 精确分析组件API测试失败")
    
    if integration_ok:
        print("✅ 分析集成测试通过")
        print("   • 分析处理器正常")
        print("   • 返回精确分析结果")
    else:
        print("❌ 分析集成测试失败")
    
    if ui_ok:
        print("✅ UI功能测试通过")
        print("   • 主窗口创建正常")
        print("   • 组件创建正常")
    else:
        print("❌ UI功能测试失败")
    
    if all([structure_ok, imports_ok, api_ok, integration_ok, ui_ok]):
        print(f"\n🎊 UI系统简化完成！")
        print("现在系统结构更加简洁:")
        print("• 🗑️ 删除了 shader_analysis_widget.py")
        print("• 🎯 统一使用 precise_type_analysis_widget")
        print("• 🔄 主窗口只有一个分析action")
        print("• 📝 API调用统一为 start_analysis")
        print("• ⚡ 功能完全正常")
        print("\n启动命令: python ui/main_window.py")
    else:
        print(f"\n❌ UI系统简化未完全完成，请检查失败的测试项")

if __name__ == "__main__":
    main()
