{"analysis": {"precise_analysis": {"code_lines": ["CodeLineInfo(line_number=74, content='constant half3 _18526 = {};', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_18526', 'constant'], conversion_details=[])", "CodeLineInfo(line_number=75, content='constant float3 _19185 = {};', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19185', 'constant'], conversion_details=[])", "CodeLineInfo(line_number=76, content='constant half _19493 = {};', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_19493', 'constant'], conversion_details=[])", "CodeLineInfo(line_number=77, content='constant float _19585 = {};', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19585', 'constant'], conversion_details=[])", "CodeLineInfo(line_number=78, content='constant int _19621 = {};', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19621', 'int', 'constant'], conversion_details=[])", "CodeLineInfo(line_number=79, content='constant float3 _21234 = {};', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21234', 'constant'], conversion_details=[])", "CodeLineInfo(line_number=80, content='constant float3 _21295 = {};', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21295', 'constant'], conversion_details=[])", "CodeLineInfo(line_number=81, content='constant half4 _21296 = {};', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_21296', 'constant'], conversion_details=[])", "CodeLineInfo(line_number=82, content='constant half3 _21297 = {};', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_21297', 'constant'], conversion_details=[])", "CodeLineInfo(line_number=86, content='float4 _Ret [[color(0)]];', variables=1, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_Ret'], conversion_details=[])", "CodeLineInfo(line_number=91, content='float4 IN_TexCoord [[user(locn0)]];', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['IN_TexCoord', 'locn0'], conversion_details=[])", "CodeLineInfo(line_number=92, content='float4 IN_WorldPosition [[user(locn1)]];', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['IN_WorldPosition', 'locn1'], conversion_details=[])", "CodeLineInfo(line_number=93, content='half4 IN_WorldNormal [[user(locn2)]];', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['IN_WorldNormal', 'locn2'], conversion_details=[])", "CodeLineInfo(line_number=94, content='half4 IN_WorldTangent [[user(locn3)]];', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['IN_WorldTangent', 'locn3'], conversion_details=[])", "CodeLineInfo(line_number=95, content='half4 IN_WorldBinormal [[user(locn4)]];', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['IN_WorldBinormal', 'locn4'], conversion_details=[])", "CodeLineInfo(line_number=96, content='half4 IN_TintColor [[user(locn5)]];', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['IN_TintColor', 'locn5'], conversion_details=[])", "CodeLineInfo(line_number=97, content='float IN_LinearZ [[user(locn6)]];', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['IN_LinearZ', 'locn6'], conversion_details=[])", "CodeLineInfo(line_number=98, content='half3 IN_LocalPosition [[user(locn7)]];', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['IN_LocalPosition', 'locn7'], conversion_details=[])", "CodeLineInfo(line_number=99, content='half4 IN_StaticWorldNormal [[user(locn8)]];', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['IN_StaticWorldNormal', 'locn8'], conversion_details=[])", "CodeLineInfo(line_number=104, content='constexpr sampler sShadowMapArraySamplerSmplr(filter::linear, mip_filter::linear, compare_func::greater);', variables=7, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['linear', 'compare_func', 'mip_filter', 'sampler', 'greater', 'filter', 'constexpr'], conversion_details=[])", "CodeLineInfo(line_number=105, content='main0_out out = {};', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['out', 'main0_out'], conversion_details=[])", "CodeLineInfo(line_number=106, content='half _9176 = half(0);', variables=1, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_9176'], conversion_details=[])", "CodeLineInfo(line_number=107, content='half3 _9179 = half3(_9176);', variables=2, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_9179', '_9176'], conversion_details=[{'target_type': 'half3', 'source_expression': '_9176', 'line_position': 18}])", "CodeLineInfo(line_number=108, content='half _9199 = half(1);', variables=1, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_9199'], conversion_details=[])", "CodeLineInfo(line_number=109, content='float3 _8921 = fast::normalize(_Block1.CameraPos.xyz - in.IN_WorldPosition.xyz);', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8921', '_Block1.CameraPos.xyz', 'fast', 'in.IN_WorldPosition.xyz'], conversion_details=[])", "CodeLineInfo(line_number=110, content='float3 _8925 = float3(in.IN_WorldNormal.xyz);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_8925', 'in.IN_WorldNormal.xyz'], conversion_details=[{'target_type': 'float3', 'source_expression': 'in.IN_WorldNormal.xyz', 'line_position': 19}])", "CodeLineInfo(line_number=111, content='half _8929 = half(fast::clamp(dot(_8925, _8921), 0.0, 1.0));', variables=4, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_8929', 'fast', '_8925', '_8921'], conversion_details=[{'target_type': 'half', 'source_expression': 'fast::clamp(dot(_8925, _8921', 'line_position': 17}])", "CodeLineInfo(line_number=112, content='half4 _8939 = sBaseSampler.sample(sBaseSamplerSmplr, in.IN_TexCoord.xy);', variables=4, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_8939', 'in.IN_TexCoord.xy', 'sBaseSamplerSmplr', 'sBaseSampler.sample'], conversion_details=[])", "CodeLineInfo(line_number=113, content='half3 _8941 = _8939.xyz;', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_8941', '_8939.xyz'], conversion_details=[])", "CodeLineInfo(line_number=114, content='half3 _8949 = half3(float3(_8941 * _8941) * float3(_Block1.cBaseColor));', variables=3, float_vars=0, half_vars=1, conversions=2, has_conversion=True, all_variables=['_8949', '_Block1.cBaseColor', '_8941'], conversion_details=[{'target_type': 'half3', 'source_expression': 'float3(_8941 * _8941', 'line_position': 18}, {'target_type': 'float3', 'source_expression': '_Block1.cBaseColor', 'line_position': 48}])", "CodeLineInfo(line_number=115, content='float4 _8973 = sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy * _Block1.cNoise1Scale));', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8973', 'sNoiseSampler.sample', 'sNoiseSamplerSmplr', 'in.IN_TexCoord.xy', '_Block1.cNoise1Scale'], conversion_details=[])", "CodeLineInfo(line_number=116, content='half4 _8974 = half4(_8973);', variables=2, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_8974', '_8973'], conversion_details=[{'target_type': 'half4', 'source_expression': '_8973', 'line_position': 18}])", "CodeLineInfo(line_number=117, content='float4 _8982 = sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy * _Block1.cNoise2Scale));', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8982', 'sNoiseSampler.sample', '_Block1.cNoise2Scale', 'sNoiseSamplerSmplr', 'in.IN_TexCoord.xy'], conversion_details=[])", "CodeLineInfo(line_number=118, content='half4 _8983 = half4(_8982);', variables=2, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_8983', '_8982'], conversion_details=[{'target_type': 'half4', 'source_expression': '_8982', 'line_position': 18}])", "CodeLineInfo(line_number=119, content='float _8991 = fast::clamp(powr(float(max(in.IN_TintColor.w, half(9.9956989288330078125e-05))), _Block1.cNoise2Bias), 0.0, 1.0);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_8991', 'fast', 'in.IN_TintColor.w', '_Block1.cNoise2Bias'], conversion_details=[{'target_type': 'float', 'source_expression': 'max(in.IN_TintColor.w, half(9.9956989288330078125e-05', 'line_position': 35}])", "CodeLineInfo(line_number=120, content='half _8994 = _8974.x;', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_8994', '_8974.x'], conversion_details=[])", "CodeLineInfo(line_number=121, content='half _8996 = _8983.x;', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_8996', '_8983.x'], conversion_details=[])", "CodeLineInfo(line_number=122, content='float _9014 = 1.0 - _8991;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9014', '_8991'], conversion_details=[])", "CodeLineInfo(line_number=123, content='half _9019 = half(1.0);', variables=1, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_9019'], conversion_details=[])", "CodeLineInfo(line_number=124, content='if ((float((_8994 * _8996) - half(mix(0.0, 0.5, powr(float(max(_9199 - _8929, half(9.9956989288330078125e-05))), 3.0)))) - 0.100000001490116119384765625) < 0.0)', variables=4, float_vars=0, half_vars=0, conversions=2, has_conversion=True, all_variables=['_8996', '_9199', '_8929', '_8994'], conversion_details=[{'target_type': 'float', 'source_expression': '(_8994 * _8996', 'line_position': 9}, {'target_type': 'half', 'source_expression': 'mix(0.0, 0.5, powr(float(max(_9199 - _8929, half(9.9956989288330078125e-05', 'line_position': 33}])", "CodeLineInfo(line_number=126, content='discard_fragment();', variables=0, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=[], conversion_details=[])", "CodeLineInfo(line_number=128, content='half4 _9248 = sNormalSampler.sample(sNormalSamplerSmplr, in.IN_TexCoord.xy);', variables=4, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_9248', 'in.IN_TexCoord.xy', 'sNormalSampler.sample', 'sNormalSamplerSmplr'], conversion_details=[])", "CodeLineInfo(line_number=129, content='half _9251 = half(2);', variables=1, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_9251'], conversion_details=[])", "CodeLineInfo(line_number=130, content='half3 _9254 = half3(_9199);', variables=2, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_9254', '_9199'], conversion_details=[{'target_type': 'half3', 'source_expression': '_9199', 'line_position': 18}])", "CodeLineInfo(line_number=131, content='half3 _9255 = (_9248.xyz * _9251) - _9254;', variables=4, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_9255', '_9251', '_9254', '_9248.xyz'], conversion_details=[])", "CodeLineInfo(line_number=132, content='half _9257 = _9255.x;', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_9257', '_9255.x'], conversion_details=[])", "CodeLineInfo(line_number=133, content='half _9263 = _9255.y;', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_9263', '_9255.y'], conversion_details=[])", "CodeLineInfo(line_number=134, content='half _9270 = _9255.z;', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_9270', '_9255.z'], conversion_details=[])", "CodeLineInfo(line_number=135, content='float3 _9279 = float3(((in.IN_WorldTangent.xyz * _9257) + (in.IN_WorldBinormal.xyz * _9263)) + (in.IN_WorldNormal.xyz * _9270));', variables=7, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9279', '_9257', 'in.IN_WorldBinormal.xyz', 'in.IN_WorldTangent.xyz', '_9263', 'in.IN_WorldNormal.xyz', '_9270'], conversion_details=[{'target_type': 'float3', 'source_expression': '((in.IN_WorldTangent.xyz * _9257', 'line_position': 19}])", "CodeLineInfo(line_number=136, content='float3 _9286 = float3(in.IN_StaticWorldNormal.xyz);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9286', 'in.IN_StaticWorldNormal.xyz'], conversion_details=[{'target_type': 'float3', 'source_expression': 'in.IN_StaticWorldNormal.xyz', 'line_position': 19}])", "CodeLineInfo(line_number=137, content='float3 _9331 = (((float4(float3(in.IN_WorldTangent.xyz), 0.0) * _Block1.Local) * float(_9257)) + ((float4(float3(in.IN_WorldBinormal.xyz), 0.0) * _Block1.Local) * float(_9263))) + (_9286 * float(_9270));', variables=8, float_vars=1, half_vars=0, conversions=5, has_conversion=True, all_variables=['_9331', '_9286', '_Block1.Local', '_9257', 'in.IN_WorldBinormal.xyz', 'in.IN_WorldTangent.xyz', '_9263', '_9270'], conversion_details=[{'target_type': 'float4', 'source_expression': 'float3(in.IN_WorldTangent.xyz', 'line_position': 22}, {'target_type': 'float', 'source_expression': '_9257', 'line_position': 85}, {'target_type': 'float4', 'source_expression': 'float3(in.IN_WorldBinormal.xyz', 'line_position': 103}, {'target_type': 'float', 'source_expression': '_9263', 'line_position': 167}, {'target_type': 'float', 'source_expression': '_9270', 'line_position': 193}])", "CodeLineInfo(line_number=138, content='half _9334 = half((_9331 * rsqrt(fast::max(dot(_9331, _9331), 9.9999997473787516355514526367188e-06))).y);', variables=4, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_9334', 'y', '_9331', 'fast'], conversion_details=[{'target_type': 'half', 'source_expression': '(_9331 * rsqrt(fast::max(dot(_9331, _9331', 'line_position': 17}])", "CodeLineInfo(line_number=139, content='half3 _9064 = mix(in.IN_WorldNormal.xyz, half3(_9279 * rsqrt(fast::max(dot(_9279, _9279), 9.9999997473787516355514526367188e-06))), half3(half(_Block1.cNormalMapStrength)));', variables=5, float_vars=0, half_vars=1, conversions=2, has_conversion=True, all_variables=['_9064', '_9279', 'in.IN_WorldNormal.xyz', 'fast', '_Block1.cNormalMapStrength'], conversion_details=[{'target_type': 'half3', 'source_expression': '_9279 * rsqrt(fast::max(dot(_9279, _9279', 'line_position': 45}, {'target_type': 'half3', 'source_expression': 'half(_Block1.cNormalMapStrength', 'line_position': 136}])", "CodeLineInfo(line_number=140, content='half4 _9074 = sMixSampler.sample(sMixSamplerSmplr, in.IN_TexCoord.xy);', variables=4, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_9074', 'in.IN_TexCoord.xy', 'sMixSamplerSmplr', 'sMixSampler.sample'], conversion_details=[])", "CodeLineInfo(line_number=141, content='half _9079 = _9074.y;', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_9079', '_9074.y'], conversion_details=[])", "CodeLineInfo(line_number=142, content='half _9096 = half(mix(1.0, float(mix(half3(_9019), half3(half(fast::clamp(_9014 + _Block1.cAOoffset, 0.0, 1.0))), in.IN_TintColor.xxx).x * _9074.z), float(_8929)));', variables=9, float_vars=0, half_vars=1, conversions=3, has_conversion=True, all_variables=['_9096', 'x', '_9074.z', '_9014', 'in.IN_TintColor.xxx', '_8929', 'fast', '_Block1.cAOoffset', '_9019'], conversion_details=[{'target_type': 'half', 'source_expression': 'mix(1.0, float(mix(half3(_9019', 'line_position': 17}, {'target_type': 'half3', 'source_expression': 'half(fast::clamp(_9014 + _Block1.cAOoffset, 0.0, 1.0', 'line_position': 55}, {'target_type': 'float', 'source_expression': '_8929', 'line_position': 153}])", "CodeLineInfo(line_number=143, content='float _9100 = float(_9096);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9100', '_9096'], conversion_details=[{'target_type': 'float', 'source_expression': '_9096', 'line_position': 18}])", "CodeLineInfo(line_number=144, content='float3 _9109 = float3(_9064);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9109', '_9064'], conversion_details=[{'target_type': 'float3', 'source_expression': '_9064', 'line_position': 19}])", "CodeLineInfo(line_number=145, content='half4 _9130 = sEmissionMapSampler.sample(sEmissionMapSamplerSmplr, in.IN_TexCoord.xy);', variables=4, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_9130', 'sEmissionMapSampler.sample', 'sEmissionMapSamplerSmplr', 'in.IN_TexCoord.xy'], conversion_details=[])", "CodeLineInfo(line_number=146, content='half3 _9145 = _9130.xyz * half3((float3(_Block1.cEmissionColor) * _Block1.cEmissionScale) * float3(_8949));', variables=5, float_vars=0, half_vars=1, conversions=2, has_conversion=True, all_variables=['_9145', '_Block1.cEmissionScale', '_Block1.cEmissionColor', '_8949', '_9130.xyz'], conversion_details=[{'target_type': 'half3', 'source_expression': '(float3(_Block1.cEmissionColor', 'line_position': 30}, {'target_type': 'float3', 'source_expression': '_8949', 'line_position': 96}])", "CodeLineInfo(line_number=148, content='if (!gl_FrontFacing)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['gl_FrontFacing'], conversion_details=[])", "CodeLineInfo(line_number=150, content='_18217 = _9334 * half(-1);', variables=2, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_18217', '_9334'], conversion_details=[{'target_type': 'half', 'source_expression': '-1', 'line_position': 25}])", "CodeLineInfo(line_number=154, content='_18217 = _9334;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18217', '_9334'], conversion_details=[])", "CodeLineInfo(line_number=156, content='float3 _9698 = float3(_9179);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9698', '_9179'], conversion_details=[{'target_type': 'float3', 'source_expression': '_9179', 'line_position': 19}])", "CodeLineInfo(line_number=157, content='float _9408 = mix(_Block1.cCIFadeTime.y, _Block1.cCIFadeTime.w, fast::clamp((_Block1.CameraPos.w - _Block1.cCIFadeTime.x) * _Block1.cCIFadeTime.z, 0.0, 1.0));', variables=7, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9408', '_Block1.CameraPos.w', '_Block1.cCIFadeTime.y', '_Block1.cCIFadeTime.z', '_Block1.cCIFadeTime.x', 'fast', '_Block1.cCIFadeTime.w'], conversion_details=[])", "CodeLineInfo(line_number=158, content='float _9721 = fast::clamp(dot(_9286, float3(half3(_9176, _9176, _9199))), 0.0, 1.0);', variables=5, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9721', '_9286', '_9199', '_9176', 'fast'], conversion_details=[{'target_type': 'float3', 'source_expression': 'half3(_9176, _9176, _9199', 'line_position': 41}])", "CodeLineInfo(line_number=159, content='float _9734 = (_9721 * _9721) * float(half(fast::clamp((_Block1.cCIMudBuff[2u] - 1.0) * 0.699999988079071044921875, 0.0, 1.0)));', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9734', '_Block1.cCIMudBuff', 'fast', '_9721'], conversion_details=[{'target_type': 'float', 'source_expression': 'half(fast::clamp((_Block1.cCIMudBuff[2u] - 1.0', 'line_position': 36}])", "CodeLineInfo(line_number=160, content='half3 _9761 = mix(_8949, half3(half(0.20700000226497650146484375), half(0.18400000035762786865234375), half(0.1369999945163726806640625)), half3(half(_9734 * _9408)));', variables=4, float_vars=0, half_vars=1, conversions=2, has_conversion=True, all_variables=['_9761', '_8949', '_9408', '_9734'], conversion_details=[{'target_type': 'half3', 'source_expression': 'half(0.20700000226497650146484375', 'line_position': 29}, {'target_type': 'half3', 'source_expression': 'half(_9734 * _9408', 'line_position': 143}])", "CodeLineInfo(line_number=161, content='half _9772 = half(mix(float(_9199 - _9074.x), 0.89999997615814208984375, _9734 * float(half(mix(_9408 - (0.5 * _Block1.cCIFadeTime.x), _9408, _Block1.cCIFadeTime.y)))));', variables=7, float_vars=0, half_vars=1, conversions=2, has_conversion=True, all_variables=['_9772', '_9199', '_9734', '_9408', '_9074.x', '_Block1.cCIFadeTime.y', '_Block1.cCIFadeTime.x'], conversion_details=[{'target_type': 'half', 'source_expression': 'mix(float(_9199 - _9074.x', 'line_position': 17}, {'target_type': 'float', 'source_expression': 'half(mix(_9408 - (0.5 * _Block1.cCIFadeTime.x', 'line_position': 85}])", "CodeLineInfo(line_number=162, content='float _9429 = (float(half(_9014)) * _9408) * fast::clamp(1.0 + _Block1.EnvInfo.y, 0.0, 1.0);', variables=5, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9429', '_Block1.EnvInfo.y', '_9014', '_9408', 'fast'], conversion_details=[{'target_type': 'float', 'source_expression': 'half(_9014', 'line_position': 19}])", "CodeLineInfo(line_number=163, content='float4 _9443 = sCharInteractionSampler.sample(sCharInteractionSamplerSmplr, ((in.IN_TexCoord.xy * _Block1.cCISnowData.x) * 12.0));', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9443', 'sCharInteractionSampler.sample', '_Block1.cCISnowData.x', 'sCharInteractionSamplerSmplr', 'in.IN_TexCoord.xy'], conversion_details=[])", "CodeLineInfo(line_number=169, content='if (_Block1.cCISwitchData.x > 0.0)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_Block1.cCISwitchData.x'], conversion_details=[])", "CodeLineInfo(line_number=171, content='float _9460 = float(half(fast::max(0.0, _Block1.EnvInfo.y)));', variables=3, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9460', 'fast', '_Block1.EnvInfo.y'], conversion_details=[{'target_type': 'float', 'source_expression': 'half(fast::max(0.0, _Block1.EnvInfo.y', 'line_position': 22}])", "CodeLineInfo(line_number=172, content='float _9462 = fast::min(2.0, fast::clamp(_Block1.cCISnowData.z, 0.0, 1.0) + _9460);', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9462', '_9460', 'fast', '_Block1.cCISnowData.z'], conversion_details=[])", "CodeLineInfo(line_number=173, content='float _9499 = 1.0 - _9429;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9499', '_9429'], conversion_details=[])", "CodeLineInfo(line_number=174, content='float _9505 = _9443.y;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9505', '_9443.y'], conversion_details=[])", "CodeLineInfo(line_number=175, content='float _9510 = float(half(fast::clamp(((float2(0.800000011920928955078125, 0.5) * _9429).x * (1.0 - powr(float(half(powr(float(clamp(_18217, half(0.0), half(1.0))) + fast::clamp(0.20000000298023223876953125 - fast::clamp((-1.0) * _Block1.cCISnowData.z, 0.0, 1.0), 0.0, 1.0), 2.0 - _9462) * float(in.IN_StaticWorldNormal.w))), 0.800000011920928955078125 - (_9462 * 0.4000000059604644775390625)))) + ((_9499 * _9499) * _9499), 0.0, 1.0)));', variables=9, float_vars=1, half_vars=0, conversions=3, has_conversion=True, all_variables=['_9510', 'x', '_9462', '_Block1.cCISnowData.z', '_9499', '_18217', 'in.IN_StaticWorldNormal.w', 'fast', '_9429'], conversion_details=[{'target_type': 'float', 'source_expression': 'half(fast::clamp(((float2(0.800000011920928955078125, 0.5', 'line_position': 22}, {'target_type': 'float', 'source_expression': 'half(powr(float(clamp(_18217, half(0.0', 'line_position': 112}, {'target_type': 'float', 'source_expression': 'in.IN_StaticWorldNormal.w', 'line_position': 297}])", "CodeLineInfo(line_number=176, content='float _9519 = float(half(9.9956989288330078125e-05));', variables=1, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9519'], conversion_details=[{'target_type': 'float', 'source_expression': 'half(9.9956989288330078125e-05', 'line_position': 22}])", "CodeLineInfo(line_number=177, content='half _9535 = half(1.0 - fast::clamp((float(in.IN_LocalPosition.y) * (2.0 - _9460)) * _Block1.cCISnowData.y, 0.0, 1.0));', variables=5, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_9535', '_9460', 'in.IN_LocalPosition.y', 'fast', '_Block1.cCISnowData.y'], conversion_details=[{'target_type': 'half', 'source_expression': '1.0 - fast::clamp((float(in.IN_LocalPosition.y', 'line_position': 21}])", "CodeLineInfo(line_number=178, content='float _9556 = float(half((float(_9535) * fast::clamp(float(_9535 + _18217) + 0.5, 0.0, 1.0)) * _9429));', variables=5, float_vars=1, half_vars=0, conversions=2, has_conversion=True, all_variables=['_9556', '_18217', 'fast', '_9429', '_9535'], conversion_details=[{'target_type': 'float', 'source_expression': 'half((float(_9535', 'line_position': 22}, {'target_type': 'float', 'source_expression': '_9535 + _18217', 'line_position': 61}])", "CodeLineInfo(line_number=179, content='float _9557 = 1.0 - _9556;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9557', '_9556'], conversion_details=[])", "CodeLineInfo(line_number=180, content='half _9585 = half((_Block1.cCISwitchData.x * fast::clamp(1.0 - _Block1.cCISnowData.w, 0.0, 1.0)) * float(max(half(fast::clamp((fast::max(_9505, _9443.w) - _9510) / fast::max(_9519, fast::clamp(_9510 + 0.1500000059604644775390625, 0.0, 1.0) - _9510), 0.0, 1.0)), half(fast::clamp((fast::max(_9505, _9443.z) - _9557) / fast::max(_9519, (1.5 - _9556) - _9557), 0.0, 1.0)))));', variables=11, float_vars=0, half_vars=1, conversions=3, has_conversion=True, all_variables=['_9585', '_9519', '_Block1.cCISnowData.w', '_9556', '_9510', '_9505', '_9443.z', '_9557', '_9443.w', 'fast', '_Block1.cCISwitchData.x'], conversion_details=[{'target_type': 'half', 'source_expression': '(_Block1.cCISwitchData.x * fast::clamp(1.0 - _Block1.cCISnowData.w, 0.0, 1.0', 'line_position': 21}, {'target_type': 'float', 'source_expression': 'max(half(fast::clamp((fast::max(_9505, _9443.w', 'line_position': 107}, {'target_type': 'half', 'source_expression': 'fast::clamp((fast::max(_9505, _9443.z', 'line_position': 270}])", "CodeLineInfo(line_number=181, content='half _9588 = _9199 - _9585;', variables=3, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_9588', '_9199', '_9585'], conversion_details=[])", "CodeLineInfo(line_number=182, content='float _9603 = float(_9585);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9603', '_9585'], conversion_details=[{'target_type': 'float', 'source_expression': '_9585', 'line_position': 22}])", "CodeLineInfo(line_number=183, content='_18272 = half(mix(float(_9772), 1.0, _9603));', variables=3, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_18272', '_9772', '_9603'], conversion_details=[{'target_type': 'half', 'source_expression': 'mix(float(_9772', 'line_position': 17}])", "CodeLineInfo(line_number=184, content='_18268 = _9145 * _9588;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18268', '_9145', '_9588'], conversion_details=[])", "CodeLineInfo(line_number=185, content='_18236 = half(mix(_9100, 1.0, _9603));', variables=3, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9100', '_18236', '_9603'], conversion_details=[{'target_type': 'half', 'source_expression': 'mix(_9100, 1.0, _9603', 'line_position': 17}])", "CodeLineInfo(line_number=186, content='_18234 = _9079 * _9588;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9588', '_9079', '_18234'], conversion_details=[])", "CodeLineInfo(line_number=187, content='_18225 = mix(_9761, half3(half(0.61000001430511474609375), half(0.660000026226043701171875), half(0.790000021457672119140625)), half3(_9585));', variables=3, float_vars=0, half_vars=0, conversions=2, has_conversion=True, all_variables=['_18225', '_9761', '_9585'], conversion_details=[{'target_type': 'half3', 'source_expression': 'half(0.61000001430511474609375', 'line_position': 28}, {'target_type': 'half3', 'source_expression': '_9585', 'line_position': 136}])", "CodeLineInfo(line_number=191, content='_18272 = _9772;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18272', '_9772'], conversion_details=[])", "CodeLineInfo(line_number=192, content='_18268 = _9145;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18268', '_9145'], conversion_details=[])", "CodeLineInfo(line_number=193, content='_18236 = _9096;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18236', '_9096'], conversion_details=[])", "CodeLineInfo(line_number=194, content='_18234 = _9079;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9079', '_18234'], conversion_details=[])", "CodeLineInfo(line_number=195, content='_18225 = _9761;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18225', '_9761'], conversion_details=[])", "CodeLineInfo(line_number=197, content='half _8295 = half(fast::clamp(1.0 - _Block1.HexRenderOptionData[0].x, 0.0, 1.0));', variables=4, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_8295', 'fast', '_Block1.HexRenderOptionData', 'x'], conversion_details=[{'target_type': 'half', 'source_expression': 'fast::clamp(1.0 - _Block1.HexRenderOptionData[0].x, 0.0, 1.0', 'line_position': 17}])", "CodeLineInfo(line_number=198, content='float _8298 = float(_8939.w * half((_8991 * float(min(_8994, _8996))) * powr(float(max(in.IN_TintColor.x, half(9.9956989288330078125e-05))), _Block1.cFurFadeInt)));', variables=7, float_vars=1, half_vars=0, conversions=2, has_conversion=True, all_variables=['_8298', '_8991', 'in.IN_TintColor.x', '_Block1.cFurFadeInt', '_8939.w', '_8994', '_8996'], conversion_details=[{'target_type': 'float', 'source_expression': '_8939.w * half((_8991 * float(min(_8994, _8996', 'line_position': 18}, {'target_type': 'float', 'source_expression': 'max(in.IN_TintColor.x, half(9.9956989288330078125e-05', 'line_position': 81}])", "CodeLineInfo(line_number=199, content='half3 _8303 = half3(half(0.2125999927520751953125), half(0.715200006961822509765625), half(0.072200000286102294921875));', variables=1, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_8303'], conversion_details=[{'target_type': 'half3', 'source_expression': 'half(0.2125999927520751953125', 'line_position': 18}])", "CodeLineInfo(line_number=200, content='half3 _8315 = mix(half3(dot(_18225, _8303)), _18225, half3(half(_Block1.cSaturation)));', variables=4, float_vars=0, half_vars=1, conversions=2, has_conversion=True, all_variables=['_8315', '_Block1.cSaturation', '_18225', '_8303'], conversion_details=[{'target_type': 'half3', 'source_expression': 'dot(_18225, _8303', 'line_position': 22}, {'target_type': 'half3', 'source_expression': 'half(_Block1.cSaturation', 'line_position': 57}])", "CodeLineInfo(line_number=201, content='half _9787 = half(_8298);', variables=2, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_9787', '_8298'], conversion_details=[{'target_type': 'half', 'source_expression': '_8298', 'line_position': 17}])", "CodeLineInfo(line_number=204, content='if (_Block1.eDynamicFresnelIntensity > 0.0)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_Block1.eDynamicFresnelIntensity'], conversion_details=[])", "CodeLineInfo(line_number=206, content='float _9806 = abs(dot(_9109, -_8921));', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9806', '_9109', '_8921'], conversion_details=[])", "CodeLineInfo(line_number=207, content='float _9813 = abs(_Block1.eFresnelPower);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9813', '_Block1.eFresnelPower'], conversion_details=[])", "CodeLineInfo(line_number=208, content='float _9831 = fast::max(_Block1.eFresnelMinIntensity, (_Block1.eFresnelPower < 0.0) ? powr(_9806, _9813) : powr(1.0 - fast::min(float(_9199 - half(9.9956989288330078125e-05)), _9806), _9813));', variables=7, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9831', '_9813', '_9199', '_9806', '_Block1.eFresnelPower', '_Block1.eFresnelMinIntensity', 'fast'], conversion_details=[{'target_type': 'float', 'source_expression': '_9199 - half(9.9956989288330078125e-05', 'line_position': 136}])", "CodeLineInfo(line_number=209, content='float _9846 = float(_9787 * half(_9831));', variables=3, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9846', '_9787', '_9831'], conversion_details=[{'target_type': 'float', 'source_expression': '_9787 * half(_9831', 'line_position': 22}])", "CodeLineInfo(line_number=210, content='_18255 = _9179 + half3((((float3(_Block1.eFresnelColor) * _9831) * _Block1.eFresnelIntensity) * 1.0) * _9846);', variables=6, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9831', '_Block1.eFresnelColor', '_18255', '_9179', '_Block1.eFresnelIntensity', '_9846'], conversion_details=[{'target_type': 'half3', 'source_expression': '(((float3(_Block1.eFresnelColor', 'line_position': 25}])", "CodeLineInfo(line_number=211, content='_18230 = clamp(half(fast::max(fast::clamp(_Block1.eFresnelAlphaAdd, 0.0, 1.0) * _8298, _9846)), half(0.0), half(1.0));', variables=5, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_18230', '_9846', '_Block1.eFresnelAlphaAdd', '_8298', 'fast'], conversion_details=[{'target_type': 'half', 'source_expression': 'fast::max(fast::clamp(_Block1.eFresnelAlphaAdd, 0.0, 1.0', 'line_position': 23}])", "CodeLineInfo(line_number=215, content='_18255 = _9179;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9179', '_18255'], conversion_details=[])", "CodeLineInfo(line_number=216, content='_18230 = _9787;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18230', '_9787'], conversion_details=[])", "CodeLineInfo(line_number=218, content='float _9868 = float(_18230);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9868', '_18230'], conversion_details=[{'target_type': 'float', 'source_expression': '_18230', 'line_position': 18}])", "CodeLineInfo(line_number=219, content='half _8346 = _18236 * half(_Block1.SHAOParam.w);', variables=3, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_8346', '_18236', '_Block1.SHAOParam.w'], conversion_details=[{'target_type': 'half', 'source_expression': '_Block1.SHAOParam.w', 'line_position': 26}])", "CodeLineInfo(line_number=220, content='float4 _9926 = float4((in.IN_WorldPosition.xyz + (_8925 * _Block1.cBiasFarAwayShadow)) - _Block1.CameraPos.xyz, 1.0) * _Block1.ShadowViewProjTexs0;', variables=6, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9926', '_Block1.ShadowViewProjTexs0', 'in.IN_WorldPosition.xyz', '_Block1.CameraPos.xyz', '_8925', '_Block1.cBiasFarAwayShadow'], conversion_details=[{'target_type': 'float4', 'source_expression': '(in.IN_WorldPosition.xyz + (_8925 * _Block1.cBiasFarAwayShadow', 'line_position': 19}])", "CodeLineInfo(line_number=221, content='float4 _17899 = _9926;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_17899', '_9926'], conversion_details=[])", "CodeLineInfo(line_number=222, content='_17899.z = _9926.z - _Block1.CSMShadowBiases.x;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_Block1.CSMShadowBiases.x', '_17899.z', '_9926.z'], conversion_details=[])", "CodeLineInfo(line_number=223, content='float4 _9942 = float4(in.IN_WorldPosition.xyz, 1.0);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9942', 'in.IN_WorldPosition.xyz'], conversion_details=[{'target_type': 'float4', 'source_expression': 'in.IN_WorldPosition.xyz, 1.0', 'line_position': 19}])", "CodeLineInfo(line_number=224, content='float4 _9945 = _9942 * _Block1.ShadowViewProjTexs1;', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9945', '_Block1.ShadowViewProjTexs1', '_9942'], conversion_details=[])", "CodeLineInfo(line_number=225, content='float4 _17902 = _9945;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_17902', '_9945'], conversion_details=[])", "CodeLineInfo(line_number=226, content='_17902.z = _9945.z - _Block1.CSMShadowBiases.y;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9945.z', '_Block1.CSMShadowBiases.y', '_17902.z'], conversion_details=[])", "CodeLineInfo(line_number=228, content='if (_Block1.CSMCacheIndexs.z > 0.0)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_Block1.CSMCacheIndexs.z'], conversion_details=[])", "CodeLineInfo(line_number=230, content='float4 _9971 = _9942 * _Block1.ShadowViewProjTexs2;', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9971', '_9942', '_Block1.ShadowViewProjTexs2'], conversion_details=[])", "CodeLineInfo(line_number=231, content='_9971.z = _9971.z - _Block1.CSMShadowBiases.z;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_Block1.CSMShadowBiases.z', '_9971.z'], conversion_details=[])", "CodeLineInfo(line_number=232, content='_18237 = _9971;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9971', '_18237'], conversion_details=[])", "CodeLineInfo(line_number=236, content='_18237 = float4(0.0, 0.0, 0.0, 1.0);', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18237'], conversion_details=[])", "CodeLineInfo(line_number=238, content='float3 _10033 = _17902.xyz / float3(_9945.w);', variables=3, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_10033', '_9945.w', '_17902.xyz'], conversion_details=[{'target_type': 'float3', 'source_expression': '_9945.w', 'line_position': 33}])", "CodeLineInfo(line_number=239, content='float3 _10040 = _18237.xyz / float3(_18237.w);', variables=3, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_10040', '_18237.xyz', '_18237.w'], conversion_details=[{'target_type': 'float3', 'source_expression': '_18237.w', 'line_position': 33}])", "CodeLineInfo(line_number=240, content='float3 _10077 = _10040 * (step(-0.100000001490116119384765625, _Block1.CSMShadowBiases.z) * float(all(_10040 > float3(0.0)) && all(_10040 < float3(1.0))));', variables=3, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_10077', '_Block1.CSMShadowBiases.z', '_10040'], conversion_details=[{'target_type': 'float', 'source_expression': 'all(_10040 > float3(0.0', 'line_position': 96}])", "CodeLineInfo(line_number=241, content='float _21135 = step(-0.100000001490116119384765625, _Block1.CSMShadowBiases.y) * float(all(_10033 > float3(0.0)) && all(_10033 < float3(1.0)));', variables=3, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_21135', '_Block1.CSMShadowBiases.y', '_10033'], conversion_details=[{'target_type': 'float', 'source_expression': 'all(_10033 > float3(0.0', 'line_position': 85}])", "CodeLineInfo(line_number=242, content='float3 _21138 = _10077 + ((_10033 - _10077) * _21135);', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21138', '_10077', '_21135', '_10033'], conversion_details=[])", "CodeLineInfo(line_number=243, content='float _10113 = _21138.z;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10113', '_21138.z'], conversion_details=[])", "CodeLineInfo(line_number=244, content='float2 _10120 = float2(_Block1.cShadowBias.w);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_10120', '_Block1.cShadowBias.w'], conversion_details=[{'target_type': 'float2', 'source_expression': '_Block1.cShadowBias.w', 'line_position': 20}])", "CodeLineInfo(line_number=245, content='float2 _10167 = (_21138.xy / _10120) - float2(0.5);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10167', '_10120', '_21138.xy'], conversion_details=[])", "CodeLineInfo(line_number=246, content='float2 _10169 = fract(_10167);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10169', '_10167'], conversion_details=[])", "CodeLineInfo(line_number=247, content='float2 _10171 = floor(_10167);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10171', '_10167'], conversion_details=[])", "CodeLineInfo(line_number=248, content='float2 _10177 = float2(2.0) - _10169;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10177', '_10169'], conversion_details=[])", "CodeLineInfo(line_number=249, content='float2 _10181 = _10169 + float2(1.0);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10181', '_10169'], conversion_details=[])", "CodeLineInfo(line_number=250, content='float2 _10184 = float2(1.0) / _10177;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10184', '_10177'], conversion_details=[])", "CodeLineInfo(line_number=251, content='float2 _10187 = _10169 / _10181;', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10187', '_10169', '_10181'], conversion_details=[])", "CodeLineInfo(line_number=252, content='float _10205 = float(int(_Block1.CSMCacheIndexs[int(2.0 + ((-1.0) * _21135))]));', variables=3, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_10205', '_Block1.CSMCacheIndexs', '_21135'], conversion_details=[{'target_type': 'float', 'source_expression': 'int(_Block1.CSMCacheIndexs[int(2.0 + ((-1.0', 'line_position': 19}])", "CodeLineInfo(line_number=253, content='float3 _10208 = float3(((_10171 + float2(-0.5)) + _10184) * _10120, _10205);', variables=5, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_10208', '_10184', '_10205', '_10171', '_10120'], conversion_details=[{'target_type': 'float3', 'source_expression': '((_10171 + float2(-0.5', 'line_position': 20}])", "CodeLineInfo(line_number=254, content='float3 _10231 = float3(((_10171 + float2(1.5, -0.5)) + float2(_10187.x, _10184.y)) * _10120, _10205);', variables=6, float_vars=1, half_vars=0, conversions=2, has_conversion=True, all_variables=['_10231', '_10187.x', '_10205', '_10171', '_10120', '_10184.y'], conversion_details=[{'target_type': 'float3', 'source_expression': '((_10171 + float2(1.5, -0.5', 'line_position': 20}, {'target_type': 'float2', 'source_expression': '_10187.x, _10184.y', 'line_position': 59}])", "CodeLineInfo(line_number=255, content='float3 _10254 = float3(((_10171 + float2(-0.5, 1.5)) + float2(_10184.x, _10187.y)) * _10120, _10205);', variables=6, float_vars=1, half_vars=0, conversions=2, has_conversion=True, all_variables=['_10254', '_10184.x', '_10205', '_10171', '_10120', '_10187.y'], conversion_details=[{'target_type': 'float3', 'source_expression': '((_10171 + float2(-0.5, 1.5', 'line_position': 20}, {'target_type': 'float2', 'source_expression': '_10184.x, _10187.y', 'line_position': 59}])", "CodeLineInfo(line_number=256, content='float3 _10276 = float3(((_10171 + float2(1.5)) + _10187) * _10120, _10205);', variables=5, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_10276', '_10205', '_10187', '_10171', '_10120'], conversion_details=[{'target_type': 'float3', 'source_expression': '((_10171 + float2(1.5', 'line_position': 20}])", "CodeLineInfo(line_number=257, content='float _10282 = _10177.x;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10282', '_10177.x'], conversion_details=[])", "CodeLineInfo(line_number=258, content='float _10289 = _10181.x;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10289', '_10181.x'], conversion_details=[])", "CodeLineInfo(line_number=259, content='float _10300 = _10181.y;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10300', '_10181.y'], conversion_details=[])", "CodeLineInfo(line_number=260, content='float3 _9997 = _17899.xyz / float3(_9926.w);', variables=3, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9997', '_9926.w', '_17899.xyz'], conversion_details=[{'target_type': 'float3', 'source_expression': '_9926.w', 'line_position': 32}])", "CodeLineInfo(line_number=261, content='float _10004 = fast::min(1.0 - float(half(10) * half(9.9956989288330078125e-05)), _9997.z);', variables=3, float_vars=1, half_vars=0, conversions=2, has_conversion=True, all_variables=['_10004', 'fast', '_9997.z'], conversion_details=[{'target_type': 'float', 'source_expression': 'half(10', 'line_position': 35}, {'target_type': 'half', 'source_expression': '9.9956989288330078125e-05', 'line_position': 52}])", "CodeLineInfo(line_number=262, content='float3 _17928 = _9997;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_17928', '_9997'], conversion_details=[])", "CodeLineInfo(line_number=263, content='_17928.z = _10004;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_17928.z', '_10004'], conversion_details=[])", "CodeLineInfo(line_number=264, content='float2 _10378 = (_17928.xy / _10120) - float2(0.5);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10378', '_17928.xy', '_10120'], conversion_details=[])", "CodeLineInfo(line_number=265, content='float2 _10380 = fract(_10378);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10380', '_10378'], conversion_details=[])", "CodeLineInfo(line_number=266, content='float2 _10382 = floor(_10378);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10382', '_10378'], conversion_details=[])", "CodeLineInfo(line_number=267, content='float2 _10388 = float2(2.0) - _10380;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10388', '_10380'], conversion_details=[])", "CodeLineInfo(line_number=268, content='float2 _10392 = _10380 + float2(1.0);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10392', '_10380'], conversion_details=[])", "CodeLineInfo(line_number=269, content='float2 _10395 = float2(1.0) / _10388;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10395', '_10388'], conversion_details=[])", "CodeLineInfo(line_number=270, content='float2 _10398 = _10380 / _10392;', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10398', '_10392', '_10380'], conversion_details=[])", "CodeLineInfo(line_number=271, content='float _10416 = float(int(_Block1.CSMCacheIndexs.x));', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_10416', '_Block1.CSMCacheIndexs.x'], conversion_details=[{'target_type': 'float', 'source_expression': 'int(_Block1.CSMCacheIndexs.x', 'line_position': 19}])", "CodeLineInfo(line_number=272, content='float3 _10419 = float3(((_10382 + float2(-0.5)) + _10395) * _10120, _10416);', variables=5, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_10419', '_10382', '_10120', '_10395', '_10416'], conversion_details=[{'target_type': 'float3', 'source_expression': '((_10382 + float2(-0.5', 'line_position': 20}])", "CodeLineInfo(line_number=273, content='float3 _10442 = float3(((_10382 + float2(1.5, -0.5)) + float2(_10398.x, _10395.y)) * _10120, _10416);', variables=6, float_vars=1, half_vars=0, conversions=2, has_conversion=True, all_variables=['_10442', '_10416', '_10398.x', '_10382', '_10120', '_10395.y'], conversion_details=[{'target_type': 'float3', 'source_expression': '((_10382 + float2(1.5, -0.5', 'line_position': 20}, {'target_type': 'float2', 'source_expression': '_10398.x, _10395.y', 'line_position': 59}])", "CodeLineInfo(line_number=274, content='float3 _10465 = float3(((_10382 + float2(-0.5, 1.5)) + float2(_10395.x, _10398.y)) * _10120, _10416);', variables=6, float_vars=1, half_vars=0, conversions=2, has_conversion=True, all_variables=['_10465', '_10398.y', '_10382', '_10120', '_10395.x', '_10416'], conversion_details=[{'target_type': 'float3', 'source_expression': '((_10382 + float2(-0.5, 1.5', 'line_position': 20}, {'target_type': 'float2', 'source_expression': '_10395.x, _10398.y', 'line_position': 59}])", "CodeLineInfo(line_number=275, content='float3 _10487 = float3(((_10382 + float2(1.5)) + _10398) * _10120, _10416);', variables=5, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_10487', '_10398', '_10382', '_10120', '_10416'], conversion_details=[{'target_type': 'float3', 'source_expression': '((_10382 + float2(1.5', 'line_position': 20}])", "CodeLineInfo(line_number=276, content='float _10493 = _10388.x;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10493', '_10388.x'], conversion_details=[])", "CodeLineInfo(line_number=277, content='float _10500 = _10392.x;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10500', '_10392.x'], conversion_details=[])", "CodeLineInfo(line_number=278, content='float _10511 = _10392.y;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10511', '_10392.y'], conversion_details=[])", "CodeLineInfo(line_number=279, content='half _8351 = max(half(mix(0.0, 1.0 - fast::clamp((abs(dot(_9109, _Block1.SunDirection.xyz)) + ((2.0 * _9100) * _9100)) - 1.0, 0.0, 1.0), _Block1.cMicroShadow)), max(half(((((_10388.y * ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10419.xy, uint(rint(_10419.z)), _10004, level(0.0)) * _10493) + (sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10442.xy, uint(rint(_10442.z)), _10004, level(0.0)) * _10500))) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10465.xy, uint(rint(_10465.z)), _10004, level(0.0)) * _10493) * _10511)) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10487.xy, uint(rint(_10487.z)), _10004, level(0.0)) * _10500) * _10511)) * 0.111111097037792205810546875) * float(all(_17928 > float3(0.0)) && all(_17928 < float3(1.0)))), half(fast::min(1.0, float(half(((((_10177.y * ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10208.xy, uint(rint(_10208.z)), _10113, level(0.0)) * _10282) + (sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10231.xy, uint(rint(_10231.z)), _10113, level(0.0)) * _10289))) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10254.xy, uint(rint(_10254.z)), _10113, level(0.0)) * _10282) * _10300)) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10276.xy, uint(rint(_10276.z)), _10113, level(0.0)) * _10289) * _10300)) * 0.111111097037792205810546875) * float(all(_21138 > float3(0.0)) && all(_21138 < float3(1.0)))))))));', variables=36, float_vars=0, half_vars=1, conversions=5, has_conversion=True, all_variables=['_8351', '_10282', '_10465.xy', 'sShadowMapArraySamplerSmplr', '_10289', '_21138', 'sShadowMapArraySampler.sample_compare', '_10004', '_10208.xy', '_Block1.cMicroShadow', '_10442.xy', '_Block1.SunDirection.xyz', '_10276.xy', '_17928', '_10177.y', '_10231.z', '_10487.z', '_10276.z', '_10388.y', '_10300', '_10208.z', '_10254.xy', '_10419.z', '_9100', '_10442.z', '_10419.xy', '_10254.z', '_10511', '_10493', '_10465.z', '_10113', '_10487.xy', '_9109', '_10500', '_10231.xy', 'fast'], conversion_details=[{'target_type': 'half', 'source_expression': 'mix(0.0, 1.0 - fast::clamp((abs(dot(_9109, _Block1.SunDirection.xyz', 'line_position': 21}, {'target_type': 'half', 'source_expression': '((((_10388.y * ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10419.xy, uint(rint(_10419.z', 'line_position': 169}, {'target_type': 'float', 'source_expression': 'all(_17928 > float3(0.0', 'line_position': 781}, {'target_type': 'half', 'source_expression': 'fast::min(1.0, float(half(((((_10177.y * ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10208.xy, uint(rint(_10208.z', 'line_position': 845}, {'target_type': 'float', 'source_expression': 'all(_21138 > float3(0.0', 'line_position': 1483}])", "CodeLineInfo(line_number=280, content='float3 _8370 = in.IN_WorldPosition.xyz - _Block1.CameraPos.xyz;', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8370', '_Block1.CameraPos.xyz', 'in.IN_WorldPosition.xyz'], conversion_details=[])", "CodeLineInfo(line_number=281, content='float3 _8373 = fast::normalize(-_8370);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8373', '_8370', 'fast'], conversion_details=[])", "CodeLineInfo(line_number=282, content='float _8378 = dot(_9109, _8373);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8378', '_9109', '_8373'], conversion_details=[])", "CodeLineInfo(line_number=283, content='half3 _10557 = mix(half3(half(0.039999999105930328369140625)), _8315, half3(_18234));', variables=3, float_vars=0, half_vars=1, conversions=2, has_conversion=True, all_variables=['_10557', '_8315', '_18234'], conversion_details=[{'target_type': 'half3', 'source_expression': 'half(0.039999999105930328369140625', 'line_position': 23}, {'target_type': 'half3', 'source_expression': '_18234', 'line_position': 74}])", "CodeLineInfo(line_number=284, content='half3 _10569 = half3(float3(_8315 - (_8315 * _18234)) * float3(0.3183098733425140380859375));', variables=3, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_10569', '_8315', '_18234'], conversion_details=[{'target_type': 'half3', 'source_expression': 'float3(_8315 - (_8315 * _18234', 'line_position': 19}])", "CodeLineInfo(line_number=285, content='float3 _10588 = float3(_Block1.EnvInfo.z);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_10588', '_Block1.EnvInfo.z'], conversion_details=[{'target_type': 'float3', 'source_expression': '_Block1.EnvInfo.z', 'line_position': 20}])", "CodeLineInfo(line_number=286, content='half3 _8393 = half3(half(0.0));', variables=1, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_8393'], conversion_details=[{'target_type': 'half3', 'source_expression': 'half(0.0', 'line_position': 18}])", "CodeLineInfo(line_number=287, content='uint _8397 = as_type<uint>(_Block1.SHGIParam.w);', variables=4, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['as_type', '_Block1.SHGIParam.w', 'uint', '_8397'], conversion_details=[])", "CodeLineInfo(line_number=288, content='bool _8401 = (_8397 & 63u) > 0u;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8397', '_8401', 'bool'], conversion_details=[])", "CodeLineInfo(line_number=290, content='if (_8401)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8401'], conversion_details=[])", "CodeLineInfo(line_number=292, content='float3 _8435 = select(_Block1.PlayerPos.xyz, _Block1.CameraPos.xyz, bool3((_8397 & 524288u) > 0u));', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8435', '_Block1.CameraPos.xyz', '_8397', '_Block1.PlayerPos.xyz'], conversion_details=[])", "CodeLineInfo(line_number=294, content='if (_8401)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8401'], conversion_details=[])", "CodeLineInfo(line_number=297, content='if ((_8397 & 8u) != 0u)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8397'], conversion_details=[])", "CodeLineInfo(line_number=299, content='float3 _10686 = (in.IN_WorldPosition.xyz + (_8373 * 0.100000001490116119384765625)) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10686', 'in.IN_WorldPosition.xyz', '_8373'], conversion_details=[])", "CodeLineInfo(line_number=300, content='float3 _10762 = _10686 - floor(_10686);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10762', '_10686'], conversion_details=[])", "CodeLineInfo(line_number=301, content='float3 _10789 = _8435 * float3(0.125);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10789', '_8435'], conversion_details=[])", "CodeLineInfo(line_number=302, content='float _10797 = _10789.x;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10797', '_10789.x'], conversion_details=[])", "CodeLineInfo(line_number=303, content='float _10799 = floor(_10797);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10799', '_10797'], conversion_details=[])", "CodeLineInfo(line_number=305, content='_21191.x = _10799 - 15.0;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21191.x', '_10799'], conversion_details=[])", "CodeLineInfo(line_number=307, content='if ((_10797 - _10799) > 0.5)', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10799', '_10797'], conversion_details=[])", "CodeLineInfo(line_number=309, content='float3 _21194 = _21191;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21194', '_21191'], conversion_details=[])", "CodeLineInfo(line_number=310, content='_21194.x = _10799 + (-14.0);', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21194.x', '_10799'], conversion_details=[])", "CodeLineInfo(line_number=311, content='_21235 = _21194;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21194', '_21235'], conversion_details=[])", "CodeLineInfo(line_number=315, content='_21235 = _21191;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21235', '_21191'], conversion_details=[])", "CodeLineInfo(line_number=317, content='float _21078 = _10789.y;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21078', '_10789.y'], conversion_details=[])", "CodeLineInfo(line_number=318, content='float _21079 = floor(_21078);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21079', '_21078'], conversion_details=[])", "CodeLineInfo(line_number=319, content='float3 _21198 = _21235;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21198', '_21235'], conversion_details=[])", "CodeLineInfo(line_number=320, content='_21198.y = _21079 - 8.0;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21079', '_21198.y'], conversion_details=[])", "CodeLineInfo(line_number=322, content='if ((_21078 - _21079) > 0.5)', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21079', '_21078'], conversion_details=[])", "CodeLineInfo(line_number=324, content='float3 _21201 = _21198;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21201', '_21198'], conversion_details=[])", "CodeLineInfo(line_number=325, content='_21201.y = _21079 + (-7.0);', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21079', '_21201.y'], conversion_details=[])", "CodeLineInfo(line_number=326, content='_21236 = _21201;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21201', '_21236'], conversion_details=[])", "CodeLineInfo(line_number=330, content='_21236 = _21198;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21198', '_21236'], conversion_details=[])", "CodeLineInfo(line_number=332, content='float _21100 = _10789.z;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21100', '_10789.z'], conversion_details=[])", "CodeLineInfo(line_number=333, content='float _21101 = floor(_21100);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21101', '_21100'], conversion_details=[])", "CodeLineInfo(line_number=334, content='float3 _21205 = _21236;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21205', '_21236'], conversion_details=[])", "CodeLineInfo(line_number=335, content='_21205.z = _21101 - 15.0;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21205.z', '_21101'], conversion_details=[])", "CodeLineInfo(line_number=337, content='if ((_21100 - _21101) > 0.5)', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21100', '_21101'], conversion_details=[])", "CodeLineInfo(line_number=339, content='float3 _21208 = _21205;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21208', '_21205'], conversion_details=[])", "CodeLineInfo(line_number=340, content='_21208.z = _21101 + (-14.0);', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21208.z', '_21101'], conversion_details=[])", "CodeLineInfo(line_number=341, content='_21237 = _21208;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21208', '_21237'], conversion_details=[])", "CodeLineInfo(line_number=345, content='_21237 = _21205;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21205', '_21237'], conversion_details=[])", "CodeLineInfo(line_number=347, content='float3 _10822 = _21237 * 8.0;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10822', '_21237'], conversion_details=[])", "CodeLineInfo(line_number=349, content='if (all(in.IN_WorldPosition.xyz >= _10822) && all(in.IN_WorldPosition.xyz < (_10822 + float3(240.0, 128.0, 240.0))))', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10822', 'in.IN_WorldPosition.xyz'], conversion_details=[])", "CodeLineInfo(line_number=351, content='uint _10704 = (_8397 & 251658240u) >> 24u;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10704', 'uint', '_8397'], conversion_details=[])", "CodeLineInfo(line_number=352, content='float _10887 = 3.0 - float((_8397 & 458752u) >> 16u);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_10887', '_8397'], conversion_details=[{'target_type': 'float', 'source_expression': '(_8397 & 458752u', 'line_position': 41}])", "CodeLineInfo(line_number=354, content='if (_10704 <= 3u)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10704'], conversion_details=[])", "CodeLineInfo(line_number=356, content='float _10900 = 3.0 - float(_10704);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_10900', '_10704'], conversion_details=[{'target_type': 'float', 'source_expression': '_10704', 'line_position': 45}])", "CodeLineInfo(line_number=357, content='float2 _10994 = ((_10762.xz - float2(0.5)) * 0.9375) + float2(0.5);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_10994', '_10762.xz'], conversion_details=[])", "CodeLineInfo(line_number=358, content='float _11001 = _10822.x * 0.0041666668839752674102783203125;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11001', '_10822.x'], conversion_details=[])", "CodeLineInfo(line_number=359, content='float _11005 = ((_11001 - floor(_11001)) - 0.5) * 0.9375;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11005', '_11001'], conversion_details=[])", "CodeLineInfo(line_number=360, content='float _11011 = _10822.z * 0.0041666668839752674102783203125;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11011', '_10822.z'], conversion_details=[])", "CodeLineInfo(line_number=361, content='float _11015 = ((_11011 - floor(_11011)) - 0.5) * 0.9375;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11015', '_11011'], conversion_details=[])", "CodeLineInfo(line_number=362, content='float _11020 = _10994.x;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11020', '_10994.x'], conversion_details=[])", "CodeLineInfo(line_number=364, content='_17954.x = (_11020 < (_11005 + 0.5)) ? fast::min(_11020, _11005 + 0.49609375) : fast::max(_11020, _11005 + 0.50390625);', variables=4, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11005', '_11020', 'fast', '_17954.x'], conversion_details=[])", "CodeLineInfo(line_number=365, content='float _11038 = _10994.y;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11038', '_10994.y'], conversion_details=[])", "CodeLineInfo(line_number=366, content='_17954.z = (_11038 < (_11015 + 0.5)) ? fast::min(_11038, _11015 + 0.49609375) : fast::max(_11038, _11015 + 0.50390625);', variables=4, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11015', '_17954.z', '_11038', 'fast'], conversion_details=[])", "CodeLineInfo(line_number=367, content='float _11059 = (_10762.y * 64.0) - 0.5;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11059', '_10762.y'], conversion_details=[])", "CodeLineInfo(line_number=368, content='float _11064 = floor(_11059);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11064', '_11059'], conversion_details=[])", "CodeLineInfo(line_number=369, content='uint _11067 = (_11059 < 0.0) ? 63u : uint(_11064);', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11067', '_11064', '_11059'], conversion_details=[])", "CodeLineInfo(line_number=370, content='uint _11070 = _11067 + 1u;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11067', 'uint', '_11070'], conversion_details=[])", "CodeLineInfo(line_number=371, content='uint _21301 = (_11070 >= 64u) ? 0u : _11070;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['uint', '_11070', '_21301'], conversion_details=[])", "CodeLineInfo(line_number=372, content='float2 _11097 = (float2(float(_11067 & 7u), float(_11067 >> 3u)) + _17954.xz) * 0.125;', variables=3, float_vars=1, half_vars=0, conversions=2, has_conversion=True, all_variables=['_11097', '_11067', '_17954.xz'], conversion_details=[{'target_type': 'float2', 'source_expression': 'float(_11067 & 7u', 'line_position': 41}, {'target_type': 'float', 'source_expression': '_11067 >> 3u', 'line_position': 68}])", "CodeLineInfo(line_number=373, content='float _11100 = _11097.x;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11100', '_11097.x'], conversion_details=[])", "CodeLineInfo(line_number=374, content='float3 _11102 = float3(_11100, _11097.y, _10887);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_11102', '_11100', '_11097.y', '_10887'], conversion_details=[{'target_type': 'float3', 'source_expression': '_11100, _11097.y, _10887', 'line_position': 40}])", "CodeLineInfo(line_number=376, content='_17962.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11102.xy, uint(rint(_11102.z)), level(0.0)).x);', variables=6, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['sSHAOAlphaVTSampler.sample', '_11102.z', 'x', '_11102.xy', '_17962.w', 'sSHAOAlphaVTSamplerSmplr'], conversion_details=[{'target_type': 'half', 'source_expression': 'sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11102.xy, uint(rint(_11102.z', 'line_position': 35}])", "CodeLineInfo(line_number=377, content='float3 _11113 = float3(_11100, _11097.y, _10900);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_11113', '_11100', '_11097.y', '_10900'], conversion_details=[{'target_type': 'float3', 'source_expression': '_11100, _11097.y, _10900', 'line_position': 40}])", "CodeLineInfo(line_number=378, content='half3 _11118 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11113.xy, uint(rint(_11113.z)), level(0.0)).xyz);', variables=6, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_11118', 'sSHAORGBVTSampler.sample', 'sSHAORGBVTSamplerSmplr', '_11113.z', 'xyz', '_11113.xy'], conversion_details=[{'target_type': 'half3', 'source_expression': 'sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11113.xy, uint(rint(_11113.z', 'line_position': 39}])", "CodeLineInfo(line_number=379, content='float2 _11135 = (float2(float(_21301 & 7u), float(_21301 >> 3u)) + _17954.xz) * 0.125;', variables=3, float_vars=1, half_vars=0, conversions=2, has_conversion=True, all_variables=['_11135', '_17954.xz', '_21301'], conversion_details=[{'target_type': 'float2', 'source_expression': 'float(_21301 & 7u', 'line_position': 41}, {'target_type': 'float', 'source_expression': '_21301 >> 3u', 'line_position': 68}])", "CodeLineInfo(line_number=380, content='float _11138 = _11135.x;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11138', '_11135.x'], conversion_details=[])", "CodeLineInfo(line_number=381, content='float3 _11140 = float3(_11138, _11135.y, _10887);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_11140', '_11135.y', '_11138', '_10887'], conversion_details=[{'target_type': 'float3', 'source_expression': '_11138, _11135.y, _10887', 'line_position': 40}])", "CodeLineInfo(line_number=383, content='_17964.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11140.xy, uint(rint(_11140.z)), level(0.0)).x);', variables=6, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['sSHAOAlphaVTSampler.sample', '_11140.z', 'x', '_11140.xy', '_17964.w', 'sSHAOAlphaVTSamplerSmplr'], conversion_details=[{'target_type': 'half', 'source_expression': 'sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11140.xy, uint(rint(_11140.z', 'line_position': 35}])", "CodeLineInfo(line_number=384, content='float3 _11151 = float3(_11138, _11135.y, _10900);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_11151', '_11138', '_10900', '_11135.y'], conversion_details=[{'target_type': 'float3', 'source_expression': '_11138, _11135.y, _10900', 'line_position': 40}])", "CodeLineInfo(line_number=385, content='half3 _11156 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11151.xy, uint(rint(_11151.z)), level(0.0)).xyz);', variables=6, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_11156', 'sSHAORGBVTSampler.sample', 'sSHAORGBVTSamplerSmplr', '_11151.xy', 'xyz', '_11151.z'], conversion_details=[{'target_type': 'half3', 'source_expression': 'sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11151.xy, uint(rint(_11151.z', 'line_position': 39}])", "CodeLineInfo(line_number=386, content='half4 _11163 = mix(half4(_11118.x, _11118.y, _11118.z, _17962.w), half4(_11156.x, _11156.y, _11156.z, _17964.w), half4(half(fast::clamp(_11059 - _11064, 0.0, 1.0))));', variables=12, float_vars=0, half_vars=1, conversions=3, has_conversion=True, all_variables=['_11163', '_11156.y', '_11118.y', '_11156.x', '_11118.x', '_11064', '_17964.w', '_17962.w', '_11118.z', '_11059', '_11156.z', 'fast'], conversion_details=[{'target_type': 'half4', 'source_expression': '_11118.x, _11118.y, _11118.z, _17962.w', 'line_position': 43}, {'target_type': 'half4', 'source_expression': '_11156.x, _11156.y, _11156.z, _17964.w', 'line_position': 90}, {'target_type': 'half4', 'source_expression': 'half(fast::clamp(_11059 - _11064, 0.0, 1.0', 'line_position': 137}])", "CodeLineInfo(line_number=387, content='_18297 = clamp((_11163.w * half(32.0)) + half(float(dot(half3(_8373), half3((float3(_11163.xyz) * float3(2.0)) - float3(1.0)))) * 2.0), half(0.0), half(1.0));', variables=4, float_vars=0, half_vars=0, conversions=2, has_conversion=True, all_variables=['_11163.xyz', '_11163.w', '_8373', '_18297'], conversion_details=[{'target_type': 'half', 'source_expression': 'float(dot(half3(_8373', 'line_position': 65}, {'target_type': 'half3', 'source_expression': '(float3(_11163.xyz', 'line_position': 94}])", "CodeLineInfo(line_number=391, content='float2 _11233 = ((_10762.xz - float2(0.5)) * 0.9375) + float2(0.5);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11233', '_10762.xz'], conversion_details=[])", "CodeLineInfo(line_number=392, content='float _11240 = _10822.x * 0.0041666668839752674102783203125;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11240', '_10822.x'], conversion_details=[])", "CodeLineInfo(line_number=393, content='float _11244 = ((_11240 - floor(_11240)) - 0.5) * 0.9375;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11244', '_11240'], conversion_details=[])", "CodeLineInfo(line_number=394, content='float _11250 = _10822.z * 0.0041666668839752674102783203125;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11250', '_10822.z'], conversion_details=[])", "CodeLineInfo(line_number=395, content='float _11254 = ((_11250 - floor(_11250)) - 0.5) * 0.9375;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11254', '_11250'], conversion_details=[])", "CodeLineInfo(line_number=396, content='float _11259 = _11233.x;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11259', '_11233.x'], conversion_details=[])", "CodeLineInfo(line_number=398, content='_17977.x = (_11259 < (_11244 + 0.5)) ? fast::min(_11259, _11244 + 0.49609375) : fast::max(_11259, _11244 + 0.50390625);', variables=4, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['fast', '_17977.x', '_11244', '_11259'], conversion_details=[])", "CodeLineInfo(line_number=399, content='float _11277 = _11233.y;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11277', '_11233.y'], conversion_details=[])", "CodeLineInfo(line_number=400, content='_17977.z = (_11277 < (_11254 + 0.5)) ? fast::min(_11277, _11254 + 0.49609375) : fast::max(_11277, _11254 + 0.50390625);', variables=4, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11277', 'fast', '_17977.z', '_11254'], conversion_details=[])", "CodeLineInfo(line_number=401, content='float _11298 = (_10762.y * 64.0) - 0.5;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11298', '_10762.y'], conversion_details=[])", "CodeLineInfo(line_number=402, content='float _11303 = floor(_11298);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11303', '_11298'], conversion_details=[])", "CodeLineInfo(line_number=403, content='uint _11306 = (_11298 < 0.0) ? 63u : uint(_11303);', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11298', '_11306', '_11303'], conversion_details=[])", "CodeLineInfo(line_number=404, content='uint _11309 = _11306 + 1u;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11309', 'uint', '_11306'], conversion_details=[])", "CodeLineInfo(line_number=405, content='uint _21300 = (_11309 >= 64u) ? 0u : _11309;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11309', 'uint', '_21300'], conversion_details=[])", "CodeLineInfo(line_number=406, content='float3 _11340 = float3((float2(float(_11306 & 7u), float(_11306 >> 3u)) + _17977.xz) * 0.125, _10887);', variables=4, float_vars=1, half_vars=0, conversions=2, has_conversion=True, all_variables=['_11340', '_17977.xz', '_11306', '_10887'], conversion_details=[{'target_type': 'float3', 'source_expression': '(float2(float(_11306 & 7u', 'line_position': 40}, {'target_type': 'float', 'source_expression': '_11306 >> 3u', 'line_position': 75}])", "CodeLineInfo(line_number=407, content='float3 _11365 = float3((float2(float(_21300 & 7u), float(_21300 >> 3u)) + _17977.xz) * 0.125, _10887);', variables=4, float_vars=1, half_vars=0, conversions=2, has_conversion=True, all_variables=['_11365', '_17977.xz', '_21300', '_10887'], conversion_details=[{'target_type': 'float3', 'source_expression': '(float2(float(_21300 & 7u', 'line_position': 40}, {'target_type': 'float', 'source_expression': '_21300 >> 3u', 'line_position': 75}])", "CodeLineInfo(line_number=408, content='_18297 = half(mix(float(half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11340.xy, uint(rint(_11340.z)), level(0.0)).x)), float(half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11365.xy, uint(rint(_11365.z)), level(0.0)).x)), fast::clamp(_11298 - _11303, 0.0, 1.0))) * half(32.0);', variables=11, float_vars=0, half_vars=0, conversions=2, has_conversion=True, all_variables=['sSHAOAlphaVTSampler.sample', 'x', '_18297', '_11340.z', '_11340.xy', '_11365.xy', '_11365.z', 'sSHAOAlphaVTSamplerSmplr', 'fast', '_11298', '_11303'], conversion_details=[{'target_type': 'half', 'source_expression': 'mix(float(half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11340.xy, uint(rint(_11340.z', 'line_position': 33}, {'target_type': 'float', 'source_expression': 'half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11365.xy, uint(rint(_11365.z', 'line_position': 156}])", "CodeLineInfo(line_number=410, content='float3 _11404 = (((in.IN_WorldPosition.xyz - _10822) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)) * 2.0) - float3(1.0);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11404', 'in.IN_WorldPosition.xyz', '_10822'], conversion_details=[])", "CodeLineInfo(line_number=411, content='float3 _11407 = _11404 * _11404;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11407', '_11404'], conversion_details=[])", "CodeLineInfo(line_number=412, content='float3 _11410 = _11407 * _11407;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11410', '_11407'], conversion_details=[])", "CodeLineInfo(line_number=414, content='if ((!((_8397 & 4u) != 0u)) && ((_8397 & 32768u) > 0u))', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8397'], conversion_details=[])", "CodeLineInfo(line_number=416, content='_18303 = half(mix(float(_18297), 1.0, fast::clamp(fast::max(_11410.x, fast::max(_11410.y, _11410.z)), 0.0, 1.0)));', variables=6, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_18297', '_11410.x', '_18303', '_11410.y', 'fast', '_11410.z'], conversion_details=[{'target_type': 'half', 'source_expression': 'mix(float(_18297', 'line_position': 33}])", "CodeLineInfo(line_number=420, content='_18303 = _18297;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18303', '_18297'], conversion_details=[])", "CodeLineInfo(line_number=422, content='_18305 = _18303;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18303', '_18305'], conversion_details=[])", "CodeLineInfo(line_number=426, content='_18305 = _9019;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9019', '_18305'], conversion_details=[])", "CodeLineInfo(line_number=428, content='_18304 = _18305;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18305', '_18304'], conversion_details=[])", "CodeLineInfo(line_number=432, content='_18304 = _9019;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9019', '_18304'], conversion_details=[])", "CodeLineInfo(line_number=434, content='float _11467 = _Block1.SHAOParam.z * _Block1.SHAOParam.z;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11467', '_Block1.SHAOParam.z'], conversion_details=[])", "CodeLineInfo(line_number=435, content='float3 _11470 = in.IN_WorldPosition.xyz - _8435;', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11470', '_8435', 'in.IN_WorldPosition.xyz'], conversion_details=[])", "CodeLineInfo(line_number=436, content='float _11479 = fast::clamp((_11467 - dot(_11470, _11470)) / _11467, 0.0, 1.0);', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11479', 'fast', '_11470', '_11467'], conversion_details=[])", "CodeLineInfo(line_number=437, content='_18306 = half(1.0 - fast::clamp((1.0 - float(half(fast::clamp(1.0 - float(half(powr(float(half(fast::clamp(1.0 - float(_18304), 0.0, 1.0))), fast::max(mix(6.0, 0.0, fast::clamp(_Block1.SHGIParam2.w, 0.0, 1.0)), 0.5)))), 0.0, 1.0)))) * float(half(mix(_Block1.SHAOParam.x, _Block1.SHAOParam.y, 1.0 - (_11479 * _11479)))), 0.0, 1.0));', variables=7, float_vars=0, half_vars=0, conversions=2, has_conversion=True, all_variables=['_11479', '_Block1.SHAOParam.x', '_Block1.SHAOParam.y', 'fast', '_Block1.SHGIParam2.w', '_18306', '_18304'], conversion_details=[{'target_type': 'half', 'source_expression': '1.0 - fast::clamp((1.0 - float(half(fast::clamp(1.0 - float(half(powr(float(half(fast::clamp(1.0 - float(_18304', 'line_position': 21}, {'target_type': 'float', 'source_expression': 'half(mix(_Block1.SHAOParam.x, _Block1.SHAOParam.y, 1.0 - (_11479 * _11479', 'line_position': 247}])", "CodeLineInfo(line_number=441, content='_18306 = _9019;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18306', '_9019'], conversion_details=[])", "CodeLineInfo(line_number=444, content='if (!((_8397 & 64u) > 0u))', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8397'], conversion_details=[])", "CodeLineInfo(line_number=446, content='_18330 = _8346 * _18306;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8346', '_18306', '_18330'], conversion_details=[])", "CodeLineInfo(line_number=450, content='_18330 = _8346;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8346', '_18330'], conversion_details=[])", "CodeLineInfo(line_number=452, content='_18329 = _18330;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18329', '_18330'], conversion_details=[])", "CodeLineInfo(line_number=456, content='_18329 = _8346;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8346', '_18329'], conversion_details=[])", "CodeLineInfo(line_number=458, content='float3 _11517 = float3(half3(_8373));', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_11517', '_8373'], conversion_details=[{'target_type': 'float3', 'source_expression': 'half3(_8373', 'line_position': 20}])", "CodeLineInfo(line_number=459, content='float3 _11600 = _Block1.CameraPos.xyz + (fast::normalize(float3(_Block1.SunDirection.x, fast::min(_Block1.SunDirection.y, 1.0), _Block1.SunDirection.z)) * 200000.0);', variables=6, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_11600', '_Block1.SunDirection.y', '_Block1.SunDirection.z', '_Block1.CameraPos.xyz', '_Block1.SunDirection.x', 'fast'], conversion_details=[{'target_type': 'float3', 'source_expression': '_Block1.SunDirection.x, fast::min(_Block1.SunDirection.y, 1.0', 'line_position': 61}])", "CodeLineInfo(line_number=460, content='float3 _11604 = reflect(-_11517, _9109);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11604', '_9109', '_11517'], conversion_details=[])", "CodeLineInfo(line_number=461, content='float3 _11611 = (_11604 * dot(_11600, _11604)) - _11600;', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11611', '_11604', '_11600'], conversion_details=[])", "CodeLineInfo(line_number=462, content='float3 _11622 = fast::normalize(_11600 + (_11611 * fast::clamp(4500.0 / length(_11611), 0.0, 1.0)));', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11622', 'fast', '_11611', '_11600'], conversion_details=[])", "CodeLineInfo(line_number=463, content='half _11536 = clamp(half(dot(_9109, _11622)), half(0.0), half(1.0));', variables=3, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_11536', '_9109', '_11622'], conversion_details=[{'target_type': 'half', 'source_expression': 'dot(_9109, _11622', 'line_position': 24}])", "CodeLineInfo(line_number=464, content='float _11560 = float(half(fast::max(0.119999997317790985107421875, float(_18272))));', variables=3, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_11560', '_18272', 'fast'], conversion_details=[{'target_type': 'float', 'source_expression': 'half(fast::max(0.119999997317790985107421875, float(_18272', 'line_position': 19}])", "CodeLineInfo(line_number=465, content='float _11629 = fast::max(0.00999999977648258209228515625, fast::clamp((4.125 * _11560) - 0.319999992847442626953125, 0.0, 1.0));', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11629', '_11560', 'fast'], conversion_details=[])", "CodeLineInfo(line_number=466, content='float _11919 = float(_11536);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_11919', '_11536'], conversion_details=[{'target_type': 'float', 'source_expression': '_11536', 'line_position': 19}])", "CodeLineInfo(line_number=467, content='float _11925 = dot(_9109, _11517);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11925', '_11517', '_9109'], conversion_details=[])", "CodeLineInfo(line_number=468, content='float3 _11940 = fast::normalize(_11517 + _11622);', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11940', 'fast', '_11517', '_11622'], conversion_details=[])", "CodeLineInfo(line_number=469, content='float _11945 = fast::clamp(dot(_9109, _11940), 0.0, 1.0);', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11945', 'fast', '_11940', '_9109'], conversion_details=[])", "CodeLineInfo(line_number=470, content='float _11951 = fast::clamp(dot(_11517, _11940), 0.0, 1.0);', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11951', 'fast', '_11940', '_11517'], conversion_details=[])", "CodeLineInfo(line_number=471, content='float _11736 = fast::clamp(abs(fast::clamp(_11925, 0.0, 1.0)) + 9.9999997473787516355514526367188e-06, 0.0, 1.0);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11736', 'fast', '_11925'], conversion_details=[])", "CodeLineInfo(line_number=472, content='half4 _11959 = half4(half(0.60000002384185791015625));', variables=1, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_11959'], conversion_details=[{'target_type': 'half4', 'source_expression': 'half(0.60000002384185791015625', 'line_position': 19}])", "CodeLineInfo(line_number=473, content='half4 _11967 = _11959 * _11959;', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_11967', '_11959'], conversion_details=[])", "CodeLineInfo(line_number=474, content='half4 _11970 = half4(half(1.0)) - _11967;', variables=2, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_11970', '_11967'], conversion_details=[{'target_type': 'half4', 'source_expression': 'half(1.0', 'line_position': 19}])", "CodeLineInfo(line_number=475, content='half4 _11973 = half4(half(1.0)) + _11967;', variables=2, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_11973', '_11967'], conversion_details=[{'target_type': 'half4', 'source_expression': 'half(1.0', 'line_position': 19}])", "CodeLineInfo(line_number=476, content='half4 _11975 = _11959 * half(2.0);', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_11975', '_11959'], conversion_details=[])", "CodeLineInfo(line_number=477, content='half4 _11982 = half4(half(1.5));', variables=1, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_11982'], conversion_details=[{'target_type': 'half4', 'source_expression': 'half(1.5', 'line_position': 19}])", "CodeLineInfo(line_number=478, content='float _11756 = exp2((((-5.554729938507080078125) * _11951) - 6.9831600189208984375) * _11951);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11756', '_11951'], conversion_details=[])", "CodeLineInfo(line_number=479, content='float _11763 = _11756 + ((1.0 - _11756) * 0.039999999105930328369140625);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11763', '_11756'], conversion_details=[])", "CodeLineInfo(line_number=480, content='half _11764 = half(0.699999988079071044921875);', variables=1, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_11764'], conversion_details=[])", "CodeLineInfo(line_number=481, content='half _11768 = half(float(_11764) + 0.100000001490116119384765625);', variables=2, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_11768', '_11764'], conversion_details=[{'target_type': 'half', 'source_expression': 'float(_11764', 'line_position': 18}])", "CodeLineInfo(line_number=482, content='half _11772 = _9199 - _8351;', variables=3, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_11772', '_9199', '_8351'], conversion_details=[])", "CodeLineInfo(line_number=483, content='half _11777 = _9199 + _11768;', variables=3, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_11777', '_9199', '_11768'], conversion_details=[])", "CodeLineInfo(line_number=484, content='half _11790 = _9199 + _11764;', variables=3, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_11790', '_11764', '_9199'], conversion_details=[])", "CodeLineInfo(line_number=485, content='half3 _11812 = half3((float3(half3(_Block1.SunColor.xyz)) * _11919) * float(clamp(((_11772 + _11768) / _11777) * _11777, half(0.0), half(1.0)) * clamp(((_11536 + _11764) / _11790) * _11790, half(0.0), half(1.0))));', variables=9, float_vars=0, half_vars=1, conversions=2, has_conversion=True, all_variables=['_11812', '_11777', '_11536', '_11919', '_11768', '_11790', '_11764', '_Block1.SunColor.xyz', '_11772'], conversion_details=[{'target_type': 'half3', 'source_expression': '(float3(half3(_Block1.SunColor.xyz', 'line_position': 19}, {'target_type': 'float', 'source_expression': 'clamp(((_11772 + _11768', 'line_position': 74}])", "CodeLineInfo(line_number=486, content='half _11815 = half(10.0);', variables=1, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_11815'], conversion_details=[])", "CodeLineInfo(line_number=487, content='half _11827 = clamp(dot(half3(fast::normalize(_11622 + _11517)), _9064), half(0.0), half(1.0));', variables=5, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_11827', '_11517', '_9064', 'fast', '_11622'], conversion_details=[{'target_type': 'half3', 'source_expression': 'fast::normalize(_11622 + _11517', 'line_position': 28}])", "CodeLineInfo(line_number=488, content='float _11858 = float(_11815) * 0.5;', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_11858', '_11815'], conversion_details=[{'target_type': 'float', 'source_expression': '_11815', 'line_position': 19}])", "CodeLineInfo(line_number=489, content='float _11862 = float(_9251 + _11815);', variables=3, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_11862', '_11815', '_9251'], conversion_details=[{'target_type': 'float', 'source_expression': '_9251 + _11815', 'line_position': 19}])", "CodeLineInfo(line_number=490, content='float _12049 = _11560 * _11560;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12049', '_11560'], conversion_details=[])", "CodeLineInfo(line_number=491, content='float _12062 = _12049 / (((((_11945 * _12049) * _12049) - _11945) * _11945) + 1.0);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12062', '_11945', '_12049'], conversion_details=[])", "CodeLineInfo(line_number=492, content='float _12080 = _12049 * _12049;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12080', '_12049'], conversion_details=[])", "CodeLineInfo(line_number=493, content='float _12108 = float(half(9.9956989288330078125e-05));', variables=1, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_12108'], conversion_details=[{'target_type': 'float', 'source_expression': 'half(9.9956989288330078125e-05', 'line_position': 19}])", "CodeLineInfo(line_number=494, content='float3 _12025 = float3(_10557);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_12025', '_10557'], conversion_details=[{'target_type': 'float3', 'source_expression': '_10557', 'line_position': 20}])", "CodeLineInfo(line_number=495, content='float3 _12120 = float3(fast::clamp(50.0 * _12025.y, 0.0, 1.0)) - _12025;', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_12120', 'fast', '_12025', '_12025.y'], conversion_details=[{'target_type': 'float3', 'source_expression': 'fast::clamp(50.0 * _12025.y, 0.0, 1.0', 'line_position': 20}])", "CodeLineInfo(line_number=496, content='float3 _8521 = ((_9698 + (float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_11827 * _11827)), 0.0078125))), _11858)) * 0.15915493667125701904296875) * float(half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05)), _11973 - (_11975 * (-half(dot(_11517, _11622))))), _11982)) * float4(0.0795769989490509033203125)).x))), half3(half(fast::clamp((_11763 * _11763) * float(dot(_11812, _8303)), 0.0, 1.0)))) * _11812) * float3(_11629))) + (((float3(_11812) * ((_12025 + (_12120 * _11756)) * (fast::min(1000.0, (_12062 * _12062) * 0.3183098733425140380859375) * (1.0 / fast::max((_11736 + sqrt((_11736 * (_11736 - (_11736 * _12080))) + _12080)) * (_11919 + sqrt((_11919 * (_11919 - (_11919 * _12080))) + _12080)), _12108))))) * _11629) * float(_11772))) * _10588;', variables=29, float_vars=1, half_vars=0, conversions=8, has_conversion=True, all_variables=['_8521', '_11756', '_11970', '_11858', '_12108', '_11973', 'x', '_11812', '_11736', '_12062', '_9179', '_12025', '_11772', '_8303', '_11763', '_10588', '_11629', '_11862', '_12120', '_12080', '_11622', '_9698', '_9199', '_11919', '_11982', '_11975', '_11517', 'fast', '_11827'], conversion_details=[{'target_type': 'float3', 'source_expression': 'mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_11827 * _11827', 'line_position': 30}, {'target_type': 'float', 'source_expression': 'half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05', 'line_position': 187}, {'target_type': 'half', 'source_expression': 'dot(_11517, _11622', 'line_position': 285}, {'target_type': 'half3', 'source_expression': 'half(fast::clamp((_11763 * _11763', 'line_position': 369}, {'target_type': 'float', 'source_expression': 'dot(_11812, _8303', 'line_position': 412}, {'target_type': 'float3', 'source_expression': '_11629', 'line_position': 464}, {'target_type': 'float3', 'source_expression': '_11812', 'line_position': 486}, {'target_type': 'float', 'source_expression': '_11772', 'line_position': 781}])", "CodeLineInfo(line_number=497, content='uint _12171 = uint(_Block1.LightDataBuffer[0].x);', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12171', 'x', '_Block1.LightDataBuffer'], conversion_details=[])", "CodeLineInfo(line_number=504, content='_19825 = _18526;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18526', '_19825'], conversion_details=[])", "CodeLineInfo(line_number=505, content='_19755 = _19185;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19755', '_19185'], conversion_details=[])", "CodeLineInfo(line_number=506, content='_19720 = _19185;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19185', '_19720'], conversion_details=[])", "CodeLineInfo(line_number=507, content='_19685 = _19185;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19185', '_19685'], conversion_details=[])", "CodeLineInfo(line_number=508, content='_18431 = _9698;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18431', '_9698'], conversion_details=[])", "CodeLineInfo(line_number=509, content='_18429 = _9179;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9179', '_18429'], conversion_details=[])", "CodeLineInfo(line_number=524, content='for (uint _18428 = 0u; _18428 < _12171; _19825 = _20953, _19790 = _20938, _19755 = _20923, _19720 = _20908, _19685 = _20893, _19613 = _20863, _19577 = _20848, _19485 = _20833, _18431 = _19965, _18429 = _19923, _18428++)', variables=23, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_20908', '_19613', '_20953', '_19755', '_20893', '_18431', '_20923', 'uint', '_19965', '_19685', '_20938', '_19825', '_19577', '_18428', '_18429', '_19485', '_20833', '_19720', '_19790', '_20848', '_20863', '_19923', '_12171'], conversion_details=[])", "CodeLineInfo(line_number=526, content='uint _12181 = _18428 * 4u;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18428', 'uint', '_12181'], conversion_details=[])", "CodeLineInfo(line_number=527, content='int _12188 = int(_12181 + 1u);', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12181', '_12188'], conversion_details=[])", "CodeLineInfo(line_number=528, content='int _12195 = int(_12181 + 2u);', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12195', '_12181'], conversion_details=[])", "CodeLineInfo(line_number=529, content='int _12202 = int(_12181 + 3u);', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12181', '_12202'], conversion_details=[])", "CodeLineInfo(line_number=530, content='int _12209 = int(_12181 + 4u);', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12181', '_12209'], conversion_details=[])", "CodeLineInfo(line_number=531, content='uint _12298 = as_type<uint>(_Block1.LightDataBuffer[_12209].x);', variables=6, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['x', '_Block1.LightDataBuffer', '_12298', '_12209', 'as_type', 'uint'], conversion_details=[])", "CodeLineInfo(line_number=532, content='if (!((_12298 & 2097152u) == 2097152u))', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12298'], conversion_details=[])", "CodeLineInfo(line_number=534, content='_20953 = _19825;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19825', '_20953'], conversion_details=[])", "CodeLineInfo(line_number=535, content='_20938 = _19790;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_20938', '_19790'], conversion_details=[])", "CodeLineInfo(line_number=536, content='_20923 = _19755;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19755', '_20923'], conversion_details=[])", "CodeLineInfo(line_number=537, content='_20908 = _19720;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_20908', '_19720'], conversion_details=[])", "CodeLineInfo(line_number=538, content='_20893 = _19685;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_20893', '_19685'], conversion_details=[])", "CodeLineInfo(line_number=539, content='_20863 = _19613;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19613', '_20863'], conversion_details=[])", "CodeLineInfo(line_number=540, content='_20848 = _19577;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19577', '_20848'], conversion_details=[])", "CodeLineInfo(line_number=541, content='_20833 = _19485;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_20833', '_19485'], conversion_details=[])", "CodeLineInfo(line_number=542, content='_19965 = _18431;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19965', '_18431'], conversion_details=[])", "CodeLineInfo(line_number=543, content='_19923 = _18429;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18429', '_19923'], conversion_details=[])", "CodeLineInfo(line_number=546, content='uint _12309 = _12298 & 196608u;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12298', '_12309', 'uint'], conversion_details=[])", "CodeLineInfo(line_number=555, content='if (_12309 == 196608u)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12309'], conversion_details=[])", "CodeLineInfo(line_number=557, content='float3 _12360 = -_Block1.LightDataBuffer[_12202].xyz;', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12360', '_Block1.LightDataBuffer', 'xyz', '_12202'], conversion_details=[])", "CodeLineInfo(line_number=558, content='float3 _12378 = in.IN_WorldPosition.xyz - (_Block1.LightDataBuffer[_12188].xyz + (_12360 * (dot(in.IN_WorldPosition.xyz - _Block1.LightDataBuffer[_12188].xyz, _12360) / dot(_12360, _12360))));', variables=6, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12378', 'xyz', '_Block1.LightDataBuffer', '_12188', '_12360', 'in.IN_WorldPosition.xyz'], conversion_details=[])", "CodeLineInfo(line_number=559, content='float _12381 = dot(_12378, _12378);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12381', '_12378'], conversion_details=[])", "CodeLineInfo(line_number=561, content='if (_12381 > (_Block1.LightDataBuffer[_12209].y * _Block1.LightDataBuffer[_12209].y))', variables=4, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12209', '_12381', '_Block1.LightDataBuffer', 'y'], conversion_details=[])", "CodeLineInfo(line_number=563, content='float _12392 = sqrt(_12381) - _Block1.LightDataBuffer[_12209].y;', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12392', '_Block1.LightDataBuffer', '_12209', '_12381', 'y'], conversion_details=[])", "CodeLineInfo(line_number=564, content='float _12395 = _12392 * _12392;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12395', '_12392'], conversion_details=[])", "CodeLineInfo(line_number=565, content='float _12398 = _12395 * abs(_Block1.LightDataBuffer[_12188].w);', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12398', '_Block1.LightDataBuffer', '_12188', 'w', '_12395'], conversion_details=[])", "CodeLineInfo(line_number=566, content='float _12404 = fast::clamp(1.0 - (_12398 * _12398), 0.0, 1.0);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12404', 'fast', '_12398'], conversion_details=[])", "CodeLineInfo(line_number=567, content='_19350 = fast::min(100.0, (_12404 * _12404) / (_12395 + 1.0));', variables=4, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12395', 'fast', '_12404', '_19350'], conversion_details=[])", "CodeLineInfo(line_number=571, content='_19350 = 1.0;', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19350'], conversion_details=[])", "CodeLineInfo(line_number=573, content='_19821 = half3(_Block1.LightDataBuffer[_12195].xyz);', variables=4, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_19821', '_12195', '_Block1.LightDataBuffer', 'xyz'], conversion_details=[{'target_type': 'half3', 'source_expression': '_Block1.LightDataBuffer[_12195].xyz', 'line_position': 21}])", "CodeLineInfo(line_number=574, content='_19786 = _19350;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19350', '_19786'], conversion_details=[])", "CodeLineInfo(line_number=575, content='_19751 = _12360;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19751', '_12360'], conversion_details=[])", "CodeLineInfo(line_number=576, content='_19716 = _11517;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19716', '_11517'], conversion_details=[])", "CodeLineInfo(line_number=577, content='_19681 = _9109;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9109', '_19681'], conversion_details=[])", "CodeLineInfo(line_number=578, content='_19609 = 0;', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19609'], conversion_details=[])", "CodeLineInfo(line_number=579, content='_19573 = abs(_Block1.LightDataBuffer[_12195].w);', variables=4, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19573', '_12195', '_Block1.LightDataBuffer', 'w'], conversion_details=[])", "CodeLineInfo(line_number=580, content='_19481 = clamp(half(dot(_9109, _12360)), half(0.0), half(1.0));', variables=3, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_9109', '_19481', '_12360'], conversion_details=[{'target_type': 'half', 'source_expression': 'dot(_9109, _12360', 'line_position': 27}])", "CodeLineInfo(line_number=592, content='if (_12309 == 0u)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12309'], conversion_details=[])", "CodeLineInfo(line_number=594, content='uint _12741 = as_type<uint>(_Block1.LightDataBuffer[_12195].w);', variables=6, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12195', '_Block1.LightDataBuffer', '_12741', 'w', 'as_type', 'uint'], conversion_details=[])", "CodeLineInfo(line_number=595, content='float _12858 = float((_12741 >> 0u) & 65535u) * 0.0001525902189314365386962890625;', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_12858', '_12741'], conversion_details=[{'target_type': 'float', 'source_expression': '(_12741 >> 0u', 'line_position': 31}])", "CodeLineInfo(line_number=596, content='float3 _12599 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12599', 'xyz', '_Block1.LightDataBuffer', '_12188', 'in.IN_WorldPosition.xyz'], conversion_details=[])", "CodeLineInfo(line_number=597, content='float _12602 = dot(_12599, _12599);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12602', '_12599'], conversion_details=[])", "CodeLineInfo(line_number=598, content='float3 _12606 = _12599 * rsqrt(_12602);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12606', '_12599', '_12602'], conversion_details=[])", "CodeLineInfo(line_number=599, content='float _12613 = _12602 * abs(_Block1.LightDataBuffer[_12188].w);', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12613', '_12602', '_Block1.LightDataBuffer', '_12188', 'w'], conversion_details=[])", "CodeLineInfo(line_number=600, content='float _12620 = fast::clamp(1.0 - (_12613 * _12613), 0.0, 1.0);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12620', '_12613', 'fast'], conversion_details=[])", "CodeLineInfo(line_number=601, content='float _12631 = _12620 * _12620;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12631', '_12620'], conversion_details=[])", "CodeLineInfo(line_number=603, content='if ((_12298 & 16777216u) == 16777216u)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12298'], conversion_details=[])", "CodeLineInfo(line_number=605, content='float _12641 = _12631 / ((_12602 * _Block1.LightDataBuffer[_12202].w) + 9.9999997473787516355514526367188e-05);', variables=6, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12641', '_12602', '_Block1.LightDataBuffer', '_12631', 'w', '_12202'], conversion_details=[])", "CodeLineInfo(line_number=607, content='if (_12858 > 0.00999999977648258209228515625)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12858'], conversion_details=[])", "CodeLineInfo(line_number=609, content='_19211 = fast::min(_12641, _12858);', variables=4, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['fast', '_12858', '_12641', '_19211'], conversion_details=[])", "CodeLineInfo(line_number=613, content='_19211 = _12641;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12641', '_19211'], conversion_details=[])", "CodeLineInfo(line_number=615, content='_19213 = fast::min(100.0, _19211);', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19213', 'fast', '_19211'], conversion_details=[])", "CodeLineInfo(line_number=619, content='_19213 = _12631 * 0.100000001490116119384765625;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19213', '_12631'], conversion_details=[])", "CodeLineInfo(line_number=621, content='float _12677 = fast::clamp((dot(_Block1.LightDataBuffer[_12202].xyz, -_12606) - _Block1.LightDataBuffer[_12209].z) * _Block1.LightDataBuffer[_12209].y, 0.0, 1.0);', variables=9, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12677', '_12606', 'xyz', 'z', '_Block1.LightDataBuffer', '_12202', '_12209', 'fast', 'y'], conversion_details=[])", "CodeLineInfo(line_number=622, content='_19822 = half3(fast::min(float3(3000.0), _Block1.LightDataBuffer[_12195].xyz * _19213));', variables=6, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_19213', 'xyz', '_12195', '_Block1.LightDataBuffer', '_19822', 'fast'], conversion_details=[{'target_type': 'half3', 'source_expression': 'fast::min(float3(3000.0', 'line_position': 25}])", "CodeLineInfo(line_number=623, content='_19787 = _12677 * _12677;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12677', '_19787'], conversion_details=[])", "CodeLineInfo(line_number=624, content='_19752 = _12606;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12606', '_19752'], conversion_details=[])", "CodeLineInfo(line_number=625, content='_19717 = _11517;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19717', '_11517'], conversion_details=[])", "CodeLineInfo(line_number=626, content='_19682 = _9109;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9109', '_19682'], conversion_details=[])", "CodeLineInfo(line_number=627, content='_19610 = 0;', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19610'], conversion_details=[])", "CodeLineInfo(line_number=628, content='_19574 = float((_12741 >> 16u) & 65535u) * 0.001525902189314365386962890625;', variables=2, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_12741', '_19574'], conversion_details=[{'target_type': 'float', 'source_expression': '(_12741 >> 16u', 'line_position': 25}])", "CodeLineInfo(line_number=629, content='_19482 = clamp(half(dot(_9109, _12606)), half(0.0), half(1.0));', variables=3, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_12606', '_9109', '_19482'], conversion_details=[{'target_type': 'half', 'source_expression': 'dot(_9109, _12606', 'line_position': 31}])", "CodeLineInfo(line_number=641, content='if (_12309 == 65536u)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12309'], conversion_details=[])", "CodeLineInfo(line_number=643, content='uint _13098 = as_type<uint>(_Block1.LightDataBuffer[_12195].w);', variables=6, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12195', '_Block1.LightDataBuffer', 'w', '_13098', 'as_type', 'uint'], conversion_details=[])", "CodeLineInfo(line_number=644, content='float3 _12933 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12933', 'xyz', '_Block1.LightDataBuffer', '_12188', 'in.IN_WorldPosition.xyz'], conversion_details=[])", "CodeLineInfo(line_number=645, content='float _12936 = dot(_12933, _12933);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12936', '_12933'], conversion_details=[])", "CodeLineInfo(line_number=646, content='float3 _12942 = _12933 / float3(sqrt(_12936));', variables=3, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_12942', '_12933', '_12936'], conversion_details=[{'target_type': 'float3', 'source_expression': 'sqrt(_12936', 'line_position': 45}])", "CodeLineInfo(line_number=647, content='float _12950 = _12936 * abs(_Block1.LightDataBuffer[_12188].w);', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12950', '_Block1.LightDataBuffer', '_12188', 'w', '_12936'], conversion_details=[])", "CodeLineInfo(line_number=648, content='float _12956 = fast::clamp(1.0 - (_12950 * _12950), 0.0, 1.0);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12956', '_12950', 'fast'], conversion_details=[])", "CodeLineInfo(line_number=649, content='float _12972 = fast::min(100.0, (_12956 * _12956) / ((_12936 * _Block1.LightDataBuffer[_12209].w) + 9.9999997473787516355514526367188e-05));', variables=7, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12972', '_Block1.LightDataBuffer', '_12956', 'w', '_12209', '_12936', 'fast'], conversion_details=[])", "CodeLineInfo(line_number=650, content='float _13202 = float((_13098 >> 0u) & 65535u) * 0.0001525902189314365386962890625;', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_13202', '_13098'], conversion_details=[{'target_type': 'float', 'source_expression': '(_13098 >> 0u', 'line_position': 35}])", "CodeLineInfo(line_number=652, content='if (_13202 > 0.00999999977648258209228515625)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13202'], conversion_details=[])", "CodeLineInfo(line_number=654, content='_19070 = fast::min(_12972, _13202);', variables=4, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12972', 'fast', '_19070', '_13202'], conversion_details=[])", "CodeLineInfo(line_number=658, content='_19070 = _12972;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12972', '_19070'], conversion_details=[])", "CodeLineInfo(line_number=660, content='_19823 = half3(fast::min(float3(3000.0), _Block1.LightDataBuffer[_12195].xyz * _19070) * (((_12298 & 16777216u) == 16777216u) ? _Block1.TimeOfDayInfos.y : 1.0));', variables=8, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['xyz', '_12195', '_19070', '_Block1.LightDataBuffer', '_12298', '_19823', '_Block1.TimeOfDayInfos.y', 'fast'], conversion_details=[{'target_type': 'half3', 'source_expression': 'fast::min(float3(3000.0', 'line_position': 29}])", "CodeLineInfo(line_number=661, content='_19788 = 1.0;', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19788'], conversion_details=[])", "CodeLineInfo(line_number=662, content='_19753 = _12942;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12942', '_19753'], conversion_details=[])", "CodeLineInfo(line_number=663, content='_19718 = _11517;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_11517', '_19718'], conversion_details=[])", "CodeLineInfo(line_number=664, content='_19683 = _9109;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19683', '_9109'], conversion_details=[])", "CodeLineInfo(line_number=665, content='_19611 = 0;', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19611'], conversion_details=[])", "CodeLineInfo(line_number=666, content='_19575 = float((_13098 >> 16u) & 65535u) * 0.001525902189314365386962890625;', variables=2, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_19575', '_13098'], conversion_details=[{'target_type': 'float', 'source_expression': '(_13098 >> 16u', 'line_position': 29}])", "CodeLineInfo(line_number=667, content='_19483 = ((_12298 & 262144u) == 262144u) ? _9019 : half(fast::clamp(dot(_9109, _12942), 0.0, 1.0));', variables=6, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_12298', '_19483', '_12942', '_9109', 'fast', '_9019'], conversion_details=[{'target_type': 'half', 'source_expression': 'fast::clamp(dot(_9109, _12942', 'line_position': 71}])", "CodeLineInfo(line_number=671, content='bool _13270 = _12309 == 131072u;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13270', '_12309', 'bool'], conversion_details=[])", "CodeLineInfo(line_number=677, content='if (_13270)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13270'], conversion_details=[])", "CodeLineInfo(line_number=679, content='float3 _13339 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13339', 'xyz', '_Block1.LightDataBuffer', '_12188', 'in.IN_WorldPosition.xyz'], conversion_details=[])", "CodeLineInfo(line_number=680, content='float _13342 = dot(_13339, _13339);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13342', '_13339'], conversion_details=[])", "CodeLineInfo(line_number=681, content='float _13348 = _13342 * abs(_Block1.LightDataBuffer[_12188].w);', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13348', '_Block1.LightDataBuffer', '_12188', 'w', '_13342'], conversion_details=[])", "CodeLineInfo(line_number=682, content='float _13356 = fast::clamp(1.0 - (_13348 * _13348), 0.0, 1.0);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13356', 'fast', '_13348'], conversion_details=[])", "CodeLineInfo(line_number=683, content='float3 _13433 = fast::normalize(_11517 - (_9109 * _11925));', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13433', '_11925', '_9109', '_11517', 'fast'], conversion_details=[])", "CodeLineInfo(line_number=684, content='float3x3 _13459 = float3x3(_13433, cross(_9109, _13433), _9109);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13459', '_13433', '_9109'], conversion_details=[])", "CodeLineInfo(line_number=685, content='float3 _13466 = float3(_Block1.LightDataBuffer[_12202].xyz) * _Block1.LightDataBuffer[_12195].w;', variables=6, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_13466', 'xyz', '_12195', '_Block1.LightDataBuffer', 'w', '_12202'], conversion_details=[{'target_type': 'float3', 'source_expression': '_Block1.LightDataBuffer[_12202].xyz', 'line_position': 40}])", "CodeLineInfo(line_number=686, content='float3 _13467 = _13339 - _13466;', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13467', '_13339', '_13466'], conversion_details=[])", "CodeLineInfo(line_number=687, content='float3 _13472 = float3(_Block1.LightDataBuffer[_12209].yzw) * _Block1.LightDataBuffer[_12202].w;', variables=6, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_13472', '_Block1.LightDataBuffer', 'yzw', 'w', '_12202', '_12209'], conversion_details=[{'target_type': 'float3', 'source_expression': '_Block1.LightDataBuffer[_12209].yzw', 'line_position': 40}])", "CodeLineInfo(line_number=688, content='float3 _13484 = _13339 + _13466;', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13484', '_13339', '_13466'], conversion_details=[])", "CodeLineInfo(line_number=689, content='float3 _13657 = fast::normalize((_13467 - _13472) * _13459);', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13657', '_13467', '_13472', 'fast', '_13459'], conversion_details=[])", "CodeLineInfo(line_number=690, content='float3 _13660 = fast::normalize((_13484 - _13472) * _13459);', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13660', '_13484', '_13472', 'fast', '_13459'], conversion_details=[])", "CodeLineInfo(line_number=691, content='float3 _13663 = fast::normalize((_13484 + _13472) * _13459);', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13663', '_13484', '_13472', 'fast', '_13459'], conversion_details=[])", "CodeLineInfo(line_number=692, content='float3 _13666 = fast::normalize((_13467 + _13472) * _13459);', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13666', '_13467', '_13472', 'fast', '_13459'], conversion_details=[])", "CodeLineInfo(line_number=693, content='float _13712 = dot(_13657, _13660);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13712', '_13657', '_13660'], conversion_details=[])", "CodeLineInfo(line_number=694, content='float _13714 = abs(_13712);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13714', '_13712'], conversion_details=[])", "CodeLineInfo(line_number=695, content='float _13728 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13714)) * _13714)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13714) * _13714));', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13728', '_13714'], conversion_details=[])", "CodeLineInfo(line_number=696, content='float _13753 = dot(_13660, _13663);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13753', '_13660', '_13663'], conversion_details=[])", "CodeLineInfo(line_number=697, content='float _13755 = abs(_13753);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13755', '_13753'], conversion_details=[])", "CodeLineInfo(line_number=698, content='float _13769 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13755)) * _13755)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13755) * _13755));', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13769', '_13755'], conversion_details=[])", "CodeLineInfo(line_number=699, content='float _13794 = dot(_13663, _13666);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13794', '_13663', '_13666'], conversion_details=[])", "CodeLineInfo(line_number=700, content='float _13796 = abs(_13794);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13796', '_13794'], conversion_details=[])", "CodeLineInfo(line_number=701, content='float _13810 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13796)) * _13796)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13796) * _13796));', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13810', '_13796'], conversion_details=[])", "CodeLineInfo(line_number=702, content='float _13835 = dot(_13666, _13657);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13835', '_13657', '_13666'], conversion_details=[])", "CodeLineInfo(line_number=703, content='float _13837 = abs(_13835);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13837', '_13835'], conversion_details=[])", "CodeLineInfo(line_number=704, content='float _13851 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13837)) * _13837)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13837) * _13837));', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13851', '_13837'], conversion_details=[])", "CodeLineInfo(line_number=705, content='float3 _13700 = cross(_13660, (_13657 * (-((_13712 > 0.0) ? _13728 : ((0.5 * rsqrt(fast::max(1.0 - (_13712 * _13712), 1.0000000116860974230803549289703e-07))) - _13728)))) + (_13663 * ((_13753 > 0.0) ? _13769 : ((0.5 * rsqrt(fast::max(1.0 - (_13753 * _13753), 1.0000000116860974230803549289703e-07))) - _13769)))) + cross(_13666, (_13657 * ((_13835 > 0.0) ? _13851 : ((0.5 * rsqrt(fast::max(1.0 - (_13835 * _13835), 1.0000000116860974230803549289703e-07))) - _13851))) + (_13663 * (-((_13794 > 0.0) ? _13810 : ((0.5 * rsqrt(fast::max(1.0 - (_13794 * _13794), 1.0000000116860974230803549289703e-07))) - _13810)))));', variables=14, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13700', '_13712', '_13666', '_13810', '_13657', '_13728', '_13663', '_13769', '_13851', '_13794', '_13753', '_13660', 'fast', '_13835'], conversion_details=[])", "CodeLineInfo(line_number=706, content='float _13531 = length(_13700);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13531', '_13700'], conversion_details=[])", "CodeLineInfo(line_number=707, content='float _13539 = step(0.0, dot(cross(_Block1.LightDataBuffer[_12202].xyz, _Block1.LightDataBuffer[_12209].yzw), _13339));', variables=7, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13539', 'xyz', '_13339', '_Block1.LightDataBuffer', 'yzw', '_12202', '_12209'], conversion_details=[])", "CodeLineInfo(line_number=708, content='_19824 = half3(_Block1.LightDataBuffer[_12195].xyz * (_13356 * _13356));', variables=5, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['xyz', '_12195', '_Block1.LightDataBuffer', '_13356', '_19824'], conversion_details=[{'target_type': 'half3', 'source_expression': '_Block1.LightDataBuffer[_12195].xyz * (_13356 * _13356', 'line_position': 33}])", "CodeLineInfo(line_number=709, content='_19789 = ((!((_12298 & 67108864u) == 67108864u)) && (_13539 > 0.0)) ? 0.0 : _13531;', variables=4, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12298', '_19789', '_13531', '_13539'], conversion_details=[])", "CodeLineInfo(line_number=710, content='_19754 = _13339 * rsqrt(_13342);', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13339', '_13342', '_19754'], conversion_details=[])", "CodeLineInfo(line_number=711, content='_19684 = _9109;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9109', '_19684'], conversion_details=[])", "CodeLineInfo(line_number=712, content='_19484 = half(fast::max(((_13531 * _13531) + ((_13700 / float3(_13531)).z * ((_13539 * 2.0) - 1.0))) / (_13531 + 1.0), 0.0));', variables=6, float_vars=0, half_vars=0, conversions=2, has_conversion=True, all_variables=['z', '_13700', '_13539', '_13531', 'fast', '_19484'], conversion_details=[{'target_type': 'half', 'source_expression': 'fast::max(((_13531 * _13531', 'line_position': 33}, {'target_type': 'float3', 'source_expression': '_13531', 'line_position': 80}])", "CodeLineInfo(line_number=716, content='_19824 = _19825;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19824', '_19825'], conversion_details=[])", "CodeLineInfo(line_number=717, content='_19789 = _19790;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19789', '_19790'], conversion_details=[])", "CodeLineInfo(line_number=718, content='_19754 = _19755;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19755', '_19754'], conversion_details=[])", "CodeLineInfo(line_number=719, content='_19684 = _19685;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19684', '_19685'], conversion_details=[])", "CodeLineInfo(line_number=720, content='_19484 = _19485;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19485', '_19484'], conversion_details=[])", "CodeLineInfo(line_number=722, content='_19823 = _19824;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19824', '_19823'], conversion_details=[])", "CodeLineInfo(line_number=723, content='_19788 = _19789;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19789', '_19788'], conversion_details=[])", "CodeLineInfo(line_number=724, content='_19753 = _19754;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19753', '_19754'], conversion_details=[])", "CodeLineInfo(line_number=725, content='_19718 = select(_19720, _11517, bool3(_13270));', variables=4, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13270', '_11517', '_19720', '_19718'], conversion_details=[])", "CodeLineInfo(line_number=726, content='_19683 = _19684;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19683', '_19684'], conversion_details=[])", "CodeLineInfo(line_number=727, content='_19611 = _13270 ? 0 : _19613;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13270', '_19613', '_19611'], conversion_details=[])", "CodeLineInfo(line_number=728, content='_19575 = _13270 ? 1.0 : _19577;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13270', '_19577', '_19575'], conversion_details=[])", "CodeLineInfo(line_number=729, content='_19483 = _19484;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19483', '_19484'], conversion_details=[])", "CodeLineInfo(line_number=731, content='_19822 = _19823;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19823', '_19822'], conversion_details=[])", "CodeLineInfo(line_number=732, content='_19787 = _19788;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19788', '_19787'], conversion_details=[])", "CodeLineInfo(line_number=733, content='_19752 = _19753;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19753', '_19752'], conversion_details=[])", "CodeLineInfo(line_number=734, content='_19717 = _19718;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19717', '_19718'], conversion_details=[])", "CodeLineInfo(line_number=735, content='_19682 = _19683;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19683', '_19682'], conversion_details=[])", "CodeLineInfo(line_number=736, content='_19610 = _19611;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19611', '_19610'], conversion_details=[])", "CodeLineInfo(line_number=737, content='_19574 = _19575;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19575', '_19574'], conversion_details=[])", "CodeLineInfo(line_number=738, content='_19482 = _19483;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19482', '_19483'], conversion_details=[])", "CodeLineInfo(line_number=740, content='_19821 = _19822;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19821', '_19822'], conversion_details=[])", "CodeLineInfo(line_number=741, content='_19786 = _19787;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19787', '_19786'], conversion_details=[])", "CodeLineInfo(line_number=742, content='_19751 = _19752;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19751', '_19752'], conversion_details=[])", "CodeLineInfo(line_number=743, content='_19716 = _19717;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19716', '_19717'], conversion_details=[])", "CodeLineInfo(line_number=744, content='_19681 = _19682;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19682', '_19681'], conversion_details=[])", "CodeLineInfo(line_number=745, content='_19609 = _19610;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19610', '_19609'], conversion_details=[])", "CodeLineInfo(line_number=746, content='_19573 = _19574;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19573', '_19574'], conversion_details=[])", "CodeLineInfo(line_number=747, content='_19481 = _19482;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19482', '_19481'], conversion_details=[])", "CodeLineInfo(line_number=749, content='float _14176 = float(_19481);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_14176', '_19481'], conversion_details=[{'target_type': 'float', 'source_expression': '_19481', 'line_position': 23}])", "CodeLineInfo(line_number=750, content='float3 _14197 = fast::normalize(_19716 + _19751);', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14197', 'fast', '_19751', '_19716'], conversion_details=[])", "CodeLineInfo(line_number=751, content='float _14202 = fast::clamp(dot(_19681, _14197), 0.0, 1.0);', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14202', '_14197', 'fast', '_19681'], conversion_details=[])", "CodeLineInfo(line_number=752, content='float _14208 = fast::clamp(dot(_19716, _14197), 0.0, 1.0);', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14208', '_14197', '_19716', 'fast'], conversion_details=[])", "CodeLineInfo(line_number=753, content='float _13993 = fast::clamp(abs(fast::clamp(dot(_19681, _19716), 0.0, 1.0)) + 9.9999997473787516355514526367188e-06, 0.0, 1.0);', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_13993', '_19716', 'fast', '_19681'], conversion_details=[])", "CodeLineInfo(line_number=754, content='float _14013 = exp2((((-5.554729938507080078125) * _14208) - 6.9831600189208984375) * _14208);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14013', '_14208'], conversion_details=[])", "CodeLineInfo(line_number=755, content='float _14020 = _14013 + ((1.0 - _14013) * 0.039999999105930328369140625);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14020', '_14013'], conversion_details=[])", "CodeLineInfo(line_number=756, content='half _14029 = _9199 - (_9199 - _19481);', variables=3, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_14029', '_9199', '_19481'], conversion_details=[])", "CodeLineInfo(line_number=757, content='half3 _14069 = half3(((float3(_19821) * float3(_19786)) * _14176) * float(clamp(((_14029 + _11768) / _11777) * _11777, half(0.0), half(1.0)) * clamp(((_19481 + _11764) / _11790) * _11790, half(0.0), half(1.0))));', variables=10, float_vars=0, half_vars=1, conversions=3, has_conversion=True, all_variables=['_14069', '_11777', '_14176', '_19481', '_19786', '_11768', '_14029', '_11790', '_19821', '_11764'], conversion_details=[{'target_type': 'half3', 'source_expression': '((float3(_19821', 'line_position': 23}, {'target_type': 'float3', 'source_expression': '_19786', 'line_position': 48}, {'target_type': 'float', 'source_expression': 'clamp(((_14029 + _11768', 'line_position': 76}])", "CodeLineInfo(line_number=758, content='half _14084 = clamp(dot(half3(fast::normalize(_19751 + _19716)), _9064), half(0.0), half(1.0));', variables=5, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_14084', '_19716', '_19751', '_9064', 'fast'], conversion_details=[{'target_type': 'half3', 'source_expression': 'fast::normalize(_19751 + _19716', 'line_position': 32}])", "CodeLineInfo(line_number=762, content='if (_19609 >= 1)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19609'], conversion_details=[])", "CodeLineInfo(line_number=764, content='float _14319 = _12049 / (((((_14202 * _12049) * _12049) - _14202) * _14202) + 1.0);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14319', '_12049', '_14202'], conversion_details=[])", "CodeLineInfo(line_number=765, content='_19883 = (_12025 + (_12120 * _14013)) * (fast::min(1000.0, (_14319 * _14319) * 0.3183098733425140380859375) * (1.0 / fast::max((_13993 + sqrt((_13993 * (_13993 - (_13993 * _12080))) + _12080)) * (_14176 + sqrt((_14176 * (_14176 - (_14176 * _12080))) + _12080)), _12108)));', variables=10, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14319', '_14176', '_13993', '_12080', '_14013', '_12108', '_12120', 'fast', '_12025', '_19883'], conversion_details=[])", "CodeLineInfo(line_number=770, content='_19883 = float3(0.0);', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19883'], conversion_details=[])", "CodeLineInfo(line_number=773, content='break; // unreachable workaround', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['break'], conversion_details=[])", "CodeLineInfo(line_number=774, content='} while(false);', variables=0, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=[], conversion_details=[])", "CodeLineInfo(line_number=775, content='_20953 = _19821;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19821', '_20953'], conversion_details=[])", "CodeLineInfo(line_number=776, content='_20938 = _19786;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_20938', '_19786'], conversion_details=[])", "CodeLineInfo(line_number=777, content='_20923 = _19751;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19751', '_20923'], conversion_details=[])", "CodeLineInfo(line_number=778, content='_20908 = _19716;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_20908', '_19716'], conversion_details=[])", "CodeLineInfo(line_number=779, content='_20893 = _19681;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_20893', '_19681'], conversion_details=[])", "CodeLineInfo(line_number=780, content='_20863 = _19609;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_20863', '_19609'], conversion_details=[])", "CodeLineInfo(line_number=781, content='_20848 = _19573;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19573', '_20848'], conversion_details=[])", "CodeLineInfo(line_number=782, content='_20833 = _19481;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_20833', '_19481'], conversion_details=[])", "CodeLineInfo(line_number=783, content='_19965 = (_18431 + (float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_14084 * _14084)), 0.0078125))), _11858)) * 0.15915493667125701904296875) * float(half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05)), _11973 - (_11975 * (-half(dot(_19716, _19751))))), _11982)) * float4(0.0795769989490509033203125)).x))), half3(half(fast::clamp((_14020 * _14020) * float(dot(_14069, _8303)), 0.0, 1.0)))) * _14069) * float3(_19573))) + (((float3(_14069) * _19883) * _19573) * float(_14029));', variables=21, float_vars=0, half_vars=0, conversions=8, has_conversion=True, all_variables=['_11970', '_11858', '_14029', '_19751', '_18431', '_14020', '_14084', '_11973', '_19965', 'x', '_19716', '_9179', '_8303', '_11862', '_19573', '_14069', '_19883', '_9199', '_11982', '_11975', 'fast'], conversion_details=[{'target_type': 'float3', 'source_expression': 'mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_14084 * _14084', 'line_position': 28}, {'target_type': 'float', 'source_expression': 'half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05', 'line_position': 185}, {'target_type': 'half', 'source_expression': 'dot(_19716, _19751', 'line_position': 283}, {'target_type': 'half3', 'source_expression': 'half(fast::clamp((_14020 * _14020', 'line_position': 367}, {'target_type': 'float', 'source_expression': 'dot(_14069, _8303', 'line_position': 410}, {'target_type': 'float3', 'source_expression': '_19573', 'line_position': 462}, {'target_type': 'float3', 'source_expression': '_14069', 'line_position': 484}, {'target_type': 'float', 'source_expression': '_14029', 'line_position': 521}])", "CodeLineInfo(line_number=784, content='_19923 = _18429 + (_14069 * _14029);', variables=4, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14069', '_14029', '_18429', '_19923'], conversion_details=[])", "CodeLineInfo(line_number=786, content='half3 _8560 = (_9179 + (((_9179 + (_11812 * _11772)) * _9254) * half3(half(_Block1.EnvInfo.z)))) + _18429;', variables=7, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_8560', '_11812', '_9254', '_Block1.EnvInfo.z', '_18429', '_9179', '_11772'], conversion_details=[{'target_type': 'half3', 'source_expression': 'half(_Block1.EnvInfo.z', 'line_position': 68}])", "CodeLineInfo(line_number=787, content='float3 _8565 = (_9698 + _8521) + _18431;', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8565', '_18431', '_8521', '_9698'], conversion_details=[])", "CodeLineInfo(line_number=788, content='bool _8573 = (_8397 & 16128u) > 0u;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8573', '_8397', 'bool'], conversion_details=[])", "CodeLineInfo(line_number=791, content='if (_8573)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8573'], conversion_details=[])", "CodeLineInfo(line_number=793, content='bool _8590 = (_8397 & 16384u) > 0u;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8590', '_8397', 'bool'], conversion_details=[])", "CodeLineInfo(line_number=796, content='if (_8573)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8573'], conversion_details=[])", "CodeLineInfo(line_number=798, content='uint _14451 = (_8397 & 458752u) >> 16u;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14451', 'uint', '_8397'], conversion_details=[])", "CodeLineInfo(line_number=799, content='uint _14469 = (_8397 & 4026531840u) >> 28u;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14469', 'uint', '_8397'], conversion_details=[])", "CodeLineInfo(line_number=800, content='float _14482 = fast::clamp((_Block1.CameraInfo.y * in.IN_LinearZ) / fast::max(_Block1.SHGIParam2.x, 0.001000000047497451305389404296875), 0.0, 1.0);', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14482', 'in.IN_LinearZ', '_Block1.SHGIParam2.x', '_Block1.CameraInfo.y', 'fast'], conversion_details=[])", "CodeLineInfo(line_number=803, content='if ((_8397 & 2048u) != 0u)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8397'], conversion_details=[])", "CodeLineInfo(line_number=805, content='float3 _14684 = select(_Block1.PlayerPos.xyz, _Block1.CameraPos.xyz, bool3((_8397 & 524288u) > 0u)) * float3(0.125);', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14684', '_Block1.CameraPos.xyz', '_Block1.PlayerPos.xyz', '_8397'], conversion_details=[])", "CodeLineInfo(line_number=806, content='float _14692 = _14684.x;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14692', '_14684.x'], conversion_details=[])", "CodeLineInfo(line_number=807, content='float _14694 = floor(_14692);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14694', '_14692'], conversion_details=[])", "CodeLineInfo(line_number=809, content='_21212.x = _14694 - 15.0;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14694', '_21212.x'], conversion_details=[])", "CodeLineInfo(line_number=811, content='if ((_14692 - _14694) > 0.5)', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14694', '_14692'], conversion_details=[])", "CodeLineInfo(line_number=813, content='float3 _21215 = _21212;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21215', '_21212'], conversion_details=[])", "CodeLineInfo(line_number=814, content='_21215.x = _14694 + (-14.0);', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14694', '_21215.x'], conversion_details=[])", "CodeLineInfo(line_number=815, content='_21268 = _21215;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21268', '_21215'], conversion_details=[])", "CodeLineInfo(line_number=819, content='_21268 = _21212;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21268', '_21212'], conversion_details=[])", "CodeLineInfo(line_number=821, content='float _21034 = _14684.y;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21034', '_14684.y'], conversion_details=[])", "CodeLineInfo(line_number=822, content='float _21035 = floor(_21034);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21035', '_21034'], conversion_details=[])", "CodeLineInfo(line_number=823, content='float3 _21219 = _21268;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21219', '_21268'], conversion_details=[])", "CodeLineInfo(line_number=824, content='_21219.y = _21035 - 8.0;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21219.y', '_21035'], conversion_details=[])", "CodeLineInfo(line_number=826, content='if ((_21034 - _21035) > 0.5)', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21034', '_21035'], conversion_details=[])", "CodeLineInfo(line_number=828, content='float3 _21222 = _21219;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21222', '_21219'], conversion_details=[])", "CodeLineInfo(line_number=829, content='_21222.y = _21035 + (-7.0);', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21222.y', '_21035'], conversion_details=[])", "CodeLineInfo(line_number=830, content='_21269 = _21222;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21269', '_21222'], conversion_details=[])", "CodeLineInfo(line_number=834, content='_21269 = _21219;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21219', '_21269'], conversion_details=[])", "CodeLineInfo(line_number=836, content='float _21056 = _14684.z;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21056', '_14684.z'], conversion_details=[])", "CodeLineInfo(line_number=837, content='float _21057 = floor(_21056);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21057', '_21056'], conversion_details=[])", "CodeLineInfo(line_number=838, content='float3 _21226 = _21269;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21226', '_21269'], conversion_details=[])", "CodeLineInfo(line_number=839, content='_21226.z = _21057 - 15.0;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21057', '_21226.z'], conversion_details=[])", "CodeLineInfo(line_number=841, content='if ((_21056 - _21057) > 0.5)', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21057', '_21056'], conversion_details=[])", "CodeLineInfo(line_number=843, content='float3 _21229 = _21226;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21229', '_21226'], conversion_details=[])", "CodeLineInfo(line_number=844, content='_21229.z = _21057 + (-14.0);', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21229.z', '_21057'], conversion_details=[])", "CodeLineInfo(line_number=845, content='_21270 = _21229;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21270', '_21229'], conversion_details=[])", "CodeLineInfo(line_number=849, content='_21270 = _21226;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_21226', '_21270'], conversion_details=[])", "CodeLineInfo(line_number=851, content='float3 _14717 = _21270 * 8.0;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14717', '_21270'], conversion_details=[])", "CodeLineInfo(line_number=854, content='if (all(in.IN_WorldPosition.xyz >= _14717) && all(in.IN_WorldPosition.xyz < (_14717 + float3(240.0, 128.0, 240.0))))', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14717', 'in.IN_WorldPosition.xyz'], conversion_details=[])", "CodeLineInfo(line_number=856, content='float3 _14534 = (in.IN_WorldPosition.xyz + (_9109 * 0.100000001490116119384765625)) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14534', 'in.IN_WorldPosition.xyz', '_9109'], conversion_details=[])", "CodeLineInfo(line_number=857, content='float3 _14747 = _14534 - floor(_14534);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14747', '_14534'], conversion_details=[])", "CodeLineInfo(line_number=858, content='half3 _14556 = half3(_9109);', variables=2, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_14556', '_9109'], conversion_details=[{'target_type': 'half3', 'source_expression': '_9109', 'line_position': 35}])", "CodeLineInfo(line_number=859, content='float _14788 = float((_8397 & 15728640u) >> 20u);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_14788', '_8397'], conversion_details=[{'target_type': 'float', 'source_expression': '(_8397 & 15728640u', 'line_position': 35}])", "CodeLineInfo(line_number=860, content='float _14797 = float(_14451);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_14797', '_14451'], conversion_details=[{'target_type': 'float', 'source_expression': '_14451', 'line_position': 35}])", "CodeLineInfo(line_number=861, content='float _14827 = (_14788 - _14797) + ((3.0 - _14797) * 3.0);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14827', '_14797', '_14788'], conversion_details=[])", "CodeLineInfo(line_number=862, content='float _14831 = _14827 + 1.0;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14831', '_14827'], conversion_details=[])", "CodeLineInfo(line_number=863, content='float _14833 = _14827 + 2.0;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14833', '_14827'], conversion_details=[])", "CodeLineInfo(line_number=866, content='if (3 >= int(_14469))', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14469'], conversion_details=[])", "CodeLineInfo(line_number=868, content='float _14850 = (_14788 - float((_8397 & 251658240u) >> 24u)) + ((3.0 - float(_14469)) * 3.0);', variables=4, float_vars=1, half_vars=0, conversions=2, has_conversion=True, all_variables=['_14850', '_8397', '_14469', '_14788'], conversion_details=[{'target_type': 'float', 'source_expression': '(_8397 & 251658240u', 'line_position': 49}, {'target_type': 'float', 'source_expression': '_14469', 'line_position': 95}])", "CodeLineInfo(line_number=869, content='float _14854 = _14850 + 1.0;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14854', '_14850'], conversion_details=[])", "CodeLineInfo(line_number=870, content='float _14856 = _14850 + 2.0;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14856', '_14850'], conversion_details=[])", "CodeLineInfo(line_number=871, content='float2 _14956 = ((_14747.xz - float2(0.5)) * 0.9375) + float2(0.5);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14956', '_14747.xz'], conversion_details=[])", "CodeLineInfo(line_number=872, content='float _14963 = _14717.x * 0.0041666668839752674102783203125;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14963', '_14717.x'], conversion_details=[])", "CodeLineInfo(line_number=873, content='float _14967 = ((_14963 - floor(_14963)) - 0.5) * 0.9375;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14967', '_14963'], conversion_details=[])", "CodeLineInfo(line_number=874, content='float _14973 = _14717.z * 0.0041666668839752674102783203125;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14973', '_14717.z'], conversion_details=[])", "CodeLineInfo(line_number=875, content='float _14977 = ((_14973 - floor(_14973)) - 0.5) * 0.9375;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14977', '_14973'], conversion_details=[])", "CodeLineInfo(line_number=876, content='float _14982 = _14956.x;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14982', '_14956.x'], conversion_details=[])", "CodeLineInfo(line_number=878, content='_18095.x = (_14982 < (_14967 + 0.5)) ? fast::min(_14982, _14967 + 0.49609375) : fast::max(_14982, _14967 + 0.50390625);', variables=4, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14967', '_18095.x', 'fast', '_14982'], conversion_details=[])", "CodeLineInfo(line_number=879, content='float _15000 = _14956.y;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15000', '_14956.y'], conversion_details=[])", "CodeLineInfo(line_number=880, content='_18095.z = (_15000 < (_14977 + 0.5)) ? fast::min(_15000, _14977 + 0.49609375) : fast::max(_15000, _14977 + 0.50390625);', variables=4, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14977', '_15000', 'fast', '_18095.z'], conversion_details=[])", "CodeLineInfo(line_number=881, content='float _15021 = (_14747.y * 64.0) - 0.5;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15021', '_14747.y'], conversion_details=[])", "CodeLineInfo(line_number=882, content='float _15026 = floor(_15021);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15026', '_15021'], conversion_details=[])", "CodeLineInfo(line_number=883, content='uint _15029 = (_15021 < 0.0) ? 63u : uint(_15026);', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15021', '_15026', '_15029'], conversion_details=[])", "CodeLineInfo(line_number=884, content='uint _15032 = _15029 + 1u;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15032', 'uint', '_15029'], conversion_details=[])", "CodeLineInfo(line_number=885, content='uint _21309 = (_15032 >= 64u) ? 0u : _15032;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15032', 'uint', '_21309'], conversion_details=[])", "CodeLineInfo(line_number=886, content='float2 _15059 = (float2(float(_15029 & 7u), float(_15029 >> 3u)) + _18095.xz) * 0.125;', variables=3, float_vars=1, half_vars=0, conversions=2, has_conversion=True, all_variables=['_15059', '_15029', '_18095.xz'], conversion_details=[{'target_type': 'float2', 'source_expression': 'float(_15029 & 7u', 'line_position': 41}, {'target_type': 'float', 'source_expression': '_15029 >> 3u', 'line_position': 68}])", "CodeLineInfo(line_number=887, content='float2 _15074 = (float2(float(_21309 & 7u), float(_21309 >> 3u)) + _18095.xz) * 0.125;', variables=3, float_vars=1, half_vars=0, conversions=2, has_conversion=True, all_variables=['_15074', '_18095.xz', '_21309'], conversion_details=[{'target_type': 'float2', 'source_expression': 'float(_21309 & 7u', 'line_position': 41}, {'target_type': 'float', 'source_expression': '_21309 >> 3u', 'line_position': 68}])", "CodeLineInfo(line_number=888, content='float _15078 = _15059.x;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15078', '_15059.x'], conversion_details=[])", "CodeLineInfo(line_number=889, content='float3 _15080 = float3(_15078, _15059.y, _14827);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15080', '_14827', '_15059.y', '_15078'], conversion_details=[{'target_type': 'float3', 'source_expression': '_15078, _15059.y, _14827', 'line_position': 40}])", "CodeLineInfo(line_number=891, content='_18103.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15080.xy, uint(rint(_15080.z)), level(0.0)).x);', variables=6, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['sSHAOAlphaVTSampler.sample', 'x', '_15080.xy', '_18103.w', 'sSHAOAlphaVTSamplerSmplr', '_15080.z'], conversion_details=[{'target_type': 'half', 'source_expression': 'sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15080.xy, uint(rint(_15080.z', 'line_position': 35}])", "CodeLineInfo(line_number=892, content='float3 _15092 = float3(_15078, _15059.y, _14850);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15092', '_15059.y', '_14850', '_15078'], conversion_details=[{'target_type': 'float3', 'source_expression': '_15078, _15059.y, _14850', 'line_position': 40}])", "CodeLineInfo(line_number=893, content='half3 _15097 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15092.xy, uint(rint(_15092.z)), level(0.0)).xyz);', variables=6, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_15097', 'sSHAORGBVTSampler.sample', 'sSHAORGBVTSamplerSmplr', 'xyz', '_15092.xy', '_15092.z'], conversion_details=[{'target_type': 'half3', 'source_expression': 'sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15092.xy, uint(rint(_15092.z', 'line_position': 39}])", "CodeLineInfo(line_number=894, content='float _15104 = _15074.x;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15104', '_15074.x'], conversion_details=[])", "CodeLineInfo(line_number=895, content='float3 _15106 = float3(_15104, _15074.y, _14827);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15106', '_15104', '_14827', '_15074.y'], conversion_details=[{'target_type': 'float3', 'source_expression': '_15104, _15074.y, _14827', 'line_position': 40}])", "CodeLineInfo(line_number=897, content='_18105.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15106.xy, uint(rint(_15106.z)), level(0.0)).x);', variables=6, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['sSHAOAlphaVTSampler.sample', 'x', '_18105.w', '_15106.xy', 'sSHAOAlphaVTSamplerSmplr', '_15106.z'], conversion_details=[{'target_type': 'half', 'source_expression': 'sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15106.xy, uint(rint(_15106.z', 'line_position': 35}])", "CodeLineInfo(line_number=898, content='float3 _15118 = float3(_15104, _15074.y, _14850);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15118', '_15104', '_14850', '_15074.y'], conversion_details=[{'target_type': 'float3', 'source_expression': '_15104, _15074.y, _14850', 'line_position': 40}])", "CodeLineInfo(line_number=899, content='half3 _15123 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15118.xy, uint(rint(_15118.z)), level(0.0)).xyz);', variables=6, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_15123', 'sSHAORGBVTSampler.sample', 'sSHAORGBVTSamplerSmplr', 'xyz', '_15118.z', '_15118.xy'], conversion_details=[{'target_type': 'half3', 'source_expression': 'sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15118.xy, uint(rint(_15118.z', 'line_position': 39}])", "CodeLineInfo(line_number=900, content='half4 _15132 = half4(half(fast::clamp(_15021 - _15026, 0.0, 1.0)));', variables=4, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_15132', 'fast', '_15026', '_15021'], conversion_details=[{'target_type': 'half4', 'source_expression': 'half(fast::clamp(_15021 - _15026, 0.0, 1.0', 'line_position': 39}])", "CodeLineInfo(line_number=901, content='half4 _15133 = mix(half4(_15097.x, _15097.y, _15097.z, _18103.w), half4(_15123.x, _15123.y, _15123.z, _18105.w), _15132);', variables=10, float_vars=0, half_vars=1, conversions=2, has_conversion=True, all_variables=['_15133', '_15097.z', '_15097.y', '_15132', '_18105.w', '_18103.w', '_15123.z', '_15123.x', '_15097.x', '_15123.y'], conversion_details=[{'target_type': 'half4', 'source_expression': '_15097.x, _15097.y, _15097.z, _18103.w', 'line_position': 43}, {'target_type': 'half4', 'source_expression': '_15123.x, _15123.y, _15123.z, _18105.w', 'line_position': 90}])", "CodeLineInfo(line_number=902, content='float3 _15140 = float3(_15078, _15059.y, _14831);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15140', '_14831', '_15059.y', '_15078'], conversion_details=[{'target_type': 'float3', 'source_expression': '_15078, _15059.y, _14831', 'line_position': 40}])", "CodeLineInfo(line_number=904, content='_18107.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15140.xy, uint(rint(_15140.z)), level(0.0)).x);', variables=6, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15140.z', 'sSHAOAlphaVTSampler.sample', 'x', '_15140.xy', 'sSHAOAlphaVTSamplerSmplr', '_18107.w'], conversion_details=[{'target_type': 'half', 'source_expression': 'sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15140.xy, uint(rint(_15140.z', 'line_position': 35}])", "CodeLineInfo(line_number=905, content='float3 _15152 = float3(_15078, _15059.y, _14854);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15152', '_14854', '_15059.y', '_15078'], conversion_details=[{'target_type': 'float3', 'source_expression': '_15078, _15059.y, _14854', 'line_position': 40}])", "CodeLineInfo(line_number=906, content='half3 _15157 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15152.xy, uint(rint(_15152.z)), level(0.0)).xyz);', variables=6, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_15157', 'sSHAORGBVTSampler.sample', 'sSHAORGBVTSamplerSmplr', '_15152.xy', 'xyz', '_15152.z'], conversion_details=[{'target_type': 'half3', 'source_expression': 'sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15152.xy, uint(rint(_15152.z', 'line_position': 39}])", "CodeLineInfo(line_number=907, content='float3 _15166 = float3(_15104, _15074.y, _14831);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15166', '_14831', '_15104', '_15074.y'], conversion_details=[{'target_type': 'float3', 'source_expression': '_15104, _15074.y, _14831', 'line_position': 40}])", "CodeLineInfo(line_number=909, content='_18109.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15166.xy, uint(rint(_15166.z)), level(0.0)).x);', variables=6, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['sSHAOAlphaVTSampler.sample', 'x', '_15166.xy', '_18109.w', '_15166.z', 'sSHAOAlphaVTSamplerSmplr'], conversion_details=[{'target_type': 'half', 'source_expression': 'sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15166.xy, uint(rint(_15166.z', 'line_position': 35}])", "CodeLineInfo(line_number=910, content='float3 _15178 = float3(_15104, _15074.y, _14854);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15178', '_14854', '_15104', '_15074.y'], conversion_details=[{'target_type': 'float3', 'source_expression': '_15104, _15074.y, _14854', 'line_position': 40}])", "CodeLineInfo(line_number=911, content='half3 _15183 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15178.xy, uint(rint(_15178.z)), level(0.0)).xyz);', variables=6, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_15183', 'sSHAORGBVTSampler.sample', 'sSHAORGBVTSamplerSmplr', 'xyz', '_15178.xy', '_15178.z'], conversion_details=[{'target_type': 'half3', 'source_expression': 'sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15178.xy, uint(rint(_15178.z', 'line_position': 39}])", "CodeLineInfo(line_number=912, content='half4 _15193 = mix(half4(_15157.x, _15157.y, _15157.z, _18107.w), half4(_15183.x, _15183.y, _15183.z, _18109.w), _15132);', variables=10, float_vars=0, half_vars=1, conversions=2, has_conversion=True, all_variables=['_15193', '_15183.y', '_15157.y', '_15157.x', '_18109.w', '_15132', '_15157.z', '_15183.z', '_15183.x', '_18107.w'], conversion_details=[{'target_type': 'half4', 'source_expression': '_15157.x, _15157.y, _15157.z, _18107.w', 'line_position': 43}, {'target_type': 'half4', 'source_expression': '_15183.x, _15183.y, _15183.z, _18109.w', 'line_position': 90}])", "CodeLineInfo(line_number=913, content='float3 _15200 = float3(_15078, _15059.y, _14833);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15200', '_14833', '_15059.y', '_15078'], conversion_details=[{'target_type': 'float3', 'source_expression': '_15078, _15059.y, _14833', 'line_position': 40}])", "CodeLineInfo(line_number=915, content='_18111.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15200.xy, uint(rint(_15200.z)), level(0.0)).x);', variables=6, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['sSHAOAlphaVTSampler.sample', 'x', '_15200.xy', '_15200.z', '_18111.w', 'sSHAOAlphaVTSamplerSmplr'], conversion_details=[{'target_type': 'half', 'source_expression': 'sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15200.xy, uint(rint(_15200.z', 'line_position': 35}])", "CodeLineInfo(line_number=916, content='float3 _15212 = float3(_15078, _15059.y, _14856);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15212', '_15059.y', '_14856', '_15078'], conversion_details=[{'target_type': 'float3', 'source_expression': '_15078, _15059.y, _14856', 'line_position': 40}])", "CodeLineInfo(line_number=917, content='half3 _15217 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15212.xy, uint(rint(_15212.z)), level(0.0)).xyz);', variables=6, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_15217', 'sSHAORGBVTSampler.sample', 'sSHAORGBVTSamplerSmplr', 'xyz', '_15212.xy', '_15212.z'], conversion_details=[{'target_type': 'half3', 'source_expression': 'sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15212.xy, uint(rint(_15212.z', 'line_position': 39}])", "CodeLineInfo(line_number=918, content='float3 _15226 = float3(_15104, _15074.y, _14833);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15226', '_14833', '_15104', '_15074.y'], conversion_details=[{'target_type': 'float3', 'source_expression': '_15104, _15074.y, _14833', 'line_position': 40}])", "CodeLineInfo(line_number=920, content='_18113.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15226.xy, uint(rint(_15226.z)), level(0.0)).x);', variables=6, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['sSHAOAlphaVTSampler.sample', 'x', '_18113.w', '_15226.xy', 'sSHAOAlphaVTSamplerSmplr', '_15226.z'], conversion_details=[{'target_type': 'half', 'source_expression': 'sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15226.xy, uint(rint(_15226.z', 'line_position': 35}])", "CodeLineInfo(line_number=921, content='float3 _15238 = float3(_15104, _15074.y, _14856);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15238', '_15104', '_14856', '_15074.y'], conversion_details=[{'target_type': 'float3', 'source_expression': '_15104, _15074.y, _14856', 'line_position': 40}])", "CodeLineInfo(line_number=922, content='half3 _15243 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15238.xy, uint(rint(_15238.z)), level(0.0)).xyz);', variables=6, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_15243', 'sSHAORGBVTSampler.sample', 'sSHAORGBVTSamplerSmplr', 'xyz', '_15238.z', '_15238.xy'], conversion_details=[{'target_type': 'half3', 'source_expression': 'sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15238.xy, uint(rint(_15238.z', 'line_position': 39}])", "CodeLineInfo(line_number=923, content='half4 _15253 = mix(half4(_15217.x, _15217.y, _15217.z, _18111.w), half4(_15243.x, _15243.y, _15243.z, _18113.w), _15132);', variables=10, float_vars=0, half_vars=1, conversions=2, has_conversion=True, all_variables=['_15253', '_15217.y', '_15217.z', '_15243.y', '_18113.w', '_15132', '_15243.x', '_15243.z', '_18111.w', '_15217.x'], conversion_details=[{'target_type': 'half4', 'source_expression': '_15217.x, _15217.y, _15217.z, _18111.w', 'line_position': 43}, {'target_type': 'half4', 'source_expression': '_15243.x, _15243.y, _15243.z, _18113.w', 'line_position': 90}])", "CodeLineInfo(line_number=924, content='half _15255 = half(32.0);', variables=1, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_15255'], conversion_details=[])", "CodeLineInfo(line_number=925, content='half _15258 = _15133.w * _15255;', variables=3, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_15258', '_15133.w', '_15255'], conversion_details=[])", "CodeLineInfo(line_number=926, content='half _15262 = _15193.w * _15255;', variables=3, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_15262', '_15193.w', '_15255'], conversion_details=[])", "CodeLineInfo(line_number=927, content='half _15266 = _15253.w * _15255;', variables=3, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_15266', '_15255', '_15253.w'], conversion_details=[])", "CodeLineInfo(line_number=928, content='half3 _15343 = half3(((float3(_15133.xyz) * float3(2.0)) - float3(1.0)) * float(_15258)).xyz;', variables=4, float_vars=0, half_vars=1, conversions=2, has_conversion=True, all_variables=['_15343', '_15133.xyz', '_15258', 'xyz'], conversion_details=[{'target_type': 'half3', 'source_expression': '((float3(_15133.xyz', 'line_position': 39}, {'target_type': 'float', 'source_expression': '_15258', 'line_position': 98}])", "CodeLineInfo(line_number=930, content='_18130.x = half(float(dot(_14556, _15343)) * 2.0);', variables=3, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_18130.x', '_15343', '_14556'], conversion_details=[{'target_type': 'half', 'source_expression': 'float(dot(_14556, _15343', 'line_position': 35}])", "CodeLineInfo(line_number=931, content='half3 _15352 = half3(((float3(_15193.xyz) * float3(2.0)) - float3(1.0)) * float(_15262)).xyz;', variables=4, float_vars=0, half_vars=1, conversions=2, has_conversion=True, all_variables=['_15352', '_15193.xyz', '_15262', 'xyz'], conversion_details=[{'target_type': 'half3', 'source_expression': '((float3(_15193.xyz', 'line_position': 39}, {'target_type': 'float', 'source_expression': '_15262', 'line_position': 98}])", "CodeLineInfo(line_number=932, content='_18130.y = half(float(dot(_14556, _15352)) * 2.0);', variables=3, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15352', '_18130.y', '_14556'], conversion_details=[{'target_type': 'half', 'source_expression': 'float(dot(_14556, _15352', 'line_position': 35}])", "CodeLineInfo(line_number=933, content='half3 _15361 = half3(((float3(_15253.xyz) * float3(2.0)) - float3(1.0)) * float(_15266)).xyz;', variables=4, float_vars=0, half_vars=1, conversions=2, has_conversion=True, all_variables=['_15361', 'xyz', '_15253.xyz', '_15266'], conversion_details=[{'target_type': 'half3', 'source_expression': '((float3(_15253.xyz', 'line_position': 39}, {'target_type': 'float', 'source_expression': '_15266', 'line_position': 98}])", "CodeLineInfo(line_number=934, content='_18130.z = half(float(dot(_14556, _15361)) * 2.0);', variables=3, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_18130.z', '_15361', '_14556'], conversion_details=[{'target_type': 'half', 'source_expression': 'float(dot(_14556, _15361', 'line_position': 35}])", "CodeLineInfo(line_number=936, content='if (_8590)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8590'], conversion_details=[])", "CodeLineInfo(line_number=938, content='_18819 = half3(((float3(_15343) * float3(0.21199999749660491943359375)) + (float3(_15352) * float3(0.714999973773956298828125))) + (float3(_15361) * float3(0.0719999969005584716796875)));', variables=4, float_vars=0, half_vars=0, conversions=3, has_conversion=True, all_variables=['_18819', '_15361', '_15343', '_15352'], conversion_details=[{'target_type': 'half3', 'source_expression': '((float3(_15343', 'line_position': 37}, {'target_type': 'float3', 'source_expression': '_15352', 'line_position': 103}, {'target_type': 'float3', 'source_expression': '_15361', 'line_position': 160}])", "CodeLineInfo(line_number=942, content='_18819 = _8393;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18819', '_8393'], conversion_details=[])", "CodeLineInfo(line_number=944, content='_18818 = _18819;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18819', '_18818'], conversion_details=[])", "CodeLineInfo(line_number=945, content='_18806 = max(half3(_15258, _15262, _15266) + (_18130 * half(mix(_Block1.SHGIParam2.z, 1.0, _14482))), _8393);', variables=8, float_vars=0, half_vars=0, conversions=2, has_conversion=True, all_variables=['_15266', '_18130', '_15258', '_15262', '_8393', '_14482', '_Block1.SHGIParam2.z', '_18806'], conversion_details=[{'target_type': 'half3', 'source_expression': '_15258, _15262, _15266', 'line_position': 37}, {'target_type': 'half', 'source_expression': 'mix(_Block1.SHGIParam2.z, 1.0, _14482', 'line_position': 79}])", "CodeLineInfo(line_number=949, content='float2 _15427 = ((_14747.xz - float2(0.5)) * 0.9375) + float2(0.5);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15427', '_14747.xz'], conversion_details=[])", "CodeLineInfo(line_number=950, content='float _15434 = _14717.x * 0.0041666668839752674102783203125;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15434', '_14717.x'], conversion_details=[])", "CodeLineInfo(line_number=951, content='float _15438 = ((_15434 - floor(_15434)) - 0.5) * 0.9375;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15438', '_15434'], conversion_details=[])", "CodeLineInfo(line_number=952, content='float _15444 = _14717.z * 0.0041666668839752674102783203125;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15444', '_14717.z'], conversion_details=[])", "CodeLineInfo(line_number=953, content='float _15448 = ((_15444 - floor(_15444)) - 0.5) * 0.9375;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15448', '_15444'], conversion_details=[])", "CodeLineInfo(line_number=954, content='float _15453 = _15427.x;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15453', '_15427.x'], conversion_details=[])", "CodeLineInfo(line_number=956, content='_18143.x = (_15453 < (_15438 + 0.5)) ? fast::min(_15453, _15438 + 0.49609375) : fast::max(_15453, _15438 + 0.50390625);', variables=4, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15438', 'fast', '_15453', '_18143.x'], conversion_details=[])", "CodeLineInfo(line_number=957, content='float _15471 = _15427.y;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15471', '_15427.y'], conversion_details=[])", "CodeLineInfo(line_number=958, content='_18143.z = (_15471 < (_15448 + 0.5)) ? fast::min(_15471, _15448 + 0.49609375) : fast::max(_15471, _15448 + 0.50390625);', variables=4, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15471', '_18143.z', '_15448', 'fast'], conversion_details=[])", "CodeLineInfo(line_number=959, content='float _15492 = (_14747.y * 64.0) - 0.5;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15492', '_14747.y'], conversion_details=[])", "CodeLineInfo(line_number=960, content='float _15497 = floor(_15492);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15497', '_15492'], conversion_details=[])", "CodeLineInfo(line_number=961, content='uint _15500 = (_15492 < 0.0) ? 63u : uint(_15497);', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15497', '_15492', '_15500'], conversion_details=[])", "CodeLineInfo(line_number=962, content='uint _15503 = _15500 + 1u;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15500', '_15503', 'uint'], conversion_details=[])", "CodeLineInfo(line_number=963, content='uint _21308 = (_15503 >= 64u) ? 0u : _15503;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15503', 'uint', '_21308'], conversion_details=[])", "CodeLineInfo(line_number=964, content='float2 _15530 = (float2(float(_15500 & 7u), float(_15500 >> 3u)) + _18143.xz) * 0.125;', variables=3, float_vars=1, half_vars=0, conversions=2, has_conversion=True, all_variables=['_15530', '_15500', '_18143.xz'], conversion_details=[{'target_type': 'float2', 'source_expression': 'float(_15500 & 7u', 'line_position': 41}, {'target_type': 'float', 'source_expression': '_15500 >> 3u', 'line_position': 68}])", "CodeLineInfo(line_number=965, content='float2 _15545 = (float2(float(_21308 & 7u), float(_21308 >> 3u)) + _18143.xz) * 0.125;', variables=3, float_vars=1, half_vars=0, conversions=2, has_conversion=True, all_variables=['_15545', '_18143.xz', '_21308'], conversion_details=[{'target_type': 'float2', 'source_expression': 'float(_21308 & 7u', 'line_position': 41}, {'target_type': 'float', 'source_expression': '_21308 >> 3u', 'line_position': 68}])", "CodeLineInfo(line_number=966, content='float _15549 = _15530.x;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15549', '_15530.x'], conversion_details=[])", "CodeLineInfo(line_number=967, content='float3 _15551 = float3(_15549, _15530.y, _14827);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15551', '_15530.y', '_14827', '_15549'], conversion_details=[{'target_type': 'float3', 'source_expression': '_15549, _15530.y, _14827', 'line_position': 40}])", "CodeLineInfo(line_number=969, content='_18151.x = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15551.xy, uint(rint(_15551.z)), level(0.0)).x);', variables=6, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['sSHAOAlphaVTSampler.sample', 'x', '_15551.xy', '_18151.x', 'sSHAOAlphaVTSamplerSmplr', '_15551.z'], conversion_details=[{'target_type': 'half', 'source_expression': 'sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15551.xy, uint(rint(_15551.z', 'line_position': 35}])", "CodeLineInfo(line_number=970, content='float _15561 = _15545.x;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15561', '_15545.x'], conversion_details=[])", "CodeLineInfo(line_number=971, content='float3 _15563 = float3(_15561, _15545.y, _14827);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15563', '_15545.y', '_14827', '_15561'], conversion_details=[{'target_type': 'float3', 'source_expression': '_15561, _15545.y, _14827', 'line_position': 40}])", "CodeLineInfo(line_number=973, content='_18153.x = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15563.xy, uint(rint(_15563.z)), level(0.0)).x);', variables=6, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['sSHAOAlphaVTSampler.sample', 'x', '_15563.xy', '_15563.z', 'sSHAOAlphaVTSamplerSmplr', '_18153.x'], conversion_details=[{'target_type': 'half', 'source_expression': 'sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15563.xy, uint(rint(_15563.z', 'line_position': 35}])", "CodeLineInfo(line_number=974, content='float3 _15575 = float3(_15549, _15530.y, _14831);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15575', '_14831', '_15549', '_15530.y'], conversion_details=[{'target_type': 'float3', 'source_expression': '_15549, _15530.y, _14831', 'line_position': 40}])", "CodeLineInfo(line_number=975, content='_18151.y = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15575.xy, uint(rint(_15575.z)), level(0.0)).x);', variables=6, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_18151.y', 'sSHAOAlphaVTSampler.sample', 'x', '_15575.xy', 'sSHAOAlphaVTSamplerSmplr', '_15575.z'], conversion_details=[{'target_type': 'half', 'source_expression': 'sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15575.xy, uint(rint(_15575.z', 'line_position': 35}])", "CodeLineInfo(line_number=976, content='float3 _15587 = float3(_15561, _15545.y, _14831);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15587', '_15545.y', '_14831', '_15561'], conversion_details=[{'target_type': 'float3', 'source_expression': '_15561, _15545.y, _14831', 'line_position': 40}])", "CodeLineInfo(line_number=977, content='_18153.y = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15587.xy, uint(rint(_15587.z)), level(0.0)).x);', variables=6, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['sSHAOAlphaVTSampler.sample', 'x', '_15587.xy', '_15587.z', 'sSHAOAlphaVTSamplerSmplr', '_18153.y'], conversion_details=[{'target_type': 'half', 'source_expression': 'sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15587.xy, uint(rint(_15587.z', 'line_position': 35}])", "CodeLineInfo(line_number=978, content='float3 _15599 = float3(_15549, _15530.y, _14833);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15599', '_15530.y', '_14833', '_15549'], conversion_details=[{'target_type': 'float3', 'source_expression': '_15549, _15530.y, _14833', 'line_position': 40}])", "CodeLineInfo(line_number=979, content='_18151.z = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15599.xy, uint(rint(_15599.z)), level(0.0)).x);', variables=6, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['sSHAOAlphaVTSampler.sample', 'x', '_15599.xy', '_15599.z', '_18151.z', 'sSHAOAlphaVTSamplerSmplr'], conversion_details=[{'target_type': 'half', 'source_expression': 'sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15599.xy, uint(rint(_15599.z', 'line_position': 35}])", "CodeLineInfo(line_number=980, content='float3 _15611 = float3(_15561, _15545.y, _14833);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15611', '_15545.y', '_14833', '_15561'], conversion_details=[{'target_type': 'float3', 'source_expression': '_15561, _15545.y, _14833', 'line_position': 40}])", "CodeLineInfo(line_number=981, content='_18153.z = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15611.xy, uint(rint(_15611.z)), level(0.0)).x);', variables=6, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['sSHAOAlphaVTSampler.sample', 'x', '_15611.xy', '_15611.z', '_18153.z', 'sSHAOAlphaVTSamplerSmplr'], conversion_details=[{'target_type': 'half', 'source_expression': 'sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15611.xy, uint(rint(_15611.z', 'line_position': 35}])", "CodeLineInfo(line_number=982, content='half3 _15622 = mix(_18151, _18153, half3(half(fast::clamp(_15492 - _15497, 0.0, 1.0))));', variables=6, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_15622', '_15492', '_18153', '_18151', '_15497', 'fast'], conversion_details=[{'target_type': 'half3', 'source_expression': 'half(fast::clamp(_15492 - _15497, 0.0, 1.0', 'line_position': 59}])", "CodeLineInfo(line_number=983, content='half _15623 = half(32.0);', variables=1, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_15623'], conversion_details=[])", "CodeLineInfo(line_number=985, content='_18164.x = _15622.x * _15623;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18164.x', '_15622.x', '_15623'], conversion_details=[])", "CodeLineInfo(line_number=986, content='_18164.y = _15622.y * _15623;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15623', '_18164.y', '_15622.y'], conversion_details=[])", "CodeLineInfo(line_number=987, content='_18164.z = _15622.z * _15623;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18164.z', '_15622.z', '_15623'], conversion_details=[])", "CodeLineInfo(line_number=988, content='_18818 = _8393;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18818', '_8393'], conversion_details=[])", "CodeLineInfo(line_number=989, content='_18806 = _18164;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18806', '_18164'], conversion_details=[])", "CodeLineInfo(line_number=991, content='float3 _15658 = (((in.IN_WorldPosition.xyz - _14717) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)) * 2.0) - float3(1.0);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15658', '_14717', 'in.IN_WorldPosition.xyz'], conversion_details=[])", "CodeLineInfo(line_number=992, content='float3 _15661 = _15658 * _15658;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15661', '_15658'], conversion_details=[])", "CodeLineInfo(line_number=993, content='float3 _15664 = _15661 * _15661;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15664', '_15661'], conversion_details=[])", "CodeLineInfo(line_number=996, content='if ((max(int(_14451), 2) == 3) && ((_8397 & 32768u) > 0u))', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14451', '_8397'], conversion_details=[])", "CodeLineInfo(line_number=998, content='half3 _14921 = half3(half(1.0 - fast::clamp(fast::max(_15664.x, fast::max(_15664.y, _15664.z)), 0.0, 1.0)));', variables=5, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_14921', '_15664.y', '_15664.z', '_15664.x', 'fast'], conversion_details=[{'target_type': 'half3', 'source_expression': 'half(1.0 - fast::clamp(fast::max(_15664.x, fast::max(_15664.y, _15664.z', 'line_position': 39}])", "CodeLineInfo(line_number=999, content='_18823 = _18818 * _14921;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18823', '_18818', '_14921'], conversion_details=[])", "CodeLineInfo(line_number=1000, content='_18822 = _18806 * _14921;', variables=3, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18822', '_18806', '_14921'], conversion_details=[])", "CodeLineInfo(line_number=1004, content='_18823 = _18818;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18823', '_18818'], conversion_details=[])", "CodeLineInfo(line_number=1005, content='_18822 = _18806;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18822', '_18806'], conversion_details=[])", "CodeLineInfo(line_number=1007, content='_18827 = _18823;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18823', '_18827'], conversion_details=[])", "CodeLineInfo(line_number=1008, content='_18825 = _18822;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18822', '_18825'], conversion_details=[])", "CodeLineInfo(line_number=1012, content='_18827 = _8393;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18827', '_8393'], conversion_details=[])", "CodeLineInfo(line_number=1013, content='_18825 = _8393;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8393', '_18825'], conversion_details=[])", "CodeLineInfo(line_number=1015, content='_18826 = _18827;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18827', '_18826'], conversion_details=[])", "CodeLineInfo(line_number=1016, content='_18824 = _18825;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18824', '_18825'], conversion_details=[])", "CodeLineInfo(line_number=1020, content='_18826 = _8393;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18826', '_8393'], conversion_details=[])", "CodeLineInfo(line_number=1021, content='_18824 = _8393;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18824', '_8393'], conversion_details=[])", "CodeLineInfo(line_number=1023, content='half3 _14565 = half3(float3(0.0));', variables=1, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_14565'], conversion_details=[{'target_type': 'half3', 'source_expression': 'float3(0.0', 'line_position': 27}])", "CodeLineInfo(line_number=1025, content='if (_8590)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8590'], conversion_details=[])", "CodeLineInfo(line_number=1027, content='float3 _14569 = float3(_18824);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_14569', '_18824'], conversion_details=[{'target_type': 'float3', 'source_expression': '_18824', 'line_position': 32}])", "CodeLineInfo(line_number=1028, content='float _14575 = float(dot(_18826, _18826));', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_14575', '_18826'], conversion_details=[{'target_type': 'float', 'source_expression': 'dot(_18826, _18826', 'line_position': 31}])", "CodeLineInfo(line_number=1030, content='if ((float(half(((0.21267099678516387939453125 * _14569.x) + (0.71516001224517822265625 * _14569.y)) + (0.072168998420238494873046875 * _14569.z))) > 0.001000000047497451305389404296875) && (_14575 > 9.9999999747524270787835121154785e-07))', variables=4, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_14569.y', '_14575', '_14569.z', '_14569.x'], conversion_details=[{'target_type': 'float', 'source_expression': 'half(((0.21267099678516387939453125 * _14569.x', 'line_position': 21}])", "CodeLineInfo(line_number=1032, content='float _14592 = fast::clamp(_9109.y, 0.0, 1.0) * 0.75;', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14592', '_9109.y', 'fast'], conversion_details=[])", "CodeLineInfo(line_number=1033, content='float3 _14600 = (float3(_18826) / float3(sqrt(_14575))) * float3(fast::clamp(1.0 - (_14592 * _14592), 0.0, 1.0));', variables=5, float_vars=1, half_vars=0, conversions=3, has_conversion=True, all_variables=['_14600', '_14575', '_18826', 'fast', '_14592'], conversion_details=[{'target_type': 'float3', 'source_expression': '_18826', 'line_position': 37}, {'target_type': 'float3', 'source_expression': 'sqrt(_14575', 'line_position': 54}, {'target_type': 'float3', 'source_expression': 'fast::clamp(1.0 - (_14592 * _14592', 'line_position': 78}])", "CodeLineInfo(line_number=1034, content='float _14604 = mix(_11560, 1.0, 0.25);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14604', '_11560'], conversion_details=[])", "CodeLineInfo(line_number=1035, content='float _14612 = fast::clamp(dot(_9109, fast::normalize(_8373 + _14600)), 0.0, 1.0);', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14612', '_8373', '_14600', '_9109', 'fast'], conversion_details=[])", "CodeLineInfo(line_number=1036, content='float _15697 = _14604 * _14604;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15697', '_14604'], conversion_details=[])", "CodeLineInfo(line_number=1037, content='float _15710 = _15697 / (((((_14612 * _15697) * _15697) - _14612) * _14612) + 1.0);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15710', '_14612', '_15697'], conversion_details=[])", "CodeLineInfo(line_number=1038, content='_18831 = half3(float3(_10557 * _18824) * float3((fast::min(1000.0, (_15710 * _15710) * 0.3183098733425140380859375) * fast::clamp(dot(_9109, _14600), 0.0, 1.0)) * _Block1.SHGIParam.y));', variables=8, float_vars=0, half_vars=0, conversions=2, has_conversion=True, all_variables=['_15710', '_10557', '_14600', '_18824', '_9109', '_18831', 'fast', '_Block1.SHGIParam.y'], conversion_details=[{'target_type': 'half3', 'source_expression': 'float3(_10557 * _18824', 'line_position': 29}, {'target_type': 'float3', 'source_expression': '(fast::min(1000.0, (_15710 * _15710', 'line_position': 61}])", "CodeLineInfo(line_number=1042, content='_18831 = _14565;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18831', '_14565'], conversion_details=[])", "CodeLineInfo(line_number=1044, content='_18830 = _18831;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18831', '_18830'], conversion_details=[])", "CodeLineInfo(line_number=1048, content='_18830 = _14565;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_14565', '_18830'], conversion_details=[])", "CodeLineInfo(line_number=1050, content='float _14641 = float(half(mix(_Block1.SHGIParam2.y, 1.0, _14482)));', variables=3, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_14641', '_14482', '_Block1.SHGIParam2.y'], conversion_details=[{'target_type': 'float', 'source_expression': 'half(mix(_Block1.SHGIParam2.y, 1.0, _14482', 'line_position': 27}])", "CodeLineInfo(line_number=1051, content='_18859 = _18830 * half3(half(_Block1.SHGIParam.y * _14641));', variables=4, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_18859', '_Block1.SHGIParam.y', '_18830', '_14641'], conversion_details=[{'target_type': 'half3', 'source_expression': 'half(_Block1.SHGIParam.y * _14641', 'line_position': 30}])", "CodeLineInfo(line_number=1052, content='_18832 = _18824 * half3(half(_Block1.SHGIParam.x * _14641));', variables=4, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_18824', '_18832', '_Block1.SHGIParam.x', '_14641'], conversion_details=[{'target_type': 'half3', 'source_expression': 'half(_Block1.SHGIParam.x * _14641', 'line_position': 30}])", "CodeLineInfo(line_number=1056, content='_18859 = _8393;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18859', '_8393'], conversion_details=[])", "CodeLineInfo(line_number=1057, content='_18832 = _8393;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_18832', '_8393'], conversion_details=[])", "CodeLineInfo(line_number=1059, content='_19028 = _8560 + (_18832 * _18329);', variables=4, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19028', '_8560', '_18832', '_18329'], conversion_details=[])", "CodeLineInfo(line_number=1060, content='_18989 = _8565 + float3(_18859 * _18329);', variables=4, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_18329', '_18859', '_8565', '_18989'], conversion_details=[{'target_type': 'float3', 'source_expression': '_18859 * _18329', 'line_position': 25}])", "CodeLineInfo(line_number=1064, content='_19028 = _8560;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19028', '_8560'], conversion_details=[])", "CodeLineInfo(line_number=1065, content='_18989 = _8565;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8565', '_18989'], conversion_details=[])", "CodeLineInfo(line_number=1067, content='float _15792 = fast::clamp(dot(_9109, fast::normalize(_8373 + _8373)), 0.0, 1.0);', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15792', 'fast', '_9109', '_8373'], conversion_details=[])", "CodeLineInfo(line_number=1068, content='float _15839 = _12049 / (((((_15792 * _12049) * _12049) - _15792) * _15792) + 1.0);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15839', '_12049', '_15792'], conversion_details=[])", "CodeLineInfo(line_number=1069, content='float _15805 = float(half(fast::clamp(_8378, 0.0, 1.0)));', variables=3, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15805', 'fast', '_8378'], conversion_details=[{'target_type': 'float', 'source_expression': 'half(fast::clamp(_8378, 0.0, 1.0', 'line_position': 19}])", "CodeLineInfo(line_number=1070, content='float _15854 = _12049 * 0.5;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15854', '_12049'], conversion_details=[])", "CodeLineInfo(line_number=1071, content='float _15858 = 1.0 - _15854;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15858', '_15854'], conversion_details=[])", "CodeLineInfo(line_number=1072, content='float _15861 = (_15805 * _15858) + _15854;', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15861', '_15805', '_15858', '_15854'], conversion_details=[])", "CodeLineInfo(line_number=1073, content='half3 _15813 = half3(_12025);', variables=2, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_15813', '_12025'], conversion_details=[{'target_type': 'half3', 'source_expression': '_12025', 'line_position': 19}])", "CodeLineInfo(line_number=1074, content='float3 _15762 = float3(_10569);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15762', '_10569'], conversion_details=[{'target_type': 'float3', 'source_expression': '_10569', 'line_position': 20}])", "CodeLineInfo(line_number=1075, content='float3 _15920 = _Block1.cLocalVirtualLitPos.xyz + _Block1.cVirtualLitParam.xyz;', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15920', '_Block1.cVirtualLitParam.xyz', '_Block1.cLocalVirtualLitPos.xyz'], conversion_details=[])", "CodeLineInfo(line_number=1076, content='float3 _15960 = ((((fast::normalize(cross(_8373, float3(0.0, 1.0, 0.0))) * _15920.x) + float3(0.0, _15920.y, 0.0)) + (_8373 * _15920.z)) + (float4(0.0, 0.0, 0.0, 1.0) * _Block1.World)) - in.IN_WorldPosition.xyz;', variables=8, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15960', '_8373', '_15920.x', '_15920.y', '_15920.z', 'fast', '_Block1.World', 'in.IN_WorldPosition.xyz'], conversion_details=[{'target_type': 'float3', 'source_expression': '0.0, _15920.y, 0.0', 'line_position': 91}])", "CodeLineInfo(line_number=1077, content='float3 _15970 = mix(_15960, fast::normalize(_15960), float3(step(0.0, _Block1.cLocalVirtualLitColor.w)));', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15970', '_15960', '_Block1.cLocalVirtualLitColor.w', 'fast'], conversion_details=[{'target_type': 'float3', 'source_expression': 'step(0.0, _Block1.cLocalVirtualLitColor.w', 'line_position': 57}])", "CodeLineInfo(line_number=1078, content='half _15974 = half(dot(_15970, _15970));', variables=2, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_15974', '_15970'], conversion_details=[{'target_type': 'half', 'source_expression': 'dot(_15970, _15970', 'line_position': 18}])", "CodeLineInfo(line_number=1079, content='float3 _15976 = fast::normalize(_15970);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_15976', 'fast', '_15970'], conversion_details=[])", "CodeLineInfo(line_number=1080, content='half _15979 = _15974 * half(1.0 / (_Block1.cLocalVirtualLitCustom.x * _Block1.cLocalVirtualLitCustom.x));', variables=3, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_15979', '_Block1.cLocalVirtualLitCustom.x', '_15974'], conversion_details=[{'target_type': 'half', 'source_expression': '1.0 / (_Block1.cLocalVirtualLitCustom.x * _Block1.cLocalVirtualLitCustom.x', 'line_position': 27}])", "CodeLineInfo(line_number=1081, content='float _15986 = fast::clamp(1.0 - float(_15979 * _15979), 0.0, 1.0);', variables=3, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_15986', 'fast', '_15979'], conversion_details=[{'target_type': 'float', 'source_expression': '_15979 * _15979', 'line_position': 37}])", "CodeLineInfo(line_number=1082, content='half _16031 = half((fast::clamp(dot(_9109, _15976), 0.0, 1.0) * _Block1.cLocalVirtualLitCustom.z) + (1.0 - _Block1.cLocalVirtualLitCustom.z));', variables=5, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_16031', '_15976', '_Block1.cLocalVirtualLitCustom.z', '_9109', 'fast'], conversion_details=[{'target_type': 'half', 'source_expression': '(fast::clamp(dot(_9109, _15976', 'line_position': 18}])", "CodeLineInfo(line_number=1083, content='float _16112 = fast::clamp(dot(_9109, fast::normalize(_8373 + _15976)), 0.0, 1.0);', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_16112', '_8373', '_15976', '_9109', 'fast'], conversion_details=[])", "CodeLineInfo(line_number=1084, content='float _16159 = _12049 / (((((_16112 * _12049) * _12049) - _16112) * _16112) + 1.0);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_16159', '_12049', '_16112'], conversion_details=[])", "CodeLineInfo(line_number=1085, content='float _8657 = float(_18329);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_8657', '_18329'], conversion_details=[{'target_type': 'float', 'source_expression': '_18329', 'line_position': 18}])", "CodeLineInfo(line_number=1086, content='half _8696 = half(fast::min(_8657, mix(abs(_Block1.cVisibilitySH[0].w), 1.0, _Block1.WorldProbeInfo.x)));', variables=6, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_8696', '_Block1.cVisibilitySH', 'w', '_8657', 'fast', '_Block1.WorldProbeInfo.x'], conversion_details=[{'target_type': 'half', 'source_expression': 'fast::min(_8657, mix(abs(_Block1.cVisibilitySH[0].w', 'line_position': 17}])", "CodeLineInfo(line_number=1087, content='half _16335 = half(-1.023326873779296875);', variables=1, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_16335'], conversion_details=[{'target_type': 'half', 'source_expression': '-1.023326873779296875', 'line_position': 18}])", "CodeLineInfo(line_number=1088, content='half _16336 = half(1.023326873779296875);', variables=1, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_16336'], conversion_details=[])", "CodeLineInfo(line_number=1089, content='half _16342 = _9064.y;', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_16342', '_9064.y'], conversion_details=[])", "CodeLineInfo(line_number=1090, content='half _16351 = half(-0.858085691928863525390625);', variables=1, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_16351'], conversion_details=[{'target_type': 'half', 'source_expression': '-0.858085691928863525390625', 'line_position': 18}])", "CodeLineInfo(line_number=1091, content='half4 _16356 = half4(_16351, half(0.7431240081787109375), _16351, half(0.4290428459644317626953125));', variables=2, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_16356', '_16351'], conversion_details=[{'target_type': 'half4', 'source_expression': '_16351, half(0.7431240081787109375', 'line_position': 19}])", "CodeLineInfo(line_number=1092, content='half _16361 = _9064.z;', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_16361', '_9064.z'], conversion_details=[])", "CodeLineInfo(line_number=1093, content='half _16369 = _9064.x;', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_16369', '_9064.x'], conversion_details=[])", "CodeLineInfo(line_number=1094, content='half4 _16385 = _16356 * half4(_16342 * _16361, _16361 * _16361, _16369 * _16361, (_16369 * _16369) - (_16342 * _16342));', variables=5, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_16385', '_16356', '_16369', '_16361', '_16342'], conversion_details=[{'target_type': 'half4', 'source_expression': '_16342 * _16361, _16361 * _16361, _16369 * _16361, (_16369 * _16369', 'line_position': 28}])", "CodeLineInfo(line_number=1095, content='half _16387 = half(-0.2477079927921295166015625);', variables=1, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_16387'], conversion_details=[{'target_type': 'half', 'source_expression': '-0.2477079927921295166015625', 'line_position': 18}])", "CodeLineInfo(line_number=1096, content='_16385.y = _16385.y + _16387;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_16387', '_16385.y'], conversion_details=[])", "CodeLineInfo(line_number=1097, content='half3 _16279 = half3(_Block1.cSHCoefficients[0].xyz * float3(half3(half(0.886226952075958251953125))));', variables=3, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_16279', 'xyz', '_Block1.cSHCoefficients'], conversion_details=[{'target_type': 'half3', 'source_expression': '_Block1.cSHCoefficients[0].xyz * float3(half3(half(0.886226952075958251953125', 'line_position': 19}])", "CodeLineInfo(line_number=1098, content='float4 _16284 = float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16342))) * _9064.yzxx);', variables=5, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_16284', '_16335', '_16342', '_9064.yzxx', '_16336'], conversion_details=[{'target_type': 'float4', 'source_expression': 'half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16342', 'line_position': 20}])", "CodeLineInfo(line_number=1099, content='float4 _16306 = float4(_16385);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_16306', '_16385'], conversion_details=[{'target_type': 'float4', 'source_expression': '_16385', 'line_position': 20}])", "CodeLineInfo(line_number=1100, content='half3 _16397 = half3(float3(0.081409998238086700439453125, 0.74361002445220947265625, -0.66364002227783203125));', variables=1, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_16397'], conversion_details=[{'target_type': 'half3', 'source_expression': 'float3(0.081409998238086700439453125, 0.74361002445220947265625, -0.66364002227783203125', 'line_position': 19}])", "CodeLineInfo(line_number=1101, content='half _16509 = _16397.y;', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_16509', '_16397.y'], conversion_details=[])", "CodeLineInfo(line_number=1102, content='half _16528 = _16397.z;', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_16528', '_16397.z'], conversion_details=[])", "CodeLineInfo(line_number=1103, content='half _16536 = _16397.x;', variables=2, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['_16536', '_16397.x'], conversion_details=[])", "CodeLineInfo(line_number=1104, content='half4 _16552 = _16356 * half4(_16509 * _16528, _16528 * _16528, _16536 * _16528, (_16536 * _16536) - (_16509 * _16509));', variables=5, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_16552', '_16356', '_16509', '_16528', '_16536'], conversion_details=[{'target_type': 'half4', 'source_expression': '_16509 * _16528, _16528 * _16528, _16536 * _16528, (_16536 * _16536', 'line_position': 28}])", "CodeLineInfo(line_number=1105, content='_16552.y = _16552.y + _16387;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_16387', '_16552.y'], conversion_details=[])", "CodeLineInfo(line_number=1106, content='float4 _16451 = float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16509))) * _16397.yzxx);', variables=5, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_16451', '_16397.yzxx', '_16335', '_16509', '_16336'], conversion_details=[{'target_type': 'float4', 'source_expression': 'half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16509', 'line_position': 20}])", "CodeLineInfo(line_number=1107, content='float4 _16473 = float4(_16552);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_16473', '_16552'], conversion_details=[{'target_type': 'float4', 'source_expression': '_16552', 'line_position': 20}])", "CodeLineInfo(line_number=1108, content='half3 _16258 = ((((max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16284)), half(dot(_Block1.cSHCoefficients[3], _16284)), half(dot(_Block1.cSHCoefficients[5], _16284))) + half3(half(dot(_Block1.cSHCoefficients[2], _16306)), half(dot(_Block1.cSHCoefficients[4], _16306)), half(dot(_Block1.cSHCoefficients[6], _16306)))), _9179) * half3(half(0.699999988079071044921875 + (0.300000011920928955078125 * _Block1.EnvInfo.z)))) * half3(half(float(_8295 * _18329) * mix(1.0, _Block1.WorldProbeInfo.y, step(0.0, _Block1.cVisibilitySH[0].w))))) * half3(half(_Block1.GIInfo.z))) + half3(float3(max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16451)), half(dot(_Block1.cSHCoefficients[3], _16451)), half(dot(_Block1.cSHCoefficients[5], _16451))) + half3(half(dot(_Block1.cSHCoefficients[2], _16473)), half(dot(_Block1.cSHCoefficients[4], _16473)), half(dot(_Block1.cSHCoefficients[6], _16473)))), _9179)) * float3(((3.1415927410125732421875 * float(clamp(dot(_9064, _16397), half(0.0), half(1.0)))) * _Block1.WorldProbeInfo.w) * float(half(float(_8295 * _18236)) * half(0.5))))) * half3(half(_Block1.cSHCoefficients[0].w));', variables=19, float_vars=0, half_vars=1, conversions=18, has_conversion=True, all_variables=['_16258', '_Block1.GIInfo.z', '_16284', '_18329', '_8295', '_9179', '_16473', '_16397', '_Block1.cVisibilitySH', '_18236', 'w', '_Block1.EnvInfo.z', '_Block1.WorldProbeInfo.y', '_9064', '_Block1.cSHCoefficients', '_16279', '_16451', '_Block1.WorldProbeInfo.w', '_16306'], conversion_details=[{'target_type': 'half3', 'source_expression': 'half(dot(_Block1.cSHCoefficients[1], _16284', 'line_position': 37}, {'target_type': 'half', 'source_expression': 'dot(_Block1.cSHCoefficients[3], _16284', 'line_position': 90}, {'target_type': 'half', 'source_expression': 'dot(_Block1.cSHCoefficients[5], _16284', 'line_position': 137}, {'target_type': 'half3', 'source_expression': 'half(dot(_Block1.cSHCoefficients[2], _16306', 'line_position': 186}, {'target_type': 'half', 'source_expression': 'dot(_Block1.cSHCoefficients[4], _16306', 'line_position': 239}, {'target_type': 'half', 'source_expression': 'dot(_Block1.cSHCoefficients[6], _16306', 'line_position': 286}, {'target_type': 'half3', 'source_expression': 'half(0.699999988079071044921875 + (0.300000011920928955078125 * _Block1.EnvInfo.z', 'line_position': 344}, {'target_type': 'half3', 'source_expression': 'half(float(_8295 * _18329', 'line_position': 438}, {'target_type': 'half3', 'source_expression': 'half(_Block1.GIInfo.z', 'line_position': 552}, {'target_type': 'half3', 'source_expression': 'float3(max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16451', 'line_position': 585}, {'target_type': 'half', 'source_expression': 'dot(_Block1.cSHCoefficients[3], _16451', 'line_position': 665}, {'target_type': 'half', 'source_expression': 'dot(_Block1.cSHCoefficients[5], _16451', 'line_position': 712}, {'target_type': 'half3', 'source_expression': 'half(dot(_Block1.cSHCoefficients[2], _16473', 'line_position': 761}, {'target_type': 'half', 'source_expression': 'dot(_Block1.cSHCoefficients[4], _16473', 'line_position': 814}, {'target_type': 'half', 'source_expression': 'dot(_Block1.cSHCoefficients[6], _16473', 'line_position': 861}, {'target_type': 'float3', 'source_expression': '((3.1415927410125732421875 * float(clamp(dot(_9064, _16397', 'line_position': 920}, {'target_type': 'float', 'source_expression': 'half(float(_8295 * _18236', 'line_position': 1042}, {'target_type': 'half3', 'source_expression': 'half(_Block1.cSHCoefficients[0].w', 'line_position': 1094}])", "CodeLineInfo(line_number=1109, content='half3 _8776 = ((_18255 + (half3((float3(_15813 * (half(fast::min(1000.0, (_15839 * _15839) * 0.3183098733425140380859375)) * half(0.25 / fast::max(_15861 * _15861, _12108)))) * _Block1.cVirtualLitColor.xyz) * _15805) + half3((_Block1.cVirtualLitColor.xyz * abs(_8378)) * _15762))) + half3(fast::min(float3(8192.0), ((((float3((_15813 * (half(fast::min(1000.0, (_16159 * _16159) * 0.3183098733425140380859375)) * half(0.25 / fast::max(_15861 * ((float(_16031) * _15858) + _15854), _12108)))) * _16031) * float3(_Block1.cLocalVirtualLitPos.w)) + float3(_10569 * _16031)) * float(half(fast::min(float(half((_15986 * _15986) / ((float(_15974) * abs(_Block1.cLocalVirtualLitCustom.y)) + 9.9999997473787516355514526367188e-05))), _Block1.cLocalVirtualLitCustom.w)))) * (_Block1.cLocalVirtualLitColor.xyz * abs(_Block1.cLocalVirtualLitColor.w))) * _Block1.DiyLightingInfo.z))) * _8696;', variables=25, float_vars=0, half_vars=1, conversions=10, has_conversion=True, all_variables=['_8776', '_Block1.cLocalVirtualLitPos.w', '_Block1.cLocalVirtualLitColor.xyz', '_12108', '_18255', '_15858', '_8378', '_Block1.cLocalVirtualLitCustom.w', '_8696', '_Block1.cLocalVirtualLitCustom.y', '_15805', '_15839', '_15854', '_16031', '_Block1.DiyLightingInfo.z', '_Block1.cVirtualLitColor.xyz', '_15762', '_15813', 'fast', '_Block1.cLocalVirtualLitColor.w', '_15974', '_15986', '_10569', '_16159', '_15861'], conversion_details=[{'target_type': 'half3', 'source_expression': '(float3(_15813 * (half(fast::min(1000.0, (_15839 * _15839', 'line_position': 30}, {'target_type': 'half', 'source_expression': '0.25 / fast::max(_15861 * _15861, _12108', 'line_position': 129}, {'target_type': 'half3', 'source_expression': '(_Block1.cVirtualLitColor.xyz * abs(_8378', 'line_position': 223}, {'target_type': 'half3', 'source_expression': 'fast::min(float3(8192.0', 'line_position': 287}, {'target_type': 'float3', 'source_expression': '(_15813 * (half(fast::min(1000.0, (_16159 * _16159', 'line_position': 323}, {'target_type': 'half', 'source_expression': '0.25 / fast::max(_15861 * ((float(_16031', 'line_position': 416}, {'target_type': 'float3', 'source_expression': '_Block1.cLocalVirtualLitPos.w', 'line_position': 507}, {'target_type': 'float3', 'source_expression': '_10569 * _16031', 'line_position': 548}, {'target_type': 'float', 'source_expression': 'half(fast::min(float(half((_15986 * _15986', 'line_position': 575}, {'target_type': 'float', 'source_expression': '_15974', 'line_position': 629}])", "CodeLineInfo(line_number=1110, content='float _16622 = length(_8370);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_16622', '_8370'], conversion_details=[])", "CodeLineInfo(line_number=1111, content='float _16645 = _8370.y;', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_16645', '_8370.y'], conversion_details=[])", "CodeLineInfo(line_number=1112, content='float _16657 = (_16645 + _12108) + float((_9251 * half(9.9956989288330078125e-05)) * half(int(sign(_16645))));', variables=4, float_vars=1, half_vars=0, conversions=2, has_conversion=True, all_variables=['_16657', '_16645', '_12108', '_9251'], conversion_details=[{'target_type': 'float', 'source_expression': '(_9251 * half(9.9956989288330078125e-05', 'line_position': 39}, {'target_type': 'half', 'source_expression': 'int(sign(_16645', 'line_position': 89}])", "CodeLineInfo(line_number=1113, content='float2 _16682 = fast::max(float2(0.0), float2(_Block1.FogInfo.w, exp((-_Block1.AerialPerspectiveMie.y) * _Block1.CameraPos.y) * _Block1.AerialPerspectiveMie.z) * ((float2(1.0) - exp(-fast::min(float2(_Block1.FogInfo.z, _Block1.AerialPerspectiveMie.y) * _16657, float2(10.0)))) / float2(_16657)));', variables=8, float_vars=1, half_vars=0, conversions=3, has_conversion=True, all_variables=['_16682', '_16657', '_Block1.FogInfo.w', '_Block1.CameraPos.y', '_Block1.AerialPerspectiveMie.z', 'fast', '_Block1.AerialPerspectiveMie.y', '_Block1.FogInfo.z'], conversion_details=[{'target_type': 'float2', 'source_expression': '_Block1.FogInfo.w, exp((-_Block1.AerialPerspectiveMie.y', 'line_position': 43}, {'target_type': 'float2', 'source_expression': '_Block1.FogInfo.z, _Block1.AerialPerspectiveMie.y', 'line_position': 197}, {'target_type': 'float2', 'source_expression': '_16657', 'line_position': 283}])", "CodeLineInfo(line_number=1114, content='float3 _16688 = fast::max(float3(_12108), _Block1.AerialPerspectiveExt.xyz);', variables=4, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_16688', '_12108', 'fast', '_Block1.AerialPerspectiveExt.xyz'], conversion_details=[{'target_type': 'float3', 'source_expression': '_12108', 'line_position': 30}])", "CodeLineInfo(line_number=1115, content='float3 _16698 = float3(_8303);', variables=2, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_16698', '_8303'], conversion_details=[{'target_type': 'float3', 'source_expression': '_8303', 'line_position': 20}])", "CodeLineInfo(line_number=1116, content='float3 _16715 = exp(-(_16688 * ((_16622 * (_Block1.FogColor.w + ((1.0 - _Block1.FogColor.w) * fast::clamp(_16622 / _Block1.FogInfo.x, 0.0, 1.0)))) * ((_16682.x / dot(_16688, _16698)) + ((_16682.y * fast::max(9.9999999747524270787835121154785e-07, _Block1.AerialPerspectiveRay.w * 0.0005000000237487256526947021484375)) * 5.0)))));', variables=10, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_16715', '_16682.y', '_16698', '_16688', '_Block1.FogInfo.x', '_Block1.AerialPerspectiveRay.w', '_16622', 'fast', '_16682.x', '_Block1.FogColor.w'], conversion_details=[])", "CodeLineInfo(line_number=1117, content='float3 _16602 = fast::normalize(_8370);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_16602', 'fast', '_8370'], conversion_details=[])", "CodeLineInfo(line_number=1118, content='float _16756 = fast::clamp(dot(_16602, _Block1.OriginSunDir.xyz), 0.0, 1.0);', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_16756', '_16602', '_Block1.OriginSunDir.xyz', 'fast'], conversion_details=[])", "CodeLineInfo(line_number=1119, content='float _16759 = fast::max(0.0, _16602.y);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_16759', '_16602.y', 'fast'], conversion_details=[])", "CodeLineInfo(line_number=1120, content='float _16820 = fast::clamp((_16622 - 80.0) / fast::max(_12108, 520.0), 0.0, 1.0);', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_16820', '_12108', 'fast', '_16622'], conversion_details=[])", "CodeLineInfo(line_number=1121, content='float _16778 = 1.0 - (_16759 * _16759);', variables=2, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_16778', '_16759'], conversion_details=[])", "CodeLineInfo(line_number=1122, content='float3 _16785 = float3(((1.0 - (_Block1.AerialPerspectiveExt.w * _Block1.AerialPerspectiveExt.w)) / (12.56637096405029296875 * powr(fast::max(1.0 + (_Block1.AerialPerspectiveExt.w * (_Block1.AerialPerspectiveExt.w - (2.0 * _16756))), _12108), 1.5))) * (_16820 * _16820)) * _Block1.SunFogColor.xyz;', variables=7, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_16785', '_16820', '_Block1.SunFogColor.xyz', '_Block1.AerialPerspectiveExt.w', '_12108', 'fast', '_16756'], conversion_details=[{'target_type': 'float3', 'source_expression': '((1.0 - (_Block1.AerialPerspectiveExt.w * _Block1.AerialPerspectiveExt.w', 'line_position': 20}])", "CodeLineInfo(line_number=1123, content='half _16793 = half(_16756);', variables=2, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['_16793', '_16756'], conversion_details=[{'target_type': 'half', 'source_expression': '_16756', 'line_position': 18}])", "CodeLineInfo(line_number=1124, content='float3 _16805 = ((_Block1.AerialPerspectiveRay.xyz * float(half(1.0) + (_16793 * _16793))) + (_16785 * _Block1.AerialPerspectiveMie.x)) + ((_Block1.FogColor.xyz * (0.0596831031143665313720703125 * (1.0 + (_16778 * _16778)))) + _16785);', variables=7, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_16805', '_Block1.FogColor.xyz', '_16793', '_Block1.AerialPerspectiveMie.x', '_16785', '_16778', '_Block1.AerialPerspectiveRay.xyz'], conversion_details=[{'target_type': 'float', 'source_expression': 'half(1.0', 'line_position': 57}])", "CodeLineInfo(line_number=1125, content='float3 _16862 = (((((((((((_Block1.cVisibilitySH[0].xyz * _8657) * _15762) * _Block1.AmbientColor.w) * _Block1.ReflectionProbeBBMin.w) * float3(_Block1.cSHCoefficients[0].w)).xyz + float3(_18268)).xyz + (_18989 + float3(((_9179 * _9254) + _8393) * _8696))).xyz + float3((_19028 + half3(float3(_16258) * float3((_15805 * 0.5) + 0.5))) * _10569)).xyz + float3((half3(_9698 * _10588) * _11772) + _8776)).xyz * dot(_16715, _16698)) + (_16805 - (_16805 * _16715))).xyz;', variables=26, float_vars=1, half_vars=0, conversions=6, has_conversion=True, all_variables=['_16862', 'xyz', '_8776', '_16698', '_8393', '_16258', '_16715', '_16805', '_9254', '_8657', '_8696', '_15805', '_9179', '_11772', '_Block1.cVisibilitySH', '_Block1.AmbientColor.w', '_Block1.ReflectionProbeBBMin.w', 'w', '_10588', '_15762', '_Block1.cSHCoefficients', '_9698', '_18268', '_10569', '_19028', '_18989'], conversion_details=[{'target_type': 'float3', 'source_expression': '_Block1.cSHCoefficients[0].w', 'line_position': 141}, {'target_type': 'float3', 'source_expression': '_18268', 'line_position': 185}, {'target_type': 'float3', 'source_expression': '((_9179 * _9254', 'line_position': 217}, {'target_type': 'float3', 'source_expression': '(_19028 + half3(float3(_16258', 'line_position': 267}, {'target_type': 'float3', 'source_expression': '(_15805 * 0.5', 'line_position': 307}, {'target_type': 'float3', 'source_expression': '(half3(_9698 * _10588', 'line_position': 355}])", "CodeLineInfo(line_number=1126, content='float3 _8804 = (_16862 * _9868).xyz;', variables=4, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8804', '_16862', 'xyz', '_9868'], conversion_details=[])", "CodeLineInfo(line_number=1128, content='if (_Block1.eIsPlayerOverride < 0.5)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_Block1.eIsPlayerOverride'], conversion_details=[])", "CodeLineInfo(line_number=1131, content='if ((_Block1.ScreenMotionGray.x * _Block1.ScreenMotionGray.x) > _12108)', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_12108', '_Block1.ScreenMotionGray.x'], conversion_details=[])", "CodeLineInfo(line_number=1133, content='float _16911 = fast::clamp((_Block1.CameraPos.w - _Block1.ScreenMotionGray.w) / fast::max(_12108, (_Block1.ScreenMotionGray.w + abs(_Block1.ScreenMotionGray.x)) - _Block1.ScreenMotionGray.w), 0.0, 1.0);', variables=6, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_16911', '_Block1.ScreenMotionGray.w', '_Block1.ScreenMotionGray.x', '_12108', '_Block1.CameraPos.w', 'fast'], conversion_details=[])", "CodeLineInfo(line_number=1135, content='if (_Block1.ScreenMotionGray.x > 0.001000000047497451305389404296875)', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_Block1.ScreenMotionGray.x'], conversion_details=[])", "CodeLineInfo(line_number=1137, content='_19029 = 1.0 - _16911;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19029', '_16911'], conversion_details=[])", "CodeLineInfo(line_number=1141, content='_19029 = _16911;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19029', '_16911'], conversion_details=[])", "CodeLineInfo(line_number=1143, content='_19032 = mix(_8804, float3(dot(_8804, _16698) * (0.00999999977648258209228515625 * floor(_Block1.ScreenMotionGray.z))), float3(_19029 * fract(_Block1.ScreenMotionGray.z)));', variables=5, float_vars=0, half_vars=0, conversions=2, has_conversion=True, all_variables=['_19029', '_19032', '_Block1.ScreenMotionGray.z', '_16698', '_8804'], conversion_details=[{'target_type': 'float3', 'source_expression': 'dot(_8804, _16698', 'line_position': 32}, {'target_type': 'float3', 'source_expression': '_19029 * fract(_Block1.ScreenMotionGray.z', 'line_position': 132}])", "CodeLineInfo(line_number=1147, content='_19032 = _8804;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8804', '_19032'], conversion_details=[])", "CodeLineInfo(line_number=1149, content='_19031 = _19032;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_19031', '_19032'], conversion_details=[])", "CodeLineInfo(line_number=1153, content='_19031 = _8804;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8804', '_19031'], conversion_details=[])", "CodeLineInfo(line_number=1155, content='float4 _8808 = float4(_19031.x, _19031.y, _19031.z, float4(0.0).w);', variables=5, float_vars=1, half_vars=0, conversions=1, has_conversion=True, all_variables=['_8808', '_19031.y', 'w', '_19031.x', '_19031.z'], conversion_details=[{'target_type': 'float4', 'source_expression': '_19031.x, _19031.y, _19031.z, float4(0.0', 'line_position': 19}])", "CodeLineInfo(line_number=1156, content='_8808.w = _9868;', variables=2, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['_9868', '_8808.w'], conversion_details=[])", "CodeLineInfo(line_number=1157, content='float3 _8816 = fast::min(_8808.xyz, float3(10000.0));', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['_8816', 'fast', '_8808.xyz'], conversion_details=[])", "CodeLineInfo(line_number=1158, content='out._Ret = float4(_8816.x, _8816.y, _8816.z, _8808.w);', variables=5, float_vars=0, half_vars=0, conversions=1, has_conversion=True, all_variables=['_8816.y', '_8808.w', '_8816.x', '_8816.z', 'out._Ret'], conversion_details=[{'target_type': 'float4', 'source_expression': '_8816.x, _8816.y, _8816.z, _8808.w', 'line_position': 15}])", "CodeLineInfo(line_number=1159, content='return out;', variables=1, float_vars=0, half_vars=0, conversions=0, has_conversion=False, all_variables=['out'], conversion_details=[])"], "syntax_trees": ["literal(constant half3 _18526 = {}:unknown)", "literal(constant float3 _19185 = {}:unknown)", "literal(constant half _19493 = {}:unknown)", "literal(constant float _19585 = {}:unknown)", "literal(constant int _19621 = {}:unknown)", "literal(constant float3 _21234 = {}:unknown)", "literal(constant float3 _21295 = {}:unknown)", "literal(constant half4 _21296 = {}:unknown)", "literal(constant half3 _21297 = {}:unknown)", "literal(float4 _Ret [[color(0)]]:unknown)", "literal(float4 IN_TexCoord [[user(locn0)]]:unknown)", "literal(float4 IN_WorldPosition [[user(locn1)]]:unknown)", "literal(half4 IN_WorldNormal [[user(locn2)]]:unknown)", "literal(half4 IN_WorldTangent [[user(locn3)]]:unknown)", "literal(half4 IN_WorldBinormal [[user(locn4)]]:unknown)", "literal(half4 IN_TintColor [[user(locn5)]]:unknown)", "literal(float IN_LinearZ [[user(locn6)]]:unknown)", "literal(half3 IN_LocalPosition [[user(locn7)]]:unknown)", "literal(half4 IN_StaticWorldNormal [[user(locn8)]]:unknown)", "literal(constexpr sampler sShadowMapArraySamplerSmplr(filter::linear, mip_filter::linear, compare_func::greater):unknown)", "literal(main0_out out = {}:unknown)", "assignment(=:half)", "assignment(=:half3)", "assignment(=:half)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:half)", "assignment(=:half4)", "assignment(=:half3)", "assignment(=:half3)", "assignment(=:float4)", "assignment(=:half4)", "assignment(=:float4)", "assignment(=:half4)", "assignment(=:float)", "assignment(=:half)", "assignment(=:half)", "assignment(=:float)", "assignment(=:half)", "function(if:unknown)", "literal(discard_fragment():unknown)", "assignment(=:half4)", "assignment(=:half)", "assignment(=:half3)", "assignment(=:half3)", "assignment(=:half)", "assignment(=:half)", "assignment(=:half)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:half)", "assignment(=:half3)", "assignment(=:half4)", "assignment(=:half)", "assignment(=:half)", "assignment(=:float)", "assignment(=:float3)", "assignment(=:half4)", "assignment(=:half3)", "function(if:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:half3)", "assignment(=:half)", "assignment(=:float)", "assignment(=:float4)", "function(if:unknown)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:half)", "assignment(=:float)", "assignment(=:float)", "assignment(=:half)", "assignment(=:half)", "assignment(=:float)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:half)", "assignment(=:float)", "assignment(=:half3)", "assignment(=:half3)", "assignment(=:half)", "function(if:unknown)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:float)", "assignment(=:half)", "assignment(=:float4)", "assignment(=:float4)", "operator(-:unknown)", "assignment(=:float4)", "assignment(=:float4)", "assignment(=:float4)", "operator(-:unknown)", "function(if:unknown)", "assignment(=:float4)", "operator(-:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:float2)", "assignment(=:float2)", "assignment(=:float2)", "assignment(=:float2)", "assignment(=:float2)", "assignment(=:float2)", "assignment(=:float2)", "assignment(=:float2)", "assignment(=:float)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:float3)", "literal(_17928.z = _10004:unknown)", "assignment(=:float2)", "assignment(=:float2)", "assignment(=:float2)", "assignment(=:float2)", "assignment(=:float2)", "assignment(=:float2)", "assignment(=:float2)", "assignment(=:float)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:half)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:half3)", "assignment(=:half3)", "assignment(=:float3)", "assignment(=:half3)", "assignment(=:uint)", "assignment(=:bool)", "function(if:unknown)", "assignment(=:float3)", "function(if:unknown)", "function(if:unknown)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:float)", "operator(-:unknown)", "function(if:unknown)", "assignment(=:float3)", "operator(+:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float3)", "operator(-:unknown)", "function(if:unknown)", "assignment(=:float3)", "operator(+:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float3)", "operator(-:unknown)", "function(if:unknown)", "assignment(=:float3)", "operator(+:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:float3)", "function(if:unknown)", "assignment(=:uint)", "assignment(=:float)", "function(if:unknown)", "assignment(=:float)", "assignment(=:float2)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "literal(_17954.x = (_11020 < (_11005 + 0.5)) ? fast::min(_11020, _11005 + 0.49609375) : fast::max(_11020, _11005 + 0.50390625):unknown)", "assignment(=:float)", "literal(_17954.z = (_11038 < (_11015 + 0.5)) ? fast::min(_11038, _11015 + 0.49609375) : fast::max(_11038, _11015 + 0.50390625):unknown)", "assignment(=:float)", "assignment(=:float)", "assignment(=:uint)", "assignment(=:uint)", "assignment(=:uint)", "assignment(=:float2)", "assignment(=:float)", "assignment(=:float3)", "literal(_17962.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11102.xy, uint(rint(_11102.z)), level(0.0)).x):unknown)", "assignment(=:float3)", "assignment(=:half3)", "assignment(=:float2)", "assignment(=:float)", "assignment(=:float3)", "literal(_17964.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11140.xy, uint(rint(_11140.z)), level(0.0)).x):unknown)", "assignment(=:float3)", "assignment(=:half3)", "assignment(=:half4)", "assignment(=:unknown)", "assignment(=:float2)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "literal(_17977.x = (_11259 < (_11244 + 0.5)) ? fast::min(_11259, _11244 + 0.49609375) : fast::max(_11259, _11244 + 0.50390625):unknown)", "assignment(=:float)", "literal(_17977.z = (_11277 < (_11254 + 0.5)) ? fast::min(_11277, _11254 + 0.49609375) : fast::max(_11277, _11254 + 0.50390625):unknown)", "assignment(=:float)", "assignment(=:float)", "assignment(=:uint)", "assignment(=:uint)", "assignment(=:uint)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:unknown)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "function(if:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:float)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:unknown)", "assignment(=:unknown)", "function(if:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:half)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:half4)", "assignment(=:half4)", "assignment(=:half4)", "assignment(=:half4)", "assignment(=:half4)", "assignment(=:half4)", "assignment(=:float)", "assignment(=:float)", "assignment(=:half)", "assignment(=:half)", "assignment(=:half)", "assignment(=:half)", "assignment(=:half)", "assignment(=:half3)", "assignment(=:half)", "assignment(=:half)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:uint)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "function(for:unknown)", "assignment(=:uint)", "assignment(=:int)", "assignment(=:int)", "assignment(=:int)", "assignment(=:int)", "assignment(=:uint)", "function(if:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:uint)", "function(if:unknown)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float)", "function(if:unknown)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "function(if:unknown)", "assignment(=:uint)", "assignment(=:float)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "function(if:unknown)", "assignment(=:float)", "function(if:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:float)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "function(if:unknown)", "assignment(=:uint)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "function(if:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:bool)", "function(if:unknown)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float3)", "assignment(=:float3x3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:float)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:float)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:half)", "assignment(=:half3)", "assignment(=:half)", "function(if:unknown)", "assignment(=:float)", "assignment(=:unknown)", "assignment(=:unknown)", "operator(/:unknown)", "literal(} while(false):unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:half3)", "assignment(=:float3)", "assignment(=:bool)", "function(if:unknown)", "assignment(=:bool)", "function(if:unknown)", "assignment(=:uint)", "assignment(=:uint)", "assignment(=:float)", "function(if:unknown)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:float)", "operator(-:unknown)", "function(if:unknown)", "assignment(=:float3)", "operator(+:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float3)", "operator(-:unknown)", "function(if:unknown)", "assignment(=:float3)", "operator(+:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float3)", "operator(-:unknown)", "function(if:unknown)", "assignment(=:float3)", "operator(+:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:float3)", "function(if:unknown)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:half3)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "function(if:unknown)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float2)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "literal(_18095.x = (_14982 < (_14967 + 0.5)) ? fast::min(_14982, _14967 + 0.49609375) : fast::max(_14982, _14967 + 0.50390625):unknown)", "assignment(=:float)", "literal(_18095.z = (_15000 < (_14977 + 0.5)) ? fast::min(_15000, _14977 + 0.49609375) : fast::max(_15000, _14977 + 0.50390625):unknown)", "assignment(=:float)", "assignment(=:float)", "assignment(=:uint)", "assignment(=:uint)", "assignment(=:uint)", "assignment(=:float2)", "assignment(=:float2)", "assignment(=:float)", "assignment(=:float3)", "literal(_18103.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15080.xy, uint(rint(_15080.z)), level(0.0)).x):unknown)", "assignment(=:float3)", "assignment(=:half3)", "assignment(=:float)", "assignment(=:float3)", "literal(_18105.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15106.xy, uint(rint(_15106.z)), level(0.0)).x):unknown)", "assignment(=:float3)", "assignment(=:half3)", "assignment(=:half4)", "assignment(=:half4)", "assignment(=:float3)", "literal(_18107.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15140.xy, uint(rint(_15140.z)), level(0.0)).x):unknown)", "assignment(=:float3)", "assignment(=:half3)", "assignment(=:float3)", "literal(_18109.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15166.xy, uint(rint(_15166.z)), level(0.0)).x):unknown)", "assignment(=:float3)", "assignment(=:half3)", "assignment(=:half4)", "assignment(=:float3)", "literal(_18111.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15200.xy, uint(rint(_15200.z)), level(0.0)).x):unknown)", "assignment(=:float3)", "assignment(=:half3)", "assignment(=:float3)", "literal(_18113.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15226.xy, uint(rint(_15226.z)), level(0.0)).x):unknown)", "assignment(=:float3)", "assignment(=:half3)", "assignment(=:half4)", "assignment(=:half)", "assignment(=:half)", "assignment(=:half)", "assignment(=:half)", "assignment(=:half3)", "literal(_18130.x = half(float(dot(_14556, _15343)) * 2.0):unknown)", "assignment(=:half3)", "literal(_18130.y = half(float(dot(_14556, _15352)) * 2.0):unknown)", "assignment(=:half3)", "literal(_18130.z = half(float(dot(_14556, _15361)) * 2.0):unknown)", "function(if:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:float2)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "literal(_18143.x = (_15453 < (_15438 + 0.5)) ? fast::min(_15453, _15438 + 0.49609375) : fast::max(_15453, _15438 + 0.50390625):unknown)", "assignment(=:float)", "literal(_18143.z = (_15471 < (_15448 + 0.5)) ? fast::min(_15471, _15448 + 0.49609375) : fast::max(_15471, _15448 + 0.50390625):unknown)", "assignment(=:float)", "assignment(=:float)", "assignment(=:uint)", "assignment(=:uint)", "assignment(=:uint)", "assignment(=:float2)", "assignment(=:float2)", "assignment(=:float)", "assignment(=:float3)", "literal(_18151.x = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15551.xy, uint(rint(_15551.z)), level(0.0)).x):unknown)", "assignment(=:float)", "assignment(=:float3)", "literal(_18153.x = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15563.xy, uint(rint(_15563.z)), level(0.0)).x):unknown)", "assignment(=:float3)", "literal(_18151.y = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15575.xy, uint(rint(_15575.z)), level(0.0)).x):unknown)", "assignment(=:float3)", "literal(_18153.y = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15587.xy, uint(rint(_15587.z)), level(0.0)).x):unknown)", "assignment(=:float3)", "literal(_18151.z = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15599.xy, uint(rint(_15599.z)), level(0.0)).x):unknown)", "assignment(=:float3)", "literal(_18153.z = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15611.xy, uint(rint(_15611.z)), level(0.0)).x):unknown)", "assignment(=:half3)", "assignment(=:half)", "operator(*:unknown)", "operator(*:unknown)", "operator(*:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "function(if:unknown)", "assignment(=:half3)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:half3)", "function(if:unknown)", "assignment(=:float3)", "assignment(=:float)", "function(if:unknown)", "assignment(=:float)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:float)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:half3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:half)", "assignment(=:float3)", "assignment(=:half)", "assignment(=:float)", "assignment(=:half)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:half)", "assignment(=:half)", "assignment(=:half)", "assignment(=:half)", "assignment(=:half)", "assignment(=:half4)", "assignment(=:half)", "assignment(=:half)", "assignment(=:half4)", "assignment(=:half)", "operator(+:unknown)", "assignment(=:half3)", "assignment(=:float4)", "assignment(=:float4)", "assignment(=:half3)", "assignment(=:half)", "assignment(=:half)", "assignment(=:half)", "assignment(=:half4)", "operator(+:unknown)", "assignment(=:float4)", "assignment(=:float4)", "assignment(=:half3)", "assignment(=:half3)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float2)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float)", "assignment(=:float3)", "assignment(=:half)", "assignment(=:float3)", "assignment(=:float3)", "assignment(=:float3)", "function(if:unknown)", "function(if:unknown)", "assignment(=:float)", "function(if:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:unknown)", "assignment(=:float4)", "literal(_8808.w = _9868:unknown)", "assignment(=:float3)", "literal(out._Ret = float4(_8816.x, _8816.y, _8816.z, _8808.w):unknown)", "literal(return out:unknown)"], "precise_analyses": ["<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B71161B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E78080>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E78050>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E780E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E78170>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E781D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E78230>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E78290>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E782F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E78350>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E783B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E78410>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E78470>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E784D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E78530>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E78590>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E785F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E78650>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E786B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E78710>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E78770>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E787D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E789B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E78860>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E78CE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E78E60>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E79040>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E79220>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E793A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E79520>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E798E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E79A60>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E79C40>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E79DC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E79FA0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E7A120>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E7A2A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E7A420>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E7A660>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E7A840>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E7AB10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E7ABA0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E7AD20>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E7AF00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E7B0E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E7B3E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E7B560>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E7B6E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E7B860>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E7BE00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5E7BFE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA48C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA4AA0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA4F80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA5100>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA5280>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA5700>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA58E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA5AC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA5C40>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA5DC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA5EB0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA6120>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA6270>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA6450>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA66F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA6870>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA69F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA6ED0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA72F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA76B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA7830>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA79E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA7C20>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA7DA0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5EA7FE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB0170>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB03B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB0770>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB08F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB0E30>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB0F50>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB1580>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB1790>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB1910>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB1CA0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB1EE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB2150>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB23C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB26F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB27E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB29F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB2AE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB2BD0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB2DE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB2F90>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB31A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB33E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB3860>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB3950>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB3C20>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CB3E00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBC050>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBC1D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBC4D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBCAA0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBCE30>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBCF80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBD0D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBD2B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBD550>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBDA30>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBDBB0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBDD00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBDEE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBE120>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBE2A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBE3F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBE5A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBE7E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBE930>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBEA80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBEC30>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBEED0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBF170>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBF2F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBF9B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBFD70>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CBFEF0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCC110>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCC470>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCC650>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCC830>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCCAD0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCCD70>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCD010>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCD250>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCD490>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCD910>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCDEB0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCE390>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCE810>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCE990>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCEB10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCEC90>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCEF30>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCF0B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCF230>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCF2C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCF620>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCF800>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCF9E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCFC80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CCFF20>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CD8200>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CD8440>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CD8680>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CD8B00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CD90A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CD9580>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CD9A00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CD9B80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CD9D00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CD9E80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CDAD80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CDAFC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CDB140>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CDB380>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CDB740>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CDBB00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CDBCE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CDBF20>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE0260>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE04A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE0590>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE0950>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE0A40>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE0BF0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE1010>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE12B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE1550>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE16D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE18B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE1A00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE1C70>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE1DF0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE1F40>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE2090>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE21E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE2360>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE2540>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE26C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE2810>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE2A80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE2C00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE2D50>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE2EA0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE2FF0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE3170>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE3350>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE34D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE3620>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE3890>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE3A10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE3B60>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE3CB0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CE3E00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF4080>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF4530>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF4770>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF4AD0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF4C80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF4F20>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF53A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF55E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF5A00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF5C40>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF6060>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF61E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF6270>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF63F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF6480>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF6780>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF6960>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF6AE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF6D20>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF6EA0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF7320>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF74A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF7680>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF7710>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF78F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF7AD0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF7F50>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF8110>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF82F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF8380>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF8560>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF8740>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF8B60>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF95B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF9A30>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CF9C70>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CFA090>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CFA2D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CFA6F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CFA870>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CFA900>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CFAA80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CFAB10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CFAE10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CFAFF0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CFB170>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CFB3B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CFB530>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CFBA10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5CFBEF0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D08440>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D08980>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D08BC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D08E00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D09070>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D093A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D094F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D09640>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D09790>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D098E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D09A30>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D09C70>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D09EB0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D0A030>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D0A1E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D0A330>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D0A420>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D0A630>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D0A780>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D0A8D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D0AA20>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D0AC60>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D0AF60>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D0B1A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D0B560>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D0B6E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D0BB60>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D0BDA0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D0BF20>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D14140>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D14380>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D14500>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D14680>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D14800>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D14980>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D14BC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D14E00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D15100>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D15400>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D156A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D158E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D15D00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D160C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D162A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D165A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D167E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D16A20>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D16C60>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D16E40>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D17020>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D174A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D17740>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D179E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D17C20>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D24260>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D244A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D247A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D24980>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D24C20>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D24E60>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D25040>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D25190>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D252E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D25430>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D25580>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D256D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D25820>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D25D90>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D25FD0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D26270>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D26510>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D267B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D26A50>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D26D50>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D26E40>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D26F90>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D270E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D27230>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D27380>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D274D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D27620>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D27770>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D278C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D27A10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D27B60>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D27CE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D27E90>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D300E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D30110>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D30470>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D305C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D308C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D30C20>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D30D40>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D30FB0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D310D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D311F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D313A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D31550>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D31610>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D31820>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D31970>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D31A60>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D31C10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D320C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D32270>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D32570>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D32750>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D32A20>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D32C00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D32F00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D331A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D332C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D33530>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D336E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D33B00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D33CB0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D33EC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D33FE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3C140>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3C350>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3C4D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3C680>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3C890>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3C9E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3CB30>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3CC80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3CDD0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3D040>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3D490>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3D640>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3D940>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3DB80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3DDC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3E0C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3E360>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3E4E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3E660>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3E900>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3EAB0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3EC00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3ED50>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3EFC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3F110>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3F260>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3F3B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3F500>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3F650>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3F8C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3FA10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3FC50>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3FD40>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D3FF80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4C200>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4C4A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4C620>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4C7A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4CB00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4CDA0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4CFE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4D280>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4D4C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4D640>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4D7C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4D940>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4DAC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4DD00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4DEE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4E120>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4E360>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4E540>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4E780>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4E9C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4EBA0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4EDE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4F020>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4F200>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4F440>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4F920>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4FB00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D4FEC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D580B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D58200>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D58470>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D585C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D58770>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D588C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D58A10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D58B60>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D58CB0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D58E00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D58F50>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D590A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D591F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D594C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D59610>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D59760>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D598B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D59A00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D59B50>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D59CA0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D59DF0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D59F40>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D5A090>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D5A1E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D5A330>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D5A480>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D5A5D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D5A720>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D5A870>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D5A9C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D5AB10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D5AC60>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D5ADB0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D5AF00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D5B0E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D5B260>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D5B3E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D5B560>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D5B6E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D5BB00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D5BEC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D68080>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D68260>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D686E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D68890>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D68E90>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D68FE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D69190>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D692E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D69370>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D694C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D69610>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D69760>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D698B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D69A00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D69B50>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D69CA0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D69DF0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D6A840>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D6AB10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D6AD50>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D6B050>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D6B290>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D6B380>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D6B5C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D6B6B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D6B8F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D6BB30>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D6BCB0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D6BE60>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D78320>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D784A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D78680>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D787D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D78A40>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D78BC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D78D10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D78E60>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D78FB0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D79130>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D79310>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D79490>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D795E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D79850>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D799D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D79B20>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D79C70>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D79DC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D79F40>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D7A120>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D7A2A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D7A3F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D7A660>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D7A7E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D7A930>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D7AA80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D7ABD0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D7AE10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D7B2C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D7B6E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D7B980>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D7BB60>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D7BE00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D7BFE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D884A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D886E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D88920>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D88AD0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D890D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D89310>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D89550>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D899D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D89C10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D8A030>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D8A270>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D8A690>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D8A810>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D8A8A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D8AA20>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D8AAB0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D8ADB0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D8AF90>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D8B110>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D8B350>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D8B4D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D8B950>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D8BDD0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D8BF50>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D90140>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D901D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D90470>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D905C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D906E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D90980>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D909E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D90B90>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D90CE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D90F20>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D91370>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D91520>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D91580>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D91730>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D918E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D91B80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D91BE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D91D90>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D91F10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D92270>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D92420>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D92510>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D927B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D928D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D92B70>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D92AE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D92D80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D92F00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D93260>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D93410>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D93650>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D93890>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D93AD0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D93C80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D93D40>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D93EF0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5D93FB0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA4110>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA41A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA4290>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA4920>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA4A70>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA4BC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA5010>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA5490>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA56D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA5AF0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA5D30>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA6150>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA62D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA6360>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA64E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA6570>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA6870>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA6A50>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA6BD0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA6E10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA6F90>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA7410>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA7890>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA7A10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA7BF0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA7C80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA7E00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5DA7FE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F500B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F50290>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F50320>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F50500>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F50590>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F50770>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F50800>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F509E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F50A70>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F50DD0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F50FB0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F51100>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F51250>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F513A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F514F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F51640>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F51B80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F51DC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F52000>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F52450>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F52750>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F52960>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F52B70>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F52CC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F52E10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F52F60>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F530B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F53200>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F53350>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F534A0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F535F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F53740>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F53890>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F53AD0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F53BC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F53DA0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F60080>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F608F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F60B30>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F60CB0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F60F50>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F610D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F61310>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F61910>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F61BE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F61D30>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F61E80>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F61FD0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F62330>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F62480>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F625D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F62720>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F62870>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F62B40>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F62E70>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F62FC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F63110>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F63290>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F63890>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F63AD0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F63D10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F63F50>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F68290>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F68470>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F68650>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F68890>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F69010>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F693D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F69670>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F697F0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F69970>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F69AF0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F69F10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F6A090>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F6A690>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F6A870>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F6AA50>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F6AC30>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F6AE10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F6AF90>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F6B170>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F6B350>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F6B4D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F6B650>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F6B7D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F6B9B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F6BB00>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F6BEC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F78200>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F783E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F786E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F78860>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F789E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F78B60>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F78CE0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F78E30>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F79130>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F79310>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F79610>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F79850>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F79A30>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F79BB0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F7A2D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F7A450>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F7A5D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F7A7B0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F7A990>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F7AB10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F7AC90>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F7AE10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F7AF90>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F7B290>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F7B530>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F7B710>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F7B890>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F7BA10>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F7BB90>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F7BD40>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F7BFB0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F88170>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F88320>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F88530>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F88680>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F88D70>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F88EC0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F89010>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F89160>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F89340>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F893D0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F89550>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x000001D6B5F895E0>"], "global_type_table": {"_9176": "DataType.HALF", "_9179": "DataType.HALF3", "_9199": "DataType.HALF", "_8921": "DataType.FLOAT3", "_8925": "DataType.FLOAT3", "_8929": "DataType.HALF", "_8939": "DataType.HALF4", "_8941": "DataType.HALF3", "_8949": "DataType.HALF3", "_8973": "DataType.FLOAT4", "_8974": "DataType.HALF4", "_8982": "DataType.FLOAT4", "_8983": "DataType.HALF4", "_8991": "DataType.FLOAT", "_8994": "DataType.HALF", "_8996": "DataType.HALF", "_9014": "DataType.FLOAT", "_9019": "DataType.HALF", "_9248": "DataType.HALF4", "_9251": "DataType.HALF", "_9254": "DataType.HALF3", "_9255": "DataType.HALF3", "_9257": "DataType.HALF", "_9263": "DataType.HALF", "_9270": "DataType.HALF", "_9279": "DataType.FLOAT3", "_9286": "DataType.FLOAT3", "_9331": "DataType.FLOAT3", "_9334": "DataType.HALF", "_9064": "DataType.HALF3", "_9074": "DataType.HALF4", "_9079": "DataType.HALF", "_9096": "DataType.HALF", "_9100": "DataType.FLOAT", "_9109": "DataType.FLOAT3", "_9130": "DataType.HALF4", "_9145": "DataType.HALF3", "_18217": "DataType.UNKNOWN", "_9698": "DataType.FLOAT3", "_9408": "DataType.FLOAT", "_9721": "DataType.FLOAT", "_9734": "DataType.FLOAT", "_9761": "DataType.HALF3", "_9772": "DataType.HALF", "_9429": "DataType.FLOAT", "_9443": "DataType.FLOAT4", "_9460": "DataType.FLOAT", "_9462": "DataType.FLOAT", "_9499": "DataType.FLOAT", "_9505": "DataType.FLOAT", "_9510": "DataType.FLOAT", "_9519": "DataType.FLOAT", "_9535": "DataType.HALF", "_9556": "DataType.FLOAT", "_9557": "DataType.FLOAT", "_9585": "DataType.HALF", "_9588": "DataType.HALF", "_9603": "DataType.FLOAT", "_18272": "DataType.UNKNOWN", "_18268": "DataType.UNKNOWN", "_18236": "DataType.UNKNOWN", "_18234": "DataType.UNKNOWN", "_18225": "DataType.UNKNOWN", "_8295": "DataType.HALF", "_8298": "DataType.FLOAT", "_8303": "DataType.HALF3", "_8315": "DataType.HALF3", "_9787": "DataType.HALF", "_9806": "DataType.FLOAT", "_9813": "DataType.FLOAT", "_9831": "DataType.FLOAT", "_9846": "DataType.FLOAT", "_18255": "DataType.UNKNOWN", "_18230": "DataType.UNKNOWN", "_9868": "DataType.FLOAT", "_8346": "DataType.HALF", "_9926": "DataType.FLOAT4", "_17899": "DataType.FLOAT4", "_9942": "DataType.FLOAT4", "_9945": "DataType.FLOAT4", "_17902": "DataType.FLOAT4", "_9971": "DataType.FLOAT4", "_18237": "DataType.UNKNOWN", "_10033": "DataType.FLOAT3", "_10040": "DataType.FLOAT3", "_10077": "DataType.FLOAT3", "_21135": "DataType.FLOAT", "_21138": "DataType.FLOAT3", "_10113": "DataType.FLOAT", "_10120": "DataType.FLOAT2", "_10167": "DataType.FLOAT2", "_10169": "DataType.FLOAT2", "_10171": "DataType.FLOAT2", "_10177": "DataType.FLOAT2", "_10181": "DataType.FLOAT2", "_10184": "DataType.FLOAT2", "_10187": "DataType.FLOAT2", "_10205": "DataType.FLOAT", "_10208": "DataType.FLOAT3", "_10231": "DataType.FLOAT3", "_10254": "DataType.FLOAT3", "_10276": "DataType.FLOAT3", "_10282": "DataType.FLOAT", "_10289": "DataType.FLOAT", "_10300": "DataType.FLOAT", "_9997": "DataType.FLOAT3", "_10004": "DataType.FLOAT", "_17928": "DataType.FLOAT3", "_10378": "DataType.FLOAT2", "_10380": "DataType.FLOAT2", "_10382": "DataType.FLOAT2", "_10388": "DataType.FLOAT2", "_10392": "DataType.FLOAT2", "_10395": "DataType.FLOAT2", "_10398": "DataType.FLOAT2", "_10416": "DataType.FLOAT", "_10419": "DataType.FLOAT3", "_10442": "DataType.FLOAT3", "_10465": "DataType.FLOAT3", "_10487": "DataType.FLOAT3", "_10493": "DataType.FLOAT", "_10500": "DataType.FLOAT", "_10511": "DataType.FLOAT", "_8351": "DataType.HALF", "_8370": "DataType.FLOAT3", "_8373": "DataType.FLOAT3", "_8378": "DataType.FLOAT", "_10557": "DataType.HALF3", "_10569": "DataType.HALF3", "_10588": "DataType.FLOAT3", "_8393": "DataType.HALF3", "_8397": "DataType.UINT", "_8401": "DataType.BOOL", "_8435": "DataType.FLOAT3", "_10686": "DataType.FLOAT3", "_10762": "DataType.FLOAT3", "_10789": "DataType.FLOAT3", "_10797": "DataType.FLOAT", "_10799": "DataType.FLOAT", "_21194": "DataType.FLOAT3", "_21235": "DataType.UNKNOWN", "_21078": "DataType.FLOAT", "_21079": "DataType.FLOAT", "_21198": "DataType.FLOAT3", "_21201": "DataType.FLOAT3", "_21236": "DataType.UNKNOWN", "_21100": "DataType.FLOAT", "_21101": "DataType.FLOAT", "_21205": "DataType.FLOAT3", "_21208": "DataType.FLOAT3", "_21237": "DataType.UNKNOWN", "_10822": "DataType.FLOAT3", "_10704": "DataType.UINT", "_10887": "DataType.FLOAT", "_10900": "DataType.FLOAT", "_10994": "DataType.FLOAT2", "_11001": "DataType.FLOAT", "_11005": "DataType.FLOAT", "_11011": "DataType.FLOAT", "_11015": "DataType.FLOAT", "_11020": "DataType.FLOAT", "_11038": "DataType.FLOAT", "_11059": "DataType.FLOAT", "_11064": "DataType.FLOAT", "_11067": "DataType.UINT", "_11070": "DataType.UINT", "_21301": "DataType.UINT", "_11097": "DataType.FLOAT2", "_11100": "DataType.FLOAT", "_11102": "DataType.FLOAT3", "_11113": "DataType.FLOAT3", "_11118": "DataType.HALF3", "_11135": "DataType.FLOAT2", "_11138": "DataType.FLOAT", "_11140": "DataType.FLOAT3", "_11151": "DataType.FLOAT3", "_11156": "DataType.HALF3", "_11163": "DataType.HALF4", "_18297": "DataType.UNKNOWN", "_11233": "DataType.FLOAT2", "_11240": "DataType.FLOAT", "_11244": "DataType.FLOAT", "_11250": "DataType.FLOAT", "_11254": "DataType.FLOAT", "_11259": "DataType.FLOAT", "_11277": "DataType.FLOAT", "_11298": "DataType.FLOAT", "_11303": "DataType.FLOAT", "_11306": "DataType.UINT", "_11309": "DataType.UINT", "_21300": "DataType.UINT", "_11340": "DataType.FLOAT3", "_11365": "DataType.FLOAT3", "_11404": "DataType.FLOAT3", "_11407": "DataType.FLOAT3", "_11410": "DataType.FLOAT3", "_18303": "DataType.UNKNOWN", "_18305": "DataType.UNKNOWN", "_18304": "DataType.UNKNOWN", "_11467": "DataType.FLOAT", "_11470": "DataType.FLOAT3", "_11479": "DataType.FLOAT", "_18306": "DataType.UNKNOWN", "_18330": "DataType.UNKNOWN", "_18329": "DataType.UNKNOWN", "_11517": "DataType.FLOAT3", "_11600": "DataType.FLOAT3", "_11604": "DataType.FLOAT3", "_11611": "DataType.FLOAT3", "_11622": "DataType.FLOAT3", "_11536": "DataType.HALF", "_11560": "DataType.FLOAT", "_11629": "DataType.FLOAT", "_11919": "DataType.FLOAT", "_11925": "DataType.FLOAT", "_11940": "DataType.FLOAT3", "_11945": "DataType.FLOAT", "_11951": "DataType.FLOAT", "_11736": "DataType.FLOAT", "_11959": "DataType.HALF4", "_11967": "DataType.HALF4", "_11970": "DataType.HALF4", "_11973": "DataType.HALF4", "_11975": "DataType.HALF4", "_11982": "DataType.HALF4", "_11756": "DataType.FLOAT", "_11763": "DataType.FLOAT", "_11764": "DataType.HALF", "_11768": "DataType.HALF", "_11772": "DataType.HALF", "_11777": "DataType.HALF", "_11790": "DataType.HALF", "_11812": "DataType.HALF3", "_11815": "DataType.HALF", "_11827": "DataType.HALF", "_11858": "DataType.FLOAT", "_11862": "DataType.FLOAT", "_12049": "DataType.FLOAT", "_12062": "DataType.FLOAT", "_12080": "DataType.FLOAT", "_12108": "DataType.FLOAT", "_12025": "DataType.FLOAT3", "_12120": "DataType.FLOAT3", "_8521": "DataType.FLOAT3", "_12171": "DataType.UINT", "_19825": "DataType.UNKNOWN", "_19755": "DataType.UNKNOWN", "_19720": "DataType.UNKNOWN", "_19685": "DataType.UNKNOWN", "_18431": "DataType.UNKNOWN", "_18429": "DataType.UNKNOWN", "_12181": "DataType.UINT", "_12188": "DataType.INT", "_12195": "DataType.INT", "_12202": "DataType.INT", "_12209": "DataType.INT", "_12298": "DataType.UINT", "_20953": "DataType.UNKNOWN", "_20938": "DataType.UNKNOWN", "_20923": "DataType.UNKNOWN", "_20908": "DataType.UNKNOWN", "_20893": "DataType.UNKNOWN", "_20863": "DataType.UNKNOWN", "_20848": "DataType.UNKNOWN", "_20833": "DataType.UNKNOWN", "_19965": "DataType.UNKNOWN", "_19923": "DataType.UNKNOWN", "_12309": "DataType.UINT", "_12360": "DataType.FLOAT3", "_12378": "DataType.FLOAT3", "_12381": "DataType.FLOAT", "_12392": "DataType.FLOAT", "_12395": "DataType.FLOAT", "_12398": "DataType.FLOAT", "_12404": "DataType.FLOAT", "_19350": "DataType.UNKNOWN", "_19821": "DataType.UNKNOWN", "_19786": "DataType.UNKNOWN", "_19751": "DataType.UNKNOWN", "_19716": "DataType.UNKNOWN", "_19681": "DataType.UNKNOWN", "_19609": "DataType.UNKNOWN", "_19573": "DataType.UNKNOWN", "_19481": "DataType.UNKNOWN", "_12741": "DataType.UINT", "_12858": "DataType.FLOAT", "_12599": "DataType.FLOAT3", "_12602": "DataType.FLOAT", "_12606": "DataType.FLOAT3", "_12613": "DataType.FLOAT", "_12620": "DataType.FLOAT", "_12631": "DataType.FLOAT", "_12641": "DataType.FLOAT", "_19211": "DataType.UNKNOWN", "_19213": "DataType.UNKNOWN", "_12677": "DataType.FLOAT", "_19822": "DataType.UNKNOWN", "_19787": "DataType.UNKNOWN", "_19752": "DataType.UNKNOWN", "_19717": "DataType.UNKNOWN", "_19682": "DataType.UNKNOWN", "_19610": "DataType.UNKNOWN", "_19574": "DataType.UNKNOWN", "_19482": "DataType.UNKNOWN", "_13098": "DataType.UINT", "_12933": "DataType.FLOAT3", "_12936": "DataType.FLOAT", "_12942": "DataType.FLOAT3", "_12950": "DataType.FLOAT", "_12956": "DataType.FLOAT", "_12972": "DataType.FLOAT", "_13202": "DataType.FLOAT", "_19070": "DataType.UNKNOWN", "_19823": "DataType.UNKNOWN", "_19788": "DataType.UNKNOWN", "_19753": "DataType.UNKNOWN", "_19718": "DataType.UNKNOWN", "_19683": "DataType.UNKNOWN", "_19611": "DataType.UNKNOWN", "_19575": "DataType.UNKNOWN", "_19483": "DataType.UNKNOWN", "_13270": "DataType.BOOL", "_13339": "DataType.FLOAT3", "_13342": "DataType.FLOAT", "_13348": "DataType.FLOAT", "_13356": "DataType.FLOAT", "_13433": "DataType.FLOAT3", "_13459": "DataType.FLOAT3X3", "_13466": "DataType.FLOAT3", "_13467": "DataType.FLOAT3", "_13472": "DataType.FLOAT3", "_13484": "DataType.FLOAT3", "_13657": "DataType.FLOAT3", "_13660": "DataType.FLOAT3", "_13663": "DataType.FLOAT3", "_13666": "DataType.FLOAT3", "_13712": "DataType.FLOAT", "_13714": "DataType.FLOAT", "_13728": "DataType.FLOAT", "_13753": "DataType.FLOAT", "_13755": "DataType.FLOAT", "_13769": "DataType.FLOAT", "_13794": "DataType.FLOAT", "_13796": "DataType.FLOAT", "_13810": "DataType.FLOAT", "_13835": "DataType.FLOAT", "_13837": "DataType.FLOAT", "_13851": "DataType.FLOAT", "_13700": "DataType.FLOAT3", "_13531": "DataType.FLOAT", "_13539": "DataType.FLOAT", "_19824": "DataType.UNKNOWN", "_19789": "DataType.UNKNOWN", "_19754": "DataType.UNKNOWN", "_19684": "DataType.UNKNOWN", "_19484": "DataType.UNKNOWN", "_14176": "DataType.FLOAT", "_14197": "DataType.FLOAT3", "_14202": "DataType.FLOAT", "_14208": "DataType.FLOAT", "_13993": "DataType.FLOAT", "_14013": "DataType.FLOAT", "_14020": "DataType.FLOAT", "_14029": "DataType.HALF", "_14069": "DataType.HALF3", "_14084": "DataType.HALF", "_14319": "DataType.FLOAT", "_19883": "DataType.UNKNOWN", "_8560": "DataType.HALF3", "_8565": "DataType.FLOAT3", "_8573": "DataType.BOOL", "_8590": "DataType.BOOL", "_14451": "DataType.UINT", "_14469": "DataType.UINT", "_14482": "DataType.FLOAT", "_14684": "DataType.FLOAT3", "_14692": "DataType.FLOAT", "_14694": "DataType.FLOAT", "_21215": "DataType.FLOAT3", "_21268": "DataType.UNKNOWN", "_21034": "DataType.FLOAT", "_21035": "DataType.FLOAT", "_21219": "DataType.FLOAT3", "_21222": "DataType.FLOAT3", "_21269": "DataType.UNKNOWN", "_21056": "DataType.FLOAT", "_21057": "DataType.FLOAT", "_21226": "DataType.FLOAT3", "_21229": "DataType.FLOAT3", "_21270": "DataType.UNKNOWN", "_14717": "DataType.FLOAT3", "_14534": "DataType.FLOAT3", "_14747": "DataType.FLOAT3", "_14556": "DataType.HALF3", "_14788": "DataType.FLOAT", "_14797": "DataType.FLOAT", "_14827": "DataType.FLOAT", "_14831": "DataType.FLOAT", "_14833": "DataType.FLOAT", "_14850": "DataType.FLOAT", "_14854": "DataType.FLOAT", "_14856": "DataType.FLOAT", "_14956": "DataType.FLOAT2", "_14963": "DataType.FLOAT", "_14967": "DataType.FLOAT", "_14973": "DataType.FLOAT", "_14977": "DataType.FLOAT", "_14982": "DataType.FLOAT", "_15000": "DataType.FLOAT", "_15021": "DataType.FLOAT", "_15026": "DataType.FLOAT", "_15029": "DataType.UINT", "_15032": "DataType.UINT", "_21309": "DataType.UINT", "_15059": "DataType.FLOAT2", "_15074": "DataType.FLOAT2", "_15078": "DataType.FLOAT", "_15080": "DataType.FLOAT3", "_15092": "DataType.FLOAT3", "_15097": "DataType.HALF3", "_15104": "DataType.FLOAT", "_15106": "DataType.FLOAT3", "_15118": "DataType.FLOAT3", "_15123": "DataType.HALF3", "_15132": "DataType.HALF4", "_15133": "DataType.HALF4", "_15140": "DataType.FLOAT3", "_15152": "DataType.FLOAT3", "_15157": "DataType.HALF3", "_15166": "DataType.FLOAT3", "_15178": "DataType.FLOAT3", "_15183": "DataType.HALF3", "_15193": "DataType.HALF4", "_15200": "DataType.FLOAT3", "_15212": "DataType.FLOAT3", "_15217": "DataType.HALF3", "_15226": "DataType.FLOAT3", "_15238": "DataType.FLOAT3", "_15243": "DataType.HALF3", "_15253": "DataType.HALF4", "_15255": "DataType.HALF", "_15258": "DataType.HALF", "_15262": "DataType.HALF", "_15266": "DataType.HALF", "_15343": "DataType.HALF3", "_15352": "DataType.HALF3", "_15361": "DataType.HALF3", "_18819": "DataType.UNKNOWN", "_18818": "DataType.UNKNOWN", "_18806": "DataType.UNKNOWN", "_15427": "DataType.FLOAT2", "_15434": "DataType.FLOAT", "_15438": "DataType.FLOAT", "_15444": "DataType.FLOAT", "_15448": "DataType.FLOAT", "_15453": "DataType.FLOAT", "_15471": "DataType.FLOAT", "_15492": "DataType.FLOAT", "_15497": "DataType.FLOAT", "_15500": "DataType.UINT", "_15503": "DataType.UINT", "_21308": "DataType.UINT", "_15530": "DataType.FLOAT2", "_15545": "DataType.FLOAT2", "_15549": "DataType.FLOAT", "_15551": "DataType.FLOAT3", "_15561": "DataType.FLOAT", "_15563": "DataType.FLOAT3", "_15575": "DataType.FLOAT3", "_15587": "DataType.FLOAT3", "_15599": "DataType.FLOAT3", "_15611": "DataType.FLOAT3", "_15622": "DataType.HALF3", "_15623": "DataType.HALF", "_15658": "DataType.FLOAT3", "_15661": "DataType.FLOAT3", "_15664": "DataType.FLOAT3", "_14921": "DataType.HALF3", "_18823": "DataType.UNKNOWN", "_18822": "DataType.UNKNOWN", "_18827": "DataType.UNKNOWN", "_18825": "DataType.UNKNOWN", "_18826": "DataType.UNKNOWN", "_18824": "DataType.UNKNOWN", "_14565": "DataType.HALF3", "_14569": "DataType.FLOAT3", "_14575": "DataType.FLOAT", "_14592": "DataType.FLOAT", "_14600": "DataType.FLOAT3", "_14604": "DataType.FLOAT", "_14612": "DataType.FLOAT", "_15697": "DataType.FLOAT", "_15710": "DataType.FLOAT", "_18831": "DataType.UNKNOWN", "_18830": "DataType.UNKNOWN", "_14641": "DataType.FLOAT", "_18859": "DataType.UNKNOWN", "_18832": "DataType.UNKNOWN", "_19028": "DataType.UNKNOWN", "_18989": "DataType.UNKNOWN", "_15792": "DataType.FLOAT", "_15839": "DataType.FLOAT", "_15805": "DataType.FLOAT", "_15854": "DataType.FLOAT", "_15858": "DataType.FLOAT", "_15861": "DataType.FLOAT", "_15813": "DataType.HALF3", "_15762": "DataType.FLOAT3", "_15920": "DataType.FLOAT3", "_15960": "DataType.FLOAT3", "_15970": "DataType.FLOAT3", "_15974": "DataType.HALF", "_15976": "DataType.FLOAT3", "_15979": "DataType.HALF", "_15986": "DataType.FLOAT", "_16031": "DataType.HALF", "_16112": "DataType.FLOAT", "_16159": "DataType.FLOAT", "_8657": "DataType.FLOAT", "_8696": "DataType.HALF", "_16335": "DataType.HALF", "_16336": "DataType.HALF", "_16342": "DataType.HALF", "_16351": "DataType.HALF", "_16356": "DataType.HALF4", "_16361": "DataType.HALF", "_16369": "DataType.HALF", "_16385": "DataType.HALF4", "_16387": "DataType.HALF", "_16279": "DataType.HALF3", "_16284": "DataType.FLOAT4", "_16306": "DataType.FLOAT4", "_16397": "DataType.HALF3", "_16509": "DataType.HALF", "_16528": "DataType.HALF", "_16536": "DataType.HALF", "_16552": "DataType.HALF4", "_16451": "DataType.FLOAT4", "_16473": "DataType.FLOAT4", "_16258": "DataType.HALF3", "_8776": "DataType.HALF3", "_16622": "DataType.FLOAT", "_16645": "DataType.FLOAT", "_16657": "DataType.FLOAT", "_16682": "DataType.FLOAT2", "_16688": "DataType.FLOAT3", "_16698": "DataType.FLOAT3", "_16715": "DataType.FLOAT3", "_16602": "DataType.FLOAT3", "_16756": "DataType.FLOAT", "_16759": "DataType.FLOAT", "_16820": "DataType.FLOAT", "_16778": "DataType.FLOAT", "_16785": "DataType.FLOAT3", "_16793": "DataType.HALF", "_16805": "DataType.FLOAT3", "_16862": "DataType.FLOAT3", "_8804": "DataType.FLOAT3", "_16911": "DataType.FLOAT", "_19029": "DataType.UNKNOWN", "_19032": "DataType.UNKNOWN", "_19031": "DataType.UNKNOWN", "_8808": "DataType.FLOAT4", "_8816": "DataType.FLOAT3"}, "overall_statistics": {"total_lines": 762, "total_nodes": 3704, "total_variables": 648, "total_intermediate_results": 1031, "total_type_conversions": 226, "total_precision_issues": 0, "type_distribution": {"unknown": 1649, "half": 260, "int": 24, "half3": 149, "float3": 391, "half4": 59, "float4": 42, "float": 839, "bool": 86, "float2": 157, "uint": 47, "float3x3": 1}, "precision_accuracy_score": 39.55183585313175}}}, "files": {}, "analysis_method": "precise_tree_based"}