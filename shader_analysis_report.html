
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Metal着色器Half/Float运算分析报告</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007acc;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4fc3f7;
        }
        
        .stat-label {
            color: #cccccc;
            margin-top: 5px;
        }
        
        .code-container {
            background: #1e1e1e;
            border: 1px solid #3c3c3c;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 30px;
        }
        
        .code-header {
            background: #2d2d30;
            padding: 15px;
            border-bottom: 1px solid #3c3c3c;
            font-weight: bold;
        }
        
        .code-content {
            max-height: 600px;
            overflow-y: auto;
        }
        
        .code-line {
            display: flex;
            padding: 2px 0;
            border-bottom: 1px solid #2d2d30;
        }
        
        .line-number {
            width: 60px;
            text-align: right;
            padding-right: 15px;
            color: #858585;
            background: #252526;
            border-right: 1px solid #3c3c3c;
            user-select: none;
        }
        
        .line-content {
            flex: 1;
            padding-left: 15px;
            white-space: pre;
            font-family: 'Consolas', monospace;
        }
        
        .highlight-mixed {
            background-color: rgba(255, 87, 87, 0.2);
        }
        
        .highlight-conversion {
            background-color: rgba(255, 193, 7, 0.2);
        }
        
        .highlight-texture {
            background-color: rgba(76, 175, 80, 0.2);
        }
        
        .highlight-high-impact {
            background-color: rgba(156, 39, 176, 0.2);
        }
        
        .operation-info {
            font-size: 0.8em;
            color: #ffc107;
            margin-left: 10px;
        }
        
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background: #2d2d30;
            border-radius: 8px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }
        
        .chart-container {
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .bar-chart {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .bar-item {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .bar-label {
            width: 150px;
            text-align: right;
        }
        
        .bar {
            height: 25px;
            background: linear-gradient(90deg, #4fc3f7, #29b6f6);
            border-radius: 4px;
            display: flex;
            align-items: center;
            padding: 0 10px;
            color: white;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Metal着色器Half/Float运算分析报告</h1>
        <p>深度分析着色器中的数据类型转换和运算模式</p>
    </div>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">1873</div>
            <div class="stat-label">总运算操作</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">497</div>
            <div class="stat-label">精度转换</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">60</div>
            <div class="stat-label">混合精度运算</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">91</div>
            <div class="stat-label">高影响操作</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">31</div>
            <div class="stat-label">纹理采样</div>
        </div>
    </div>
    
    <div class="chart-container">
        <h3>📊 运算类型分布</h3>
        <div class="bar-chart">

            <div class="bar-item">
                <div class="bar-label">arithmetic</div>
                <div class="bar" style="width: 263.1073144687667px;">
                    616 (32.9%)
                </div>
            </div>

            <div class="bar-item">
                <div class="bar-label">assignment</div>
                <div class="bar" style="width: 193.05926321409504px;">
                    452 (24.1%)
                </div>
            </div>

            <div class="bar-item">
                <div class="bar-label">mixed_precision</div>
                <div class="bar" style="width: 25.62733582487987px;">
                    60 (3.2%)
                </div>
            </div>

            <div class="bar-item">
                <div class="bar-label">type_conversion</div>
                <div class="bar" style="width: 186.65242925787507px;">
                    437 (23.3%)
                </div>
            </div>

            <div class="bar-item">
                <div class="bar-label">function_call</div>
                <div class="bar" style="width: 118.31286705819541px;">
                    277 (14.8%)
                </div>
            </div>

            <div class="bar-item">
                <div class="bar-label">texture_sample</div>
                <div class="bar" style="width: 13.240790176187932px;">
                    31 (1.7%)
                </div>
            </div>

        </div>
    </div>
    
    <div class="legend">
        <div class="legend-item">
            <div class="legend-color" style="background-color: rgba(255, 87, 87, 0.2);"></div>
            <span>混合精度运算</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: rgba(255, 193, 7, 0.2);"></div>
            <span>类型转换</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: rgba(76, 175, 80, 0.2);"></div>
            <span>纹理采样</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: rgba(156, 39, 176, 0.2);"></div>
            <span>高性能影响</span>
        </div>
    </div>
    
    <div class="code-container">
        <div class="code-header">
            🔍 着色器代码分析 (显示前200行)
        </div>
        <div class="code-content">

            <div class="code-line ">
                <div class="line-number">1</div>
                <div class="line-content">#include <metal_stdlib><span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">2</div>
                <div class="line-content">#include <simd/simd.h><span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">3</div>
                <div class="line-content"><span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">4</div>
                <div class="line-content">using namespace metal;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">5</div>
                <div class="line-content"><span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">6</div>
                <div class="line-content">struct _Block1T<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">7</div>
                <div class="line-content">{<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">8</div>
                <div class="line-content">    float4 AerialPerspectiveExt;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">9</div>
                <div class="line-content">    float4 AerialPerspectiveMie;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">10</div>
                <div class="line-content">    float4 AerialPerspectiveRay;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">11</div>
                <div class="line-content">    float4 AmbientColor;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">12</div>
                <div class="line-content">    float4 CSMCacheIndexs;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">13</div>
                <div class="line-content">    float4 CSMShadowBiases;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">14</div>
                <div class="line-content">    float4 CameraInfo;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">15</div>
                <div class="line-content">    float4 CameraPos;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">16</div>
                <div class="line-content">    float4 DiyLightingInfo;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">17</div>
                <div class="line-content">    float4 EnvInfo;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">18</div>
                <div class="line-content">    float4 FogColor;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">19</div>
                <div class="line-content">    float4 FogInfo;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">20</div>
                <div class="line-content">    float4 GIInfo;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">21</div>
                <div class="line-content">    float4 HexRenderOptionData[4];<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">22</div>
                <div class="line-content">    float4 LightDataBuffer[65];<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">23</div>
                <div class="line-content">    float3x4 Local;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">24</div>
                <div class="line-content">    float4 OriginSunDir;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">25</div>
                <div class="line-content">    float4 PlayerPos;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">26</div>
                <div class="line-content">    float4 ReflectionProbeBBMin;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">27</div>
                <div class="line-content">    float4 SHAOParam;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">28</div>
                <div class="line-content">    float4 SHGIParam;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">29</div>
                <div class="line-content">    float4 SHGIParam2;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">30</div>
                <div class="line-content">    float4 ScreenInfo;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">31</div>
                <div class="line-content">    float4 ScreenMotionGray;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">32</div>
                <div class="line-content">    float4x4 ShadowViewProjTexs0;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">33</div>
                <div class="line-content">    float4x4 ShadowViewProjTexs1;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">34</div>
                <div class="line-content">    float4x4 ShadowViewProjTexs2;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">35</div>
                <div class="line-content">    float4 SunColor;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">36</div>
                <div class="line-content">    float4 SunDirection;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">37</div>
                <div class="line-content">    float4 SunFogColor;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">38</div>
                <div class="line-content">    float4 TimeOfDayInfos;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">39</div>
                <div class="line-content">    float3x4 World;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">40</div>
                <div class="line-content">    float4 WorldProbeInfo;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">41</div>
                <div class="line-content">    float4 cCIFadeTime;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">42</div>
                <div class="line-content">    float4 cCISnowData;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">43</div>
                <div class="line-content">    float4 cCISwitchData;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">44</div>
                <div class="line-content">    float4 cLocalVirtualLitColor;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">45</div>
                <div class="line-content">    float4 cLocalVirtualLitCustom;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">46</div>
                <div class="line-content">    float4 cLocalVirtualLitPos;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">47</div>
                <div class="line-content">    float4 cSHCoefficients[7];<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">48</div>
                <div class="line-content">    float4 cShadowBias;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">49</div>
                <div class="line-content">    float4 cVirtualLitColor;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">50</div>
                <div class="line-content">    float4 cVirtualLitParam;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">51</div>
                <div class="line-content">    float4 cVisibilitySH[2];<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">52</div>
                <div class="line-content">    packed_float3 cBaseColor;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">53</div>
                <div class="line-content">    float eIsPlayerOverride;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">54</div>
                <div class="line-content">    packed_float3 cCIMudBuff;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">55</div>
                <div class="line-content">    float eFresnelPower;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">56</div>
                <div class="line-content">    packed_float3 cEmissionColor;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">57</div>
                <div class="line-content">    float eFresnelMinIntensity;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">58</div>
                <div class="line-content">    packed_float3 eFresnelColor;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">59</div>
                <div class="line-content">    float eFresnelIntensity;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">60</div>
                <div class="line-content">    float cAOoffset;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">61</div>
                <div class="line-content">    float cBiasFarAwayShadow;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">62</div>
                <div class="line-content">    float cEmissionScale;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">63</div>
                <div class="line-content">    float cFurFadeInt;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">64</div>
                <div class="line-content">    float cMicroShadow;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">65</div>
                <div class="line-content">    float cNoise1Scale;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">66</div>
                <div class="line-content">    float cNoise2Bias;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">67</div>
                <div class="line-content">    float cNoise2Scale;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">68</div>
                <div class="line-content">    float cNormalMapStrength;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">69</div>
                <div class="line-content">    float cSaturation;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">70</div>
                <div class="line-content">    float eDynamicFresnelIntensity;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">71</div>
                <div class="line-content">    float eFresnelAlphaAdd;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">72</div>
                <div class="line-content">};<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">73</div>
                <div class="line-content"><span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">74</div>
                <div class="line-content">constant half3 _18526 = {};<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">75</div>
                <div class="line-content">constant float3 _19185 = {};<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">76</div>
                <div class="line-content">constant half _19493 = {};<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">77</div>
                <div class="line-content">constant float _19585 = {};<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">78</div>
                <div class="line-content">constant int _19621 = {};<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">79</div>
                <div class="line-content">constant float3 _21234 = {};<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">80</div>
                <div class="line-content">constant float3 _21295 = {};<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">81</div>
                <div class="line-content">constant half4 _21296 = {};<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">82</div>
                <div class="line-content">constant half3 _21297 = {};<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">83</div>
                <div class="line-content"><span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">84</div>
                <div class="line-content">struct main0_out<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">85</div>
                <div class="line-content">{<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">86</div>
                <div class="line-content">    float4 _Ret [[color(0)]];<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">87</div>
                <div class="line-content">};<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">88</div>
                <div class="line-content"><span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">89</div>
                <div class="line-content">struct main0_in<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">90</div>
                <div class="line-content">{<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">91</div>
                <div class="line-content">    float4 IN_TexCoord [[user(locn0)]];<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">92</div>
                <div class="line-content">    float4 IN_WorldPosition [[user(locn1)]];<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">93</div>
                <div class="line-content">    half4 IN_WorldNormal [[user(locn2)]];<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">94</div>
                <div class="line-content">    half4 IN_WorldTangent [[user(locn3)]];<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">95</div>
                <div class="line-content">    half4 IN_WorldBinormal [[user(locn4)]];<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">96</div>
                <div class="line-content">    half4 IN_TintColor [[user(locn5)]];<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">97</div>
                <div class="line-content">    float IN_LinearZ [[user(locn6)]];<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">98</div>
                <div class="line-content">    half3 IN_LocalPosition [[user(locn7)]];<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">99</div>
                <div class="line-content">    half4 IN_StaticWorldNormal [[user(locn8)]];<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">100</div>
                <div class="line-content">};<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">101</div>
                <div class="line-content"><span class="operation-info"></span></div>
            </div>

            <div class="code-line highlight-mixed">
                <div class="line-number">102</div>
                <div class="line-content">fragment main0_out main0(main0_in in [[stage_in]], constant _Block1T& _Block1 [[buffer(0)]], texture2d<half> sBaseSampler [[texture(0)]], texture2d<half> sMixSampler [[texture(1)]], texture2d<half> sNormalSampler [[texture(2)]], texture2d<half> sEmissionMapSampler [[texture(3)]], texture2d<float> sNoiseSampler [[texture(4)]], texture2d<float> sCharInteractionSampler [[texture(5)]], texture2d_array<float> sSHAORGBVTSampler [[texture(6)]], texture2d_array<float> sSHAOAlphaVTSampler [[texture(7)]], depth2d_array<float> sShadowMapArraySampler [[texture(8)]], sampler sBaseSamplerSmplr [[sampler(0)]], sampler sMixSamplerSmplr [[sampler(1)]], sampler sNormalSamplerSmplr [[sampler(2)]], sampler sEmissionMapSamplerSmplr [[sampler(3)]], sampler sNoiseSamplerSmplr [[sampler(4)]], sampler sCharInteractionSamplerSmplr [[sampler(5)]], sampler sSHAORGBVTSamplerSmplr [[sampler(6)]], sampler sSHAOAlphaVTSamplerSmplr [[sampler(7)]], bool gl_FrontFacing [[front_facing]])<span class="operation-info"> [混合精度]</span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">103</div>
                <div class="line-content">{<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">104</div>
                <div class="line-content">    constexpr sampler sShadowMapArraySamplerSmplr(filter::linear, mip_filter::linear, compare_func::greater);<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">105</div>
                <div class="line-content">    main0_out out = {};<span class="operation-info"></span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">106</div>
                <div class="line-content">    half _9176 = half(0);<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">107</div>
                <div class="line-content">    half3 _9179 = half3(_9176);<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">108</div>
                <div class="line-content">    half _9199 = half(1);<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">109</div>
                <div class="line-content">    float3 _8921 = fast::normalize(_Block1.CameraPos.xyz - in.IN_WorldPosition.xyz);<span class="operation-info"></span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">110</div>
                <div class="line-content">    float3 _8925 = float3(in.IN_WorldNormal.xyz);<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">111</div>
                <div class="line-content">    half _8929 = half(fast::clamp(dot(_8925, _8921), 0.0, 1.0));<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line highlight-texture">
                <div class="line-number">112</div>
                <div class="line-content">    half4 _8939 = sBaseSampler.sample(sBaseSamplerSmplr, in.IN_TexCoord.xy);<span class="operation-info"> [纹理采样]</span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">113</div>
                <div class="line-content">    half3 _8941 = _8939.xyz;<span class="operation-info"></span></div>
            </div>

            <div class="code-line highlight-mixed">
                <div class="line-number">114</div>
                <div class="line-content">    half3 _8949 = half3(float3(_8941 * _8941) * float3(_Block1.cBaseColor));<span class="operation-info"> [混合精度]</span></div>
            </div>

            <div class="code-line highlight-texture">
                <div class="line-number">115</div>
                <div class="line-content">    float4 _8973 = sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy * _Block1.cNoise1Scale));<span class="operation-info"> [纹理采样]</span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">116</div>
                <div class="line-content">    half4 _8974 = half4(_8973);<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line highlight-texture">
                <div class="line-number">117</div>
                <div class="line-content">    float4 _8982 = sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy * _Block1.cNoise2Scale));<span class="operation-info"> [纹理采样]</span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">118</div>
                <div class="line-content">    half4 _8983 = half4(_8982);<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line highlight-mixed">
                <div class="line-number">119</div>
                <div class="line-content">    float _8991 = fast::clamp(powr(float(max(in.IN_TintColor.w, half(9.9956989288330078125e-05))), _Block1.cNoise2Bias), 0.0, 1.0);<span class="operation-info"> [混合精度]</span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">120</div>
                <div class="line-content">    half _8994 = _8974.x;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">121</div>
                <div class="line-content">    half _8996 = _8983.x;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">122</div>
                <div class="line-content">    float _9014 = 1.0 - _8991;<span class="operation-info"></span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">123</div>
                <div class="line-content">    half _9019 = half(1.0);<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line highlight-mixed">
                <div class="line-number">124</div>
                <div class="line-content">    if ((float((_8994 * _8996) - half(mix(0.0, 0.5, powr(float(max(_9199 - _8929, half(9.9956989288330078125e-05))), 3.0)))) - 0.100000001490116119384765625) < 0.0)<span class="operation-info"> [混合精度]</span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">125</div>
                <div class="line-content">    {<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">126</div>
                <div class="line-content">        discard_fragment();<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">127</div>
                <div class="line-content">    }<span class="operation-info"></span></div>
            </div>

            <div class="code-line highlight-texture">
                <div class="line-number">128</div>
                <div class="line-content">    half4 _9248 = sNormalSampler.sample(sNormalSamplerSmplr, in.IN_TexCoord.xy);<span class="operation-info"> [纹理采样]</span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">129</div>
                <div class="line-content">    half _9251 = half(2);<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">130</div>
                <div class="line-content">    half3 _9254 = half3(_9199);<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">131</div>
                <div class="line-content">    half3 _9255 = (_9248.xyz * _9251) - _9254;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">132</div>
                <div class="line-content">    half _9257 = _9255.x;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">133</div>
                <div class="line-content">    half _9263 = _9255.y;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">134</div>
                <div class="line-content">    half _9270 = _9255.z;<span class="operation-info"></span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">135</div>
                <div class="line-content">    float3 _9279 = float3(((in.IN_WorldTangent.xyz * _9257) + (in.IN_WorldBinormal.xyz * _9263)) + (in.IN_WorldNormal.xyz * _9270));<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">136</div>
                <div class="line-content">    float3 _9286 = float3(in.IN_StaticWorldNormal.xyz);<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">137</div>
                <div class="line-content">    float3 _9331 = (((float4(float3(in.IN_WorldTangent.xyz), 0.0) * _Block1.Local) * float(_9257)) + ((float4(float3(in.IN_WorldBinormal.xyz), 0.0) * _Block1.Local) * float(_9263))) + (_9286 * float(_9270));<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">138</div>
                <div class="line-content">    half _9334 = half((_9331 * rsqrt(fast::max(dot(_9331, _9331), 9.9999997473787516355514526367188e-06))).y);<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">139</div>
                <div class="line-content">    half3 _9064 = mix(in.IN_WorldNormal.xyz, half3(_9279 * rsqrt(fast::max(dot(_9279, _9279), 9.9999997473787516355514526367188e-06))), half3(half(_Block1.cNormalMapStrength)));<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line highlight-texture">
                <div class="line-number">140</div>
                <div class="line-content">    half4 _9074 = sMixSampler.sample(sMixSamplerSmplr, in.IN_TexCoord.xy);<span class="operation-info"> [纹理采样]</span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">141</div>
                <div class="line-content">    half _9079 = _9074.y;<span class="operation-info"></span></div>
            </div>

            <div class="code-line highlight-mixed">
                <div class="line-number">142</div>
                <div class="line-content">    half _9096 = half(mix(1.0, float(mix(half3(_9019), half3(half(fast::clamp(_9014 + _Block1.cAOoffset, 0.0, 1.0))), in.IN_TintColor.xxx).x * _9074.z), float(_8929)));<span class="operation-info"> [混合精度]</span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">143</div>
                <div class="line-content">    float _9100 = float(_9096);<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">144</div>
                <div class="line-content">    float3 _9109 = float3(_9064);<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line highlight-texture">
                <div class="line-number">145</div>
                <div class="line-content">    half4 _9130 = sEmissionMapSampler.sample(sEmissionMapSamplerSmplr, in.IN_TexCoord.xy);<span class="operation-info"> [纹理采样]</span></div>
            </div>

            <div class="code-line highlight-mixed">
                <div class="line-number">146</div>
                <div class="line-content">    half3 _9145 = _9130.xyz * half3((float3(_Block1.cEmissionColor) * _Block1.cEmissionScale) * float3(_8949));<span class="operation-info"> [混合精度]</span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">147</div>
                <div class="line-content">    half _18217;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">148</div>
                <div class="line-content">    if (!gl_FrontFacing)<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">149</div>
                <div class="line-content">    {<span class="operation-info"></span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">150</div>
                <div class="line-content">        _18217 = _9334 * half(-1);<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">151</div>
                <div class="line-content">    }<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">152</div>
                <div class="line-content">    else<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">153</div>
                <div class="line-content">    {<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">154</div>
                <div class="line-content">        _18217 = _9334;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">155</div>
                <div class="line-content">    }<span class="operation-info"></span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">156</div>
                <div class="line-content">    float3 _9698 = float3(_9179);<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">157</div>
                <div class="line-content">    float _9408 = mix(_Block1.cCIFadeTime.y, _Block1.cCIFadeTime.w, fast::clamp((_Block1.CameraPos.w - _Block1.cCIFadeTime.x) * _Block1.cCIFadeTime.z, 0.0, 1.0));<span class="operation-info"></span></div>
            </div>

            <div class="code-line highlight-mixed">
                <div class="line-number">158</div>
                <div class="line-content">    float _9721 = fast::clamp(dot(_9286, float3(half3(_9176, _9176, _9199))), 0.0, 1.0);<span class="operation-info"> [混合精度]</span></div>
            </div>

            <div class="code-line highlight-mixed">
                <div class="line-number">159</div>
                <div class="line-content">    float _9734 = (_9721 * _9721) * float(half(fast::clamp((_Block1.cCIMudBuff[2u] - 1.0) * 0.699999988079071044921875, 0.0, 1.0)));<span class="operation-info"> [混合精度]</span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">160</div>
                <div class="line-content">    half3 _9761 = mix(_8949, half3(half(0.20700000226497650146484375), half(0.18400000035762786865234375), half(0.1369999945163726806640625)), half3(half(_9734 * _9408)));<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line highlight-mixed">
                <div class="line-number">161</div>
                <div class="line-content">    half _9772 = half(mix(float(_9199 - _9074.x), 0.89999997615814208984375, _9734 * float(half(mix(_9408 - (0.5 * _Block1.cCIFadeTime.x), _9408, _Block1.cCIFadeTime.y)))));<span class="operation-info"> [混合精度]</span></div>
            </div>

            <div class="code-line highlight-mixed">
                <div class="line-number">162</div>
                <div class="line-content">    float _9429 = (float(half(_9014)) * _9408) * fast::clamp(1.0 + _Block1.EnvInfo.y, 0.0, 1.0);<span class="operation-info"> [混合精度]</span></div>
            </div>

            <div class="code-line highlight-texture">
                <div class="line-number">163</div>
                <div class="line-content">    float4 _9443 = sCharInteractionSampler.sample(sCharInteractionSamplerSmplr, ((in.IN_TexCoord.xy * _Block1.cCISnowData.x) * 12.0));<span class="operation-info"> [纹理采样]</span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">164</div>
                <div class="line-content">    half3 _18225;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">165</div>
                <div class="line-content">    half _18234;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">166</div>
                <div class="line-content">    half _18236;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">167</div>
                <div class="line-content">    half3 _18268;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">168</div>
                <div class="line-content">    half _18272;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">169</div>
                <div class="line-content">    if (_Block1.cCISwitchData.x > 0.0)<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">170</div>
                <div class="line-content">    {<span class="operation-info"></span></div>
            </div>

            <div class="code-line highlight-mixed">
                <div class="line-number">171</div>
                <div class="line-content">        float _9460 = float(half(fast::max(0.0, _Block1.EnvInfo.y)));<span class="operation-info"> [混合精度]</span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">172</div>
                <div class="line-content">        float _9462 = fast::min(2.0, fast::clamp(_Block1.cCISnowData.z, 0.0, 1.0) + _9460);<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">173</div>
                <div class="line-content">        float _9499 = 1.0 - _9429;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">174</div>
                <div class="line-content">        float _9505 = _9443.y;<span class="operation-info"></span></div>
            </div>

            <div class="code-line highlight-mixed">
                <div class="line-number">175</div>
                <div class="line-content">        float _9510 = float(half(fast::clamp(((float2(0.800000011920928955078125, 0.5) * _9429).x * (1.0 - powr(float(half(powr(float(clamp(_18217, half(0.0), half(1.0))) + fast::clamp(0.20000000298023223876953125 - fast::clamp((-1.0) * _Block1.cCISnowData.z, 0.0, 1.0), 0.0, 1.0), 2.0 - _9462) * float(in.IN_StaticWorldNormal.w))), 0.800000011920928955078125 - (_9462 * 0.4000000059604644775390625)))) + ((_9499 * _9499) * _9499), 0.0, 1.0)));<span class="operation-info"> [混合精度]</span></div>
            </div>

            <div class="code-line highlight-mixed">
                <div class="line-number">176</div>
                <div class="line-content">        float _9519 = float(half(9.9956989288330078125e-05));<span class="operation-info"> [混合精度]</span></div>
            </div>

            <div class="code-line highlight-mixed">
                <div class="line-number">177</div>
                <div class="line-content">        half _9535 = half(1.0 - fast::clamp((float(in.IN_LocalPosition.y) * (2.0 - _9460)) * _Block1.cCISnowData.y, 0.0, 1.0));<span class="operation-info"> [混合精度]</span></div>
            </div>

            <div class="code-line highlight-mixed">
                <div class="line-number">178</div>
                <div class="line-content">        float _9556 = float(half((float(_9535) * fast::clamp(float(_9535 + _18217) + 0.5, 0.0, 1.0)) * _9429));<span class="operation-info"> [混合精度]</span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">179</div>
                <div class="line-content">        float _9557 = 1.0 - _9556;<span class="operation-info"></span></div>
            </div>

            <div class="code-line highlight-mixed">
                <div class="line-number">180</div>
                <div class="line-content">        half _9585 = half((_Block1.cCISwitchData.x * fast::clamp(1.0 - _Block1.cCISnowData.w, 0.0, 1.0)) * float(max(half(fast::clamp((fast::max(_9505, _9443.w) - _9510) / fast::max(_9519, fast::clamp(_9510 + 0.1500000059604644775390625, 0.0, 1.0) - _9510), 0.0, 1.0)), half(fast::clamp((fast::max(_9505, _9443.z) - _9557) / fast::max(_9519, (1.5 - _9556) - _9557), 0.0, 1.0)))));<span class="operation-info"> [混合精度]</span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">181</div>
                <div class="line-content">        half _9588 = _9199 - _9585;<span class="operation-info"></span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">182</div>
                <div class="line-content">        float _9603 = float(_9585);<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line highlight-mixed">
                <div class="line-number">183</div>
                <div class="line-content">        _18272 = half(mix(float(_9772), 1.0, _9603));<span class="operation-info"> [混合精度]</span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">184</div>
                <div class="line-content">        _18268 = _9145 * _9588;<span class="operation-info"></span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">185</div>
                <div class="line-content">        _18236 = half(mix(_9100, 1.0, _9603));<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">186</div>
                <div class="line-content">        _18234 = _9079 * _9588;<span class="operation-info"></span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">187</div>
                <div class="line-content">        _18225 = mix(_9761, half3(half(0.61000001430511474609375), half(0.660000026226043701171875), half(0.790000021457672119140625)), half3(_9585));<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">188</div>
                <div class="line-content">    }<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">189</div>
                <div class="line-content">    else<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">190</div>
                <div class="line-content">    {<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">191</div>
                <div class="line-content">        _18272 = _9772;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">192</div>
                <div class="line-content">        _18268 = _9145;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">193</div>
                <div class="line-content">        _18236 = _9096;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">194</div>
                <div class="line-content">        _18234 = _9079;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">195</div>
                <div class="line-content">        _18225 = _9761;<span class="operation-info"></span></div>
            </div>

            <div class="code-line ">
                <div class="line-number">196</div>
                <div class="line-content">    }<span class="operation-info"></span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">197</div>
                <div class="line-content">    half _8295 = half(fast::clamp(1.0 - _Block1.HexRenderOptionData[0].x, 0.0, 1.0));<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line highlight-mixed">
                <div class="line-number">198</div>
                <div class="line-content">    float _8298 = float(_8939.w * half((_8991 * float(min(_8994, _8996))) * powr(float(max(in.IN_TintColor.x, half(9.9956989288330078125e-05))), _Block1.cFurFadeInt)));<span class="operation-info"> [混合精度]</span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">199</div>
                <div class="line-content">    half3 _8303 = half3(half(0.2125999927520751953125), half(0.715200006961822509765625), half(0.072200000286102294921875));<span class="operation-info"> [类型转换]</span></div>
            </div>

            <div class="code-line highlight-conversion">
                <div class="line-number">200</div>
                <div class="line-content">    half3 _8315 = mix(half3(dot(_18225, _8303)), _18225, half3(half(_Block1.cSaturation)));<span class="operation-info"> [类型转换]</span></div>
            </div>

        </div>
    </div>
    
    <div style="text-align: center; margin-top: 30px; color: #858585;">
        <p>报告生成时间: <span id="timestamp"></span></p>
    </div>
    
    <script>
        document.getElementById('timestamp').textContent = new Date().toLocaleString('zh-CN');
    </script>
</body>
</html>
