{"operations": [{"line": 8, "type": "arithmetic", "detail": "float4 AerialPerspectiveExt;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 9, "type": "arithmetic", "detail": "float4 AerialPerspectiveMie;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 10, "type": "arithmetic", "detail": "float4 AerialPerspectiveRay;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 11, "type": "arithmetic", "detail": "float4 AmbientColor;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 12, "type": "arithmetic", "detail": "float4 CSMCacheIndexs;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 13, "type": "arithmetic", "detail": "float4 CSMShadowBiases;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 14, "type": "arithmetic", "detail": "float4 CameraInfo;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 15, "type": "arithmetic", "detail": "float4 CameraPos;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 16, "type": "arithmetic", "detail": "float4 DiyLightingInfo;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 17, "type": "arithmetic", "detail": "float4 EnvInfo;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 18, "type": "arithmetic", "detail": "float4 FogColor;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 19, "type": "arithmetic", "detail": "float4 FogInfo;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 20, "type": "arithmetic", "detail": "float4 GIInfo;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 21, "type": "arithmetic", "detail": "float4 HexRenderOptionData[4];", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 22, "type": "arithmetic", "detail": "float4 LightDataBuffer[65];", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 24, "type": "arithmetic", "detail": "float4 OriginSunDir;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 25, "type": "arithmetic", "detail": "float4 PlayerPos;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 26, "type": "arithmetic", "detail": "float4 ReflectionProbeBBMin;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 27, "type": "arithmetic", "detail": "float4 SHAOParam;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 28, "type": "arithmetic", "detail": "float4 SHGIParam;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 29, "type": "arithmetic", "detail": "float4 SHGIParam2;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 30, "type": "arithmetic", "detail": "float4 ScreenInfo;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 31, "type": "arithmetic", "detail": "float4 ScreenMotionGray;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 35, "type": "arithmetic", "detail": "float4 SunColor;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 36, "type": "arithmetic", "detail": "float4 SunDirection;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 37, "type": "arithmetic", "detail": "float4 SunFogColor;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 38, "type": "arithmetic", "detail": "float4 TimeOfDayInfos;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 40, "type": "arithmetic", "detail": "float4 WorldProbeInfo;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 41, "type": "arithmetic", "detail": "float4 cCIFadeTime;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 42, "type": "arithmetic", "detail": "float4 cCISnowData;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 43, "type": "arithmetic", "detail": "float4 cCISwitchData;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 44, "type": "arithmetic", "detail": "float4 cLocalVirtualLitColor;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 45, "type": "arithmetic", "detail": "float4 cLocalVirtualLitCustom;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 46, "type": "arithmetic", "detail": "float4 cLocalVirtualLitPos;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 47, "type": "arithmetic", "detail": "float4 cSHCoefficients[7];", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 48, "type": "arithmetic", "detail": "float4 cShadowBias;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 49, "type": "arithmetic", "detail": "float4 cVirtualLitColor;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 50, "type": "arithmetic", "detail": "float4 cVirtualLitParam;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 51, "type": "arithmetic", "detail": "float4 cVisibilitySH[2];", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 53, "type": "arithmetic", "detail": "float eIsPlayerOverride;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 55, "type": "arithmetic", "detail": "float eFresnelPower;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 57, "type": "arithmetic", "detail": "float eFresnelMinIntensity;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 59, "type": "arithmetic", "detail": "float eFresnelIntensity;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 60, "type": "arithmetic", "detail": "float cAOoffset;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 61, "type": "arithmetic", "detail": "float cBiasFarAwayShadow;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 62, "type": "arithmetic", "detail": "float cEmissionScale;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 63, "type": "arithmetic", "detail": "float cFurFadeInt;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 64, "type": "arithmetic", "detail": "float cMicroShadow;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 65, "type": "arithmetic", "detail": "float cNoise1Scale;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 66, "type": "arithmetic", "detail": "float cNoise2Bias;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 67, "type": "arithmetic", "detail": "float cNoise2Scale;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 68, "type": "arithmetic", "detail": "float cNormalMapStrength;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 69, "type": "arithmetic", "detail": "float cSaturation;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 70, "type": "arithmetic", "detail": "float eDynamicFresnelIntensity;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 71, "type": "arithmetic", "detail": "float eFresnelAlphaAdd;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 74, "type": "arithmetic", "detail": "constant half3 _18526 = {};", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 74, "type": "assignment", "detail": "声明half变量: _18526", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 75, "type": "arithmetic", "detail": "constant float3 _19185 = {};", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 75, "type": "assignment", "detail": "声明float变量: _19185", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 76, "type": "arithmetic", "detail": "constant half _19493 = {};", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 76, "type": "assignment", "detail": "声明half变量: _19493", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 77, "type": "arithmetic", "detail": "constant float _19585 = {};", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 77, "type": "assignment", "detail": "声明float变量: _19585", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 79, "type": "arithmetic", "detail": "constant float3 _21234 = {};", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 79, "type": "assignment", "detail": "声明float变量: _21234", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 80, "type": "arithmetic", "detail": "constant float3 _21295 = {};", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 80, "type": "assignment", "detail": "声明float变量: _21295", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 81, "type": "arithmetic", "detail": "constant half4 _21296 = {};", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 81, "type": "assignment", "detail": "声明half变量: _21296", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 82, "type": "arithmetic", "detail": "constant half3 _21297 = {};", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 82, "type": "assignment", "detail": "声明half变量: _21297", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 86, "type": "arithmetic", "detail": "float4 _Ret [[color(0)]];", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 91, "type": "arithmetic", "detail": "float4 IN_TexCoord [[user(locn0)]];", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 92, "type": "arithmetic", "detail": "float4 IN_WorldPosition [[user(locn1)]];", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 93, "type": "arithmetic", "detail": "half4 IN_WorldNormal [[user(locn2)]];", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 94, "type": "arithmetic", "detail": "half4 IN_WorldTangent [[user(locn3)]];", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 95, "type": "arithmetic", "detail": "half4 IN_WorldBinormal [[user(locn4)]];", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 96, "type": "arithmetic", "detail": "half4 IN_TintColor [[user(locn5)]];", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 97, "type": "arithmetic", "detail": "float IN_LinearZ [[user(locn6)]];", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 98, "type": "arithmetic", "detail": "half3 IN_LocalPosition [[user(locn7)]];", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 99, "type": "arithmetic", "detail": "half4 IN_StaticWorldNormal [[user(locn8)]];", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 102, "type": "mixed_precision", "detail": "fragment main0_out main0(main0_in in [[stage_in]], constant _Block1T& _Block1 [[buffer(0)]], texture2d<half> sBaseSampler [[texture(0)]], texture2d<half> sMixSampler [[texture(1)]], texture2d<half> sNormalSampler [[texture(2)]], texture2d<half> sEmissionMapSampler [[texture(3)]], texture2d<float> sNoiseSampler [[texture(4)]], texture2d<float> sCharInteractionSampler [[texture(5)]], texture2d_array<float> sSHAORGBVTSampler [[texture(6)]], texture2d_array<float> sSHAOAlphaVTSampler [[texture(7)]], depth2d_array<float> sShadowMapArraySampler [[texture(8)]], sampler sBaseSamplerSmplr [[sampler(0)]], sampler sMixSamplerSmplr [[sampler(1)]], sampler sNormalSamplerSmplr [[sampler(2)]], sampler sEmissionMapSamplerSmplr [[sampler(3)]], sampler sNoiseSamplerSmplr [[sampler(4)]], sampler sCharInteractionSamplerSmplr [[sampler(5)]], sampler sSHAORGBVTSamplerSmplr [[sampler(6)]], sampler sSHAOAlphaVTSamplerSmplr [[sampler(7)]], bool gl_FrontFacing [[front_facing]])", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 106, "type": "type_conversion", "detail": "half(0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 106, "type": "arithmetic", "detail": "half _9176 = half(0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 106, "type": "assignment", "detail": "声明half变量: _9176", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 107, "type": "type_conversion", "detail": "half3(_9176)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 107, "type": "arithmetic", "detail": "half3 _9179 = half3(_9176);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 107, "type": "assignment", "detail": "声明half变量: _9179", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 108, "type": "type_conversion", "detail": "half(1)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 108, "type": "arithmetic", "detail": "half _9199 = half(1);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 108, "type": "assignment", "detail": "声明half变量: _9199", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 109, "type": "function_call", "detail": "fast::normalize(_Block1.CameraPos.xyz - in.IN_WorldPosition.xyz)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 109, "type": "arithmetic", "detail": "float3 _8921 = fast::normalize(_Block1.CameraPos.xyz - in.IN_WorldPosition.xyz);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 109, "type": "assignment", "detail": "声明float变量: _8921", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 110, "type": "type_conversion", "detail": "float3(in.IN_WorldNormal.xyz)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 110, "type": "arithmetic", "detail": "float3 _8925 = float3(in.IN_WorldNormal.xyz);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 110, "type": "assignment", "detail": "声明float变量: _8925", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 111, "type": "type_conversion", "detail": "half(fast::clamp(dot(_8925, _8921)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 111, "type": "function_call", "detail": "fast::clamp(dot(_8925, _8921)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 111, "type": "arithmetic", "detail": "half _8929 = half(fast::clamp(dot(_8925, _8921), 0.0, 1.0));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 111, "type": "assignment", "detail": "声明half变量: _8929", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 112, "type": "texture_sample", "detail": "sBaseSampler.sample(sBaseSamplerSmplr, in.IN_TexCoord.xy)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 112, "type": "arithmetic", "detail": "half4 _8939 = sBaseSampler.sample(sBaseSamplerSmplr, in.IN_TexCoord.xy);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 112, "type": "assignment", "detail": "声明half变量: _8939", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 113, "type": "arithmetic", "detail": "half3 _8941 = _8939.xyz;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 113, "type": "assignment", "detail": "声明half变量: _8941", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 114, "type": "type_conversion", "detail": "half3(float3(_8941 * _8941)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 114, "type": "type_conversion", "detail": "float3(_Block1.cBaseColor)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 114, "type": "mixed_precision", "detail": "half3 _8949 = half3(float3(_8941 * _8941) * float3(_Block1.cBaseColor));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 114, "type": "assignment", "detail": "声明half变量: _8949", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 115, "type": "texture_sample", "detail": "sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy * _Block1.cNoise1Scale)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 115, "type": "arithmetic", "detail": "float4 _8973 = sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy * _Block1.cNoise1Scale));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 115, "type": "assignment", "detail": "声明float变量: _8973", "precision_conversion": null, "performance_impact": "low", "output_type": "float4"}, {"line": 116, "type": "type_conversion", "detail": "half4(_8973)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half4"}, {"line": 116, "type": "arithmetic", "detail": "half4 _8974 = half4(_8973);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 116, "type": "assignment", "detail": "声明half变量: _8974", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 117, "type": "texture_sample", "detail": "sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy * _Block1.cNoise2Scale)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 117, "type": "arithmetic", "detail": "float4 _8982 = sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy * _Block1.cNoise2Scale));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 117, "type": "assignment", "detail": "声明float变量: _8982", "precision_conversion": null, "performance_impact": "low", "output_type": "float4"}, {"line": 118, "type": "type_conversion", "detail": "half4(_8982)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half4"}, {"line": 118, "type": "arithmetic", "detail": "half4 _8983 = half4(_8982);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 118, "type": "assignment", "detail": "声明half变量: _8983", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 119, "type": "type_conversion", "detail": "float(max(in.IN_TintColor.w, half(9.9956989288330078125e-05)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 119, "type": "function_call", "detail": "fast::clamp(powr(float(max(in.IN_TintColor.w, half(9.9956989288330078125e-05)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 119, "type": "mixed_precision", "detail": "float _8991 = fast::clamp(powr(float(max(in.IN_TintColor.w, half(9.9956989288330078125e-05))), _Block1.cNoise2Bias), 0.0, 1.0);", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 119, "type": "assignment", "detail": "声明float变量: _8991", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 120, "type": "arithmetic", "detail": "half _8994 = _8974.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 120, "type": "assignment", "detail": "声明half变量: _8994", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 121, "type": "arithmetic", "detail": "half _8996 = _8983.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 121, "type": "assignment", "detail": "声明half变量: _8996", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 122, "type": "arithmetic", "detail": "float _9014 = 1.0 - _8991;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 122, "type": "assignment", "detail": "声明float变量: _9014", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 123, "type": "type_conversion", "detail": "half(1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 123, "type": "arithmetic", "detail": "half _9019 = half(1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 123, "type": "assignment", "detail": "声明half变量: _9019", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 124, "type": "type_conversion", "detail": "float((_8994 * _8996)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 124, "type": "type_conversion", "detail": "half(mix(0.0, 0.5, powr(float(max(_9199 - _8929, half(9.9956989288330078125e-05)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 124, "type": "function_call", "detail": "mix(0.0, 0.5, powr(float(max(_9199 - _8929, half(9.9956989288330078125e-05)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 124, "type": "mixed_precision", "detail": "if ((float((_8994 * _8996) - half(mix(0.0, 0.5, powr(float(max(_9199 - _8929, half(9.9956989288330078125e-05))), 3.0)))) - 0.100000001490116119384765625) < 0.0)", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 128, "type": "texture_sample", "detail": "sNormalSampler.sample(sNormalSamplerSmplr, in.IN_TexCoord.xy)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 128, "type": "arithmetic", "detail": "half4 _9248 = sNormalSampler.sample(sNormalSamplerSmplr, in.IN_TexCoord.xy);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 128, "type": "assignment", "detail": "声明half变量: _9248", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 129, "type": "type_conversion", "detail": "half(2)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 129, "type": "arithmetic", "detail": "half _9251 = half(2);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 129, "type": "assignment", "detail": "声明half变量: _9251", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 130, "type": "type_conversion", "detail": "half3(_9199)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 130, "type": "arithmetic", "detail": "half3 _9254 = half3(_9199);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 130, "type": "assignment", "detail": "声明half变量: _9254", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 131, "type": "arithmetic", "detail": "half3 _9255 = (_9248.xyz * _9251) - _9254;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 131, "type": "assignment", "detail": "声明half变量: _9255", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 132, "type": "arithmetic", "detail": "half _9257 = _9255.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 132, "type": "assignment", "detail": "声明half变量: _9257", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 133, "type": "arithmetic", "detail": "half _9263 = _9255.y;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 133, "type": "assignment", "detail": "声明half变量: _9263", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 134, "type": "arithmetic", "detail": "half _9270 = _9255.z;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 134, "type": "assignment", "detail": "声明half变量: _9270", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 135, "type": "type_conversion", "detail": "float3(((in.IN_WorldTangent.xyz * _9257)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 135, "type": "arithmetic", "detail": "float3 _9279 = float3(((in.IN_WorldTangent.xyz * _9257) + (in.IN_WorldBinormal.xyz * _9263)) + (in.IN_WorldNormal.xyz * _9270));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 135, "type": "assignment", "detail": "声明float变量: _9279", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 136, "type": "type_conversion", "detail": "float3(in.IN_StaticWorldNormal.xyz)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 136, "type": "arithmetic", "detail": "float3 _9286 = float3(in.IN_StaticWorldNormal.xyz);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 136, "type": "assignment", "detail": "声明float变量: _9286", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 137, "type": "type_conversion", "detail": "float4(float3(in.IN_WorldTangent.xyz)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float4"}, {"line": 137, "type": "type_conversion", "detail": "float(_9257)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 137, "type": "type_conversion", "detail": "float4(float3(in.IN_WorldBinormal.xyz)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float4"}, {"line": 137, "type": "type_conversion", "detail": "float(_9263)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 137, "type": "type_conversion", "detail": "float(_9270)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 137, "type": "arithmetic", "detail": "float3 _9331 = (((float4(float3(in.IN_WorldTangent.xyz), 0.0) * _Block1.Local) * float(_9257)) + ((float4(float3(in.IN_WorldBinormal.xyz), 0.0) * _Block1.Local) * float(_9263))) + (_9286 * float(_9270));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 137, "type": "assignment", "detail": "声明float变量: _9331", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 138, "type": "type_conversion", "detail": "half((_9331 * rsqrt(fast::max(dot(_9331, _9331)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 138, "type": "function_call", "detail": "rsqrt(fast::max(dot(_9331, _9331)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 138, "type": "arithmetic", "detail": "half _9334 = half((_9331 * rsqrt(fast::max(dot(_9331, _9331), 9.9999997473787516355514526367188e-06))).y);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 138, "type": "assignment", "detail": "声明half变量: _9334", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 139, "type": "type_conversion", "detail": "half3(_9279 * rsqrt(fast::max(dot(_9279, _9279)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 139, "type": "type_conversion", "detail": "half3(half(_Block1.cNormalMapStrength)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 139, "type": "function_call", "detail": "mix(in.IN_WorldNormal.xyz, half3(_9279 * rsqrt(fast::max(dot(_9279, _9279)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 139, "type": "arithmetic", "detail": "half3 _9064 = mix(in.IN_WorldNormal.xyz, half3(_9279 * rsqrt(fast::max(dot(_9279, _9279), 9.9999997473787516355514526367188e-06))), half3(half(_Block1.cNormalMapStrength)));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 139, "type": "assignment", "detail": "声明half变量: _9064", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 140, "type": "texture_sample", "detail": "sMixSampler.sample(sMixSamplerSmplr, in.IN_TexCoord.xy)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 140, "type": "arithmetic", "detail": "half4 _9074 = sMixSampler.sample(sMixSamplerSmplr, in.IN_TexCoord.xy);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 140, "type": "assignment", "detail": "声明half变量: _9074", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 141, "type": "arithmetic", "detail": "half _9079 = _9074.y;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 141, "type": "assignment", "detail": "声明half变量: _9079", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 142, "type": "type_conversion", "detail": "half(mix(1.0, float(mix(half3(_9019)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 142, "type": "type_conversion", "detail": "half3(half(fast::clamp(_9014 + _Block1.cAOoffset, 0.0, 1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 142, "type": "type_conversion", "detail": "float(_8929)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 142, "type": "function_call", "detail": "mix(1.0, float(mix(half3(_9019)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 142, "type": "function_call", "detail": "fast::clamp(_9014 + _Block1.cAOoffset, 0.0, 1.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 142, "type": "mixed_precision", "detail": "half _9096 = half(mix(1.0, float(mix(half3(_9019), half3(half(fast::clamp(_9014 + _Block1.cAOoffset, 0.0, 1.0))), in.IN_TintColor.xxx).x * _9074.z), float(_8929)));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 142, "type": "assignment", "detail": "声明half变量: _9096", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 143, "type": "type_conversion", "detail": "float(_9096)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 143, "type": "arithmetic", "detail": "float _9100 = float(_9096);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 143, "type": "assignment", "detail": "声明float变量: _9100", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 144, "type": "type_conversion", "detail": "float3(_9064)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 144, "type": "arithmetic", "detail": "float3 _9109 = float3(_9064);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 144, "type": "assignment", "detail": "声明float变量: _9109", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 145, "type": "texture_sample", "detail": "sEmissionMapSampler.sample(sEmissionMapSamplerSmplr, in.IN_TexCoord.xy)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 145, "type": "arithmetic", "detail": "half4 _9130 = sEmissionMapSampler.sample(sEmissionMapSamplerSmplr, in.IN_TexCoord.xy);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 145, "type": "assignment", "detail": "声明half变量: _9130", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 146, "type": "type_conversion", "detail": "half3((float3(_Block1.cEmissionColor)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 146, "type": "type_conversion", "detail": "float3(_8949)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 146, "type": "mixed_precision", "detail": "half3 _9145 = _9130.xyz * half3((float3(_Block1.cEmissionColor) * _Block1.cEmissionScale) * float3(_8949));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 146, "type": "assignment", "detail": "声明half变量: _9145", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 147, "type": "arithmetic", "detail": "half _18217;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 150, "type": "type_conversion", "detail": "half(-1)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 150, "type": "arithmetic", "detail": "_18217 = _9334 * half(-1);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 156, "type": "type_conversion", "detail": "float3(_9179)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 156, "type": "arithmetic", "detail": "float3 _9698 = float3(_9179);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 156, "type": "assignment", "detail": "声明float变量: _9698", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 157, "type": "function_call", "detail": "mix(_Block1.cCIFadeTime.y, _Block1.cCIFadeTime.w, fast::clamp((_Block1.CameraPos.w - _Block1.cCIFadeTime.x)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 157, "type": "arithmetic", "detail": "float _9408 = mix(_Block1.cCIFadeTime.y, _Block1.cCIFadeTime.w, fast::clamp((_Block1.CameraPos.w - _Block1.cCIFadeTime.x) * _Block1.cCIFadeTime.z, 0.0, 1.0));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 157, "type": "assignment", "detail": "声明float变量: _9408", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 158, "type": "type_conversion", "detail": "float3(half3(_9176, _9176, _9199)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 158, "type": "function_call", "detail": "fast::clamp(dot(_9286, float3(half3(_9176, _9176, _9199)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 158, "type": "mixed_precision", "detail": "float _9721 = fast::clamp(dot(_9286, float3(half3(_9176, _9176, _9199))), 0.0, 1.0);", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 158, "type": "assignment", "detail": "声明float变量: _9721", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 159, "type": "type_conversion", "detail": "float(half(fast::clamp((_Block1.cCIMudBuff[2u] - 1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 159, "type": "function_call", "detail": "fast::clamp((_Block1.cCIMudBuff[2u] - 1.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 159, "type": "mixed_precision", "detail": "float _9734 = (_9721 * _9721) * float(half(fast::clamp((_Block1.cCIMudBuff[2u] - 1.0) * 0.699999988079071044921875, 0.0, 1.0)));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 159, "type": "assignment", "detail": "声明float变量: _9734", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 160, "type": "type_conversion", "detail": "half3(half(0.20700000226497650146484375)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 160, "type": "type_conversion", "detail": "half(0.18400000035762786865234375)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 160, "type": "type_conversion", "detail": "half(0.1369999945163726806640625)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 160, "type": "type_conversion", "detail": "half3(half(_9734 * _9408)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 160, "type": "function_call", "detail": "mix(_8949, half3(half(0.20700000226497650146484375)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 160, "type": "arithmetic", "detail": "half3 _9761 = mix(_8949, half3(half(0.20700000226497650146484375), half(0.18400000035762786865234375), half(0.1369999945163726806640625)), half3(half(_9734 * _9408)));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 160, "type": "assignment", "detail": "声明half变量: _9761", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 161, "type": "type_conversion", "detail": "half(mix(float(_9199 - _9074.x)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 161, "type": "type_conversion", "detail": "float(half(mix(_9408 - (0.5 * _Block1.cCIFadeTime.x)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 161, "type": "function_call", "detail": "mix(float(_9199 - _9074.x)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 161, "type": "function_call", "detail": "mix(_9408 - (0.5 * _Block1.cCIFadeTime.x)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 161, "type": "mixed_precision", "detail": "half _9772 = half(mix(float(_9199 - _9074.x), 0.89999997615814208984375, _9734 * float(half(mix(_9408 - (0.5 * _Block1.cCIFadeTime.x), _9408, _Block1.cCIFadeTime.y)))));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 161, "type": "assignment", "detail": "声明half变量: _9772", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 162, "type": "type_conversion", "detail": "float(half(_9014)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 162, "type": "function_call", "detail": "fast::clamp(1.0 + _Block1.EnvInfo.y, 0.0, 1.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 162, "type": "mixed_precision", "detail": "float _9429 = (float(half(_9014)) * _9408) * fast::clamp(1.0 + _Block1.EnvInfo.y, 0.0, 1.0);", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 162, "type": "assignment", "detail": "声明float变量: _9429", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 163, "type": "texture_sample", "detail": "sCharInteractionSampler.sample(sCharInteractionSamplerSmplr, ((in.IN_TexCoord.xy * _Block1.cCISnowData.x)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 163, "type": "arithmetic", "detail": "float4 _9443 = sCharInteractionSampler.sample(sCharInteractionSamplerSmplr, ((in.IN_TexCoord.xy * _Block1.cCISnowData.x) * 12.0));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 163, "type": "assignment", "detail": "声明float变量: _9443", "precision_conversion": null, "performance_impact": "low", "output_type": "float4"}, {"line": 164, "type": "arithmetic", "detail": "half3 _18225;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 165, "type": "arithmetic", "detail": "half _18234;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 166, "type": "arithmetic", "detail": "half _18236;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 167, "type": "arithmetic", "detail": "half3 _18268;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 168, "type": "arithmetic", "detail": "half _18272;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 171, "type": "type_conversion", "detail": "float(half(fast::max(0.0, _Block1.EnvInfo.y)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 171, "type": "function_call", "detail": "fast::max(0.0, _Block1.EnvInfo.y)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 171, "type": "mixed_precision", "detail": "float _9460 = float(half(fast::max(0.0, _Block1.EnvInfo.y)));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 171, "type": "assignment", "detail": "声明float变量: _9460", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 172, "type": "function_call", "detail": "fast::min(2.0, fast::clamp(_Block1.cCISnowData.z, 0.0, 1.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 172, "type": "arithmetic", "detail": "float _9462 = fast::min(2.0, fast::clamp(_Block1.cCISnowData.z, 0.0, 1.0) + _9460);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 172, "type": "assignment", "detail": "声明float变量: _9462", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 173, "type": "arithmetic", "detail": "float _9499 = 1.0 - _9429;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 173, "type": "assignment", "detail": "声明float变量: _9499", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 174, "type": "arithmetic", "detail": "float _9505 = _9443.y;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 174, "type": "assignment", "detail": "声明float变量: _9505", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 175, "type": "type_conversion", "detail": "float(half(fast::clamp(((float2(0.800000011920928955078125, 0.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 175, "type": "type_conversion", "detail": "float(half(powr(float(clamp(_18217, half(0.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 175, "type": "type_conversion", "detail": "half(1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 175, "type": "type_conversion", "detail": "float(in.IN_StaticWorldNormal.w)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 175, "type": "function_call", "detail": "fast::clamp(((float2(0.800000011920928955078125, 0.5)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 175, "type": "function_call", "detail": "powr(float(half(powr(float(clamp(_18217, half(0.0)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 175, "type": "function_call", "detail": "fast::clamp(0.20000000298023223876953125 - fast::clamp((-1.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 175, "type": "mixed_precision", "detail": "float _9510 = float(half(fast::clamp(((float2(0.800000011920928955078125, 0.5) * _9429).x * (1.0 - powr(float(half(powr(float(clamp(_18217, half(0.0), half(1.0))) + fast::clamp(0.20000000298023223876953125 - fast::clamp((-1.0) * _Block1.cCISnowData.z, 0.0, 1.0), 0.0, 1.0), 2.0 - _9462) * float(in.IN_StaticWorldNormal.w))), 0.800000011920928955078125 - (_9462 * 0.4000000059604644775390625)))) + ((_9499 * _9499) * _9499), 0.0, 1.0)));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 175, "type": "assignment", "detail": "声明float变量: _9510", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 176, "type": "type_conversion", "detail": "float(half(9.9956989288330078125e-05)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 176, "type": "mixed_precision", "detail": "float _9519 = float(half(9.9956989288330078125e-05));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 176, "type": "assignment", "detail": "声明float变量: _9519", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 177, "type": "type_conversion", "detail": "half(1.0 - fast::clamp((float(in.IN_LocalPosition.y)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 177, "type": "function_call", "detail": "fast::clamp((float(in.IN_LocalPosition.y)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 177, "type": "mixed_precision", "detail": "half _9535 = half(1.0 - fast::clamp((float(in.IN_LocalPosition.y) * (2.0 - _9460)) * _Block1.cCISnowData.y, 0.0, 1.0));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 177, "type": "assignment", "detail": "声明half变量: _9535", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 178, "type": "type_conversion", "detail": "float(half((float(_9535)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 178, "type": "type_conversion", "detail": "float(_9535 + _18217)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 178, "type": "function_call", "detail": "fast::clamp(float(_9535 + _18217)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 178, "type": "mixed_precision", "detail": "float _9556 = float(half((float(_9535) * fast::clamp(float(_9535 + _18217) + 0.5, 0.0, 1.0)) * _9429));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 178, "type": "assignment", "detail": "声明float变量: _9556", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 179, "type": "arithmetic", "detail": "float _9557 = 1.0 - _9556;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 179, "type": "assignment", "detail": "声明float变量: _9557", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 180, "type": "type_conversion", "detail": "half((_Block1.cCISwitchData.x * fast::clamp(1.0 - _Block1.cCISnowData.w, 0.0, 1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 180, "type": "type_conversion", "detail": "float(max(half(fast::clamp((fast::max(_9505, _9443.w)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 180, "type": "type_conversion", "detail": "half(fast::clamp((fast::max(_9505, _9443.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 180, "type": "function_call", "detail": "fast::clamp(1.0 - _Block1.cCISnowData.w, 0.0, 1.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 180, "type": "function_call", "detail": "max(half(fast::clamp((fast::max(_9505, _9443.w)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 180, "type": "function_call", "detail": "fast::max(_9519, fast::clamp(_9510 + 0.1500000059604644775390625, 0.0, 1.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 180, "type": "function_call", "detail": "fast::clamp((fast::max(_9505, _9443.z)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 180, "type": "function_call", "detail": "fast::max(_9519, (1.5 - _9556)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 180, "type": "mixed_precision", "detail": "half _9585 = half((_Block1.cCISwitchData.x * fast::clamp(1.0 - _Block1.cCISnowData.w, 0.0, 1.0)) * float(max(half(fast::clamp((fast::max(_9505, _9443.w) - _9510) / fast::max(_9519, fast::clamp(_9510 + 0.1500000059604644775390625, 0.0, 1.0) - _9510), 0.0, 1.0)), half(fast::clamp((fast::max(_9505, _9443.z) - _9557) / fast::max(_9519, (1.5 - _9556) - _9557), 0.0, 1.0)))));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 180, "type": "assignment", "detail": "声明half变量: _9585", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 181, "type": "arithmetic", "detail": "half _9588 = _9199 - _9585;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 181, "type": "assignment", "detail": "声明half变量: _9588", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 182, "type": "type_conversion", "detail": "float(_9585)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 182, "type": "arithmetic", "detail": "float _9603 = float(_9585);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 182, "type": "assignment", "detail": "声明float变量: _9603", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 183, "type": "type_conversion", "detail": "half(mix(float(_9772)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 183, "type": "function_call", "detail": "mix(float(_9772)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 183, "type": "mixed_precision", "detail": "_18272 = half(mix(float(_9772), 1.0, _9603));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 185, "type": "type_conversion", "detail": "half(mix(_9100, 1.0, _9603)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 185, "type": "function_call", "detail": "mix(_9100, 1.0, _9603)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 185, "type": "arithmetic", "detail": "_18236 = half(mix(_9100, 1.0, _9603));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 187, "type": "type_conversion", "detail": "half3(half(0.61000001430511474609375)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 187, "type": "type_conversion", "detail": "half(0.660000026226043701171875)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 187, "type": "type_conversion", "detail": "half(0.790000021457672119140625)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 187, "type": "type_conversion", "detail": "half3(_9585)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 187, "type": "function_call", "detail": "mix(_9761, half3(half(0.61000001430511474609375)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 187, "type": "arithmetic", "detail": "_18225 = mix(_9761, half3(half(0.61000001430511474609375), half(0.660000026226043701171875), half(0.790000021457672119140625)), half3(_9585));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 197, "type": "type_conversion", "detail": "half(fast::clamp(1.0 - _Block1.HexRenderOptionData[0].x, 0.0, 1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 197, "type": "function_call", "detail": "fast::clamp(1.0 - _Block1.HexRenderOptionData[0].x, 0.0, 1.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 197, "type": "arithmetic", "detail": "half _8295 = half(fast::clamp(1.0 - _Block1.HexRenderOptionData[0].x, 0.0, 1.0));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 197, "type": "assignment", "detail": "声明half变量: _8295", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 198, "type": "type_conversion", "detail": "float(_8939.w * half((_8991 * float(min(_8994, _8996)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 198, "type": "type_conversion", "detail": "float(max(in.IN_TintColor.x, half(9.9956989288330078125e-05)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 198, "type": "function_call", "detail": "min(_8994, _8996)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 198, "type": "function_call", "detail": "powr(float(max(in.IN_TintColor.x, half(9.9956989288330078125e-05)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 198, "type": "mixed_precision", "detail": "float _8298 = float(_8939.w * half((_8991 * float(min(_8994, _8996))) * powr(float(max(in.IN_TintColor.x, half(9.9956989288330078125e-05))), _Block1.cFurFadeInt)));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 198, "type": "assignment", "detail": "声明float变量: _8298", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 199, "type": "type_conversion", "detail": "half3(half(0.2125999927520751953125)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 199, "type": "type_conversion", "detail": "half(0.715200006961822509765625)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 199, "type": "type_conversion", "detail": "half(0.072200000286102294921875)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 199, "type": "arithmetic", "detail": "half3 _8303 = half3(half(0.2125999927520751953125), half(0.715200006961822509765625), half(0.072200000286102294921875));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 199, "type": "assignment", "detail": "声明half变量: _8303", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 200, "type": "type_conversion", "detail": "half3(dot(_18225, _8303)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 200, "type": "type_conversion", "detail": "half3(half(_Block1.cSaturation)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 200, "type": "function_call", "detail": "mix(half3(dot(_18225, _8303)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 200, "type": "arithmetic", "detail": "half3 _8315 = mix(half3(dot(_18225, _8303)), _18225, half3(half(_Block1.cSaturation)));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 200, "type": "assignment", "detail": "声明half变量: _8315", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 201, "type": "type_conversion", "detail": "half(_8298)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 201, "type": "arithmetic", "detail": "half _9787 = half(_8298);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 201, "type": "assignment", "detail": "声明half变量: _9787", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 202, "type": "arithmetic", "detail": "half _18230;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 203, "type": "arithmetic", "detail": "half3 _18255;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 206, "type": "function_call", "detail": "abs(dot(_9109, -_8921)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 206, "type": "arithmetic", "detail": "float _9806 = abs(dot(_9109, -_8921));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 206, "type": "assignment", "detail": "声明float变量: _9806", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 207, "type": "function_call", "detail": "abs(_Block1.eFresnelPower)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 207, "type": "arithmetic", "detail": "float _9813 = abs(_Block1.eFresnelPower);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 207, "type": "assignment", "detail": "声明float变量: _9813", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 208, "type": "type_conversion", "detail": "float(_9199 - half(9.9956989288330078125e-05)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 208, "type": "function_call", "detail": "fast::max(_Block1.eFresnelMinIntensity, (_Block1.eFresnelPower < 0.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 208, "type": "function_call", "detail": "powr(_9806, _9813)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 208, "type": "function_call", "detail": "powr(1.0 - fast::min(float(_9199 - half(9.9956989288330078125e-05)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 208, "type": "mixed_precision", "detail": "float _9831 = fast::max(_Block1.eFresnelMinIntensity, (_Block1.eFresnelPower < 0.0) ? powr(_9806, _9813) : powr(1.0 - fast::min(float(_9199 - half(9.9956989288330078125e-05)), _9806), _9813));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 208, "type": "assignment", "detail": "声明float变量: _9831", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 209, "type": "type_conversion", "detail": "float(_9787 * half(_9831)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 209, "type": "mixed_precision", "detail": "float _9846 = float(_9787 * half(_9831));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 209, "type": "assignment", "detail": "声明float变量: _9846", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 210, "type": "type_conversion", "detail": "half3((((float3(_Block1.eFresnelColor)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 210, "type": "mixed_precision", "detail": "_18255 = _9179 + half3((((float3(_Block1.eFresnelColor) * _9831) * _Block1.eFresnelIntensity) * 1.0) * _9846);", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 211, "type": "type_conversion", "detail": "half(fast::max(fast::clamp(_Block1.eFresnelAlphaAdd, 0.0, 1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 211, "type": "type_conversion", "detail": "half(0.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 211, "type": "type_conversion", "detail": "half(1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 211, "type": "function_call", "detail": "clamp(half(fast::max(fast::clamp(_Block1.eFresnelAlphaAdd, 0.0, 1.0)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 211, "type": "arithmetic", "detail": "_18230 = clamp(half(fast::max(fast::clamp(_Block1.eFresnelAlphaAdd, 0.0, 1.0) * _8298, _9846)), half(0.0), half(1.0));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 218, "type": "type_conversion", "detail": "float(_18230)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 218, "type": "arithmetic", "detail": "float _9868 = float(_18230);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 218, "type": "assignment", "detail": "声明float变量: _9868", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 219, "type": "type_conversion", "detail": "half(_Block1.SHAOParam.w)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 219, "type": "arithmetic", "detail": "half _8346 = _18236 * half(_Block1.SHAOParam.w);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 219, "type": "assignment", "detail": "声明half变量: _8346", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 220, "type": "type_conversion", "detail": "float4((in.IN_WorldPosition.xyz + (_8925 * _Block1.cBiasFarAwayShadow)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float4"}, {"line": 220, "type": "arithmetic", "detail": "float4 _9926 = float4((in.IN_WorldPosition.xyz + (_8925 * _Block1.cBiasFarAwayShadow)) - _Block1.CameraPos.xyz, 1.0) * _Block1.ShadowViewProjTexs0;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 220, "type": "assignment", "detail": "声明float变量: _9926", "precision_conversion": null, "performance_impact": "low", "output_type": "float4"}, {"line": 221, "type": "arithmetic", "detail": "float4 _17899 = _9926;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 221, "type": "assignment", "detail": "声明float变量: _17899", "precision_conversion": null, "performance_impact": "low", "output_type": "float4"}, {"line": 223, "type": "type_conversion", "detail": "float4(in.IN_WorldPosition.xyz, 1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float4"}, {"line": 223, "type": "arithmetic", "detail": "float4 _9942 = float4(in.IN_WorldPosition.xyz, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 223, "type": "assignment", "detail": "声明float变量: _9942", "precision_conversion": null, "performance_impact": "low", "output_type": "float4"}, {"line": 224, "type": "arithmetic", "detail": "float4 _9945 = _9942 * _Block1.ShadowViewProjTexs1;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 224, "type": "assignment", "detail": "声明float变量: _9945", "precision_conversion": null, "performance_impact": "low", "output_type": "float4"}, {"line": 225, "type": "arithmetic", "detail": "float4 _17902 = _9945;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 225, "type": "assignment", "detail": "声明float变量: _17902", "precision_conversion": null, "performance_impact": "low", "output_type": "float4"}, {"line": 227, "type": "arithmetic", "detail": "float4 _18237;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 230, "type": "arithmetic", "detail": "float4 _9971 = _9942 * _Block1.ShadowViewProjTexs2;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 230, "type": "assignment", "detail": "声明float变量: _9971", "precision_conversion": null, "performance_impact": "low", "output_type": "float4"}, {"line": 236, "type": "type_conversion", "detail": "float4(0.0, 0.0, 0.0, 1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float4"}, {"line": 236, "type": "arithmetic", "detail": "_18237 = float4(0.0, 0.0, 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 238, "type": "type_conversion", "detail": "float3(_9945.w)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 238, "type": "arithmetic", "detail": "float3 _10033 = _17902.xyz / float3(_9945.w);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 238, "type": "assignment", "detail": "声明float变量: _10033", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 239, "type": "type_conversion", "detail": "float3(_18237.w)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 239, "type": "arithmetic", "detail": "float3 _10040 = _18237.xyz / float3(_18237.w);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 239, "type": "assignment", "detail": "声明float变量: _10040", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 240, "type": "type_conversion", "detail": "float(all(_10040 > float3(0.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 240, "type": "type_conversion", "detail": "float3(1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 240, "type": "function_call", "detail": "step(-0.100000001490116119384765625, _Block1.CSMShadowBiases.z)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 240, "type": "arithmetic", "detail": "float3 _10077 = _10040 * (step(-0.100000001490116119384765625, _Block1.CSMShadowBiases.z) * float(all(_10040 > float3(0.0)) && all(_10040 < float3(1.0))));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 240, "type": "assignment", "detail": "声明float变量: _10077", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 241, "type": "type_conversion", "detail": "float(all(_10033 > float3(0.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 241, "type": "type_conversion", "detail": "float3(1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 241, "type": "function_call", "detail": "step(-0.100000001490116119384765625, _Block1.CSMShadowBiases.y)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 241, "type": "arithmetic", "detail": "float _21135 = step(-0.100000001490116119384765625, _Block1.CSMShadowBiases.y) * float(all(_10033 > float3(0.0)) && all(_10033 < float3(1.0)));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 241, "type": "assignment", "detail": "声明float变量: _21135", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 242, "type": "arithmetic", "detail": "float3 _21138 = _10077 + ((_10033 - _10077) * _21135);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 242, "type": "assignment", "detail": "声明float变量: _21138", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 243, "type": "arithmetic", "detail": "float _10113 = _21138.z;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 243, "type": "assignment", "detail": "声明float变量: _10113", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 244, "type": "type_conversion", "detail": "float2(_Block1.cShadowBias.w)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 244, "type": "arithmetic", "detail": "float2 _10120 = float2(_Block1.cShadowBias.w);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 244, "type": "assignment", "detail": "声明float变量: _10120", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 245, "type": "type_conversion", "detail": "float2(0.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 245, "type": "arithmetic", "detail": "float2 _10167 = (_21138.xy / _10120) - float2(0.5);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 245, "type": "assignment", "detail": "声明float变量: _10167", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 246, "type": "function_call", "detail": "fract(_10167)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 246, "type": "arithmetic", "detail": "float2 _10169 = fract(_10167);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 246, "type": "assignment", "detail": "声明float变量: _10169", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 247, "type": "function_call", "detail": "floor(_10167)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 247, "type": "arithmetic", "detail": "float2 _10171 = floor(_10167);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 247, "type": "assignment", "detail": "声明float变量: _10171", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 248, "type": "type_conversion", "detail": "float2(2.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 248, "type": "arithmetic", "detail": "float2 _10177 = float2(2.0) - _10169;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 248, "type": "assignment", "detail": "声明float变量: _10177", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 249, "type": "type_conversion", "detail": "float2(1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 249, "type": "arithmetic", "detail": "float2 _10181 = _10169 + float2(1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 249, "type": "assignment", "detail": "声明float变量: _10181", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 250, "type": "type_conversion", "detail": "float2(1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 250, "type": "arithmetic", "detail": "float2 _10184 = float2(1.0) / _10177;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 250, "type": "assignment", "detail": "声明float变量: _10184", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 251, "type": "arithmetic", "detail": "float2 _10187 = _10169 / _10181;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 251, "type": "assignment", "detail": "声明float变量: _10187", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 252, "type": "type_conversion", "detail": "float(int(_Block1.CSMCacheIndexs[int(2.0 + ((-1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 252, "type": "arithmetic", "detail": "float _10205 = float(int(_Block1.CSMCacheIndexs[int(2.0 + ((-1.0) * _21135))]));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 252, "type": "assignment", "detail": "声明float变量: _10205", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 253, "type": "type_conversion", "detail": "float3(((_10171 + float2(-0.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 253, "type": "arithmetic", "detail": "float3 _10208 = float3(((_10171 + float2(-0.5)) + _10184) * _10120, _10205);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 253, "type": "assignment", "detail": "声明float变量: _10208", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 254, "type": "type_conversion", "detail": "float3(((_10171 + float2(1.5, -0.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 254, "type": "type_conversion", "detail": "float2(_10187.x, _10184.y)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 254, "type": "arithmetic", "detail": "float3 _10231 = float3(((_10171 + float2(1.5, -0.5)) + float2(_10187.x, _10184.y)) * _10120, _10205);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 254, "type": "assignment", "detail": "声明float变量: _10231", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 255, "type": "type_conversion", "detail": "float3(((_10171 + float2(-0.5, 1.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 255, "type": "type_conversion", "detail": "float2(_10184.x, _10187.y)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 255, "type": "arithmetic", "detail": "float3 _10254 = float3(((_10171 + float2(-0.5, 1.5)) + float2(_10184.x, _10187.y)) * _10120, _10205);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 255, "type": "assignment", "detail": "声明float变量: _10254", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 256, "type": "type_conversion", "detail": "float3(((_10171 + float2(1.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 256, "type": "arithmetic", "detail": "float3 _10276 = float3(((_10171 + float2(1.5)) + _10187) * _10120, _10205);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 256, "type": "assignment", "detail": "声明float变量: _10276", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 257, "type": "arithmetic", "detail": "float _10282 = _10177.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 257, "type": "assignment", "detail": "声明float变量: _10282", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 258, "type": "arithmetic", "detail": "float _10289 = _10181.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 258, "type": "assignment", "detail": "声明float变量: _10289", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 259, "type": "arithmetic", "detail": "float _10300 = _10181.y;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 259, "type": "assignment", "detail": "声明float变量: _10300", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 260, "type": "type_conversion", "detail": "float3(_9926.w)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 260, "type": "arithmetic", "detail": "float3 _9997 = _17899.xyz / float3(_9926.w);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 260, "type": "assignment", "detail": "声明float变量: _9997", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 261, "type": "type_conversion", "detail": "float(half(10)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 261, "type": "type_conversion", "detail": "half(9.9956989288330078125e-05)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 261, "type": "function_call", "detail": "fast::min(1.0 - float(half(10)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 261, "type": "mixed_precision", "detail": "float _10004 = fast::min(1.0 - float(half(10) * half(9.9956989288330078125e-05)), _9997.z);", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 261, "type": "assignment", "detail": "声明float变量: _10004", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 262, "type": "arithmetic", "detail": "float3 _17928 = _9997;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 262, "type": "assignment", "detail": "声明float变量: _17928", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 264, "type": "type_conversion", "detail": "float2(0.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 264, "type": "arithmetic", "detail": "float2 _10378 = (_17928.xy / _10120) - float2(0.5);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 264, "type": "assignment", "detail": "声明float变量: _10378", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 265, "type": "function_call", "detail": "fract(_10378)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 265, "type": "arithmetic", "detail": "float2 _10380 = fract(_10378);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 265, "type": "assignment", "detail": "声明float变量: _10380", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 266, "type": "function_call", "detail": "floor(_10378)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 266, "type": "arithmetic", "detail": "float2 _10382 = floor(_10378);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 266, "type": "assignment", "detail": "声明float变量: _10382", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 267, "type": "type_conversion", "detail": "float2(2.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 267, "type": "arithmetic", "detail": "float2 _10388 = float2(2.0) - _10380;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 267, "type": "assignment", "detail": "声明float变量: _10388", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 268, "type": "type_conversion", "detail": "float2(1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 268, "type": "arithmetic", "detail": "float2 _10392 = _10380 + float2(1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 268, "type": "assignment", "detail": "声明float变量: _10392", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 269, "type": "type_conversion", "detail": "float2(1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 269, "type": "arithmetic", "detail": "float2 _10395 = float2(1.0) / _10388;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 269, "type": "assignment", "detail": "声明float变量: _10395", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 270, "type": "arithmetic", "detail": "float2 _10398 = _10380 / _10392;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 270, "type": "assignment", "detail": "声明float变量: _10398", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 271, "type": "type_conversion", "detail": "float(int(_Block1.CSMCacheIndexs.x)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 271, "type": "arithmetic", "detail": "float _10416 = float(int(_Block1.CSMCacheIndexs.x));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 271, "type": "assignment", "detail": "声明float变量: _10416", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 272, "type": "type_conversion", "detail": "float3(((_10382 + float2(-0.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 272, "type": "arithmetic", "detail": "float3 _10419 = float3(((_10382 + float2(-0.5)) + _10395) * _10120, _10416);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 272, "type": "assignment", "detail": "声明float变量: _10419", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 273, "type": "type_conversion", "detail": "float3(((_10382 + float2(1.5, -0.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 273, "type": "type_conversion", "detail": "float2(_10398.x, _10395.y)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 273, "type": "arithmetic", "detail": "float3 _10442 = float3(((_10382 + float2(1.5, -0.5)) + float2(_10398.x, _10395.y)) * _10120, _10416);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 273, "type": "assignment", "detail": "声明float变量: _10442", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 274, "type": "type_conversion", "detail": "float3(((_10382 + float2(-0.5, 1.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 274, "type": "type_conversion", "detail": "float2(_10395.x, _10398.y)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 274, "type": "arithmetic", "detail": "float3 _10465 = float3(((_10382 + float2(-0.5, 1.5)) + float2(_10395.x, _10398.y)) * _10120, _10416);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 274, "type": "assignment", "detail": "声明float变量: _10465", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 275, "type": "type_conversion", "detail": "float3(((_10382 + float2(1.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 275, "type": "arithmetic", "detail": "float3 _10487 = float3(((_10382 + float2(1.5)) + _10398) * _10120, _10416);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 275, "type": "assignment", "detail": "声明float变量: _10487", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 276, "type": "arithmetic", "detail": "float _10493 = _10388.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 276, "type": "assignment", "detail": "声明float变量: _10493", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 277, "type": "arithmetic", "detail": "float _10500 = _10392.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 277, "type": "assignment", "detail": "声明float变量: _10500", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 278, "type": "arithmetic", "detail": "float _10511 = _10392.y;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 278, "type": "assignment", "detail": "声明float变量: _10511", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 279, "type": "type_conversion", "detail": "half(mix(0.0, 1.0 - fast::clamp((abs(dot(_9109, _Block1.SunDirection.xyz)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 279, "type": "type_conversion", "detail": "half(((((_10388.y * ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10419.xy, uint(rint(_10419.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 279, "type": "type_conversion", "detail": "float(all(_17928 > float3(0.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 279, "type": "type_conversion", "detail": "float3(1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 279, "type": "type_conversion", "detail": "half(fast::min(1.0, float(half(((((_10177.y * ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10208.xy, uint(rint(_10208.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 279, "type": "type_conversion", "detail": "float(all(_21138 > float3(0.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 279, "type": "type_conversion", "detail": "float3(1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 279, "type": "function_call", "detail": "max(half(mix(0.0, 1.0 - fast::clamp((abs(dot(_9109, _Block1.SunDirection.xyz)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 279, "type": "function_call", "detail": "max(half(((((_10388.y * ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10419.xy, uint(rint(_10419.z)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 279, "type": "function_call", "detail": "fast::min(1.0, float(half(((((_10177.y * ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10208.xy, uint(rint(_10208.z)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 279, "type": "mixed_precision", "detail": "half _8351 = max(half(mix(0.0, 1.0 - fast::clamp((abs(dot(_9109, _Block1.SunDirection.xyz)) + ((2.0 * _9100) * _9100)) - 1.0, 0.0, 1.0), _Block1.cMicroShadow)), max(half(((((_10388.y * ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10419.xy, uint(rint(_10419.z)), _10004, level(0.0)) * _10493) + (sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10442.xy, uint(rint(_10442.z)), _10004, level(0.0)) * _10500))) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10465.xy, uint(rint(_10465.z)), _10004, level(0.0)) * _10493) * _10511)) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10487.xy, uint(rint(_10487.z)), _10004, level(0.0)) * _10500) * _10511)) * 0.111111097037792205810546875) * float(all(_17928 > float3(0.0)) && all(_17928 < float3(1.0)))), half(fast::min(1.0, float(half(((((_10177.y * ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10208.xy, uint(rint(_10208.z)), _10113, level(0.0)) * _10282) + (sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10231.xy, uint(rint(_10231.z)), _10113, level(0.0)) * _10289))) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10254.xy, uint(rint(_10254.z)), _10113, level(0.0)) * _10282) * _10300)) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10276.xy, uint(rint(_10276.z)), _10113, level(0.0)) * _10289) * _10300)) * 0.111111097037792205810546875) * float(all(_21138 > float3(0.0)) && all(_21138 < float3(1.0)))))))));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 279, "type": "assignment", "detail": "声明half变量: _8351", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 280, "type": "arithmetic", "detail": "float3 _8370 = in.IN_WorldPosition.xyz - _Block1.CameraPos.xyz;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 280, "type": "assignment", "detail": "声明float变量: _8370", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 281, "type": "function_call", "detail": "fast::normalize(-_8370)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 281, "type": "arithmetic", "detail": "float3 _8373 = fast::normalize(-_8370);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 281, "type": "assignment", "detail": "声明float变量: _8373", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 282, "type": "function_call", "detail": "dot(_9109, _8373)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 282, "type": "arithmetic", "detail": "float _8378 = dot(_9109, _8373);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 282, "type": "assignment", "detail": "声明float变量: _8378", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 283, "type": "type_conversion", "detail": "half3(half(0.039999999105930328369140625)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 283, "type": "type_conversion", "detail": "half3(_18234)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 283, "type": "function_call", "detail": "mix(half3(half(0.039999999105930328369140625)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 283, "type": "arithmetic", "detail": "half3 _10557 = mix(half3(half(0.039999999105930328369140625)), _8315, half3(_18234));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 283, "type": "assignment", "detail": "声明half变量: _10557", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 284, "type": "type_conversion", "detail": "half3(float3(_8315 - (_8315 * _18234)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 284, "type": "type_conversion", "detail": "float3(0.3183098733425140380859375)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 284, "type": "mixed_precision", "detail": "half3 _10569 = half3(float3(_8315 - (_8315 * _18234)) * float3(0.3183098733425140380859375));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 284, "type": "assignment", "detail": "声明half变量: _10569", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 285, "type": "type_conversion", "detail": "float3(_Block1.EnvInfo.z)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 285, "type": "arithmetic", "detail": "float3 _10588 = float3(_Block1.EnvInfo.z);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 285, "type": "assignment", "detail": "声明float变量: _10588", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 286, "type": "type_conversion", "detail": "half3(half(0.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 286, "type": "arithmetic", "detail": "half3 _8393 = half3(half(0.0));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 286, "type": "assignment", "detail": "声明half变量: _8393", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 289, "type": "arithmetic", "detail": "half _18329;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 292, "type": "function_call", "detail": "select(_Block1.PlayerPos.xyz, _Block1.CameraPos.xyz, bool3((_8397 & 524288u)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 292, "type": "arithmetic", "detail": "float3 _8435 = select(_Block1.PlayerPos.xyz, _Block1.CameraPos.xyz, bool3((_8397 & 524288u) > 0u));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 292, "type": "assignment", "detail": "声明float变量: _8435", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 293, "type": "arithmetic", "detail": "half _18306;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 296, "type": "arithmetic", "detail": "half _18304;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 299, "type": "type_conversion", "detail": "float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 299, "type": "arithmetic", "detail": "float3 _10686 = (in.IN_WorldPosition.xyz + (_8373 * 0.100000001490116119384765625)) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 299, "type": "assignment", "detail": "声明float变量: _10686", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 300, "type": "function_call", "detail": "floor(_10686)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 300, "type": "arithmetic", "detail": "float3 _10762 = _10686 - floor(_10686);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 300, "type": "assignment", "detail": "声明float变量: _10762", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 301, "type": "type_conversion", "detail": "float3(0.125)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 301, "type": "arithmetic", "detail": "float3 _10789 = _8435 * float3(0.125);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 301, "type": "assignment", "detail": "声明float变量: _10789", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 302, "type": "arithmetic", "detail": "float _10797 = _10789.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 302, "type": "assignment", "detail": "声明float变量: _10797", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 303, "type": "function_call", "detail": "floor(_10797)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 303, "type": "arithmetic", "detail": "float _10799 = floor(_10797);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 303, "type": "assignment", "detail": "声明float变量: _10799", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 304, "type": "arithmetic", "detail": "float3 _21191;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 306, "type": "arithmetic", "detail": "float3 _21235;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 309, "type": "arithmetic", "detail": "float3 _21194 = _21191;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 309, "type": "assignment", "detail": "声明float变量: _21194", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 317, "type": "arithmetic", "detail": "float _21078 = _10789.y;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 317, "type": "assignment", "detail": "声明float变量: _21078", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 318, "type": "function_call", "detail": "floor(_21078)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 318, "type": "arithmetic", "detail": "float _21079 = floor(_21078);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 318, "type": "assignment", "detail": "声明float变量: _21079", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 319, "type": "arithmetic", "detail": "float3 _21198 = _21235;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 319, "type": "assignment", "detail": "声明float变量: _21198", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 321, "type": "arithmetic", "detail": "float3 _21236;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 324, "type": "arithmetic", "detail": "float3 _21201 = _21198;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 324, "type": "assignment", "detail": "声明float变量: _21201", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 332, "type": "arithmetic", "detail": "float _21100 = _10789.z;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 332, "type": "assignment", "detail": "声明float变量: _21100", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 333, "type": "function_call", "detail": "floor(_21100)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 333, "type": "arithmetic", "detail": "float _21101 = floor(_21100);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 333, "type": "assignment", "detail": "声明float变量: _21101", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 334, "type": "arithmetic", "detail": "float3 _21205 = _21236;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 334, "type": "assignment", "detail": "声明float变量: _21205", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 336, "type": "arithmetic", "detail": "float3 _21237;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 339, "type": "arithmetic", "detail": "float3 _21208 = _21205;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 339, "type": "assignment", "detail": "声明float变量: _21208", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 347, "type": "arithmetic", "detail": "float3 _10822 = _21237 * 8.0;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 347, "type": "assignment", "detail": "声明float变量: _10822", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 348, "type": "arithmetic", "detail": "half _18305;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 349, "type": "type_conversion", "detail": "float3(240.0, 128.0, 240.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 349, "type": "arithmetic", "detail": "if (all(in.IN_WorldPosition.xyz >= _10822) && all(in.IN_WorldPosition.xyz < (_10822 + float3(240.0, 128.0, 240.0))))", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 352, "type": "type_conversion", "detail": "float((_8397 & 458752u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 352, "type": "arithmetic", "detail": "float _10887 = 3.0 - float((_8397 & 458752u) >> 16u);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 352, "type": "assignment", "detail": "声明float变量: _10887", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 353, "type": "arithmetic", "detail": "half _18297;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 356, "type": "type_conversion", "detail": "float(_10704)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 356, "type": "arithmetic", "detail": "float _10900 = 3.0 - float(_10704);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 356, "type": "assignment", "detail": "声明float变量: _10900", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 357, "type": "type_conversion", "detail": "float2(0.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 357, "type": "type_conversion", "detail": "float2(0.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 357, "type": "arithmetic", "detail": "float2 _10994 = ((_10762.xz - float2(0.5)) * 0.9375) + float2(0.5);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 357, "type": "assignment", "detail": "声明float变量: _10994", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 358, "type": "arithmetic", "detail": "float _11001 = _10822.x * 0.0041666668839752674102783203125;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 358, "type": "assignment", "detail": "声明float变量: _11001", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 359, "type": "function_call", "detail": "floor(_11001)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 359, "type": "arithmetic", "detail": "float _11005 = ((_11001 - floor(_11001)) - 0.5) * 0.9375;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 359, "type": "assignment", "detail": "声明float变量: _11005", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 360, "type": "arithmetic", "detail": "float _11011 = _10822.z * 0.0041666668839752674102783203125;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 360, "type": "assignment", "detail": "声明float变量: _11011", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 361, "type": "function_call", "detail": "floor(_11011)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 361, "type": "arithmetic", "detail": "float _11015 = ((_11011 - floor(_11011)) - 0.5) * 0.9375;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 361, "type": "assignment", "detail": "声明float变量: _11015", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 362, "type": "arithmetic", "detail": "float _11020 = _10994.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 362, "type": "assignment", "detail": "声明float变量: _11020", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 363, "type": "arithmetic", "detail": "float3 _17954;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 364, "type": "function_call", "detail": "fast::min(_11020, _11005 + 0.49609375)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 364, "type": "function_call", "detail": "fast::max(_11020, _11005 + 0.50390625)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 365, "type": "arithmetic", "detail": "float _11038 = _10994.y;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 365, "type": "assignment", "detail": "声明float变量: _11038", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 366, "type": "function_call", "detail": "fast::min(_11038, _11015 + 0.49609375)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 366, "type": "function_call", "detail": "fast::max(_11038, _11015 + 0.50390625)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 367, "type": "arithmetic", "detail": "float _11059 = (_10762.y * 64.0) - 0.5;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 367, "type": "assignment", "detail": "声明float变量: _11059", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 368, "type": "function_call", "detail": "floor(_11059)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 368, "type": "arithmetic", "detail": "float _11064 = floor(_11059);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 368, "type": "assignment", "detail": "声明float变量: _11064", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 372, "type": "type_conversion", "detail": "float2(float(_11067 & 7u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 372, "type": "type_conversion", "detail": "float(_11067 >> 3u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 372, "type": "arithmetic", "detail": "float2 _11097 = (float2(float(_11067 & 7u), float(_11067 >> 3u)) + _17954.xz) * 0.125;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 372, "type": "assignment", "detail": "声明float变量: _11097", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 373, "type": "arithmetic", "detail": "float _11100 = _11097.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 373, "type": "assignment", "detail": "声明float变量: _11100", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 374, "type": "type_conversion", "detail": "float3(_11100, _11097.y, _10887)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 374, "type": "arithmetic", "detail": "float3 _11102 = float3(_11100, _11097.y, _10887);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 374, "type": "assignment", "detail": "声明float变量: _11102", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 375, "type": "arithmetic", "detail": "half4 _17962;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 376, "type": "type_conversion", "detail": "half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11102.xy, uint(rint(_11102.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 376, "type": "texture_sample", "detail": "sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11102.xy, uint(rint(_11102.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 376, "type": "arithmetic", "detail": "_17962.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11102.xy, uint(rint(_11102.z)), level(0.0)).x);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 377, "type": "type_conversion", "detail": "float3(_11100, _11097.y, _10900)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 377, "type": "arithmetic", "detail": "float3 _11113 = float3(_11100, _11097.y, _10900);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 377, "type": "assignment", "detail": "声明float变量: _11113", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 378, "type": "type_conversion", "detail": "half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11113.xy, uint(rint(_11113.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 378, "type": "texture_sample", "detail": "sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11113.xy, uint(rint(_11113.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 378, "type": "arithmetic", "detail": "half3 _11118 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11113.xy, uint(rint(_11113.z)), level(0.0)).xyz);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 378, "type": "assignment", "detail": "声明half变量: _11118", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 379, "type": "type_conversion", "detail": "float2(float(_21301 & 7u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 379, "type": "type_conversion", "detail": "float(_21301 >> 3u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 379, "type": "arithmetic", "detail": "float2 _11135 = (float2(float(_21301 & 7u), float(_21301 >> 3u)) + _17954.xz) * 0.125;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 379, "type": "assignment", "detail": "声明float变量: _11135", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 380, "type": "arithmetic", "detail": "float _11138 = _11135.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 380, "type": "assignment", "detail": "声明float变量: _11138", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 381, "type": "type_conversion", "detail": "float3(_11138, _11135.y, _10887)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 381, "type": "arithmetic", "detail": "float3 _11140 = float3(_11138, _11135.y, _10887);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 381, "type": "assignment", "detail": "声明float变量: _11140", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 382, "type": "arithmetic", "detail": "half4 _17964;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 383, "type": "type_conversion", "detail": "half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11140.xy, uint(rint(_11140.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 383, "type": "texture_sample", "detail": "sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11140.xy, uint(rint(_11140.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 383, "type": "arithmetic", "detail": "_17964.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11140.xy, uint(rint(_11140.z)), level(0.0)).x);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 384, "type": "type_conversion", "detail": "float3(_11138, _11135.y, _10900)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 384, "type": "arithmetic", "detail": "float3 _11151 = float3(_11138, _11135.y, _10900);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 384, "type": "assignment", "detail": "声明float变量: _11151", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 385, "type": "type_conversion", "detail": "half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11151.xy, uint(rint(_11151.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 385, "type": "texture_sample", "detail": "sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11151.xy, uint(rint(_11151.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 385, "type": "arithmetic", "detail": "half3 _11156 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11151.xy, uint(rint(_11151.z)), level(0.0)).xyz);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 385, "type": "assignment", "detail": "声明half变量: _11156", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 386, "type": "type_conversion", "detail": "half4(_11118.x, _11118.y, _11118.z, _17962.w)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half4"}, {"line": 386, "type": "type_conversion", "detail": "half4(_11156.x, _11156.y, _11156.z, _17964.w)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half4"}, {"line": 386, "type": "type_conversion", "detail": "half4(half(fast::clamp(_11059 - _11064, 0.0, 1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half4"}, {"line": 386, "type": "function_call", "detail": "mix(half4(_11118.x, _11118.y, _11118.z, _17962.w)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 386, "type": "function_call", "detail": "fast::clamp(_11059 - _11064, 0.0, 1.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 386, "type": "arithmetic", "detail": "half4 _11163 = mix(half4(_11118.x, _11118.y, _11118.z, _17962.w), half4(_11156.x, _11156.y, _11156.z, _17964.w), half4(half(fast::clamp(_11059 - _11064, 0.0, 1.0))));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 386, "type": "assignment", "detail": "声明half变量: _11163", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 387, "type": "type_conversion", "detail": "half(32.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 387, "type": "type_conversion", "detail": "half(float(dot(half3(_8373)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 387, "type": "type_conversion", "detail": "half3((float3(_11163.xyz)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 387, "type": "type_conversion", "detail": "float3(2.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 387, "type": "type_conversion", "detail": "float3(1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 387, "type": "type_conversion", "detail": "half(0.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 387, "type": "type_conversion", "detail": "half(1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 387, "type": "function_call", "detail": "clamp((_11163.w * half(32.0)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 387, "type": "function_call", "detail": "dot(half3(_8373)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 387, "type": "mixed_precision", "detail": "_18297 = clamp((_11163.w * half(32.0)) + half(float(dot(half3(_8373), half3((float3(_11163.xyz) * float3(2.0)) - float3(1.0)))) * 2.0), half(0.0), half(1.0));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 391, "type": "type_conversion", "detail": "float2(0.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 391, "type": "type_conversion", "detail": "float2(0.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 391, "type": "arithmetic", "detail": "float2 _11233 = ((_10762.xz - float2(0.5)) * 0.9375) + float2(0.5);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 391, "type": "assignment", "detail": "声明float变量: _11233", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 392, "type": "arithmetic", "detail": "float _11240 = _10822.x * 0.0041666668839752674102783203125;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 392, "type": "assignment", "detail": "声明float变量: _11240", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 393, "type": "function_call", "detail": "floor(_11240)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 393, "type": "arithmetic", "detail": "float _11244 = ((_11240 - floor(_11240)) - 0.5) * 0.9375;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 393, "type": "assignment", "detail": "声明float变量: _11244", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 394, "type": "arithmetic", "detail": "float _11250 = _10822.z * 0.0041666668839752674102783203125;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 394, "type": "assignment", "detail": "声明float变量: _11250", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 395, "type": "function_call", "detail": "floor(_11250)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 395, "type": "arithmetic", "detail": "float _11254 = ((_11250 - floor(_11250)) - 0.5) * 0.9375;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 395, "type": "assignment", "detail": "声明float变量: _11254", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 396, "type": "arithmetic", "detail": "float _11259 = _11233.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 396, "type": "assignment", "detail": "声明float变量: _11259", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 397, "type": "arithmetic", "detail": "float3 _17977;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 398, "type": "function_call", "detail": "fast::min(_11259, _11244 + 0.49609375)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 398, "type": "function_call", "detail": "fast::max(_11259, _11244 + 0.50390625)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 399, "type": "arithmetic", "detail": "float _11277 = _11233.y;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 399, "type": "assignment", "detail": "声明float变量: _11277", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 400, "type": "function_call", "detail": "fast::min(_11277, _11254 + 0.49609375)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 400, "type": "function_call", "detail": "fast::max(_11277, _11254 + 0.50390625)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 401, "type": "arithmetic", "detail": "float _11298 = (_10762.y * 64.0) - 0.5;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 401, "type": "assignment", "detail": "声明float变量: _11298", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 402, "type": "function_call", "detail": "floor(_11298)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 402, "type": "arithmetic", "detail": "float _11303 = floor(_11298);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 402, "type": "assignment", "detail": "声明float变量: _11303", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 406, "type": "type_conversion", "detail": "float3((float2(float(_11306 & 7u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 406, "type": "type_conversion", "detail": "float(_11306 >> 3u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 406, "type": "arithmetic", "detail": "float3 _11340 = float3((float2(float(_11306 & 7u), float(_11306 >> 3u)) + _17977.xz) * 0.125, _10887);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 406, "type": "assignment", "detail": "声明float变量: _11340", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 407, "type": "type_conversion", "detail": "float3((float2(float(_21300 & 7u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 407, "type": "type_conversion", "detail": "float(_21300 >> 3u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 407, "type": "arithmetic", "detail": "float3 _11365 = float3((float2(float(_21300 & 7u), float(_21300 >> 3u)) + _17977.xz) * 0.125, _10887);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 407, "type": "assignment", "detail": "声明float变量: _11365", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 408, "type": "type_conversion", "detail": "half(mix(float(half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11340.xy, uint(rint(_11340.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 408, "type": "type_conversion", "detail": "float(half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11365.xy, uint(rint(_11365.z)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 408, "type": "type_conversion", "detail": "half(32.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 408, "type": "texture_sample", "detail": "sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11340.xy, uint(rint(_11340.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 408, "type": "texture_sample", "detail": "sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11365.xy, uint(rint(_11365.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 408, "type": "function_call", "detail": "mix(float(half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11340.xy, uint(rint(_11340.z)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 408, "type": "function_call", "detail": "fast::clamp(_11298 - _11303, 0.0, 1.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 408, "type": "mixed_precision", "detail": "_18297 = half(mix(float(half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11340.xy, uint(rint(_11340.z)), level(0.0)).x)), float(half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11365.xy, uint(rint(_11365.z)), level(0.0)).x)), fast::clamp(_11298 - _11303, 0.0, 1.0))) * half(32.0);", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 410, "type": "type_conversion", "detail": "float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 410, "type": "type_conversion", "detail": "float3(1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 410, "type": "arithmetic", "detail": "float3 _11404 = (((in.IN_WorldPosition.xyz - _10822) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)) * 2.0) - float3(1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 410, "type": "assignment", "detail": "声明float变量: _11404", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 411, "type": "arithmetic", "detail": "float3 _11407 = _11404 * _11404;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 411, "type": "assignment", "detail": "声明float变量: _11407", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 412, "type": "arithmetic", "detail": "float3 _11410 = _11407 * _11407;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 412, "type": "assignment", "detail": "声明float变量: _11410", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 413, "type": "arithmetic", "detail": "half _18303;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 416, "type": "type_conversion", "detail": "half(mix(float(_18297)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 416, "type": "function_call", "detail": "mix(float(_18297)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 416, "type": "function_call", "detail": "fast::clamp(fast::max(_11410.x, fast::max(_11410.y, _11410.z)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 416, "type": "mixed_precision", "detail": "_18303 = half(mix(float(_18297), 1.0, fast::clamp(fast::max(_11410.x, fast::max(_11410.y, _11410.z)), 0.0, 1.0)));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 434, "type": "arithmetic", "detail": "float _11467 = _Block1.SHAOParam.z * _Block1.SHAOParam.z;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 434, "type": "assignment", "detail": "声明float变量: _11467", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 435, "type": "arithmetic", "detail": "float3 _11470 = in.IN_WorldPosition.xyz - _8435;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 435, "type": "assignment", "detail": "声明float变量: _11470", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 436, "type": "function_call", "detail": "fast::clamp((_11467 - dot(_11470, _11470)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 436, "type": "arithmetic", "detail": "float _11479 = fast::clamp((_11467 - dot(_11470, _11470)) / _11467, 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 436, "type": "assignment", "detail": "声明float变量: _11479", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 437, "type": "type_conversion", "detail": "half(1.0 - fast::clamp((1.0 - float(half(fast::clamp(1.0 - float(half(powr(float(half(fast::clamp(1.0 - float(_18304)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 437, "type": "type_conversion", "detail": "float(half(mix(_Block1.SHAOParam.x, _Block1.SHAOParam.y, 1.0 - (_11479 * _11479)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 437, "type": "function_call", "detail": "fast::clamp((1.0 - float(half(fast::clamp(1.0 - float(half(powr(float(half(fast::clamp(1.0 - float(_18304)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 437, "type": "function_call", "detail": "fast::max(mix(6.0, 0.0, fast::clamp(_Block1.SHGIParam2.w, 0.0, 1.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 437, "type": "function_call", "detail": "mix(_Block1.SHAOParam.x, _Block1.SHAOParam.y, 1.0 - (_11479 * _11479)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 437, "type": "mixed_precision", "detail": "_18306 = half(1.0 - fast::clamp((1.0 - float(half(fast::clamp(1.0 - float(half(powr(float(half(fast::clamp(1.0 - float(_18304), 0.0, 1.0))), fast::max(mix(6.0, 0.0, fast::clamp(_Block1.SHGIParam2.w, 0.0, 1.0)), 0.5)))), 0.0, 1.0)))) * float(half(mix(_Block1.SHAOParam.x, _Block1.SHAOParam.y, 1.0 - (_11479 * _11479)))), 0.0, 1.0));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 443, "type": "arithmetic", "detail": "half _18330;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 458, "type": "type_conversion", "detail": "float3(half3(_8373)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 458, "type": "mixed_precision", "detail": "float3 _11517 = float3(half3(_8373));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 458, "type": "assignment", "detail": "声明float变量: _11517", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 459, "type": "type_conversion", "detail": "float3(_Block1.SunDirection.x, fast::min(_Block1.SunDirection.y, 1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 459, "type": "function_call", "detail": "fast::normalize(float3(_Block1.SunDirection.x, fast::min(_Block1.SunDirection.y, 1.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 459, "type": "arithmetic", "detail": "float3 _11600 = _Block1.CameraPos.xyz + (fast::normalize(float3(_Block1.SunDirection.x, fast::min(_Block1.SunDirection.y, 1.0), _Block1.SunDirection.z)) * 200000.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 459, "type": "assignment", "detail": "声明float变量: _11600", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 460, "type": "function_call", "detail": "reflect(-_11517, _9109)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 460, "type": "arithmetic", "detail": "float3 _11604 = reflect(-_11517, _9109);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 460, "type": "assignment", "detail": "声明float变量: _11604", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 461, "type": "function_call", "detail": "dot(_11600, _11604)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 461, "type": "arithmetic", "detail": "float3 _11611 = (_11604 * dot(_11600, _11604)) - _11600;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 461, "type": "assignment", "detail": "声明float变量: _11611", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 462, "type": "function_call", "detail": "fast::normalize(_11600 + (_11611 * fast::clamp(4500.0 / length(_11611)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 462, "type": "arithmetic", "detail": "float3 _11622 = fast::normalize(_11600 + (_11611 * fast::clamp(4500.0 / length(_11611), 0.0, 1.0)));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 462, "type": "assignment", "detail": "声明float变量: _11622", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 463, "type": "type_conversion", "detail": "half(dot(_9109, _11622)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 463, "type": "type_conversion", "detail": "half(0.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 463, "type": "type_conversion", "detail": "half(1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 463, "type": "function_call", "detail": "clamp(half(dot(_9109, _11622)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 463, "type": "arithmetic", "detail": "half _11536 = clamp(half(dot(_9109, _11622)), half(0.0), half(1.0));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 463, "type": "assignment", "detail": "声明half变量: _11536", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 464, "type": "type_conversion", "detail": "float(half(fast::max(0.119999997317790985107421875, float(_18272)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 464, "type": "function_call", "detail": "fast::max(0.119999997317790985107421875, float(_18272)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 464, "type": "mixed_precision", "detail": "float _11560 = float(half(fast::max(0.119999997317790985107421875, float(_18272))));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 464, "type": "assignment", "detail": "声明float变量: _11560", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 465, "type": "function_call", "detail": "fast::max(0.00999999977648258209228515625, fast::clamp((4.125 * _11560)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 465, "type": "arithmetic", "detail": "float _11629 = fast::max(0.00999999977648258209228515625, fast::clamp((4.125 * _11560) - 0.319999992847442626953125, 0.0, 1.0));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 465, "type": "assignment", "detail": "声明float变量: _11629", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 466, "type": "type_conversion", "detail": "float(_11536)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 466, "type": "arithmetic", "detail": "float _11919 = float(_11536);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 466, "type": "assignment", "detail": "声明float变量: _11919", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 467, "type": "function_call", "detail": "dot(_9109, _11517)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 467, "type": "arithmetic", "detail": "float _11925 = dot(_9109, _11517);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 467, "type": "assignment", "detail": "声明float变量: _11925", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 468, "type": "function_call", "detail": "fast::normalize(_11517 + _11622)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 468, "type": "arithmetic", "detail": "float3 _11940 = fast::normalize(_11517 + _11622);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 468, "type": "assignment", "detail": "声明float变量: _11940", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 469, "type": "function_call", "detail": "fast::clamp(dot(_9109, _11940)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 469, "type": "arithmetic", "detail": "float _11945 = fast::clamp(dot(_9109, _11940), 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 469, "type": "assignment", "detail": "声明float变量: _11945", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 470, "type": "function_call", "detail": "fast::clamp(dot(_11517, _11940)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 470, "type": "arithmetic", "detail": "float _11951 = fast::clamp(dot(_11517, _11940), 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 470, "type": "assignment", "detail": "声明float变量: _11951", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 471, "type": "function_call", "detail": "fast::clamp(abs(fast::clamp(_11925, 0.0, 1.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 471, "type": "arithmetic", "detail": "float _11736 = fast::clamp(abs(fast::clamp(_11925, 0.0, 1.0)) + 9.9999997473787516355514526367188e-06, 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 471, "type": "assignment", "detail": "声明float变量: _11736", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 472, "type": "type_conversion", "detail": "half4(half(0.60000002384185791015625)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half4"}, {"line": 472, "type": "arithmetic", "detail": "half4 _11959 = half4(half(0.60000002384185791015625));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 472, "type": "assignment", "detail": "声明half变量: _11959", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 473, "type": "arithmetic", "detail": "half4 _11967 = _11959 * _11959;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 473, "type": "assignment", "detail": "声明half变量: _11967", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 474, "type": "type_conversion", "detail": "half4(half(1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half4"}, {"line": 474, "type": "arithmetic", "detail": "half4 _11970 = half4(half(1.0)) - _11967;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 474, "type": "assignment", "detail": "声明half变量: _11970", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 475, "type": "type_conversion", "detail": "half4(half(1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half4"}, {"line": 475, "type": "arithmetic", "detail": "half4 _11973 = half4(half(1.0)) + _11967;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 475, "type": "assignment", "detail": "声明half变量: _11973", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 476, "type": "type_conversion", "detail": "half(2.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 476, "type": "arithmetic", "detail": "half4 _11975 = _11959 * half(2.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 476, "type": "assignment", "detail": "声明half变量: _11975", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 477, "type": "type_conversion", "detail": "half4(half(1.5)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half4"}, {"line": 477, "type": "arithmetic", "detail": "half4 _11982 = half4(half(1.5));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 477, "type": "assignment", "detail": "声明half变量: _11982", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 478, "type": "function_call", "detail": "exp2((((-5.554729938507080078125)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 478, "type": "arithmetic", "detail": "float _11756 = exp2((((-5.554729938507080078125) * _11951) - 6.9831600189208984375) * _11951);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 478, "type": "assignment", "detail": "声明float变量: _11756", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 479, "type": "arithmetic", "detail": "float _11763 = _11756 + ((1.0 - _11756) * 0.039999999105930328369140625);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 479, "type": "assignment", "detail": "声明float变量: _11763", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 480, "type": "type_conversion", "detail": "half(0.699999988079071044921875)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 480, "type": "arithmetic", "detail": "half _11764 = half(0.699999988079071044921875);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 480, "type": "assignment", "detail": "声明half变量: _11764", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 481, "type": "type_conversion", "detail": "half(float(_11764)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 481, "type": "mixed_precision", "detail": "half _11768 = half(float(_11764) + 0.100000001490116119384765625);", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 481, "type": "assignment", "detail": "声明half变量: _11768", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 482, "type": "arithmetic", "detail": "half _11772 = _9199 - _8351;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 482, "type": "assignment", "detail": "声明half变量: _11772", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 483, "type": "arithmetic", "detail": "half _11777 = _9199 + _11768;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 483, "type": "assignment", "detail": "声明half变量: _11777", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 484, "type": "arithmetic", "detail": "half _11790 = _9199 + _11764;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 484, "type": "assignment", "detail": "声明half变量: _11790", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 485, "type": "type_conversion", "detail": "half3((float3(half3(_Block1.SunColor.xyz)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 485, "type": "type_conversion", "detail": "float(clamp(((_11772 + _11768)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 485, "type": "type_conversion", "detail": "half(0.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 485, "type": "type_conversion", "detail": "half(1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 485, "type": "type_conversion", "detail": "half(0.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 485, "type": "type_conversion", "detail": "half(1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 485, "type": "function_call", "detail": "clamp(((_11772 + _11768)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 485, "type": "function_call", "detail": "clamp(((_11536 + _11764)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 485, "type": "mixed_precision", "detail": "half3 _11812 = half3((float3(half3(_Block1.SunColor.xyz)) * _11919) * float(clamp(((_11772 + _11768) / _11777) * _11777, half(0.0), half(1.0)) * clamp(((_11536 + _11764) / _11790) * _11790, half(0.0), half(1.0))));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 485, "type": "assignment", "detail": "声明half变量: _11812", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 486, "type": "type_conversion", "detail": "half(10.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 486, "type": "arithmetic", "detail": "half _11815 = half(10.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 486, "type": "assignment", "detail": "声明half变量: _11815", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 487, "type": "type_conversion", "detail": "half3(fast::normalize(_11622 + _11517)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 487, "type": "type_conversion", "detail": "half(0.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 487, "type": "type_conversion", "detail": "half(1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 487, "type": "function_call", "detail": "clamp(dot(half3(fast::normalize(_11622 + _11517)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 487, "type": "arithmetic", "detail": "half _11827 = clamp(dot(half3(fast::normalize(_11622 + _11517)), _9064), half(0.0), half(1.0));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 487, "type": "assignment", "detail": "声明half变量: _11827", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 488, "type": "type_conversion", "detail": "float(_11815)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 488, "type": "arithmetic", "detail": "float _11858 = float(_11815) * 0.5;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 488, "type": "assignment", "detail": "声明float变量: _11858", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 489, "type": "type_conversion", "detail": "float(_9251 + _11815)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 489, "type": "arithmetic", "detail": "float _11862 = float(_9251 + _11815);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 489, "type": "assignment", "detail": "声明float变量: _11862", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 490, "type": "arithmetic", "detail": "float _12049 = _11560 * _11560;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 490, "type": "assignment", "detail": "声明float变量: _12049", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 491, "type": "arithmetic", "detail": "float _12062 = _12049 / (((((_11945 * _12049) * _12049) - _11945) * _11945) + 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 491, "type": "assignment", "detail": "声明float变量: _12062", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 492, "type": "arithmetic", "detail": "float _12080 = _12049 * _12049;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 492, "type": "assignment", "detail": "声明float变量: _12080", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 493, "type": "type_conversion", "detail": "float(half(9.9956989288330078125e-05)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 493, "type": "mixed_precision", "detail": "float _12108 = float(half(9.9956989288330078125e-05));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 493, "type": "assignment", "detail": "声明float变量: _12108", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 494, "type": "type_conversion", "detail": "float3(_10557)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 494, "type": "arithmetic", "detail": "float3 _12025 = float3(_10557);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 494, "type": "assignment", "detail": "声明float变量: _12025", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 495, "type": "type_conversion", "detail": "float3(fast::clamp(50.0 * _12025.y, 0.0, 1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 495, "type": "function_call", "detail": "fast::clamp(50.0 * _12025.y, 0.0, 1.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 495, "type": "arithmetic", "detail": "float3 _12120 = float3(fast::clamp(50.0 * _12025.y, 0.0, 1.0)) - _12025;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 495, "type": "assignment", "detail": "声明float变量: _12120", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 496, "type": "type_conversion", "detail": "float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_11827 * _11827)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 496, "type": "type_conversion", "detail": "float(half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 496, "type": "type_conversion", "detail": "half(dot(_11517, _11622)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 496, "type": "type_conversion", "detail": "float4(0.0795769989490509033203125)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float4"}, {"line": 496, "type": "type_conversion", "detail": "half3(half(fast::clamp((_11763 * _11763)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 496, "type": "type_conversion", "detail": "float(dot(_11812, _8303)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 496, "type": "type_conversion", "detail": "float3(_11629)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 496, "type": "type_conversion", "detail": "float3(_11812)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 496, "type": "type_conversion", "detail": "float(_11772)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 496, "type": "function_call", "detail": "mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_11827 * _11827)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 496, "type": "function_call", "detail": "powr(max(half4(half(9.9956989288330078125e-05)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 496, "type": "function_call", "detail": "dot(_11517, _11622)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 496, "type": "function_call", "detail": "fast::clamp((_11763 * _11763)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 496, "type": "function_call", "detail": "dot(_11812, _8303)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 496, "type": "function_call", "detail": "fast::min(1000.0, (_12062 * _12062)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 496, "type": "function_call", "detail": "fast::max((_11736 + sqrt((_11736 * (_11736 - (_11736 * _12080)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 496, "type": "function_call", "detail": "sqrt((_11919 * (_11919 - (_11919 * _12080)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 496, "type": "mixed_precision", "detail": "float3 _8521 = ((_9698 + (float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_11827 * _11827)), 0.0078125))), _11858)) * 0.15915493667125701904296875) * float(half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05)), _11973 - (_11975 * (-half(dot(_11517, _11622))))), _11982)) * float4(0.0795769989490509033203125)).x))), half3(half(fast::clamp((_11763 * _11763) * float(dot(_11812, _8303)), 0.0, 1.0)))) * _11812) * float3(_11629))) + (((float3(_11812) * ((_12025 + (_12120 * _11756)) * (fast::min(1000.0, (_12062 * _12062) * 0.3183098733425140380859375) * (1.0 / fast::max((_11736 + sqrt((_11736 * (_11736 - (_11736 * _12080))) + _12080)) * (_11919 + sqrt((_11919 * (_11919 - (_11919 * _12080))) + _12080)), _12108))))) * _11629) * float(_11772))) * _10588;", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 496, "type": "assignment", "detail": "声明float变量: _8521", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 498, "type": "arithmetic", "detail": "half3 _18429;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 499, "type": "arithmetic", "detail": "float3 _18431;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 500, "type": "arithmetic", "detail": "float3 _19685;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 501, "type": "arithmetic", "detail": "float3 _19720;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 502, "type": "arithmetic", "detail": "float3 _19755;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 503, "type": "arithmetic", "detail": "half3 _19825;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 510, "type": "arithmetic", "detail": "half3 _19923;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 511, "type": "arithmetic", "detail": "float3 _19965;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 512, "type": "arithmetic", "detail": "half _20833;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 513, "type": "arithmetic", "detail": "float _20848;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 515, "type": "arithmetic", "detail": "float3 _20893;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 516, "type": "arithmetic", "detail": "float3 _20908;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 517, "type": "arithmetic", "detail": "float3 _20923;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 518, "type": "arithmetic", "detail": "float _20938;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 519, "type": "arithmetic", "detail": "half3 _20953;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 520, "type": "arithmetic", "detail": "half _19485;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 521, "type": "arithmetic", "detail": "float _19577;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 523, "type": "arithmetic", "detail": "float _19790;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 547, "type": "arithmetic", "detail": "half _19481;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 548, "type": "arithmetic", "detail": "float _19573;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 550, "type": "arithmetic", "detail": "float3 _19681;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 551, "type": "arithmetic", "detail": "float3 _19716;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 552, "type": "arithmetic", "detail": "float3 _19751;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 553, "type": "arithmetic", "detail": "float _19786;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 554, "type": "arithmetic", "detail": "half3 _19821;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 557, "type": "arithmetic", "detail": "float3 _12360 = -_Block1.LightDataBuffer[_12202].xyz;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 557, "type": "assignment", "detail": "声明float变量: _12360", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 558, "type": "function_call", "detail": "dot(in.IN_WorldPosition.xyz - _Block1.LightDataBuffer[_12188].xyz, _12360)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 558, "type": "function_call", "detail": "dot(_12360, _12360)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 558, "type": "arithmetic", "detail": "float3 _12378 = in.IN_WorldPosition.xyz - (_Block1.LightDataBuffer[_12188].xyz + (_12360 * (dot(in.IN_WorldPosition.xyz - _Block1.LightDataBuffer[_12188].xyz, _12360) / dot(_12360, _12360))));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 558, "type": "assignment", "detail": "声明float变量: _12378", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 559, "type": "function_call", "detail": "dot(_12378, _12378)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 559, "type": "arithmetic", "detail": "float _12381 = dot(_12378, _12378);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 559, "type": "assignment", "detail": "声明float变量: _12381", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 560, "type": "arithmetic", "detail": "float _19350;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 563, "type": "function_call", "detail": "sqrt(_12381)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 563, "type": "arithmetic", "detail": "float _12392 = sqrt(_12381) - _Block1.LightDataBuffer[_12209].y;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 563, "type": "assignment", "detail": "声明float变量: _12392", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 564, "type": "arithmetic", "detail": "float _12395 = _12392 * _12392;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 564, "type": "assignment", "detail": "声明float变量: _12395", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 565, "type": "function_call", "detail": "abs(_Block1.LightDataBuffer[_12188].w)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 565, "type": "arithmetic", "detail": "float _12398 = _12395 * abs(_Block1.LightDataBuffer[_12188].w);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 565, "type": "assignment", "detail": "声明float变量: _12398", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 566, "type": "function_call", "detail": "fast::clamp(1.0 - (_12398 * _12398)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 566, "type": "arithmetic", "detail": "float _12404 = fast::clamp(1.0 - (_12398 * _12398), 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 566, "type": "assignment", "detail": "声明float变量: _12404", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 567, "type": "function_call", "detail": "fast::min(100.0, (_12404 * _12404)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 573, "type": "type_conversion", "detail": "half3(_Block1.LightDataBuffer[_12195].xyz)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 573, "type": "arithmetic", "detail": "_19821 = half3(_Block1.LightDataBuffer[_12195].xyz);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 579, "type": "function_call", "detail": "abs(_Block1.LightDataBuffer[_12195].w)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 580, "type": "type_conversion", "detail": "half(dot(_9109, _12360)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 580, "type": "type_conversion", "detail": "half(0.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 580, "type": "type_conversion", "detail": "half(1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 580, "type": "function_call", "detail": "clamp(half(dot(_9109, _12360)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 580, "type": "arithmetic", "detail": "_19481 = clamp(half(dot(_9109, _12360)), half(0.0), half(1.0));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 584, "type": "arithmetic", "detail": "half _19482;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 585, "type": "arithmetic", "detail": "float _19574;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 587, "type": "arithmetic", "detail": "float3 _19682;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 588, "type": "arithmetic", "detail": "float3 _19717;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 589, "type": "arithmetic", "detail": "float3 _19752;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 590, "type": "arithmetic", "detail": "float _19787;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 591, "type": "arithmetic", "detail": "half3 _19822;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 595, "type": "type_conversion", "detail": "float((_12741 >> 0u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 595, "type": "arithmetic", "detail": "float _12858 = float((_12741 >> 0u) & 65535u) * 0.0001525902189314365386962890625;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 595, "type": "assignment", "detail": "声明float变量: _12858", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 596, "type": "arithmetic", "detail": "float3 _12599 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 596, "type": "assignment", "detail": "声明float变量: _12599", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 597, "type": "function_call", "detail": "dot(_12599, _12599)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 597, "type": "arithmetic", "detail": "float _12602 = dot(_12599, _12599);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 597, "type": "assignment", "detail": "声明float变量: _12602", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 598, "type": "function_call", "detail": "rsqrt(_12602)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 598, "type": "arithmetic", "detail": "float3 _12606 = _12599 * rsqrt(_12602);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 598, "type": "assignment", "detail": "声明float变量: _12606", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 599, "type": "function_call", "detail": "abs(_Block1.LightDataBuffer[_12188].w)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 599, "type": "arithmetic", "detail": "float _12613 = _12602 * abs(_Block1.LightDataBuffer[_12188].w);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 599, "type": "assignment", "detail": "声明float变量: _12613", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 600, "type": "function_call", "detail": "fast::clamp(1.0 - (_12613 * _12613)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 600, "type": "arithmetic", "detail": "float _12620 = fast::clamp(1.0 - (_12613 * _12613), 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 600, "type": "assignment", "detail": "声明float变量: _12620", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 601, "type": "arithmetic", "detail": "float _12631 = _12620 * _12620;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 601, "type": "assignment", "detail": "声明float变量: _12631", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 602, "type": "arithmetic", "detail": "float _19213;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 605, "type": "arithmetic", "detail": "float _12641 = _12631 / ((_12602 * _Block1.LightDataBuffer[_12202].w) + 9.9999997473787516355514526367188e-05);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 605, "type": "assignment", "detail": "声明float变量: _12641", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 606, "type": "arithmetic", "detail": "float _19211;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 609, "type": "function_call", "detail": "fast::min(_12641, _12858)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 615, "type": "function_call", "detail": "fast::min(100.0, _19211)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 621, "type": "function_call", "detail": "fast::clamp((dot(_Block1.LightDataBuffer[_12202].xyz, -_12606)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 621, "type": "arithmetic", "detail": "float _12677 = fast::clamp((dot(_Block1.LightDataBuffer[_12202].xyz, -_12606) - _Block1.LightDataBuffer[_12209].z) * _Block1.LightDataBuffer[_12209].y, 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 621, "type": "assignment", "detail": "声明float变量: _12677", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 622, "type": "type_conversion", "detail": "half3(fast::min(float3(3000.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 622, "type": "function_call", "detail": "fast::min(float3(3000.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 622, "type": "mixed_precision", "detail": "_19822 = half3(fast::min(float3(3000.0), _Block1.LightDataBuffer[_12195].xyz * _19213));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 628, "type": "type_conversion", "detail": "float((_12741 >> 16u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 628, "type": "arithmetic", "detail": "_19574 = float((_12741 >> 16u) & 65535u) * 0.001525902189314365386962890625;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 629, "type": "type_conversion", "detail": "half(dot(_9109, _12606)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 629, "type": "type_conversion", "detail": "half(0.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 629, "type": "type_conversion", "detail": "half(1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 629, "type": "function_call", "detail": "clamp(half(dot(_9109, _12606)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 629, "type": "arithmetic", "detail": "_19482 = clamp(half(dot(_9109, _12606)), half(0.0), half(1.0));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 633, "type": "arithmetic", "detail": "half _19483;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 634, "type": "arithmetic", "detail": "float _19575;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 636, "type": "arithmetic", "detail": "float3 _19683;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 637, "type": "arithmetic", "detail": "float3 _19718;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 638, "type": "arithmetic", "detail": "float3 _19753;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 639, "type": "arithmetic", "detail": "float _19788;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 640, "type": "arithmetic", "detail": "half3 _19823;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 644, "type": "arithmetic", "detail": "float3 _12933 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 644, "type": "assignment", "detail": "声明float变量: _12933", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 645, "type": "function_call", "detail": "dot(_12933, _12933)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 645, "type": "arithmetic", "detail": "float _12936 = dot(_12933, _12933);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 645, "type": "assignment", "detail": "声明float变量: _12936", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 646, "type": "type_conversion", "detail": "float3(sqrt(_12936)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 646, "type": "function_call", "detail": "sqrt(_12936)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 646, "type": "arithmetic", "detail": "float3 _12942 = _12933 / float3(sqrt(_12936));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 646, "type": "assignment", "detail": "声明float变量: _12942", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 647, "type": "function_call", "detail": "abs(_Block1.LightDataBuffer[_12188].w)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 647, "type": "arithmetic", "detail": "float _12950 = _12936 * abs(_Block1.LightDataBuffer[_12188].w);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 647, "type": "assignment", "detail": "声明float变量: _12950", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 648, "type": "function_call", "detail": "fast::clamp(1.0 - (_12950 * _12950)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 648, "type": "arithmetic", "detail": "float _12956 = fast::clamp(1.0 - (_12950 * _12950), 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 648, "type": "assignment", "detail": "声明float变量: _12956", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 649, "type": "function_call", "detail": "fast::min(100.0, (_12956 * _12956)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 649, "type": "arithmetic", "detail": "float _12972 = fast::min(100.0, (_12956 * _12956) / ((_12936 * _Block1.LightDataBuffer[_12209].w) + 9.9999997473787516355514526367188e-05));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 649, "type": "assignment", "detail": "声明float变量: _12972", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 650, "type": "type_conversion", "detail": "float((_13098 >> 0u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 650, "type": "arithmetic", "detail": "float _13202 = float((_13098 >> 0u) & 65535u) * 0.0001525902189314365386962890625;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 650, "type": "assignment", "detail": "声明float变量: _13202", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 651, "type": "arithmetic", "detail": "float _19070;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 654, "type": "function_call", "detail": "fast::min(_12972, _13202)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 660, "type": "type_conversion", "detail": "half3(fast::min(float3(3000.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 660, "type": "function_call", "detail": "fast::min(float3(3000.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 660, "type": "mixed_precision", "detail": "_19823 = half3(fast::min(float3(3000.0), _Block1.LightDataBuffer[_12195].xyz * _19070) * (((_12298 & 16777216u) == 16777216u) ? _Block1.TimeOfDayInfos.y : 1.0));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 666, "type": "type_conversion", "detail": "float((_13098 >> 16u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 666, "type": "arithmetic", "detail": "_19575 = float((_13098 >> 16u) & 65535u) * 0.001525902189314365386962890625;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 667, "type": "type_conversion", "detail": "half(fast::clamp(dot(_9109, _12942)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 667, "type": "function_call", "detail": "fast::clamp(dot(_9109, _12942)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 667, "type": "arithmetic", "detail": "_19483 = ((_12298 & 262144u) == 262144u) ? _9019 : half(fast::clamp(dot(_9109, _12942), 0.0, 1.0));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 672, "type": "arithmetic", "detail": "half _19484;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 673, "type": "arithmetic", "detail": "float3 _19684;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 674, "type": "arithmetic", "detail": "float3 _19754;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 675, "type": "arithmetic", "detail": "float _19789;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 676, "type": "arithmetic", "detail": "half3 _19824;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 679, "type": "arithmetic", "detail": "float3 _13339 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 679, "type": "assignment", "detail": "声明float变量: _13339", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 680, "type": "function_call", "detail": "dot(_13339, _13339)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 680, "type": "arithmetic", "detail": "float _13342 = dot(_13339, _13339);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 680, "type": "assignment", "detail": "声明float变量: _13342", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 681, "type": "function_call", "detail": "abs(_Block1.LightDataBuffer[_12188].w)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 681, "type": "arithmetic", "detail": "float _13348 = _13342 * abs(_Block1.LightDataBuffer[_12188].w);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 681, "type": "assignment", "detail": "声明float变量: _13348", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 682, "type": "function_call", "detail": "fast::clamp(1.0 - (_13348 * _13348)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 682, "type": "arithmetic", "detail": "float _13356 = fast::clamp(1.0 - (_13348 * _13348), 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 682, "type": "assignment", "detail": "声明float变量: _13356", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 683, "type": "function_call", "detail": "fast::normalize(_11517 - (_9109 * _11925)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 683, "type": "arithmetic", "detail": "float3 _13433 = fast::normalize(_11517 - (_9109 * _11925));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 683, "type": "assignment", "detail": "声明float变量: _13433", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 684, "type": "function_call", "detail": "cross(_9109, _13433)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 684, "type": "assignment", "detail": "声明float变量: _13459", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 685, "type": "type_conversion", "detail": "float3(_Block1.LightDataBuffer[_12202].xyz)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 685, "type": "arithmetic", "detail": "float3 _13466 = float3(_Block1.LightDataBuffer[_12202].xyz) * _Block1.LightDataBuffer[_12195].w;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 685, "type": "assignment", "detail": "声明float变量: _13466", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 686, "type": "arithmetic", "detail": "float3 _13467 = _13339 - _13466;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 686, "type": "assignment", "detail": "声明float变量: _13467", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 687, "type": "type_conversion", "detail": "float3(_Block1.LightDataBuffer[_12209].yzw)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 687, "type": "arithmetic", "detail": "float3 _13472 = float3(_Block1.LightDataBuffer[_12209].yzw) * _Block1.LightDataBuffer[_12202].w;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 687, "type": "assignment", "detail": "声明float变量: _13472", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 688, "type": "arithmetic", "detail": "float3 _13484 = _13339 + _13466;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 688, "type": "assignment", "detail": "声明float变量: _13484", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 689, "type": "function_call", "detail": "fast::normalize((_13467 - _13472)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 689, "type": "arithmetic", "detail": "float3 _13657 = fast::normalize((_13467 - _13472) * _13459);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 689, "type": "assignment", "detail": "声明float变量: _13657", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 690, "type": "function_call", "detail": "fast::normalize((_13484 - _13472)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 690, "type": "arithmetic", "detail": "float3 _13660 = fast::normalize((_13484 - _13472) * _13459);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 690, "type": "assignment", "detail": "声明float变量: _13660", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 691, "type": "function_call", "detail": "fast::normalize((_13484 + _13472)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 691, "type": "arithmetic", "detail": "float3 _13663 = fast::normalize((_13484 + _13472) * _13459);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 691, "type": "assignment", "detail": "声明float变量: _13663", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 692, "type": "function_call", "detail": "fast::normalize((_13467 + _13472)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 692, "type": "arithmetic", "detail": "float3 _13666 = fast::normalize((_13467 + _13472) * _13459);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 692, "type": "assignment", "detail": "声明float变量: _13666", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 693, "type": "function_call", "detail": "dot(_13657, _13660)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 693, "type": "arithmetic", "detail": "float _13712 = dot(_13657, _13660);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 693, "type": "assignment", "detail": "声明float变量: _13712", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 694, "type": "function_call", "detail": "abs(_13712)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 694, "type": "arithmetic", "detail": "float _13714 = abs(_13712);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 694, "type": "assignment", "detail": "声明float变量: _13714", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 695, "type": "arithmetic", "detail": "float _13728 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13714)) * _13714)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13714) * _13714));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 695, "type": "assignment", "detail": "声明float变量: _13728", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 696, "type": "function_call", "detail": "dot(_13660, _13663)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 696, "type": "arithmetic", "detail": "float _13753 = dot(_13660, _13663);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 696, "type": "assignment", "detail": "声明float变量: _13753", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 697, "type": "function_call", "detail": "abs(_13753)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 697, "type": "arithmetic", "detail": "float _13755 = abs(_13753);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 697, "type": "assignment", "detail": "声明float变量: _13755", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 698, "type": "arithmetic", "detail": "float _13769 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13755)) * _13755)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13755) * _13755));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 698, "type": "assignment", "detail": "声明float变量: _13769", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 699, "type": "function_call", "detail": "dot(_13663, _13666)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 699, "type": "arithmetic", "detail": "float _13794 = dot(_13663, _13666);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 699, "type": "assignment", "detail": "声明float变量: _13794", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 700, "type": "function_call", "detail": "abs(_13794)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 700, "type": "arithmetic", "detail": "float _13796 = abs(_13794);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 700, "type": "assignment", "detail": "声明float变量: _13796", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 701, "type": "arithmetic", "detail": "float _13810 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13796)) * _13796)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13796) * _13796));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 701, "type": "assignment", "detail": "声明float变量: _13810", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 702, "type": "function_call", "detail": "dot(_13666, _13657)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 702, "type": "arithmetic", "detail": "float _13835 = dot(_13666, _13657);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 702, "type": "assignment", "detail": "声明float变量: _13835", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 703, "type": "function_call", "detail": "abs(_13835)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 703, "type": "arithmetic", "detail": "float _13837 = abs(_13835);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 703, "type": "assignment", "detail": "声明float变量: _13837", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 704, "type": "arithmetic", "detail": "float _13851 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13837)) * _13837)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13837) * _13837));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 704, "type": "assignment", "detail": "声明float变量: _13851", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 705, "type": "function_call", "detail": "cross(_13660, (_13657 * (-((_13712 > 0.0)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 705, "type": "function_call", "detail": "rsqrt(fast::max(1.0 - (_13712 * _13712)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 705, "type": "function_call", "detail": "rsqrt(fast::max(1.0 - (_13753 * _13753)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 705, "type": "function_call", "detail": "cross(_13666, (_13657 * ((_13835 > 0.0)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 705, "type": "function_call", "detail": "rsqrt(fast::max(1.0 - (_13835 * _13835)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 705, "type": "function_call", "detail": "rsqrt(fast::max(1.0 - (_13794 * _13794)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 705, "type": "arithmetic", "detail": "float3 _13700 = cross(_13660, (_13657 * (-((_13712 > 0.0) ? _13728 : ((0.5 * rsqrt(fast::max(1.0 - (_13712 * _13712), 1.0000000116860974230803549289703e-07))) - _13728)))) + (_13663 * ((_13753 > 0.0) ? _13769 : ((0.5 * rsqrt(fast::max(1.0 - (_13753 * _13753), 1.0000000116860974230803549289703e-07))) - _13769)))) + cross(_13666, (_13657 * ((_13835 > 0.0) ? _13851 : ((0.5 * rsqrt(fast::max(1.0 - (_13835 * _13835), 1.0000000116860974230803549289703e-07))) - _13851))) + (_13663 * (-((_13794 > 0.0) ? _13810 : ((0.5 * rsqrt(fast::max(1.0 - (_13794 * _13794), 1.0000000116860974230803549289703e-07))) - _13810)))));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 705, "type": "assignment", "detail": "声明float变量: _13700", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 706, "type": "function_call", "detail": "length(_13700)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 706, "type": "arithmetic", "detail": "float _13531 = length(_13700);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 706, "type": "assignment", "detail": "声明float变量: _13531", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 707, "type": "function_call", "detail": "step(0.0, dot(cross(_Block1.LightDataBuffer[_12202].xyz, _Block1.LightDataBuffer[_12209].yzw)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 707, "type": "arithmetic", "detail": "float _13539 = step(0.0, dot(cross(_Block1.LightDataBuffer[_12202].xyz, _Block1.LightDataBuffer[_12209].yzw), _13339));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 707, "type": "assignment", "detail": "声明float变量: _13539", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 708, "type": "type_conversion", "detail": "half3(_Block1.LightDataBuffer[_12195].xyz * (_13356 * _13356)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 708, "type": "arithmetic", "detail": "_19824 = half3(_Block1.LightDataBuffer[_12195].xyz * (_13356 * _13356));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 710, "type": "function_call", "detail": "rsqrt(_13342)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 712, "type": "type_conversion", "detail": "half(fast::max(((_13531 * _13531)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 712, "type": "type_conversion", "detail": "float3(_13531)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 712, "type": "function_call", "detail": "fast::max(((_13531 * _13531)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 712, "type": "mixed_precision", "detail": "_19484 = half(fast::max(((_13531 * _13531) + ((_13700 / float3(_13531)).z * ((_13539 * 2.0) - 1.0))) / (_13531 + 1.0), 0.0));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 725, "type": "function_call", "detail": "select(_19720, _11517, bool3(_13270)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 749, "type": "type_conversion", "detail": "float(_19481)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 749, "type": "arithmetic", "detail": "float _14176 = float(_19481);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 749, "type": "assignment", "detail": "声明float变量: _14176", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 750, "type": "function_call", "detail": "fast::normalize(_19716 + _19751)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 750, "type": "arithmetic", "detail": "float3 _14197 = fast::normalize(_19716 + _19751);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 750, "type": "assignment", "detail": "声明float变量: _14197", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 751, "type": "function_call", "detail": "fast::clamp(dot(_19681, _14197)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 751, "type": "arithmetic", "detail": "float _14202 = fast::clamp(dot(_19681, _14197), 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 751, "type": "assignment", "detail": "声明float变量: _14202", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 752, "type": "function_call", "detail": "fast::clamp(dot(_19716, _14197)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 752, "type": "arithmetic", "detail": "float _14208 = fast::clamp(dot(_19716, _14197), 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 752, "type": "assignment", "detail": "声明float变量: _14208", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 753, "type": "function_call", "detail": "fast::clamp(abs(fast::clamp(dot(_19681, _19716)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 753, "type": "arithmetic", "detail": "float _13993 = fast::clamp(abs(fast::clamp(dot(_19681, _19716), 0.0, 1.0)) + 9.9999997473787516355514526367188e-06, 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 753, "type": "assignment", "detail": "声明float变量: _13993", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 754, "type": "function_call", "detail": "exp2((((-5.554729938507080078125)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 754, "type": "arithmetic", "detail": "float _14013 = exp2((((-5.554729938507080078125) * _14208) - 6.9831600189208984375) * _14208);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 754, "type": "assignment", "detail": "声明float变量: _14013", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 755, "type": "arithmetic", "detail": "float _14020 = _14013 + ((1.0 - _14013) * 0.039999999105930328369140625);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 755, "type": "assignment", "detail": "声明float变量: _14020", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 756, "type": "arithmetic", "detail": "half _14029 = _9199 - (_9199 - _19481);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 756, "type": "assignment", "detail": "声明half变量: _14029", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 757, "type": "type_conversion", "detail": "half3(((float3(_19821)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 757, "type": "type_conversion", "detail": "float3(_19786)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 757, "type": "type_conversion", "detail": "float(clamp(((_14029 + _11768)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 757, "type": "type_conversion", "detail": "half(0.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 757, "type": "type_conversion", "detail": "half(1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 757, "type": "type_conversion", "detail": "half(0.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 757, "type": "type_conversion", "detail": "half(1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 757, "type": "function_call", "detail": "clamp(((_14029 + _11768)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 757, "type": "function_call", "detail": "clamp(((_19481 + _11764)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 757, "type": "mixed_precision", "detail": "half3 _14069 = half3(((float3(_19821) * float3(_19786)) * _14176) * float(clamp(((_14029 + _11768) / _11777) * _11777, half(0.0), half(1.0)) * clamp(((_19481 + _11764) / _11790) * _11790, half(0.0), half(1.0))));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 757, "type": "assignment", "detail": "声明half变量: _14069", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 758, "type": "type_conversion", "detail": "half3(fast::normalize(_19751 + _19716)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 758, "type": "type_conversion", "detail": "half(0.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 758, "type": "type_conversion", "detail": "half(1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 758, "type": "function_call", "detail": "clamp(dot(half3(fast::normalize(_19751 + _19716)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 758, "type": "arithmetic", "detail": "half _14084 = clamp(dot(half3(fast::normalize(_19751 + _19716)), _9064), half(0.0), half(1.0));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 758, "type": "assignment", "detail": "声明half变量: _14084", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 759, "type": "arithmetic", "detail": "float3 _19883;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 764, "type": "arithmetic", "detail": "float _14319 = _12049 / (((((_14202 * _12049) * _12049) - _14202) * _14202) + 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 764, "type": "assignment", "detail": "声明float变量: _14319", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 765, "type": "function_call", "detail": "fast::min(1000.0, (_14319 * _14319)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 765, "type": "function_call", "detail": "fast::max((_13993 + sqrt((_13993 * (_13993 - (_13993 * _12080)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 765, "type": "function_call", "detail": "sqrt((_14176 * (_14176 - (_14176 * _12080)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 770, "type": "type_conversion", "detail": "float3(0.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 770, "type": "arithmetic", "detail": "_19883 = float3(0.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 783, "type": "type_conversion", "detail": "float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_14084 * _14084)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 783, "type": "type_conversion", "detail": "float(half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 783, "type": "type_conversion", "detail": "half(dot(_19716, _19751)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 783, "type": "type_conversion", "detail": "float4(0.0795769989490509033203125)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float4"}, {"line": 783, "type": "type_conversion", "detail": "half3(half(fast::clamp((_14020 * _14020)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 783, "type": "type_conversion", "detail": "float(dot(_14069, _8303)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 783, "type": "type_conversion", "detail": "float3(_19573)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 783, "type": "type_conversion", "detail": "float3(_14069)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 783, "type": "type_conversion", "detail": "float(_14029)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 783, "type": "function_call", "detail": "mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_14084 * _14084)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 783, "type": "function_call", "detail": "powr(max(half4(half(9.9956989288330078125e-05)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 783, "type": "function_call", "detail": "dot(_19716, _19751)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 783, "type": "function_call", "detail": "fast::clamp((_14020 * _14020)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 783, "type": "function_call", "detail": "dot(_14069, _8303)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 783, "type": "mixed_precision", "detail": "_19965 = (_18431 + (float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_14084 * _14084)), 0.0078125))), _11858)) * 0.15915493667125701904296875) * float(half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05)), _11973 - (_11975 * (-half(dot(_19716, _19751))))), _11982)) * float4(0.0795769989490509033203125)).x))), half3(half(fast::clamp((_14020 * _14020) * float(dot(_14069, _8303)), 0.0, 1.0)))) * _14069) * float3(_19573))) + (((float3(_14069) * _19883) * _19573) * float(_14029));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 786, "type": "type_conversion", "detail": "half3(half(_Block1.EnvInfo.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 786, "type": "arithmetic", "detail": "half3 _8560 = (_9179 + (((_9179 + (_11812 * _11772)) * _9254) * half3(half(_Block1.EnvInfo.z)))) + _18429;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 786, "type": "assignment", "detail": "声明half变量: _8560", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 787, "type": "arithmetic", "detail": "float3 _8565 = (_9698 + _8521) + _18431;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 787, "type": "assignment", "detail": "声明float变量: _8565", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 789, "type": "arithmetic", "detail": "float3 _18989;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 790, "type": "arithmetic", "detail": "half3 _19028;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 794, "type": "arithmetic", "detail": "half3 _18832;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 795, "type": "arithmetic", "detail": "half3 _18859;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 800, "type": "function_call", "detail": "fast::clamp((_Block1.CameraInfo.y * in.IN_LinearZ)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 800, "type": "function_call", "detail": "fast::max(_Block1.SHGIParam2.x, 0.001000000047497451305389404296875)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 800, "type": "arithmetic", "detail": "float _14482 = fast::clamp((_Block1.CameraInfo.y * in.IN_LinearZ) / fast::max(_Block1.SHGIParam2.x, 0.001000000047497451305389404296875), 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 800, "type": "assignment", "detail": "声明float变量: _14482", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 801, "type": "arithmetic", "detail": "half3 _18824;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 802, "type": "arithmetic", "detail": "half3 _18826;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 805, "type": "type_conversion", "detail": "float3(0.125)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 805, "type": "function_call", "detail": "select(_Block1.PlayerPos.xyz, _Block1.CameraPos.xyz, bool3((_8397 & 524288u)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 805, "type": "arithmetic", "detail": "float3 _14684 = select(_Block1.PlayerPos.xyz, _Block1.CameraPos.xyz, bool3((_8397 & 524288u) > 0u)) * float3(0.125);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 805, "type": "assignment", "detail": "声明float变量: _14684", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 806, "type": "arithmetic", "detail": "float _14692 = _14684.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 806, "type": "assignment", "detail": "声明float变量: _14692", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 807, "type": "function_call", "detail": "floor(_14692)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 807, "type": "arithmetic", "detail": "float _14694 = floor(_14692);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 807, "type": "assignment", "detail": "声明float变量: _14694", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 808, "type": "arithmetic", "detail": "float3 _21212;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 810, "type": "arithmetic", "detail": "float3 _21268;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 813, "type": "arithmetic", "detail": "float3 _21215 = _21212;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 813, "type": "assignment", "detail": "声明float变量: _21215", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 821, "type": "arithmetic", "detail": "float _21034 = _14684.y;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 821, "type": "assignment", "detail": "声明float变量: _21034", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 822, "type": "function_call", "detail": "floor(_21034)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 822, "type": "arithmetic", "detail": "float _21035 = floor(_21034);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 822, "type": "assignment", "detail": "声明float变量: _21035", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 823, "type": "arithmetic", "detail": "float3 _21219 = _21268;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 823, "type": "assignment", "detail": "声明float变量: _21219", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 825, "type": "arithmetic", "detail": "float3 _21269;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 828, "type": "arithmetic", "detail": "float3 _21222 = _21219;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 828, "type": "assignment", "detail": "声明float变量: _21222", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 836, "type": "arithmetic", "detail": "float _21056 = _14684.z;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 836, "type": "assignment", "detail": "声明float变量: _21056", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 837, "type": "function_call", "detail": "floor(_21056)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 837, "type": "arithmetic", "detail": "float _21057 = floor(_21056);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 837, "type": "assignment", "detail": "声明float变量: _21057", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 838, "type": "arithmetic", "detail": "float3 _21226 = _21269;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 838, "type": "assignment", "detail": "声明float变量: _21226", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 840, "type": "arithmetic", "detail": "float3 _21270;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 843, "type": "arithmetic", "detail": "float3 _21229 = _21226;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 843, "type": "assignment", "detail": "声明float变量: _21229", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 851, "type": "arithmetic", "detail": "float3 _14717 = _21270 * 8.0;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 851, "type": "assignment", "detail": "声明float变量: _14717", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 852, "type": "arithmetic", "detail": "half3 _18825;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 853, "type": "arithmetic", "detail": "half3 _18827;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 854, "type": "type_conversion", "detail": "float3(240.0, 128.0, 240.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 854, "type": "arithmetic", "detail": "if (all(in.IN_WorldPosition.xyz >= _14717) && all(in.IN_WorldPosition.xyz < (_14717 + float3(240.0, 128.0, 240.0))))", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 856, "type": "type_conversion", "detail": "float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 856, "type": "arithmetic", "detail": "float3 _14534 = (in.IN_WorldPosition.xyz + (_9109 * 0.100000001490116119384765625)) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 856, "type": "assignment", "detail": "声明float变量: _14534", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 857, "type": "function_call", "detail": "floor(_14534)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 857, "type": "arithmetic", "detail": "float3 _14747 = _14534 - floor(_14534);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 857, "type": "assignment", "detail": "声明float变量: _14747", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 858, "type": "type_conversion", "detail": "half3(_9109)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 858, "type": "arithmetic", "detail": "half3 _14556 = half3(_9109);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 858, "type": "assignment", "detail": "声明half变量: _14556", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 859, "type": "type_conversion", "detail": "float((_8397 & 15728640u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 859, "type": "arithmetic", "detail": "float _14788 = float((_8397 & 15728640u) >> 20u);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 859, "type": "assignment", "detail": "声明float变量: _14788", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 860, "type": "type_conversion", "detail": "float(_14451)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 860, "type": "arithmetic", "detail": "float _14797 = float(_14451);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 860, "type": "assignment", "detail": "声明float变量: _14797", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 861, "type": "arithmetic", "detail": "float _14827 = (_14788 - _14797) + ((3.0 - _14797) * 3.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 861, "type": "assignment", "detail": "声明float变量: _14827", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 862, "type": "arithmetic", "detail": "float _14831 = _14827 + 1.0;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 862, "type": "assignment", "detail": "声明float变量: _14831", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 863, "type": "arithmetic", "detail": "float _14833 = _14827 + 2.0;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 863, "type": "assignment", "detail": "声明float变量: _14833", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 864, "type": "arithmetic", "detail": "half3 _18806;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 865, "type": "arithmetic", "detail": "half3 _18818;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 868, "type": "type_conversion", "detail": "float((_8397 & 251658240u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 868, "type": "type_conversion", "detail": "float(_14469)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 868, "type": "arithmetic", "detail": "float _14850 = (_14788 - float((_8397 & 251658240u) >> 24u)) + ((3.0 - float(_14469)) * 3.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 868, "type": "assignment", "detail": "声明float变量: _14850", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 869, "type": "arithmetic", "detail": "float _14854 = _14850 + 1.0;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 869, "type": "assignment", "detail": "声明float变量: _14854", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 870, "type": "arithmetic", "detail": "float _14856 = _14850 + 2.0;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 870, "type": "assignment", "detail": "声明float变量: _14856", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 871, "type": "type_conversion", "detail": "float2(0.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 871, "type": "type_conversion", "detail": "float2(0.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 871, "type": "arithmetic", "detail": "float2 _14956 = ((_14747.xz - float2(0.5)) * 0.9375) + float2(0.5);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 871, "type": "assignment", "detail": "声明float变量: _14956", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 872, "type": "arithmetic", "detail": "float _14963 = _14717.x * 0.0041666668839752674102783203125;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 872, "type": "assignment", "detail": "声明float变量: _14963", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 873, "type": "function_call", "detail": "floor(_14963)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 873, "type": "arithmetic", "detail": "float _14967 = ((_14963 - floor(_14963)) - 0.5) * 0.9375;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 873, "type": "assignment", "detail": "声明float变量: _14967", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 874, "type": "arithmetic", "detail": "float _14973 = _14717.z * 0.0041666668839752674102783203125;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 874, "type": "assignment", "detail": "声明float变量: _14973", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 875, "type": "function_call", "detail": "floor(_14973)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 875, "type": "arithmetic", "detail": "float _14977 = ((_14973 - floor(_14973)) - 0.5) * 0.9375;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 875, "type": "assignment", "detail": "声明float变量: _14977", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 876, "type": "arithmetic", "detail": "float _14982 = _14956.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 876, "type": "assignment", "detail": "声明float变量: _14982", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 877, "type": "arithmetic", "detail": "float3 _18095;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 878, "type": "function_call", "detail": "fast::min(_14982, _14967 + 0.49609375)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 878, "type": "function_call", "detail": "fast::max(_14982, _14967 + 0.50390625)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 879, "type": "arithmetic", "detail": "float _15000 = _14956.y;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 879, "type": "assignment", "detail": "声明float变量: _15000", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 880, "type": "function_call", "detail": "fast::min(_15000, _14977 + 0.49609375)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 880, "type": "function_call", "detail": "fast::max(_15000, _14977 + 0.50390625)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 881, "type": "arithmetic", "detail": "float _15021 = (_14747.y * 64.0) - 0.5;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 881, "type": "assignment", "detail": "声明float变量: _15021", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 882, "type": "function_call", "detail": "floor(_15021)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 882, "type": "arithmetic", "detail": "float _15026 = floor(_15021);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 882, "type": "assignment", "detail": "声明float变量: _15026", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 886, "type": "type_conversion", "detail": "float2(float(_15029 & 7u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 886, "type": "type_conversion", "detail": "float(_15029 >> 3u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 886, "type": "arithmetic", "detail": "float2 _15059 = (float2(float(_15029 & 7u), float(_15029 >> 3u)) + _18095.xz) * 0.125;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 886, "type": "assignment", "detail": "声明float变量: _15059", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 887, "type": "type_conversion", "detail": "float2(float(_21309 & 7u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 887, "type": "type_conversion", "detail": "float(_21309 >> 3u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 887, "type": "arithmetic", "detail": "float2 _15074 = (float2(float(_21309 & 7u), float(_21309 >> 3u)) + _18095.xz) * 0.125;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 887, "type": "assignment", "detail": "声明float变量: _15074", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 888, "type": "arithmetic", "detail": "float _15078 = _15059.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 888, "type": "assignment", "detail": "声明float变量: _15078", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 889, "type": "type_conversion", "detail": "float3(_15078, _15059.y, _14827)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 889, "type": "arithmetic", "detail": "float3 _15080 = float3(_15078, _15059.y, _14827);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 889, "type": "assignment", "detail": "声明float变量: _15080", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 890, "type": "arithmetic", "detail": "half4 _18103;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 891, "type": "type_conversion", "detail": "half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15080.xy, uint(rint(_15080.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 891, "type": "texture_sample", "detail": "sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15080.xy, uint(rint(_15080.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 891, "type": "arithmetic", "detail": "_18103.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15080.xy, uint(rint(_15080.z)), level(0.0)).x);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 892, "type": "type_conversion", "detail": "float3(_15078, _15059.y, _14850)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 892, "type": "arithmetic", "detail": "float3 _15092 = float3(_15078, _15059.y, _14850);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 892, "type": "assignment", "detail": "声明float变量: _15092", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 893, "type": "type_conversion", "detail": "half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15092.xy, uint(rint(_15092.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 893, "type": "texture_sample", "detail": "sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15092.xy, uint(rint(_15092.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 893, "type": "arithmetic", "detail": "half3 _15097 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15092.xy, uint(rint(_15092.z)), level(0.0)).xyz);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 893, "type": "assignment", "detail": "声明half变量: _15097", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 894, "type": "arithmetic", "detail": "float _15104 = _15074.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 894, "type": "assignment", "detail": "声明float变量: _15104", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 895, "type": "type_conversion", "detail": "float3(_15104, _15074.y, _14827)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 895, "type": "arithmetic", "detail": "float3 _15106 = float3(_15104, _15074.y, _14827);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 895, "type": "assignment", "detail": "声明float变量: _15106", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 896, "type": "arithmetic", "detail": "half4 _18105;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 897, "type": "type_conversion", "detail": "half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15106.xy, uint(rint(_15106.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 897, "type": "texture_sample", "detail": "sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15106.xy, uint(rint(_15106.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 897, "type": "arithmetic", "detail": "_18105.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15106.xy, uint(rint(_15106.z)), level(0.0)).x);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 898, "type": "type_conversion", "detail": "float3(_15104, _15074.y, _14850)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 898, "type": "arithmetic", "detail": "float3 _15118 = float3(_15104, _15074.y, _14850);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 898, "type": "assignment", "detail": "声明float变量: _15118", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 899, "type": "type_conversion", "detail": "half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15118.xy, uint(rint(_15118.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 899, "type": "texture_sample", "detail": "sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15118.xy, uint(rint(_15118.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 899, "type": "arithmetic", "detail": "half3 _15123 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15118.xy, uint(rint(_15118.z)), level(0.0)).xyz);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 899, "type": "assignment", "detail": "声明half变量: _15123", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 900, "type": "type_conversion", "detail": "half4(half(fast::clamp(_15021 - _15026, 0.0, 1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half4"}, {"line": 900, "type": "function_call", "detail": "fast::clamp(_15021 - _15026, 0.0, 1.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 900, "type": "arithmetic", "detail": "half4 _15132 = half4(half(fast::clamp(_15021 - _15026, 0.0, 1.0)));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 900, "type": "assignment", "detail": "声明half变量: _15132", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 901, "type": "type_conversion", "detail": "half4(_15097.x, _15097.y, _15097.z, _18103.w)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half4"}, {"line": 901, "type": "type_conversion", "detail": "half4(_15123.x, _15123.y, _15123.z, _18105.w)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half4"}, {"line": 901, "type": "function_call", "detail": "mix(half4(_15097.x, _15097.y, _15097.z, _18103.w)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 901, "type": "arithmetic", "detail": "half4 _15133 = mix(half4(_15097.x, _15097.y, _15097.z, _18103.w), half4(_15123.x, _15123.y, _15123.z, _18105.w), _15132);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 901, "type": "assignment", "detail": "声明half变量: _15133", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 902, "type": "type_conversion", "detail": "float3(_15078, _15059.y, _14831)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 902, "type": "arithmetic", "detail": "float3 _15140 = float3(_15078, _15059.y, _14831);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 902, "type": "assignment", "detail": "声明float变量: _15140", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 903, "type": "arithmetic", "detail": "half4 _18107;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 904, "type": "type_conversion", "detail": "half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15140.xy, uint(rint(_15140.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 904, "type": "texture_sample", "detail": "sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15140.xy, uint(rint(_15140.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 904, "type": "arithmetic", "detail": "_18107.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15140.xy, uint(rint(_15140.z)), level(0.0)).x);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 905, "type": "type_conversion", "detail": "float3(_15078, _15059.y, _14854)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 905, "type": "arithmetic", "detail": "float3 _15152 = float3(_15078, _15059.y, _14854);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 905, "type": "assignment", "detail": "声明float变量: _15152", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 906, "type": "type_conversion", "detail": "half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15152.xy, uint(rint(_15152.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 906, "type": "texture_sample", "detail": "sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15152.xy, uint(rint(_15152.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 906, "type": "arithmetic", "detail": "half3 _15157 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15152.xy, uint(rint(_15152.z)), level(0.0)).xyz);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 906, "type": "assignment", "detail": "声明half变量: _15157", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 907, "type": "type_conversion", "detail": "float3(_15104, _15074.y, _14831)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 907, "type": "arithmetic", "detail": "float3 _15166 = float3(_15104, _15074.y, _14831);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 907, "type": "assignment", "detail": "声明float变量: _15166", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 908, "type": "arithmetic", "detail": "half4 _18109;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 909, "type": "type_conversion", "detail": "half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15166.xy, uint(rint(_15166.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 909, "type": "texture_sample", "detail": "sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15166.xy, uint(rint(_15166.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 909, "type": "arithmetic", "detail": "_18109.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15166.xy, uint(rint(_15166.z)), level(0.0)).x);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 910, "type": "type_conversion", "detail": "float3(_15104, _15074.y, _14854)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 910, "type": "arithmetic", "detail": "float3 _15178 = float3(_15104, _15074.y, _14854);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 910, "type": "assignment", "detail": "声明float变量: _15178", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 911, "type": "type_conversion", "detail": "half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15178.xy, uint(rint(_15178.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 911, "type": "texture_sample", "detail": "sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15178.xy, uint(rint(_15178.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 911, "type": "arithmetic", "detail": "half3 _15183 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15178.xy, uint(rint(_15178.z)), level(0.0)).xyz);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 911, "type": "assignment", "detail": "声明half变量: _15183", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 912, "type": "type_conversion", "detail": "half4(_15157.x, _15157.y, _15157.z, _18107.w)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half4"}, {"line": 912, "type": "type_conversion", "detail": "half4(_15183.x, _15183.y, _15183.z, _18109.w)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half4"}, {"line": 912, "type": "function_call", "detail": "mix(half4(_15157.x, _15157.y, _15157.z, _18107.w)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 912, "type": "arithmetic", "detail": "half4 _15193 = mix(half4(_15157.x, _15157.y, _15157.z, _18107.w), half4(_15183.x, _15183.y, _15183.z, _18109.w), _15132);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 912, "type": "assignment", "detail": "声明half变量: _15193", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 913, "type": "type_conversion", "detail": "float3(_15078, _15059.y, _14833)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 913, "type": "arithmetic", "detail": "float3 _15200 = float3(_15078, _15059.y, _14833);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 913, "type": "assignment", "detail": "声明float变量: _15200", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 914, "type": "arithmetic", "detail": "half4 _18111;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 915, "type": "type_conversion", "detail": "half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15200.xy, uint(rint(_15200.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 915, "type": "texture_sample", "detail": "sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15200.xy, uint(rint(_15200.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 915, "type": "arithmetic", "detail": "_18111.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15200.xy, uint(rint(_15200.z)), level(0.0)).x);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 916, "type": "type_conversion", "detail": "float3(_15078, _15059.y, _14856)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 916, "type": "arithmetic", "detail": "float3 _15212 = float3(_15078, _15059.y, _14856);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 916, "type": "assignment", "detail": "声明float变量: _15212", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 917, "type": "type_conversion", "detail": "half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15212.xy, uint(rint(_15212.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 917, "type": "texture_sample", "detail": "sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15212.xy, uint(rint(_15212.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 917, "type": "arithmetic", "detail": "half3 _15217 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15212.xy, uint(rint(_15212.z)), level(0.0)).xyz);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 917, "type": "assignment", "detail": "声明half变量: _15217", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 918, "type": "type_conversion", "detail": "float3(_15104, _15074.y, _14833)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 918, "type": "arithmetic", "detail": "float3 _15226 = float3(_15104, _15074.y, _14833);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 918, "type": "assignment", "detail": "声明float变量: _15226", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 919, "type": "arithmetic", "detail": "half4 _18113;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 920, "type": "type_conversion", "detail": "half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15226.xy, uint(rint(_15226.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 920, "type": "texture_sample", "detail": "sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15226.xy, uint(rint(_15226.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 920, "type": "arithmetic", "detail": "_18113.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15226.xy, uint(rint(_15226.z)), level(0.0)).x);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 921, "type": "type_conversion", "detail": "float3(_15104, _15074.y, _14856)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 921, "type": "arithmetic", "detail": "float3 _15238 = float3(_15104, _15074.y, _14856);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 921, "type": "assignment", "detail": "声明float变量: _15238", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 922, "type": "type_conversion", "detail": "half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15238.xy, uint(rint(_15238.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 922, "type": "texture_sample", "detail": "sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15238.xy, uint(rint(_15238.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 922, "type": "arithmetic", "detail": "half3 _15243 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15238.xy, uint(rint(_15238.z)), level(0.0)).xyz);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 922, "type": "assignment", "detail": "声明half变量: _15243", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 923, "type": "type_conversion", "detail": "half4(_15217.x, _15217.y, _15217.z, _18111.w)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half4"}, {"line": 923, "type": "type_conversion", "detail": "half4(_15243.x, _15243.y, _15243.z, _18113.w)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half4"}, {"line": 923, "type": "function_call", "detail": "mix(half4(_15217.x, _15217.y, _15217.z, _18111.w)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 923, "type": "arithmetic", "detail": "half4 _15253 = mix(half4(_15217.x, _15217.y, _15217.z, _18111.w), half4(_15243.x, _15243.y, _15243.z, _18113.w), _15132);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 923, "type": "assignment", "detail": "声明half变量: _15253", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 924, "type": "type_conversion", "detail": "half(32.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 924, "type": "arithmetic", "detail": "half _15255 = half(32.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 924, "type": "assignment", "detail": "声明half变量: _15255", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 925, "type": "arithmetic", "detail": "half _15258 = _15133.w * _15255;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 925, "type": "assignment", "detail": "声明half变量: _15258", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 926, "type": "arithmetic", "detail": "half _15262 = _15193.w * _15255;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 926, "type": "assignment", "detail": "声明half变量: _15262", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 927, "type": "arithmetic", "detail": "half _15266 = _15253.w * _15255;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 927, "type": "assignment", "detail": "声明half变量: _15266", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 928, "type": "type_conversion", "detail": "half3(((float3(_15133.xyz)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 928, "type": "type_conversion", "detail": "float3(2.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 928, "type": "type_conversion", "detail": "float3(1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 928, "type": "type_conversion", "detail": "float(_15258)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 928, "type": "mixed_precision", "detail": "half3 _15343 = half3(((float3(_15133.xyz) * float3(2.0)) - float3(1.0)) * float(_15258)).xyz;", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 928, "type": "assignment", "detail": "声明half变量: _15343", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 929, "type": "arithmetic", "detail": "half3 _18130;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 930, "type": "type_conversion", "detail": "half(float(dot(_14556, _15343)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 930, "type": "function_call", "detail": "dot(_14556, _15343)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 930, "type": "mixed_precision", "detail": "_18130.x = half(float(dot(_14556, _15343)) * 2.0);", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 931, "type": "type_conversion", "detail": "half3(((float3(_15193.xyz)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 931, "type": "type_conversion", "detail": "float3(2.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 931, "type": "type_conversion", "detail": "float3(1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 931, "type": "type_conversion", "detail": "float(_15262)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 931, "type": "mixed_precision", "detail": "half3 _15352 = half3(((float3(_15193.xyz) * float3(2.0)) - float3(1.0)) * float(_15262)).xyz;", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 931, "type": "assignment", "detail": "声明half变量: _15352", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 932, "type": "type_conversion", "detail": "half(float(dot(_14556, _15352)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 932, "type": "function_call", "detail": "dot(_14556, _15352)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 932, "type": "mixed_precision", "detail": "_18130.y = half(float(dot(_14556, _15352)) * 2.0);", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 933, "type": "type_conversion", "detail": "half3(((float3(_15253.xyz)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 933, "type": "type_conversion", "detail": "float3(2.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 933, "type": "type_conversion", "detail": "float3(1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 933, "type": "type_conversion", "detail": "float(_15266)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 933, "type": "mixed_precision", "detail": "half3 _15361 = half3(((float3(_15253.xyz) * float3(2.0)) - float3(1.0)) * float(_15266)).xyz;", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 933, "type": "assignment", "detail": "声明half变量: _15361", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 934, "type": "type_conversion", "detail": "half(float(dot(_14556, _15361)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 934, "type": "function_call", "detail": "dot(_14556, _15361)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 934, "type": "mixed_precision", "detail": "_18130.z = half(float(dot(_14556, _15361)) * 2.0);", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 935, "type": "arithmetic", "detail": "half3 _18819;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 938, "type": "type_conversion", "detail": "half3(((float3(_15343)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 938, "type": "type_conversion", "detail": "float3(0.21199999749660491943359375)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 938, "type": "type_conversion", "detail": "float3(_15352)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 938, "type": "type_conversion", "detail": "float3(0.714999973773956298828125)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 938, "type": "type_conversion", "detail": "float3(_15361)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 938, "type": "type_conversion", "detail": "float3(0.0719999969005584716796875)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 938, "type": "mixed_precision", "detail": "_18819 = half3(((float3(_15343) * float3(0.21199999749660491943359375)) + (float3(_15352) * float3(0.714999973773956298828125))) + (float3(_15361) * float3(0.0719999969005584716796875)));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 945, "type": "type_conversion", "detail": "half3(_15258, _15262, _15266)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 945, "type": "type_conversion", "detail": "half(mix(_Block1.SHGIParam2.z, 1.0, _14482)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 945, "type": "function_call", "detail": "max(half3(_15258, _15262, _15266)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 945, "type": "function_call", "detail": "mix(_Block1.SHGIParam2.z, 1.0, _14482)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 945, "type": "arithmetic", "detail": "_18806 = max(half3(_15258, _15262, _15266) + (_18130 * half(mix(_Block1.SHGIParam2.z, 1.0, _14482))), _8393);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 949, "type": "type_conversion", "detail": "float2(0.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 949, "type": "type_conversion", "detail": "float2(0.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 949, "type": "arithmetic", "detail": "float2 _15427 = ((_14747.xz - float2(0.5)) * 0.9375) + float2(0.5);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 949, "type": "assignment", "detail": "声明float变量: _15427", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 950, "type": "arithmetic", "detail": "float _15434 = _14717.x * 0.0041666668839752674102783203125;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 950, "type": "assignment", "detail": "声明float变量: _15434", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 951, "type": "function_call", "detail": "floor(_15434)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 951, "type": "arithmetic", "detail": "float _15438 = ((_15434 - floor(_15434)) - 0.5) * 0.9375;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 951, "type": "assignment", "detail": "声明float变量: _15438", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 952, "type": "arithmetic", "detail": "float _15444 = _14717.z * 0.0041666668839752674102783203125;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 952, "type": "assignment", "detail": "声明float变量: _15444", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 953, "type": "function_call", "detail": "floor(_15444)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 953, "type": "arithmetic", "detail": "float _15448 = ((_15444 - floor(_15444)) - 0.5) * 0.9375;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 953, "type": "assignment", "detail": "声明float变量: _15448", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 954, "type": "arithmetic", "detail": "float _15453 = _15427.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 954, "type": "assignment", "detail": "声明float变量: _15453", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 955, "type": "arithmetic", "detail": "float3 _18143;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 956, "type": "function_call", "detail": "fast::min(_15453, _15438 + 0.49609375)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 956, "type": "function_call", "detail": "fast::max(_15453, _15438 + 0.50390625)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 957, "type": "arithmetic", "detail": "float _15471 = _15427.y;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 957, "type": "assignment", "detail": "声明float变量: _15471", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 958, "type": "function_call", "detail": "fast::min(_15471, _15448 + 0.49609375)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 958, "type": "function_call", "detail": "fast::max(_15471, _15448 + 0.50390625)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 959, "type": "arithmetic", "detail": "float _15492 = (_14747.y * 64.0) - 0.5;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 959, "type": "assignment", "detail": "声明float变量: _15492", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 960, "type": "function_call", "detail": "floor(_15492)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 960, "type": "arithmetic", "detail": "float _15497 = floor(_15492);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 960, "type": "assignment", "detail": "声明float变量: _15497", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 964, "type": "type_conversion", "detail": "float2(float(_15500 & 7u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 964, "type": "type_conversion", "detail": "float(_15500 >> 3u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 964, "type": "arithmetic", "detail": "float2 _15530 = (float2(float(_15500 & 7u), float(_15500 >> 3u)) + _18143.xz) * 0.125;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 964, "type": "assignment", "detail": "声明float变量: _15530", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 965, "type": "type_conversion", "detail": "float2(float(_21308 & 7u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 965, "type": "type_conversion", "detail": "float(_21308 >> 3u)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 965, "type": "arithmetic", "detail": "float2 _15545 = (float2(float(_21308 & 7u), float(_21308 >> 3u)) + _18143.xz) * 0.125;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 965, "type": "assignment", "detail": "声明float变量: _15545", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 966, "type": "arithmetic", "detail": "float _15549 = _15530.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 966, "type": "assignment", "detail": "声明float变量: _15549", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 967, "type": "type_conversion", "detail": "float3(_15549, _15530.y, _14827)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 967, "type": "arithmetic", "detail": "float3 _15551 = float3(_15549, _15530.y, _14827);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 967, "type": "assignment", "detail": "声明float变量: _15551", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 968, "type": "arithmetic", "detail": "half3 _18151;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 969, "type": "type_conversion", "detail": "half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15551.xy, uint(rint(_15551.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 969, "type": "texture_sample", "detail": "sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15551.xy, uint(rint(_15551.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 969, "type": "arithmetic", "detail": "_18151.x = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15551.xy, uint(rint(_15551.z)), level(0.0)).x);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 970, "type": "arithmetic", "detail": "float _15561 = _15545.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 970, "type": "assignment", "detail": "声明float变量: _15561", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 971, "type": "type_conversion", "detail": "float3(_15561, _15545.y, _14827)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 971, "type": "arithmetic", "detail": "float3 _15563 = float3(_15561, _15545.y, _14827);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 971, "type": "assignment", "detail": "声明float变量: _15563", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 972, "type": "arithmetic", "detail": "half3 _18153;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 973, "type": "type_conversion", "detail": "half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15563.xy, uint(rint(_15563.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 973, "type": "texture_sample", "detail": "sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15563.xy, uint(rint(_15563.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 973, "type": "arithmetic", "detail": "_18153.x = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15563.xy, uint(rint(_15563.z)), level(0.0)).x);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 974, "type": "type_conversion", "detail": "float3(_15549, _15530.y, _14831)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 974, "type": "arithmetic", "detail": "float3 _15575 = float3(_15549, _15530.y, _14831);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 974, "type": "assignment", "detail": "声明float变量: _15575", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 975, "type": "type_conversion", "detail": "half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15575.xy, uint(rint(_15575.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 975, "type": "texture_sample", "detail": "sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15575.xy, uint(rint(_15575.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 975, "type": "arithmetic", "detail": "_18151.y = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15575.xy, uint(rint(_15575.z)), level(0.0)).x);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 976, "type": "type_conversion", "detail": "float3(_15561, _15545.y, _14831)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 976, "type": "arithmetic", "detail": "float3 _15587 = float3(_15561, _15545.y, _14831);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 976, "type": "assignment", "detail": "声明float变量: _15587", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 977, "type": "type_conversion", "detail": "half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15587.xy, uint(rint(_15587.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 977, "type": "texture_sample", "detail": "sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15587.xy, uint(rint(_15587.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 977, "type": "arithmetic", "detail": "_18153.y = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15587.xy, uint(rint(_15587.z)), level(0.0)).x);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 978, "type": "type_conversion", "detail": "float3(_15549, _15530.y, _14833)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 978, "type": "arithmetic", "detail": "float3 _15599 = float3(_15549, _15530.y, _14833);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 978, "type": "assignment", "detail": "声明float变量: _15599", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 979, "type": "type_conversion", "detail": "half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15599.xy, uint(rint(_15599.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 979, "type": "texture_sample", "detail": "sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15599.xy, uint(rint(_15599.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 979, "type": "arithmetic", "detail": "_18151.z = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15599.xy, uint(rint(_15599.z)), level(0.0)).x);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 980, "type": "type_conversion", "detail": "float3(_15561, _15545.y, _14833)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 980, "type": "arithmetic", "detail": "float3 _15611 = float3(_15561, _15545.y, _14833);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 980, "type": "assignment", "detail": "声明float变量: _15611", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 981, "type": "type_conversion", "detail": "half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15611.xy, uint(rint(_15611.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 981, "type": "texture_sample", "detail": "sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15611.xy, uint(rint(_15611.z)", "precision_conversion": null, "performance_impact": "high", "output_type": "half4"}, {"line": 981, "type": "arithmetic", "detail": "_18153.z = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15611.xy, uint(rint(_15611.z)), level(0.0)).x);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 982, "type": "type_conversion", "detail": "half3(half(fast::clamp(_15492 - _15497, 0.0, 1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 982, "type": "function_call", "detail": "mix(_18151, _18153, half3(half(fast::clamp(_15492 - _15497, 0.0, 1.0)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 982, "type": "arithmetic", "detail": "half3 _15622 = mix(_18151, _18153, half3(half(fast::clamp(_15492 - _15497, 0.0, 1.0))));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 982, "type": "assignment", "detail": "声明half变量: _15622", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 983, "type": "type_conversion", "detail": "half(32.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 983, "type": "arithmetic", "detail": "half _15623 = half(32.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 983, "type": "assignment", "detail": "声明half变量: _15623", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 984, "type": "arithmetic", "detail": "half3 _18164;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 991, "type": "type_conversion", "detail": "float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 991, "type": "type_conversion", "detail": "float3(1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 991, "type": "arithmetic", "detail": "float3 _15658 = (((in.IN_WorldPosition.xyz - _14717) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)) * 2.0) - float3(1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 991, "type": "assignment", "detail": "声明float变量: _15658", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 992, "type": "arithmetic", "detail": "float3 _15661 = _15658 * _15658;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 992, "type": "assignment", "detail": "声明float变量: _15661", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 993, "type": "arithmetic", "detail": "float3 _15664 = _15661 * _15661;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 993, "type": "assignment", "detail": "声明float变量: _15664", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 994, "type": "arithmetic", "detail": "half3 _18822;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 995, "type": "arithmetic", "detail": "half3 _18823;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 996, "type": "function_call", "detail": "max(int(_14451)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 998, "type": "type_conversion", "detail": "half3(half(1.0 - fast::clamp(fast::max(_15664.x, fast::max(_15664.y, _15664.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 998, "type": "function_call", "detail": "fast::clamp(fast::max(_15664.x, fast::max(_15664.y, _15664.z)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 998, "type": "arithmetic", "detail": "half3 _14921 = half3(half(1.0 - fast::clamp(fast::max(_15664.x, fast::max(_15664.y, _15664.z)), 0.0, 1.0)));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 998, "type": "assignment", "detail": "声明half变量: _14921", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 1023, "type": "type_conversion", "detail": "half3(float3(0.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 1023, "type": "mixed_precision", "detail": "half3 _14565 = half3(float3(0.0));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 1023, "type": "assignment", "detail": "声明half变量: _14565", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 1024, "type": "arithmetic", "detail": "half3 _18830;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1027, "type": "type_conversion", "detail": "float3(_18824)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1027, "type": "arithmetic", "detail": "float3 _14569 = float3(_18824);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1027, "type": "assignment", "detail": "声明float变量: _14569", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 1028, "type": "type_conversion", "detail": "float(dot(_18826, _18826)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 1028, "type": "function_call", "detail": "dot(_18826, _18826)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1028, "type": "arithmetic", "detail": "float _14575 = float(dot(_18826, _18826));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1028, "type": "assignment", "detail": "声明float变量: _14575", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1029, "type": "arithmetic", "detail": "half3 _18831;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1030, "type": "type_conversion", "detail": "float(half(((0.21267099678516387939453125 * _14569.x)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 1030, "type": "mixed_precision", "detail": "if ((float(half(((0.21267099678516387939453125 * _14569.x) + (0.71516001224517822265625 * _14569.y)) + (0.072168998420238494873046875 * _14569.z))) > 0.001000000047497451305389404296875) && (_14575 > 9.9999999747524270787835121154785e-07))", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 1032, "type": "function_call", "detail": "fast::clamp(_9109.y, 0.0, 1.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1032, "type": "arithmetic", "detail": "float _14592 = fast::clamp(_9109.y, 0.0, 1.0) * 0.75;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1032, "type": "assignment", "detail": "声明float变量: _14592", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1033, "type": "type_conversion", "detail": "float3(_18826)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1033, "type": "type_conversion", "detail": "float3(sqrt(_14575)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1033, "type": "type_conversion", "detail": "float3(fast::clamp(1.0 - (_14592 * _14592)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1033, "type": "function_call", "detail": "sqrt(_14575)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1033, "type": "function_call", "detail": "fast::clamp(1.0 - (_14592 * _14592)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1033, "type": "arithmetic", "detail": "float3 _14600 = (float3(_18826) / float3(sqrt(_14575))) * float3(fast::clamp(1.0 - (_14592 * _14592), 0.0, 1.0));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1033, "type": "assignment", "detail": "声明float变量: _14600", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 1034, "type": "function_call", "detail": "mix(_11560, 1.0, 0.25)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1034, "type": "arithmetic", "detail": "float _14604 = mix(_11560, 1.0, 0.25);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1034, "type": "assignment", "detail": "声明float变量: _14604", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1035, "type": "function_call", "detail": "fast::clamp(dot(_9109, fast::normalize(_8373 + _14600)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1035, "type": "arithmetic", "detail": "float _14612 = fast::clamp(dot(_9109, fast::normalize(_8373 + _14600)), 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1035, "type": "assignment", "detail": "声明float变量: _14612", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1036, "type": "arithmetic", "detail": "float _15697 = _14604 * _14604;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1036, "type": "assignment", "detail": "声明float变量: _15697", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1037, "type": "arithmetic", "detail": "float _15710 = _15697 / (((((_14612 * _15697) * _15697) - _14612) * _14612) + 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1037, "type": "assignment", "detail": "声明float变量: _15710", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1038, "type": "type_conversion", "detail": "half3(float3(_10557 * _18824)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 1038, "type": "type_conversion", "detail": "float3((fast::min(1000.0, (_15710 * _15710)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1038, "type": "function_call", "detail": "fast::min(1000.0, (_15710 * _15710)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1038, "type": "function_call", "detail": "fast::clamp(dot(_9109, _14600)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1038, "type": "mixed_precision", "detail": "_18831 = half3(float3(_10557 * _18824) * float3((fast::min(1000.0, (_15710 * _15710) * 0.3183098733425140380859375) * fast::clamp(dot(_9109, _14600), 0.0, 1.0)) * _Block1.SHGIParam.y));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 1050, "type": "type_conversion", "detail": "float(half(mix(_Block1.SHGIParam2.y, 1.0, _14482)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 1050, "type": "function_call", "detail": "mix(_Block1.SHGIParam2.y, 1.0, _14482)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1050, "type": "mixed_precision", "detail": "float _14641 = float(half(mix(_Block1.SHGIParam2.y, 1.0, _14482)));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 1050, "type": "assignment", "detail": "声明float变量: _14641", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1051, "type": "type_conversion", "detail": "half3(half(_Block1.SHGIParam.y * _14641)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 1051, "type": "arithmetic", "detail": "_18859 = _18830 * half3(half(_Block1.SHGIParam.y * _14641));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1052, "type": "type_conversion", "detail": "half3(half(_Block1.SHGIParam.x * _14641)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 1052, "type": "arithmetic", "detail": "_18832 = _18824 * half3(half(_Block1.SHGIParam.x * _14641));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1060, "type": "type_conversion", "detail": "float3(_18859 * _18329)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1060, "type": "arithmetic", "detail": "_18989 = _8565 + float3(_18859 * _18329);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1067, "type": "function_call", "detail": "fast::clamp(dot(_9109, fast::normalize(_8373 + _8373)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1067, "type": "arithmetic", "detail": "float _15792 = fast::clamp(dot(_9109, fast::normalize(_8373 + _8373)), 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1067, "type": "assignment", "detail": "声明float变量: _15792", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1068, "type": "arithmetic", "detail": "float _15839 = _12049 / (((((_15792 * _12049) * _12049) - _15792) * _15792) + 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1068, "type": "assignment", "detail": "声明float变量: _15839", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1069, "type": "type_conversion", "detail": "float(half(fast::clamp(_8378, 0.0, 1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 1069, "type": "function_call", "detail": "fast::clamp(_8378, 0.0, 1.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1069, "type": "mixed_precision", "detail": "float _15805 = float(half(fast::clamp(_8378, 0.0, 1.0)));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 1069, "type": "assignment", "detail": "声明float变量: _15805", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1070, "type": "arithmetic", "detail": "float _15854 = _12049 * 0.5;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1070, "type": "assignment", "detail": "声明float变量: _15854", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1071, "type": "arithmetic", "detail": "float _15858 = 1.0 - _15854;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1071, "type": "assignment", "detail": "声明float变量: _15858", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1072, "type": "arithmetic", "detail": "float _15861 = (_15805 * _15858) + _15854;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1072, "type": "assignment", "detail": "声明float变量: _15861", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1073, "type": "type_conversion", "detail": "half3(_12025)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 1073, "type": "arithmetic", "detail": "half3 _15813 = half3(_12025);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1073, "type": "assignment", "detail": "声明half变量: _15813", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 1074, "type": "type_conversion", "detail": "float3(_10569)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1074, "type": "arithmetic", "detail": "float3 _15762 = float3(_10569);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1074, "type": "assignment", "detail": "声明float变量: _15762", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 1075, "type": "arithmetic", "detail": "float3 _15920 = _Block1.cLocalVirtualLitPos.xyz + _Block1.cVirtualLitParam.xyz;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1075, "type": "assignment", "detail": "声明float变量: _15920", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 1076, "type": "type_conversion", "detail": "float3(0.0, 1.0, 0.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1076, "type": "type_conversion", "detail": "float3(0.0, _15920.y, 0.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1076, "type": "type_conversion", "detail": "float4(0.0, 0.0, 0.0, 1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float4"}, {"line": 1076, "type": "function_call", "detail": "fast::normalize(cross(_8373, float3(0.0, 1.0, 0.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1076, "type": "arithmetic", "detail": "float3 _15960 = ((((fast::normalize(cross(_8373, float3(0.0, 1.0, 0.0))) * _15920.x) + float3(0.0, _15920.y, 0.0)) + (_8373 * _15920.z)) + (float4(0.0, 0.0, 0.0, 1.0) * _Block1.World)) - in.IN_WorldPosition.xyz;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1076, "type": "assignment", "detail": "声明float变量: _15960", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 1077, "type": "type_conversion", "detail": "float3(step(0.0, _Block1.cLocalVirtualLitColor.w)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1077, "type": "function_call", "detail": "mix(_15960, fast::normalize(_15960)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1077, "type": "function_call", "detail": "step(0.0, _Block1.cLocalVirtualLitColor.w)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1077, "type": "arithmetic", "detail": "float3 _15970 = mix(_15960, fast::normalize(_15960), float3(step(0.0, _Block1.cLocalVirtualLitColor.w)));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1077, "type": "assignment", "detail": "声明float变量: _15970", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 1078, "type": "type_conversion", "detail": "half(dot(_15970, _15970)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1078, "type": "function_call", "detail": "dot(_15970, _15970)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1078, "type": "arithmetic", "detail": "half _15974 = half(dot(_15970, _15970));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1078, "type": "assignment", "detail": "声明half变量: _15974", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 1079, "type": "function_call", "detail": "fast::normalize(_15970)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1079, "type": "arithmetic", "detail": "float3 _15976 = fast::normalize(_15970);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1079, "type": "assignment", "detail": "声明float变量: _15976", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 1080, "type": "type_conversion", "detail": "half(1.0 / (_Block1.cLocalVirtualLitCustom.x * _Block1.cLocalVirtualLitCustom.x)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1080, "type": "arithmetic", "detail": "half _15979 = _15974 * half(1.0 / (_Block1.cLocalVirtualLitCustom.x * _Block1.cLocalVirtualLitCustom.x));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1080, "type": "assignment", "detail": "声明half变量: _15979", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 1081, "type": "type_conversion", "detail": "float(_15979 * _15979)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 1081, "type": "function_call", "detail": "fast::clamp(1.0 - float(_15979 * _15979)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1081, "type": "arithmetic", "detail": "float _15986 = fast::clamp(1.0 - float(_15979 * _15979), 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1081, "type": "assignment", "detail": "声明float变量: _15986", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1082, "type": "type_conversion", "detail": "half((fast::clamp(dot(_9109, _15976)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1082, "type": "function_call", "detail": "fast::clamp(dot(_9109, _15976)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1082, "type": "arithmetic", "detail": "half _16031 = half((fast::clamp(dot(_9109, _15976), 0.0, 1.0) * _Block1.cLocalVirtualLitCustom.z) + (1.0 - _Block1.cLocalVirtualLitCustom.z));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1082, "type": "assignment", "detail": "声明half变量: _16031", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 1083, "type": "function_call", "detail": "fast::clamp(dot(_9109, fast::normalize(_8373 + _15976)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1083, "type": "arithmetic", "detail": "float _16112 = fast::clamp(dot(_9109, fast::normalize(_8373 + _15976)), 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1083, "type": "assignment", "detail": "声明float变量: _16112", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1084, "type": "arithmetic", "detail": "float _16159 = _12049 / (((((_16112 * _12049) * _12049) - _16112) * _16112) + 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1084, "type": "assignment", "detail": "声明float变量: _16159", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1085, "type": "type_conversion", "detail": "float(_18329)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 1085, "type": "arithmetic", "detail": "float _8657 = float(_18329);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1085, "type": "assignment", "detail": "声明float变量: _8657", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1086, "type": "type_conversion", "detail": "half(fast::min(_8657, mix(abs(_Block1.cVisibilitySH[0].w)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1086, "type": "function_call", "detail": "fast::min(_8657, mix(abs(_Block1.cVisibilitySH[0].w)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1086, "type": "arithmetic", "detail": "half _8696 = half(fast::min(_8657, mix(abs(_Block1.cVisibilitySH[0].w), 1.0, _Block1.WorldProbeInfo.x)));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1086, "type": "assignment", "detail": "声明half变量: _8696", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 1087, "type": "type_conversion", "detail": "half(-1.023326873779296875)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1087, "type": "arithmetic", "detail": "half _16335 = half(-1.023326873779296875);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1087, "type": "assignment", "detail": "声明half变量: _16335", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 1088, "type": "type_conversion", "detail": "half(1.023326873779296875)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1088, "type": "arithmetic", "detail": "half _16336 = half(1.023326873779296875);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1088, "type": "assignment", "detail": "声明half变量: _16336", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 1089, "type": "arithmetic", "detail": "half _16342 = _9064.y;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1089, "type": "assignment", "detail": "声明half变量: _16342", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 1090, "type": "type_conversion", "detail": "half(-0.858085691928863525390625)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1090, "type": "arithmetic", "detail": "half _16351 = half(-0.858085691928863525390625);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1090, "type": "assignment", "detail": "声明half变量: _16351", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 1091, "type": "type_conversion", "detail": "half4(_16351, half(0.7431240081787109375)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half4"}, {"line": 1091, "type": "type_conversion", "detail": "half(0.4290428459644317626953125)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1091, "type": "arithmetic", "detail": "half4 _16356 = half4(_16351, half(0.7431240081787109375), _16351, half(0.4290428459644317626953125));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1091, "type": "assignment", "detail": "声明half变量: _16356", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 1092, "type": "arithmetic", "detail": "half _16361 = _9064.z;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1092, "type": "assignment", "detail": "声明half变量: _16361", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 1093, "type": "arithmetic", "detail": "half _16369 = _9064.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1093, "type": "assignment", "detail": "声明half变量: _16369", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 1094, "type": "type_conversion", "detail": "half4(_16342 * _16361, _16361 * _16361, _16369 * _16361, (_16369 * _16369)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half4"}, {"line": 1094, "type": "arithmetic", "detail": "half4 _16385 = _16356 * half4(_16342 * _16361, _16361 * _16361, _16369 * _16361, (_16369 * _16369) - (_16342 * _16342));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1094, "type": "assignment", "detail": "声明half变量: _16385", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 1095, "type": "type_conversion", "detail": "half(-0.2477079927921295166015625)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1095, "type": "arithmetic", "detail": "half _16387 = half(-0.2477079927921295166015625);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1095, "type": "assignment", "detail": "声明half变量: _16387", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 1097, "type": "type_conversion", "detail": "half3(_Block1.cSHCoefficients[0].xyz * float3(half3(half(0.886226952075958251953125)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 1097, "type": "mixed_precision", "detail": "half3 _16279 = half3(_Block1.cSHCoefficients[0].xyz * float3(half3(half(0.886226952075958251953125))));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 1097, "type": "assignment", "detail": "声明half变量: _16279", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 1098, "type": "type_conversion", "detail": "float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16342)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float4"}, {"line": 1098, "type": "mixed_precision", "detail": "float4 _16284 = float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16342))) * _9064.yzxx);", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 1098, "type": "assignment", "detail": "声明float变量: _16284", "precision_conversion": null, "performance_impact": "low", "output_type": "float4"}, {"line": 1099, "type": "type_conversion", "detail": "float4(_16385)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float4"}, {"line": 1099, "type": "arithmetic", "detail": "float4 _16306 = float4(_16385);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1099, "type": "assignment", "detail": "声明float变量: _16306", "precision_conversion": null, "performance_impact": "low", "output_type": "float4"}, {"line": 1100, "type": "type_conversion", "detail": "half3(float3(0.081409998238086700439453125, 0.74361002445220947265625, -0.66364002227783203125)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 1100, "type": "mixed_precision", "detail": "half3 _16397 = half3(float3(0.081409998238086700439453125, 0.74361002445220947265625, -0.66364002227783203125));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 1100, "type": "assignment", "detail": "声明half变量: _16397", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 1101, "type": "arithmetic", "detail": "half _16509 = _16397.y;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1101, "type": "assignment", "detail": "声明half变量: _16509", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 1102, "type": "arithmetic", "detail": "half _16528 = _16397.z;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1102, "type": "assignment", "detail": "声明half变量: _16528", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 1103, "type": "arithmetic", "detail": "half _16536 = _16397.x;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1103, "type": "assignment", "detail": "声明half变量: _16536", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 1104, "type": "type_conversion", "detail": "half4(_16509 * _16528, _16528 * _16528, _16536 * _16528, (_16536 * _16536)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half4"}, {"line": 1104, "type": "arithmetic", "detail": "half4 _16552 = _16356 * half4(_16509 * _16528, _16528 * _16528, _16536 * _16528, (_16536 * _16536) - (_16509 * _16509));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1104, "type": "assignment", "detail": "声明half变量: _16552", "precision_conversion": null, "performance_impact": "low", "output_type": "half4"}, {"line": 1106, "type": "type_conversion", "detail": "float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16509)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float4"}, {"line": 1106, "type": "mixed_precision", "detail": "float4 _16451 = float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16509))) * _16397.yzxx);", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 1106, "type": "assignment", "detail": "声明float变量: _16451", "precision_conversion": null, "performance_impact": "low", "output_type": "float4"}, {"line": 1107, "type": "type_conversion", "detail": "float4(_16552)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float4"}, {"line": 1107, "type": "arithmetic", "detail": "float4 _16473 = float4(_16552);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1107, "type": "assignment", "detail": "声明float变量: _16473", "precision_conversion": null, "performance_impact": "low", "output_type": "float4"}, {"line": 1108, "type": "type_conversion", "detail": "half3(half(dot(_Block1.cSHCoefficients[1], _16284)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 1108, "type": "type_conversion", "detail": "half(dot(_Block1.cSHCoefficients[3], _16284)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1108, "type": "type_conversion", "detail": "half(dot(_Block1.cSHCoefficients[5], _16284)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1108, "type": "type_conversion", "detail": "half3(half(dot(_Block1.cSHCoefficients[2], _16306)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 1108, "type": "type_conversion", "detail": "half(dot(_Block1.cSHCoefficients[4], _16306)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1108, "type": "type_conversion", "detail": "half(dot(_Block1.cSHCoefficients[6], _16306)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1108, "type": "type_conversion", "detail": "half3(half(0.699999988079071044921875 + (0.300000011920928955078125 * _Block1.EnvInfo.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 1108, "type": "type_conversion", "detail": "half3(half(float(_8295 * _18329)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 1108, "type": "type_conversion", "detail": "half3(half(_Block1.GIInfo.z)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 1108, "type": "type_conversion", "detail": "half3(float3(max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16451)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 1108, "type": "type_conversion", "detail": "half(dot(_Block1.cSHCoefficients[3], _16451)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1108, "type": "type_conversion", "detail": "half(dot(_Block1.cSHCoefficients[5], _16451)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1108, "type": "type_conversion", "detail": "half3(half(dot(_Block1.cSHCoefficients[2], _16473)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 1108, "type": "type_conversion", "detail": "half(dot(_Block1.cSHCoefficients[4], _16473)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1108, "type": "type_conversion", "detail": "half(dot(_Block1.cSHCoefficients[6], _16473)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1108, "type": "type_conversion", "detail": "float3(((3.1415927410125732421875 * float(clamp(dot(_9064, _16397)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1108, "type": "type_conversion", "detail": "half(0.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1108, "type": "type_conversion", "detail": "half(1.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1108, "type": "type_conversion", "detail": "float(half(float(_8295 * _18236)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 1108, "type": "type_conversion", "detail": "half(0.5)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1108, "type": "type_conversion", "detail": "half3(half(_Block1.cSHCoefficients[0].w)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 1108, "type": "function_call", "detail": "max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16284)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1108, "type": "function_call", "detail": "dot(_Block1.cSHCoefficients[3], _16284)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1108, "type": "function_call", "detail": "dot(_Block1.cSHCoefficients[5], _16284)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1108, "type": "function_call", "detail": "dot(_Block1.cSHCoefficients[2], _16306)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1108, "type": "function_call", "detail": "dot(_Block1.cSHCoefficients[4], _16306)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1108, "type": "function_call", "detail": "dot(_Block1.cSHCoefficients[6], _16306)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1108, "type": "function_call", "detail": "mix(1.0, _Block1.WorldProbeInfo.y, step(0.0, _Block1.cVisibilitySH[0].w)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1108, "type": "function_call", "detail": "max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16451)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1108, "type": "function_call", "detail": "dot(_Block1.cSHCoefficients[3], _16451)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1108, "type": "function_call", "detail": "dot(_Block1.cSHCoefficients[5], _16451)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1108, "type": "function_call", "detail": "dot(_Block1.cSHCoefficients[2], _16473)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1108, "type": "function_call", "detail": "dot(_Block1.cSHCoefficients[4], _16473)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1108, "type": "function_call", "detail": "dot(_Block1.cSHCoefficients[6], _16473)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1108, "type": "function_call", "detail": "clamp(dot(_9064, _16397)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1108, "type": "mixed_precision", "detail": "half3 _16258 = ((((max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16284)), half(dot(_Block1.cSHCoefficients[3], _16284)), half(dot(_Block1.cSHCoefficients[5], _16284))) + half3(half(dot(_Block1.cSHCoefficients[2], _16306)), half(dot(_Block1.cSHCoefficients[4], _16306)), half(dot(_Block1.cSHCoefficients[6], _16306)))), _9179) * half3(half(0.699999988079071044921875 + (0.300000011920928955078125 * _Block1.EnvInfo.z)))) * half3(half(float(_8295 * _18329) * mix(1.0, _Block1.WorldProbeInfo.y, step(0.0, _Block1.cVisibilitySH[0].w))))) * half3(half(_Block1.GIInfo.z))) + half3(float3(max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16451)), half(dot(_Block1.cSHCoefficients[3], _16451)), half(dot(_Block1.cSHCoefficients[5], _16451))) + half3(half(dot(_Block1.cSHCoefficients[2], _16473)), half(dot(_Block1.cSHCoefficients[4], _16473)), half(dot(_Block1.cSHCoefficients[6], _16473)))), _9179)) * float3(((3.1415927410125732421875 * float(clamp(dot(_9064, _16397), half(0.0), half(1.0)))) * _Block1.WorldProbeInfo.w) * float(half(float(_8295 * _18236)) * half(0.5))))) * half3(half(_Block1.cSHCoefficients[0].w));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 1108, "type": "assignment", "detail": "声明half变量: _16258", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 1109, "type": "type_conversion", "detail": "half3((float3(_15813 * (half(fast::min(1000.0, (_15839 * _15839)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 1109, "type": "type_conversion", "detail": "half(0.25 / fast::max(_15861 * _15861, _12108)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1109, "type": "type_conversion", "detail": "half3((_Block1.cVirtualLitColor.xyz * abs(_8378)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 1109, "type": "type_conversion", "detail": "half3(fast::min(float3(8192.0)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half3"}, {"line": 1109, "type": "type_conversion", "detail": "float3((_15813 * (half(fast::min(1000.0, (_16159 * _16159)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1109, "type": "type_conversion", "detail": "half(0.25 / fast::max(_15861 * ((float(_16031)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1109, "type": "type_conversion", "detail": "float3(_Block1.cLocalVirtualLitPos.w)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1109, "type": "type_conversion", "detail": "float3(_10569 * _16031)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1109, "type": "type_conversion", "detail": "float(half(fast::min(float(half((_15986 * _15986)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 1109, "type": "type_conversion", "detail": "float(_15974)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 1109, "type": "function_call", "detail": "fast::min(1000.0, (_15839 * _15839)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1109, "type": "function_call", "detail": "fast::max(_15861 * _15861, _12108)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1109, "type": "function_call", "detail": "abs(_8378)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1109, "type": "function_call", "detail": "fast::min(float3(8192.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1109, "type": "function_call", "detail": "fast::min(1000.0, (_16159 * _16159)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1109, "type": "function_call", "detail": "fast::max(_15861 * ((float(_16031)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1109, "type": "function_call", "detail": "fast::min(float(half((_15986 * _15986)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1109, "type": "function_call", "detail": "abs(_Block1.cLocalVirtualLitCustom.y)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1109, "type": "function_call", "detail": "abs(_Block1.cLocalVirtualLitColor.w)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1109, "type": "mixed_precision", "detail": "half3 _8776 = ((_18255 + (half3((float3(_15813 * (half(fast::min(1000.0, (_15839 * _15839) * 0.3183098733425140380859375)) * half(0.25 / fast::max(_15861 * _15861, _12108)))) * _Block1.cVirtualLitColor.xyz) * _15805) + half3((_Block1.cVirtualLitColor.xyz * abs(_8378)) * _15762))) + half3(fast::min(float3(8192.0), ((((float3((_15813 * (half(fast::min(1000.0, (_16159 * _16159) * 0.3183098733425140380859375)) * half(0.25 / fast::max(_15861 * ((float(_16031) * _15858) + _15854), _12108)))) * _16031) * float3(_Block1.cLocalVirtualLitPos.w)) + float3(_10569 * _16031)) * float(half(fast::min(float(half((_15986 * _15986) / ((float(_15974) * abs(_Block1.cLocalVirtualLitCustom.y)) + 9.9999997473787516355514526367188e-05))), _Block1.cLocalVirtualLitCustom.w)))) * (_Block1.cLocalVirtualLitColor.xyz * abs(_Block1.cLocalVirtualLitColor.w))) * _Block1.DiyLightingInfo.z))) * _8696;", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 1109, "type": "assignment", "detail": "声明half变量: _8776", "precision_conversion": null, "performance_impact": "low", "output_type": "half3"}, {"line": 1110, "type": "function_call", "detail": "length(_8370)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1110, "type": "arithmetic", "detail": "float _16622 = length(_8370);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1110, "type": "assignment", "detail": "声明float变量: _16622", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1111, "type": "arithmetic", "detail": "float _16645 = _8370.y;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1111, "type": "assignment", "detail": "声明float变量: _16645", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1112, "type": "type_conversion", "detail": "float((_9251 * half(9.9956989288330078125e-05)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 1112, "type": "type_conversion", "detail": "half(int(sign(_16645)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1112, "type": "mixed_precision", "detail": "float _16657 = (_16645 + _12108) + float((_9251 * half(9.9956989288330078125e-05)) * half(int(sign(_16645))));", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 1112, "type": "assignment", "detail": "声明float变量: _16657", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1113, "type": "type_conversion", "detail": "float2(0.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 1113, "type": "type_conversion", "detail": "float2(_Block1.FogInfo.w, exp((-_Block1.AerialPerspectiveMie.y)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 1113, "type": "type_conversion", "detail": "float2(1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 1113, "type": "type_conversion", "detail": "float2(_Block1.FogInfo.z, _Block1.AerialPerspectiveMie.y)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 1113, "type": "type_conversion", "detail": "float2(10.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 1113, "type": "type_conversion", "detail": "float2(_16657)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float2"}, {"line": 1113, "type": "function_call", "detail": "fast::max(float2(0.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1113, "type": "function_call", "detail": "exp((-_Block1.AerialPerspectiveMie.y)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1113, "type": "function_call", "detail": "exp(-fast::min(float2(_Block1.FogInfo.z, _Block1.AerialPerspectiveMie.y)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1113, "type": "arithmetic", "detail": "float2 _16682 = fast::max(float2(0.0), float2(_Block1.FogInfo.w, exp((-_Block1.AerialPerspectiveMie.y) * _Block1.CameraPos.y) * _Block1.AerialPerspectiveMie.z) * ((float2(1.0) - exp(-fast::min(float2(_Block1.FogInfo.z, _Block1.AerialPerspectiveMie.y) * _16657, float2(10.0)))) / float2(_16657)));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1113, "type": "assignment", "detail": "声明float变量: _16682", "precision_conversion": null, "performance_impact": "low", "output_type": "float2"}, {"line": 1114, "type": "type_conversion", "detail": "float3(_12108)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1114, "type": "function_call", "detail": "fast::max(float3(_12108)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1114, "type": "arithmetic", "detail": "float3 _16688 = fast::max(float3(_12108), _Block1.AerialPerspectiveExt.xyz);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1114, "type": "assignment", "detail": "声明float变量: _16688", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 1115, "type": "type_conversion", "detail": "float3(_8303)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1115, "type": "arithmetic", "detail": "float3 _16698 = float3(_8303);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1115, "type": "assignment", "detail": "声明float变量: _16698", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 1116, "type": "function_call", "detail": "exp(-(_16688 * ((_16622 * (_Block1.FogColor.w + ((1.0 - _Block1.FogColor.w)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1116, "type": "function_call", "detail": "fast::clamp(_16622 / _Block1.FogInfo.x, 0.0, 1.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1116, "type": "function_call", "detail": "dot(_16688, _16698)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1116, "type": "function_call", "detail": "fast::max(9.9999999747524270787835121154785e-07, _Block1.AerialPerspectiveRay.w * 0.0005000000237487256526947021484375)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1116, "type": "arithmetic", "detail": "float3 _16715 = exp(-(_16688 * ((_16622 * (_Block1.FogColor.w + ((1.0 - _Block1.FogColor.w) * fast::clamp(_16622 / _Block1.FogInfo.x, 0.0, 1.0)))) * ((_16682.x / dot(_16688, _16698)) + ((_16682.y * fast::max(9.9999999747524270787835121154785e-07, _Block1.AerialPerspectiveRay.w * 0.0005000000237487256526947021484375)) * 5.0)))));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1116, "type": "assignment", "detail": "声明float变量: _16715", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 1117, "type": "function_call", "detail": "fast::normalize(_8370)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1117, "type": "arithmetic", "detail": "float3 _16602 = fast::normalize(_8370);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1117, "type": "assignment", "detail": "声明float变量: _16602", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 1118, "type": "function_call", "detail": "fast::clamp(dot(_16602, _Block1.OriginSunDir.xyz)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1118, "type": "arithmetic", "detail": "float _16756 = fast::clamp(dot(_16602, _Block1.OriginSunDir.xyz), 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1118, "type": "assignment", "detail": "声明float变量: _16756", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1119, "type": "function_call", "detail": "fast::max(0.0, _16602.y)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1119, "type": "arithmetic", "detail": "float _16759 = fast::max(0.0, _16602.y);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1119, "type": "assignment", "detail": "声明float变量: _16759", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1120, "type": "function_call", "detail": "fast::clamp((_16622 - 80.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1120, "type": "function_call", "detail": "fast::max(_12108, 520.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1120, "type": "arithmetic", "detail": "float _16820 = fast::clamp((_16622 - 80.0) / fast::max(_12108, 520.0), 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1120, "type": "assignment", "detail": "声明float变量: _16820", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1121, "type": "arithmetic", "detail": "float _16778 = 1.0 - (_16759 * _16759);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1121, "type": "assignment", "detail": "声明float变量: _16778", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1122, "type": "type_conversion", "detail": "float3(((1.0 - (_Block1.AerialPerspectiveExt.w * _Block1.AerialPerspectiveExt.w)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1122, "type": "function_call", "detail": "powr(fast::max(1.0 + (_Block1.AerialPerspectiveExt.w * (_Block1.AerialPerspectiveExt.w - (2.0 * _16756)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1122, "type": "arithmetic", "detail": "float3 _16785 = float3(((1.0 - (_Block1.AerialPerspectiveExt.w * _Block1.AerialPerspectiveExt.w)) / (12.56637096405029296875 * powr(fast::max(1.0 + (_Block1.AerialPerspectiveExt.w * (_Block1.AerialPerspectiveExt.w - (2.0 * _16756))), _12108), 1.5))) * (_16820 * _16820)) * _Block1.SunFogColor.xyz;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1122, "type": "assignment", "detail": "声明float变量: _16785", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 1123, "type": "type_conversion", "detail": "half(_16756)", "precision_conversion": "转换为half", "performance_impact": "medium", "output_type": "half"}, {"line": 1123, "type": "arithmetic", "detail": "half _16793 = half(_16756);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1123, "type": "assignment", "detail": "声明half变量: _16793", "precision_conversion": null, "performance_impact": "low", "output_type": "half"}, {"line": 1124, "type": "type_conversion", "detail": "float(half(1.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float"}, {"line": 1124, "type": "mixed_precision", "detail": "float3 _16805 = ((_Block1.AerialPerspectiveRay.xyz * float(half(1.0) + (_16793 * _16793))) + (_16785 * _Block1.AerialPerspectiveMie.x)) + ((_Block1.FogColor.xyz * (0.0596831031143665313720703125 * (1.0 + (_16778 * _16778)))) + _16785);", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 1124, "type": "assignment", "detail": "声明float变量: _16805", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 1125, "type": "type_conversion", "detail": "float3(_Block1.cSHCoefficients[0].w)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1125, "type": "type_conversion", "detail": "float3(_18268)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1125, "type": "type_conversion", "detail": "float3(((_9179 * _9254)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1125, "type": "type_conversion", "detail": "float3((_19028 + half3(float3(_16258)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1125, "type": "type_conversion", "detail": "float3((_15805 * 0.5)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1125, "type": "type_conversion", "detail": "float3((half3(_9698 * _10588)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1125, "type": "function_call", "detail": "dot(_16715, _16698)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1125, "type": "mixed_precision", "detail": "float3 _16862 = (((((((((((_Block1.cVisibilitySH[0].xyz * _8657) * _15762) * _Block1.AmbientColor.w) * _Block1.ReflectionProbeBBMin.w) * float3(_Block1.cSHCoefficients[0].w)).xyz + float3(_18268)).xyz + (_18989 + float3(((_9179 * _9254) + _8393) * _8696))).xyz + float3((_19028 + half3(float3(_16258) * float3((_15805 * 0.5) + 0.5))) * _10569)).xyz + float3((half3(_9698 * _10588) * _11772) + _8776)).xyz * dot(_16715, _16698)) + (_16805 - (_16805 * _16715))).xyz;", "precision_conversion": "混合精度运算", "performance_impact": "high", "output_type": null}, {"line": 1125, "type": "assignment", "detail": "声明float变量: _16862", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 1126, "type": "arithmetic", "detail": "float3 _8804 = (_16862 * _9868).xyz;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1126, "type": "assignment", "detail": "声明float变量: _8804", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 1127, "type": "arithmetic", "detail": "float3 _19031;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1130, "type": "arithmetic", "detail": "float3 _19032;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1133, "type": "function_call", "detail": "fast::clamp((_Block1.CameraPos.w - _Block1.ScreenMotionGray.w)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1133, "type": "function_call", "detail": "fast::max(_12108, (_Block1.ScreenMotionGray.w + abs(_Block1.ScreenMotionGray.x)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1133, "type": "arithmetic", "detail": "float _16911 = fast::clamp((_Block1.CameraPos.w - _Block1.ScreenMotionGray.w) / fast::max(_12108, (_Block1.ScreenMotionGray.w + abs(_Block1.ScreenMotionGray.x)) - _Block1.ScreenMotionGray.w), 0.0, 1.0);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1133, "type": "assignment", "detail": "声明float变量: _16911", "precision_conversion": null, "performance_impact": "low", "output_type": "float"}, {"line": 1134, "type": "arithmetic", "detail": "float _19029;", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1143, "type": "type_conversion", "detail": "float3(dot(_8804, _16698)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1143, "type": "type_conversion", "detail": "float3(_19029 * fract(_Block1.ScreenMotionGray.z)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1143, "type": "function_call", "detail": "mix(_8804, float3(dot(_8804, _16698)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1143, "type": "function_call", "detail": "floor(_Block1.ScreenMotionGray.z)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1143, "type": "function_call", "detail": "fract(_Block1.ScreenMotionGray.z)", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1143, "type": "arithmetic", "detail": "_19032 = mix(_8804, float3(dot(_8804, _16698) * (0.00999999977648258209228515625 * floor(_Block1.ScreenMotionGray.z))), float3(_19029 * fract(_Block1.ScreenMotionGray.z)));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1155, "type": "type_conversion", "detail": "float4(_19031.x, _19031.y, _19031.z, float4(0.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float4"}, {"line": 1155, "type": "arithmetic", "detail": "float4 _8808 = float4(_19031.x, _19031.y, _19031.z, float4(0.0).w);", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1155, "type": "assignment", "detail": "声明float变量: _8808", "precision_conversion": null, "performance_impact": "low", "output_type": "float4"}, {"line": 1157, "type": "type_conversion", "detail": "float3(10000.0)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float3"}, {"line": 1157, "type": "function_call", "detail": "fast::min(_8808.xyz, float3(10000.0)", "precision_conversion": null, "performance_impact": "medium", "output_type": null}, {"line": 1157, "type": "arithmetic", "detail": "float3 _8816 = fast::min(_8808.xyz, float3(10000.0));", "precision_conversion": null, "performance_impact": "low", "output_type": null}, {"line": 1157, "type": "assignment", "detail": "声明float变量: _8816", "precision_conversion": null, "performance_impact": "low", "output_type": "float3"}, {"line": 1158, "type": "type_conversion", "detail": "float4(_8816.x, _8816.y, _8816.z, _8808.w)", "precision_conversion": "转换为float", "performance_impact": "low", "output_type": "float4"}, {"line": 1158, "type": "arithmetic", "detail": "out._Ret = float4(_8816.x, _8816.y, _8816.z, _8808.w);", "precision_conversion": null, "performance_impact": "low", "output_type": null}], "statistics": {"total_operations": 1873, "operation_types": {"arithmetic": 616, "assignment": 452, "mixed_precision": 60, "type_conversion": 437, "function_call": 277, "texture_sample": 31}, "precision_conversions": 497, "mixed_precision_ops": 60, "high_impact_ops": 91, "texture_samples": 31}}