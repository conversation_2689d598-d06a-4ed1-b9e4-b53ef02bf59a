#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语法树构建器使用示例和集成指南
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def example_basic_usage():
    """基本使用示例"""
    print("📖 语法树构建器基本使用")
    print("=" * 50)
    
    from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer
    
    # 示例着色器代码
    shader_code = """
float a = b + c;
half d = half(a);
float result = max(a, d);
"""
    
    # 创建分析器
    analyzer = SyntaxTreeAnalyzer()
    
    # 分析代码并构建语法树
    result = analyzer.analyze_shader_with_syntax_trees(shader_code)
    
    # 显示结果
    print("分析结果:")
    print(analyzer.format_analysis_result(result))
    
    return result

def example_detailed_tree_analysis():
    """详细语法树分析示例"""
    print("\n" + "=" * 50)
    print("🔍 详细语法树分析")
    print("=" * 50)
    
    from Process.Analysis.syntax_tree_builder import SyntaxTreeBuilder
    
    builder = SyntaxTreeBuilder()
    
    # 分析单个语句
    test_statements = [
        "float a = b + c",
        "half d = half(intensity)",
        "float3 pos = normalize(worldPos)",
        "result = obj.member * 2.0"
    ]
    
    for i, stmt in enumerate(test_statements, 1):
        print(f"\n语句{i}: {stmt}")
        print("-" * 30)
        
        # 构建语法树
        tree = builder.parse_statement(stmt, i)
        
        # 显示树结构
        print("语法树结构:")
        print(builder.print_tree(tree))
        
        # 分析类型信息
        analysis = builder.analyze_tree_types(tree)
        
        if analysis['type_conversions']:
            print("类型转换:")
            for conv in analysis['type_conversions']:
                print(f"  {conv['from']} → {conv['to']}")
        
        if analysis['precision_issues']:
            print("精度问题:")
            for issue in analysis['precision_issues']:
                print(f"  {issue['left_type']} {issue['operation']} {issue['right_type']}")

def example_type_table_usage():
    """类型表使用示例"""
    print("\n" + "=" * 50)
    print("📋 类型表使用示例")
    print("=" * 50)
    
    from Process.Analysis.syntax_tree_builder import TypeTable, DataType
    
    # 创建类型表
    type_table = TypeTable()
    
    print("1. 声明变量:")
    variables = [
        ("position", DataType.FLOAT3),
        ("color", DataType.HALF4),
        ("intensity", DataType.FLOAT),
        ("normal", DataType.HALF3)
    ]
    
    for var_name, var_type in variables:
        type_table.declare_variable(var_name, var_type)
        print(f"  声明 {var_name}: {var_type.value}")
    
    print(f"\n2. 查询变量类型:")
    query_vars = ["position", "color", "unknown_var"]
    for var in query_vars:
        var_type = type_table.get_variable_type(var)
        print(f"  {var}: {var_type.value}")
    
    print(f"\n3. 查询函数信息:")
    functions = ["normalize", "dot", "max", "sample"]
    for func in functions:
        func_info = type_table.get_function_info(func)
        print(f"  {func}: 返回{func_info['return_type'].value}, 参数{func_info['params']}")

def example_performance_analysis():
    """性能分析示例"""
    print("\n" + "=" * 50)
    print("🎯 性能分析示例")
    print("=" * 50)
    
    from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer
    
    # 包含性能问题的着色器
    problematic_shader = """
float a = 1.0;
half b = 0.5;
float result1 = a + b;        // 混合精度问题
half result2 = half(a * 2.0); // 类型转换
float3 pos = float3(1.0, 2.0, 3.0);
half3 color = half3(0.5, 0.5, 0.5);
float3 mixed = pos + color;   // 混合精度问题
"""
    
    analyzer = SyntaxTreeAnalyzer()
    result = analyzer.analyze_shader_with_syntax_trees(problematic_shader)
    
    # 生成性能报告
    type_summary = result['type_summary']
    
    print("性能分析报告:")
    print("-" * 30)
    print(f"总代码行: {len(result['code_lines'])}")
    print(f"变量声明: {type_summary['variable_declarations']}")
    print(f"类型转换: {type_summary['type_conversions']}")
    print(f"混合精度问题: {type_summary['precision_issues']}")
    
    # 详细问题分析
    if type_summary['precision_issue_details']:
        print(f"\n⚠️  混合精度问题详情:")
        for issue in type_summary['precision_issue_details']:
            print(f"  行{issue['line']}: {issue['left_type']} {issue['operation']} {issue['right_type']}")
            print(f"    建议: 统一使用 {issue['left_type']} 或 {issue['right_type']} 类型")
    
    if type_summary['type_conversion_details']:
        print(f"\n🔄 类型转换详情:")
        for conv in type_summary['type_conversion_details']:
            print(f"  行{conv['line']}: {conv['from']} → {conv['to']}")
            print(f"    建议: 考虑直接使用 {conv['to']} 类型变量")

def example_integration_with_ui():
    """与UI集成示例"""
    print("\n" + "=" * 50)
    print("🖥️ UI集成示例")
    print("=" * 50)
    
    from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer
    
    def generate_ui_data(shader_content):
        """为UI生成语法树数据"""
        analyzer = SyntaxTreeAnalyzer()
        result = analyzer.analyze_shader_with_syntax_trees(shader_content)
        
        # 转换为UI友好的格式
        ui_data = {
            'syntax_trees': [],
            'type_summary': result['type_summary'],
            'performance_score': 100,  # 基础分数
            'recommendations': []
        }
        
        # 处理语法树数据
        for i, (code_line, tree) in enumerate(zip(result['code_lines'], result['syntax_trees'])):
            tree_data = {
                'line_number': code_line.line_number,
                'code': code_line.content,
                'tree_structure': analyzer.tree_builder.print_tree(tree),
                'node_type': tree.node_type.value,
                'data_type': tree.data_type.value,
                'has_issues': False
            }
            
            # 检查是否有问题
            tree_analysis = analyzer.tree_builder.analyze_tree_types(tree)
            if tree_analysis['type_conversions'] or tree_analysis['precision_issues']:
                tree_data['has_issues'] = True
                ui_data['performance_score'] -= 5  # 每个问题扣5分
            
            ui_data['syntax_trees'].append(tree_data)
        
        # 生成建议
        if result['type_summary']['precision_issues'] > 0:
            ui_data['recommendations'].append("建议统一变量精度类型以避免混合精度运算")
        
        if result['type_summary']['type_conversions'] > 3:
            ui_data['recommendations'].append("类型转换较多，建议优化变量类型选择")
        
        return ui_data
    
    # 测试UI数据生成
    test_shader = """
float3 pos = worldMatrix * localPos;
half4 color = texture.sample(sampler, uv);
float intensity = dot(normal, lightDir);
half3 result = color.rgb * half(intensity);
"""
    
    ui_data = generate_ui_data(test_shader)
    
    print("UI数据格式:")
    print(f"性能评分: {ui_data['performance_score']}")
    print(f"语法树数量: {len(ui_data['syntax_trees'])}")
    print(f"建议数量: {len(ui_data['recommendations'])}")
    
    print(f"\n语法树概览:")
    for tree_data in ui_data['syntax_trees']:
        status = "⚠️" if tree_data['has_issues'] else "✅"
        print(f"  {status} 行{tree_data['line_number']}: {tree_data['node_type']}({tree_data['data_type']})")
    
    if ui_data['recommendations']:
        print(f"\n💡 优化建议:")
        for rec in ui_data['recommendations']:
            print(f"  • {rec}")

def main():
    """主函数"""
    print("🚀 语法树构建器使用指南")
    print("=" * 60)
    print("核心功能:")
    print("• 🌳 构建完整的抽象语法树")
    print("• 📋 维护全局变量类型表")
    print("• 🔍 智能类型推断")
    print("• ⚠️  混合精度问题检测")
    print("• 🎯 性能分析和建议")
    print("• 🔗 与代码行分析器集成")
    print()
    
    example_basic_usage()
    example_detailed_tree_analysis()
    example_type_table_usage()
    example_performance_analysis()
    example_integration_with_ui()
    
    print("\n" + "=" * 60)
    print("🎯 集成建议")
    print("=" * 60)
    print("1. 在着色器分析流程中使用 SyntaxTreeAnalyzer")
    print("2. 将语法树结构显示在UI的详细分析面板中")
    print("3. 使用类型表信息进行智能代码提示")
    print("4. 基于性能分析结果生成优化建议")
    print("5. 在HTML报告中展示语法树可视化")
    print("6. 利用混合精度检测提供性能警告")

if __name__ == "__main__":
    main()
