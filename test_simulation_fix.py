#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的模拟功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simulation_with_real_shader():
    """测试真实着色器代码的模拟功能"""
    print("🔍 测试真实着色器代码的模拟功能")
    print("=" * 60)
    
    try:
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        processor = ShaderAnalysisProcessor()
        print("✅ ShaderAnalysisProcessor 创建成功")
        
        # 测试着色器代码
        test_shader = """
float3 worldPos = transform * localPos;
half4 baseColor = texture.sample(sampler, uv);
float dotNL = dot(normal, lightDir);
float3 result = worldPos + baseColor.xyz * dotNL;
float final = a + b + c + d;
"""
        
        print("🔄 开始分析测试着色器...")
        print(f"   着色器内容:")
        for i, line in enumerate(test_shader.strip().split('\n'), 1):
            if line.strip():
                print(f"     {i}: {line.strip()}")
        
        result = processor.analyze_shader(test_shader, save_reports=False)
        
        if 'analysis' in result:
            analysis_data = result['analysis']
            
            # 检查是否包含模拟数据
            if 'line_simulations' in analysis_data:
                simulations = analysis_data['line_simulations']
                print(f"\n✅ 找到 {len(simulations)} 行的模拟数据")
                
                for line_num, simulation in simulations.items():
                    print(f"\n   📝 第 {line_num} 行: {simulation['original_code'].strip()}")
                    
                    if simulation['has_simulation']:
                        print(f"     ✅ 生成了 {len(simulation['simulation_steps'])} 个模拟步骤:")
                        for step in simulation['simulation_steps']:
                            print(f"       🔸 {step['expression']}")
                        
                        if simulation['temp_variables_used']:
                            print(f"     🔧 使用的临时变量: {', '.join(simulation['temp_variables_used'])}")
                    else:
                        print(f"     ⚠️  没有生成模拟步骤")
                
                # 检查模拟摘要
                if 'simulation_summary' in analysis_data:
                    summary = analysis_data['simulation_summary']
                    print(f"\n📊 模拟摘要:")
                    print(f"     总步骤数: {summary['total_steps']}")
                    print(f"     有模拟的行数: {summary['lines_with_simulation']}")
                    print(f"     使用的临时变量数: {summary['temp_variables_used']}")
                    if summary['operation_counts']:
                        print(f"     操作类型统计: {summary['operation_counts']}")
                    
                    if summary['total_steps'] > 0:
                        print("✅ 模拟功能正常工作")
                        return True
                    else:
                        print("❌ 没有生成任何模拟步骤")
                        return False
                else:
                    print("❌ 没有找到模拟摘要")
                    return False
            else:
                print("❌ 没有找到模拟数据")
                return False
        else:
            print("❌ 分析结果中没有analysis数据")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_expressions():
    """测试特定表达式的模拟"""
    print("\n🔍 测试特定表达式的模拟")
    print("=" * 60)
    
    try:
        from Process.Analysis.syntax_tree_builder import SyntaxTreeBuilder
        
        builder = SyntaxTreeBuilder()
        print("✅ SyntaxTreeBuilder 创建成功")
        
        # 测试用例
        test_cases = [
            "a = b + c + d",
            "result = x * y + z",
            "final = (a + b) * (c + d)",
            "value = func(x, y) + z",
            "temp = obj.property * 2"
        ]
        
        for i, test_code in enumerate(test_cases, 1):
            print(f"\n   测试用例 {i}: {test_code}")
            
            # 重置模拟状态
            builder.reset_simulation()
            
            simulation = builder.simulate_line_execution(test_code, i)
            
            if simulation['has_simulation']:
                print(f"   ✅ 生成了 {len(simulation['simulation_steps'])} 个模拟步骤:")
                for step in simulation['simulation_steps']:
                    print(f"     🔸 {step['expression']}")
                
                if simulation['temp_variables_used']:
                    print(f"   🔧 使用的临时变量: {', '.join(simulation['temp_variables_used'])}")
            else:
                print("   ⚠️  没有生成模拟步骤")
                if 'error' in simulation:
                    print(f"   ❌ 错误: {simulation['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_debug_code_line_mapping():
    """调试代码行映射问题"""
    print("\n🔍 调试代码行映射问题")
    print("=" * 60)
    
    try:
        from Process.Analysis.shader_type_analyzer import ShaderTypeAnalyzer
        
        analyzer = ShaderTypeAnalyzer()
        print("✅ ShaderTypeAnalyzer 创建成功")
        
        test_shader = """
float3 worldPos = transform * localPos;
half4 baseColor = texture.sample(sampler, uv);
float final = a + b + c + d;
"""
        
        print("🔄 开始调试分析...")
        
        # 直接调用语法分析器
        syntax_result = analyzer.syntax_analyzer.analyze_shader_with_syntax_trees(test_shader)
        
        print(f"   代码行数据:")
        for line in syntax_result['code_lines']:
            print(f"     行 {line.line_number}: '{line.content}'")
        
        print(f"   语法树数据:")
        for tree in syntax_result['syntax_trees']:
            line_num = getattr(tree, 'line_number', 'unknown')
            print(f"     语法树行号: {line_num}")
        
        # 创建代码行映射
        code_line_map = {line.line_number: line.content for line in syntax_result['code_lines']}
        print(f"   代码行映射: {code_line_map}")
        
        # 测试模拟
        for tree in syntax_result['syntax_trees']:
            line_number = getattr(tree, 'line_number', 0)
            line_content = code_line_map.get(line_number, '')
            
            print(f"   行 {line_number}: '{line_content}'")
            
            if line_content and line_number > 0:
                simulation = analyzer.syntax_analyzer.tree_builder.simulate_line_execution(
                    line_content, line_number
                )
                
                if simulation['has_simulation']:
                    print(f"     ✅ 生成了 {len(simulation['simulation_steps'])} 个模拟步骤")
                    for step in simulation['simulation_steps']:
                        print(f"       🔸 {step['expression']}")
                else:
                    print(f"     ⚠️  没有生成模拟步骤")
                    if 'error' in simulation:
                        print(f"     ❌ 错误: {simulation['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎊 测试修复后的模拟功能")
    print("=" * 80)
    
    # 执行测试
    debug_ok = test_debug_code_line_mapping()
    specific_ok = test_specific_expressions()
    real_shader_ok = test_simulation_with_real_shader()
    
    print(f"\n🎉 测试结果总结")
    print("=" * 80)
    
    if debug_ok:
        print("✅ 代码行映射调试通过")
    else:
        print("❌ 代码行映射调试失败")
    
    if specific_ok:
        print("✅ 特定表达式模拟测试通过")
    else:
        print("❌ 特定表达式模拟测试失败")
    
    if real_shader_ok:
        print("✅ 真实着色器模拟测试通过")
    else:
        print("❌ 真实着色器模拟测试失败")
    
    if all([debug_ok, specific_ok, real_shader_ok]):
        print(f"\n🎊 所有测试通过！模拟功能修复成功")
        print("\n🎯 现在可以正确生成:")
        print("   • 每行代码的详细模拟步骤")
        print("   • 全局递增的临时变量 (tmp_0, tmp_1, tmp_2...)")
        print("   • 复杂表达式的拆分过程")
        print("   • 完整的统计信息")
    else:
        print(f"\n❌ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
