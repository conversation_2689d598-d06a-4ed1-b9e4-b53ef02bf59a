#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的着色器分析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication

def test_enhanced_analysis():
    """测试增强的分析功能"""
    app = QApplication(sys.argv)
    
    # 导入主窗口
    from ui.main_window import MainWindow
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    print("🔍 增强着色器分析功能测试")
    print("=" * 60)
    print("改进内容:")
    print("1. ✅ 修复弹窗几何设置问题")
    print("   - 设置最小/最大尺寸限制")
    print("   - 居中显示弹窗")
    print("   - 避免Windows几何设置警告")
    print()
    print("2. ✅ 强化变量识别能力")
    print("   - 识别每行代码的变量数量")
    print("   - 统计float和half变量使用")
    print("   - 检测类型转换次数")
    print("   - 跳过函数入口行统计")
    print()
    print("3. ✅ HTML报告类型颜色显示")
    print("   - float类型: 蓝色 (#4fc3f7)")
    print("   - half类型: 橙色 (#ff9800)")
    print("   - float向量: 深蓝色 (#2196f3)")
    print("   - half向量: 深橙色 (#ff5722)")
    print("   - 类型转换: 黄色背景高亮")
    print("   - 变量统计信息显示")
    print()
    print("测试用着色器代码:")
    print("""
#include <metal_stdlib>
using namespace metal;

struct VertexOut {
    float4 position [[position]];
    half2 texCoord;
    half3 normal;
};

fragment half4 main0(VertexOut in [[stage_in]], 
                     texture2d<half> baseTexture [[texture(0)]],
                     sampler baseSampler [[sampler(0)]]) {
    
    // 变量声明 - 应该被识别和统计
    half4 baseColor = baseTexture.sample(baseSampler, float2(in.texCoord));
    half3 normalizedNormal = normalize(in.normal);
    float lightIntensity = 0.8;
    
    // 类型转换 - 应该被高亮显示
    half3 finalColor = baseColor.rgb * half(lightIntensity);
    
    // 混合精度运算
    float3 worldPos = float3(1.0, 2.0, 3.0);
    half3 lightDir = half3(normalize(worldPos));
    
    half dotProduct = dot(normalizedNormal, lightDir);
    half3 diffuse = finalColor * max(dotProduct, half(0.0));
    
    return half4(diffuse, baseColor.a);
}
""")
    print()
    print("测试步骤:")
    print("1. 复制上面的着色器代码到编辑器")
    print("2. 使用 Ctrl+Alt+A 或菜单打开分析弹窗")
    print("3. 查看弹窗是否正常显示（无几何警告）")
    print("4. 检查指标卡片中的变量统计")
    print("5. 点击'查看详细报告'查看HTML报告")
    print("6. 验证HTML中的类型颜色显示")
    
    sys.exit(app.exec_())

def main():
    """主函数"""
    try:
        test_enhanced_analysis()
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
