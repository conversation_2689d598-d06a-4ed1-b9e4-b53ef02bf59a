
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精确着色器类型分析报告</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #1e1e1e; color: #d4d4d4; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #2d2d30; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-card { background: #252526; padding: 15px; border-radius: 6px; border-left: 4px solid #007acc; }
        .stat-value { font-size: 24px; font-weight: bold; color: #4fc3f7; }
        .stat-label { color: #cccccc; margin-top: 5px; }
        .code-section { background: #252526; border-radius: 8px; overflow: hidden; margin-bottom: 20px; }
        .code-header { background: #2d2d30; padding: 15px; border-bottom: 1px solid #3c3c3c; }
        .code-line { display: flex; padding: 8px 0; border-bottom: 1px solid #2d2d30; }
        .line-number { width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; }
        .line-content { flex: 1; padding-left: 15px; font-family: 'Consolas', monospace; }
        .node-info { font-size: 0.85em; color: #ffc107; margin-left: 10px; }
        .intermediate { color: #ff9800; }
        .direct { color: #4caf50; }
        .type-conversion { background-color: rgba(255, 193, 7, 0.2); }
        .precision-issue { background-color: rgba(244, 67, 54, 0.2); }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 精确着色器类型分析报告</h1>
            <p>基于语法树的精确类型推断分析</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">4901</div>
                <div class="stat-label">总节点数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">474</div>
                <div class="stat-label">变量声明</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">1646</div>
                <div class="stat-label">中间结果</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">27.9%</div>
                <div class="stat-label">准确性评分</div>
            </div>
        </div>

        <div class="code-section">
            <div class="code-header">
                <h3>📝 逐行精确类型分析</h3>
            </div>

            <div class="code-line ">
                <div class="line-number">74</div>
                <div class="line-content">
                    constant half3 _18526 = {};
                    <div class="node-info">🔹literal(constant half3 _18526 = {})→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">75</div>
                <div class="line-content">
                    constant float3 _19185 = {};
                    <div class="node-info">🔹literal(constant float3 _19185 = {})→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">76</div>
                <div class="line-content">
                    constant half _19493 = {};
                    <div class="node-info">🔹literal(constant half _19493 = {})→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">77</div>
                <div class="line-content">
                    constant float _19585 = {};
                    <div class="node-info">🔹literal(constant float _19585 = {})→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">78</div>
                <div class="line-content">
                    constant int _19621 = {};
                    <div class="node-info">🔹literal(constant int _19621 = {})→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">79</div>
                <div class="line-content">
                    constant float3 _21234 = {};
                    <div class="node-info">🔹literal(constant float3 _21234 = {})→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">80</div>
                <div class="line-content">
                    constant float3 _21295 = {};
                    <div class="node-info">🔹literal(constant float3 _21295 = {})→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">81</div>
                <div class="line-content">
                    constant half4 _21296 = {};
                    <div class="node-info">🔹literal(constant half4 _21296 = {})→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">82</div>
                <div class="line-content">
                    constant half3 _21297 = {};
                    <div class="node-info">🔹literal(constant half3 _21297 = {})→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">86</div>
                <div class="line-content">
                    float4 _Ret [[color(0)]];
                    <div class="node-info">🔹literal(float4 _Ret [[color(0)]])→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">91</div>
                <div class="line-content">
                    float4 IN_TexCoord [[user(locn0)]];
                    <div class="node-info">🔹literal(float4 IN_TexCoord [[user(locn0)]])→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">92</div>
                <div class="line-content">
                    float4 IN_WorldPosition [[user(locn1)]];
                    <div class="node-info">🔹literal(float4 IN_WorldPosition [[user(locn1)]])→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">93</div>
                <div class="line-content">
                    half4 IN_WorldNormal [[user(locn2)]];
                    <div class="node-info">🔹literal(half4 IN_WorldNormal [[user(locn2)]])→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">94</div>
                <div class="line-content">
                    half4 IN_WorldTangent [[user(locn3)]];
                    <div class="node-info">🔹literal(half4 IN_WorldTangent [[user(locn3)]])→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">95</div>
                <div class="line-content">
                    half4 IN_WorldBinormal [[user(locn4)]];
                    <div class="node-info">🔹literal(half4 IN_WorldBinormal [[user(locn4)]])→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">96</div>
                <div class="line-content">
                    half4 IN_TintColor [[user(locn5)]];
                    <div class="node-info">🔹literal(half4 IN_TintColor [[user(locn5)]])→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">97</div>
                <div class="line-content">
                    float IN_LinearZ [[user(locn6)]];
                    <div class="node-info">🔹literal(float IN_LinearZ [[user(locn6)]])→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">98</div>
                <div class="line-content">
                    half3 IN_LocalPosition [[user(locn7)]];
                    <div class="node-info">🔹literal(half3 IN_LocalPosition [[user(locn7)]])→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">99</div>
                <div class="line-content">
                    half4 IN_StaticWorldNormal [[user(locn8)]];
                    <div class="node-info">🔹literal(half4 IN_StaticWorldNormal [[user(locn8)]])→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">104</div>
                <div class="line-content">
                    constexpr sampler sShadowMapArraySamplerSmplr(filter::linear, mip_filter::linear, compare_func::greater);
                    <div class="node-info">🔹literal(constexpr sampler sShadowMapArraySamplerSmplr(filter::linear, mip_filter::linear, compare_func::greater))→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">105</div>
                <div class="line-content">
                    main0_out out = {};
                    <div class="node-info">🔹literal(main0_out out = {})→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">106</div>
                <div class="line-content">
                    half _9176 = half(0);
                    <div class="node-info">🔹declaration(half _9176)→half | 🔹literal(0)→int | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">107</div>
                <div class="line-content">
                    half3 _9179 = half3(_9176);
                    <div class="node-info">🔹declaration(half3 _9179)→half3 | 🔹variable(_9176)→half | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">108</div>
                <div class="line-content">
                    half _9199 = half(1);
                    <div class="node-info">🔹declaration(half _9199)→half | 🔹literal(1)→int | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">109</div>
                <div class="line-content">
                    float3 _8921 = fast::normalize(_Block1.CameraPos.xyz - in.IN_WorldPosition.xyz);
                    <div class="node-info">🔹declaration(float3 _8921)→float3 | 🔹literal(fast::normalize(_Block1.CameraPos.xyz)→unknown | 🔹literal(in.IN_WorldPosition.xyz))→unknown | 🔸operator(-)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">110</div>
                <div class="line-content">
                    float3 _8925 = float3(in.IN_WorldNormal.xyz);
                    <div class="node-info">🔹declaration(float3 _8925)→float3 | 🔹literal(in.IN_WorldNormal.xyz)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">111</div>
                <div class="line-content">
                    half _8929 = half(fast::clamp(dot(_8925, _8921), 0.0, 1.0));
                    <div class="node-info">🔹declaration(half _8929)→half | 🔹literal(fast::clamp(dot(_8925, _8921), 0.0, 1.0))→unknown | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">112</div>
                <div class="line-content">
                    half4 _8939 = sBaseSampler.sample(sBaseSamplerSmplr, in.IN_TexCoord.xy);
                    <div class="node-info">🔹declaration(half4 _8939)→half4 | 🔹literal(sBaseSampler.sample(sBaseSamplerSmplr, in.IN_TexCoord.xy))→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">113</div>
                <div class="line-content">
                    half3 _8941 = _8939.xyz;
                    <div class="node-info">🔹declaration(half3 _8941)→half3 | 🔹member_access(_8939.xyz)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">114</div>
                <div class="line-content">
                    half3 _8949 = half3(float3(_8941 * _8941) * float3(_Block1.cBaseColor));
                    <div class="node-info">🔹declaration(half3 _8949)→half3 | 🔹variable(_8941)→half3 | 🔹literal(_8941))→unknown | 🔹literal(float3(_Block1.cBaseColor)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→half3 | 🔸type_cast(float3)→float3 | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">115</div>
                <div class="line-content">
                    float4 _8973 = sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy * _Block1.cNoise1Scale));
                    <div class="node-info">🔹declaration(float4 _8973)→float4 | 🔹literal(sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy)→unknown | 🔹literal(_Block1.cNoise1Scale)))→unknown | 🔸operator(*)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">116</div>
                <div class="line-content">
                    half4 _8974 = half4(_8973);
                    <div class="node-info">🔹declaration(half4 _8974)→half4 | 🔹variable(_8973)→float4 | 🔸type_cast(half4)→half4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">117</div>
                <div class="line-content">
                    float4 _8982 = sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy * _Block1.cNoise2Scale));
                    <div class="node-info">🔹declaration(float4 _8982)→float4 | 🔹literal(sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy)→unknown | 🔹literal(_Block1.cNoise2Scale)))→unknown | 🔸operator(*)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">118</div>
                <div class="line-content">
                    half4 _8983 = half4(_8982);
                    <div class="node-info">🔹declaration(half4 _8983)→half4 | 🔹variable(_8982)→float4 | 🔸type_cast(half4)→half4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">119</div>
                <div class="line-content">
                    float _8991 = fast::clamp(powr(float(max(in.IN_TintColor.w, half(9.9956989288330078125e-05))), _Block1.cNoise2Bias), 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _8991)→float | 🔹literal(fast::clamp(powr(float(max(in.IN_TintColor.w, half(9.9956989288330078125e)→unknown | 🔹literal(05))), _Block1.cNoise2Bias), 0.0, 1.0))→unknown | 🔸operator(-)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">120</div>
                <div class="line-content">
                    half _8994 = _8974.x;
                    <div class="node-info">🔹declaration(half _8994)→half | 🔹member_access(_8974.x)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">121</div>
                <div class="line-content">
                    half _8996 = _8983.x;
                    <div class="node-info">🔹declaration(half _8996)→half | 🔹member_access(_8983.x)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">122</div>
                <div class="line-content">
                    float _9014 = 1.0 - _8991;
                    <div class="node-info">🔹declaration(float _9014)→float | 🔹literal(1.0)→float | 🔹variable(_8991)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">123</div>
                <div class="line-content">
                    half _9019 = half(1.0);
                    <div class="node-info">🔹declaration(half _9019)→half | 🔹literal(1.0)→float | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">124</div>
                <div class="line-content">
                    if ((float((_8994 * _8996) - half(mix(0.0, 0.5, powr(float(max(_9199 - _8929, half(9.9956989288330078125e-05))), 3.0)))) - 0.100000001490116119384765625) < 0.0)
                    <div class="node-info">🔹literal((float((_8994)→unknown | 🔹literal(_8996))→unknown | 🔸operator(*)→unknown | 🔹literal(half(mix(0.0, 0.5, powr(float(max(_9199)→unknown | 🔹literal(_8929, half(9.9956989288330078125e)→unknown | 🔹literal(05))), 3.0)))))→unknown | 🔹literal(0.100000001490116119384765625))→unknown | 🔹literal(0.0)→float | 🔸operator(<)→bool | 🔸operator(-)→bool | 🔸operator(-)→bool | 🔸operator(-)→bool | 🔸operator(-)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">126</div>
                <div class="line-content">
                    discard_fragment();
                    <div class="node-info">🔹literal(discard_fragment())→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">128</div>
                <div class="line-content">
                    half4 _9248 = sNormalSampler.sample(sNormalSamplerSmplr, in.IN_TexCoord.xy);
                    <div class="node-info">🔹declaration(half4 _9248)→half4 | 🔹literal(sNormalSampler.sample(sNormalSamplerSmplr, in.IN_TexCoord.xy))→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">129</div>
                <div class="line-content">
                    half _9251 = half(2);
                    <div class="node-info">🔹declaration(half _9251)→half | 🔹literal(2)→int | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">130</div>
                <div class="line-content">
                    half3 _9254 = half3(_9199);
                    <div class="node-info">🔹declaration(half3 _9254)→half3 | 🔹variable(_9199)→half | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">131</div>
                <div class="line-content">
                    half3 _9255 = (_9248.xyz * _9251) - _9254;
                    <div class="node-info">🔹declaration(half3 _9255)→half3 | 🔹literal((_9248.xyz)→unknown | 🔹literal(_9251))→unknown | 🔸operator(*)→unknown | 🔹variable(_9254)→half3 | 🔸operator(-)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">132</div>
                <div class="line-content">
                    half _9257 = _9255.x;
                    <div class="node-info">🔹declaration(half _9257)→half | 🔹member_access(_9255.x)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">133</div>
                <div class="line-content">
                    half _9263 = _9255.y;
                    <div class="node-info">🔹declaration(half _9263)→half | 🔹member_access(_9255.y)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">134</div>
                <div class="line-content">
                    half _9270 = _9255.z;
                    <div class="node-info">🔹declaration(half _9270)→half | 🔹member_access(_9255.z)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">135</div>
                <div class="line-content">
                    float3 _9279 = float3(((in.IN_WorldTangent.xyz * _9257) + (in.IN_WorldBinormal.xyz * _9263)) + (in.IN_WorldNormal.xyz * _9270));
                    <div class="node-info">🔹declaration(float3 _9279)→float3 | 🔹literal(((in.IN_WorldTangent.xyz)→unknown | 🔹literal(_9257))→unknown | 🔸operator(*)→unknown | 🔹literal((in.IN_WorldBinormal.xyz)→unknown | 🔹literal(_9263)))→unknown | 🔸operator(*)→unknown | 🔹literal((in.IN_WorldNormal.xyz)→unknown | 🔹literal(_9270))→unknown | 🔸operator(*)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">136</div>
                <div class="line-content">
                    float3 _9286 = float3(in.IN_StaticWorldNormal.xyz);
                    <div class="node-info">🔹declaration(float3 _9286)→float3 | 🔹literal(in.IN_StaticWorldNormal.xyz)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">137</div>
                <div class="line-content">
                    float3 _9331 = (((float4(float3(in.IN_WorldTangent.xyz), 0.0) * _Block1.Local) * float(_9257)) + ((float4(float3(in.IN_WorldBinormal.xyz), 0.0) * _Block1.Local) * float(_9263))) + (_9286 * float(_9270));
                    <div class="node-info">🔹declaration(float3 _9331)→float3 | 🔹literal((((float4(float3(in.IN_WorldTangent.xyz), 0.0))→unknown | 🔹literal(_Block1.Local))→unknown | 🔹literal(_9257))→unknown | 🔸type_cast(float)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔹literal(((float4(float3(in.IN_WorldBinormal.xyz), 0.0))→unknown | 🔹literal(_Block1.Local))→unknown | 🔹literal(_9263)))→unknown | 🔸type_cast(float)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔹literal((_9286)→unknown | 🔹literal(_9270))→unknown | 🔸type_cast(float)→float | 🔸operator(*)→float | 🔸operator(+)→float | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">138</div>
                <div class="line-content">
                    half _9334 = half((_9331 * rsqrt(fast::max(dot(_9331, _9331), 9.9999997473787516355514526367188e-06))).y);
                    <div class="node-info">🔹declaration(half _9334)→half | 🔹literal((_9331)→unknown | 🔹literal(rsqrt(fast::max(dot(_9331, _9331), 9.9999997473787516355514526367188e)→unknown | 🔸operator(*)→unknown | 🔹literal(06))).y)→unknown | 🔸operator(-)→unknown | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">139</div>
                <div class="line-content">
                    half3 _9064 = mix(in.IN_WorldNormal.xyz, half3(_9279 * rsqrt(fast::max(dot(_9279, _9279), 9.9999997473787516355514526367188e-06))), half3(half(_Block1.cNormalMapStrength)));
                    <div class="node-info">🔹declaration(half3 _9064)→half3 | 🔹literal(in.IN_WorldNormal.xyz)→unknown | 🔹variable(_9279)→float3 | 🔹literal(rsqrt(fast::max(dot(_9279, _9279), 9.9999997473787516355514526367188e)→unknown | 🔸operator(*)→float3 | 🔹literal(06)))→unknown | 🔸operator(-)→float3 | 🔸type_cast(half3)→half3 | 🔹member_access(_Block1.cNormalMapStrength)→unknown | 🔸type_cast(half)→half | 🔸type_cast(half3)→half3 | 🔸function(mix)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">140</div>
                <div class="line-content">
                    half4 _9074 = sMixSampler.sample(sMixSamplerSmplr, in.IN_TexCoord.xy);
                    <div class="node-info">🔹declaration(half4 _9074)→half4 | 🔹literal(sMixSampler.sample(sMixSamplerSmplr, in.IN_TexCoord.xy))→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">141</div>
                <div class="line-content">
                    half _9079 = _9074.y;
                    <div class="node-info">🔹declaration(half _9079)→half | 🔹member_access(_9074.y)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">142</div>
                <div class="line-content">
                    half _9096 = half(mix(1.0, float(mix(half3(_9019), half3(half(fast::clamp(_9014 + _Block1.cAOoffset, 0.0, 1.0))), in.IN_TintColor.xxx).x * _9074.z), float(_8929)));
                    <div class="node-info">🔹declaration(half _9096)→half | 🔹literal(1.0)→float | 🔹literal(mix(half3(_9019), half3(half(fast::clamp(_9014)→unknown | 🔹literal(_Block1.cAOoffset, 0.0, 1.0))), in.IN_TintColor.xxx).x)→unknown | 🔹member_access(_9074.z)→half | 🔸operator(*)→half | 🔸operator(+)→half | 🔸type_cast(float)→float | 🔹variable(_8929)→half | 🔸type_cast(float)→float | 🔸function(mix)→float | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">143</div>
                <div class="line-content">
                    float _9100 = float(_9096);
                    <div class="node-info">🔹declaration(float _9100)→float | 🔹variable(_9096)→half | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">144</div>
                <div class="line-content">
                    float3 _9109 = float3(_9064);
                    <div class="node-info">🔹declaration(float3 _9109)→float3 | 🔹variable(_9064)→half3 | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">145</div>
                <div class="line-content">
                    half4 _9130 = sEmissionMapSampler.sample(sEmissionMapSamplerSmplr, in.IN_TexCoord.xy);
                    <div class="node-info">🔹declaration(half4 _9130)→half4 | 🔹literal(sEmissionMapSampler.sample(sEmissionMapSamplerSmplr, in.IN_TexCoord.xy))→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">146</div>
                <div class="line-content">
                    half3 _9145 = _9130.xyz * half3((float3(_Block1.cEmissionColor) * _Block1.cEmissionScale) * float3(_8949));
                    <div class="node-info">🔹declaration(half3 _9145)→half3 | 🔹member_access(_9130.xyz)→half3 | 🔹literal((float3(_Block1.cEmissionColor))→unknown | 🔹literal(_Block1.cEmissionScale))→unknown | 🔹variable(_8949)→half3 | 🔸type_cast(float3)→float3 | 🔸operator(*)→float3 | 🔸operator(*)→float3 | 🔸type_cast(half3)→half3 | 🔸operator(*)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">148</div>
                <div class="line-content">
                    if (!gl_FrontFacing)
                    <div class="node-info">🔹literal(!gl_FrontFacing)→unknown | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">150</div>
                <div class="line-content">
                    _18217 = _9334 * half(-1);
                    <div class="node-info">🔹variable(_18217)→unknown | 🔹variable(_9334)→half | 🔹literal(half()→unknown | 🔸operator(*)→half | 🔹literal(1))→unknown | 🔸operator(-)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">154</div>
                <div class="line-content">
                    _18217 = _9334;
                    <div class="node-info">🔹variable(_18217)→unknown | 🔹variable(_9334)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">156</div>
                <div class="line-content">
                    float3 _9698 = float3(_9179);
                    <div class="node-info">🔹declaration(float3 _9698)→float3 | 🔹variable(_9179)→half3 | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">157</div>
                <div class="line-content">
                    float _9408 = mix(_Block1.cCIFadeTime.y, _Block1.cCIFadeTime.w, fast::clamp((_Block1.CameraPos.w - _Block1.cCIFadeTime.x) * _Block1.cCIFadeTime.z, 0.0, 1.0));
                    <div class="node-info">🔹declaration(float _9408)→float | 🔹literal(_Block1.cCIFadeTime.y)→unknown | 🔹literal(_Block1.cCIFadeTime.w)→unknown | 🔹literal(fast::clamp((_Block1.CameraPos.w)→unknown | 🔹literal(_Block1.cCIFadeTime.x))→unknown | 🔹literal(_Block1.cCIFadeTime.z, 0.0, 1.0))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔸function(mix)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">158</div>
                <div class="line-content">
                    float _9721 = fast::clamp(dot(_9286, float3(half3(_9176, _9176, _9199))), 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _9721)→float | 🔹literal(fast::clamp(dot(_9286, float3(half3(_9176, _9176, _9199))), 0.0, 1.0))→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">159</div>
                <div class="line-content">
                    float _9734 = (_9721 * _9721) * float(half(fast::clamp((_Block1.cCIMudBuff[2u] - 1.0) * 0.699999988079071044921875, 0.0, 1.0)));
                    <div class="node-info">🔹declaration(float _9734)→float | 🔹literal((_9721)→unknown | 🔹literal(_9721))→unknown | 🔹literal(float(half(fast::clamp((_Block1.cCIMudBuff[2u])→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(1.0))→unknown | 🔹literal(0.699999988079071044921875, 0.0, 1.0))))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">160</div>
                <div class="line-content">
                    half3 _9761 = mix(_8949, half3(half(0.20700000226497650146484375), half(0.18400000035762786865234375), half(0.1369999945163726806640625)), half3(half(_9734 * _9408)));
                    <div class="node-info">🔹declaration(half3 _9761)→half3 | 🔹variable(_8949)→half3 | 🔹literal(0.20700000226497650146484375), half(0.18400000035762786865234375), half(0.1369999945163726806640625)→unknown | 🔸type_cast(half)→half | 🔸type_cast(half3)→half3 | 🔹variable(_9734)→float | 🔹variable(_9408)→float | 🔸operator(*)→float | 🔸type_cast(half)→half | 🔸type_cast(half3)→half3 | 🔸function(mix)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">161</div>
                <div class="line-content">
                    half _9772 = half(mix(float(_9199 - _9074.x), 0.89999997615814208984375, _9734 * float(half(mix(_9408 - (0.5 * _Block1.cCIFadeTime.x), _9408, _Block1.cCIFadeTime.y)))));
                    <div class="node-info">🔹declaration(half _9772)→half | 🔹variable(_9199)→half | 🔹member_access(_9074.x)→half | 🔸operator(-)→half | 🔸type_cast(float)→float | 🔹literal(0.89999997615814208984375)→float | 🔹variable(_9734)→float | 🔹literal(float(half(mix(_9408)→unknown | 🔸operator(*)→float | 🔹literal((0.5)→unknown | 🔹literal(_Block1.cCIFadeTime.x), _9408, _Block1.cCIFadeTime.y))))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→float | 🔸function(mix)→float | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">162</div>
                <div class="line-content">
                    float _9429 = (float(half(_9014)) * _9408) * fast::clamp(1.0 + _Block1.EnvInfo.y, 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _9429)→float | 🔹literal((float(half(_9014)))→unknown | 🔹literal(_9408))→unknown | 🔹literal(fast::clamp(1.0)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(_Block1.EnvInfo.y, 0.0, 1.0))→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">163</div>
                <div class="line-content">
                    float4 _9443 = sCharInteractionSampler.sample(sCharInteractionSamplerSmplr, ((in.IN_TexCoord.xy * _Block1.cCISnowData.x) * 12.0));
                    <div class="node-info">🔹declaration(float4 _9443)→float4 | 🔹literal(sCharInteractionSampler.sample(sCharInteractionSamplerSmplr, ((in.IN_TexCoord.xy)→unknown | 🔹literal(_Block1.cCISnowData.x))→unknown | 🔹literal(12.0)))→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">169</div>
                <div class="line-content">
                    if (_Block1.cCISwitchData.x > 0.0)
                    <div class="node-info">🔹literal(_Block1.cCISwitchData.x)→unknown | 🔹literal(0.0)→float | 🔸operator(>)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">171</div>
                <div class="line-content">
                    float _9460 = float(half(fast::max(0.0, _Block1.EnvInfo.y)));
                    <div class="node-info">🔹declaration(float _9460)→float | 🔹literal(fast::max(0.0, _Block1.EnvInfo.y))→unknown | 🔸type_cast(half)→half | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">172</div>
                <div class="line-content">
                    float _9462 = fast::min(2.0, fast::clamp(_Block1.cCISnowData.z, 0.0, 1.0) + _9460);
                    <div class="node-info">🔹declaration(float _9462)→float | 🔹literal(fast::min(2.0, fast::clamp(_Block1.cCISnowData.z, 0.0, 1.0))→unknown | 🔹literal(_9460))→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">173</div>
                <div class="line-content">
                    float _9499 = 1.0 - _9429;
                    <div class="node-info">🔹declaration(float _9499)→float | 🔹literal(1.0)→float | 🔹variable(_9429)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">174</div>
                <div class="line-content">
                    float _9505 = _9443.y;
                    <div class="node-info">🔹declaration(float _9505)→float | 🔹member_access(_9443.y)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">175</div>
                <div class="line-content">
                    float _9510 = float(half(fast::clamp(((float2(0.800000011920928955078125, 0.5) * _9429).x * (1.0 - powr(float(half(powr(float(clamp(_18217, half(0.0), half(1.0))) + fast::clamp(0.20000000298023223876953125 - fast::clamp((-1.0) * _Block1.cCISnowData.z, 0.0, 1.0), 0.0, 1.0), 2.0 - _9462) * float(in.IN_StaticWorldNormal.w))), 0.800000011920928955078125 - (_9462 * 0.4000000059604644775390625)))) + ((_9499 * _9499) * _9499), 0.0, 1.0)));
                    <div class="node-info">🔹declaration(float _9510)→float | 🔹literal(fast::clamp(((float2(0.800000011920928955078125, 0.5))→unknown | 🔹literal(_9429).x)→unknown | 🔹literal((1.0)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(powr(float(clamp(_18217, half(0.0), half(1.0)→unknown | 🔸type_cast(half)→half | 🔸type_cast(float)→float | 🔸function(powr)→unknown | 🔸operator(-)→unknown | 🔹literal(fast::clamp(0.20000000298023223876953125)→unknown | 🔹literal(fast::clamp(()→unknown | 🔹literal(1.0))→unknown | 🔹literal(_Block1.cCISnowData.z, 0.0, 1.0), 0.0, 1.0), 2.0)→unknown | 🔸operator(*)→unknown | 🔹literal(_9462))→unknown | 🔹literal(float(in.IN_StaticWorldNormal.w))), 0.800000011920928955078125)→unknown | 🔸operator(*)→unknown | 🔹literal((_9462)→unknown | 🔹literal(0.4000000059604644775390625)))))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔸operator(-)→unknown | 🔸operator(-)→unknown | 🔸operator(-)→unknown | 🔹literal(((_9499)→unknown | 🔹literal(_9499))→unknown | 🔹literal(_9499), 0.0, 1.0))→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸type_cast(half)→half | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">176</div>
                <div class="line-content">
                    float _9519 = float(half(9.9956989288330078125e-05));
                    <div class="node-info">🔹declaration(float _9519)→float | 🔹literal(9.9956989288330078125e)→unknown | 🔹literal(05)→int | 🔸operator(-)→int | 🔸type_cast(half)→half | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">177</div>
                <div class="line-content">
                    half _9535 = half(1.0 - fast::clamp((float(in.IN_LocalPosition.y) * (2.0 - _9460)) * _Block1.cCISnowData.y, 0.0, 1.0));
                    <div class="node-info">🔹declaration(half _9535)→half | 🔹literal(1.0)→float | 🔹literal(fast::clamp((float(in.IN_LocalPosition.y))→unknown | 🔹literal((2.0)→unknown | 🔸operator(*)→unknown | 🔹literal(_9460)))→unknown | 🔹literal(_Block1.cCISnowData.y, 0.0, 1.0))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔸operator(-)→float | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">178</div>
                <div class="line-content">
                    float _9556 = float(half((float(_9535) * fast::clamp(float(_9535 + _18217) + 0.5, 0.0, 1.0)) * _9429));
                    <div class="node-info">🔹declaration(float _9556)→float | 🔹literal((float(_9535))→unknown | 🔹literal(fast::clamp(float(_9535)→unknown | 🔸operator(*)→unknown | 🔹literal(_18217))→unknown | 🔹literal(0.5, 0.0, 1.0)))→unknown | 🔹variable(_9429)→float | 🔸operator(*)→float | 🔸operator(+)→float | 🔸operator(+)→float | 🔸type_cast(half)→half | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">179</div>
                <div class="line-content">
                    float _9557 = 1.0 - _9556;
                    <div class="node-info">🔹declaration(float _9557)→float | 🔹literal(1.0)→float | 🔹variable(_9556)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">180</div>
                <div class="line-content">
                    half _9585 = half((_Block1.cCISwitchData.x * fast::clamp(1.0 - _Block1.cCISnowData.w, 0.0, 1.0)) * float(max(half(fast::clamp((fast::max(_9505, _9443.w) - _9510) / fast::max(_9519, fast::clamp(_9510 + 0.1500000059604644775390625, 0.0, 1.0) - _9510), 0.0, 1.0)), half(fast::clamp((fast::max(_9505, _9443.z) - _9557) / fast::max(_9519, (1.5 - _9556) - _9557), 0.0, 1.0)))));
                    <div class="node-info">🔹declaration(half _9585)→half | 🔹literal((_Block1.cCISwitchData.x)→unknown | 🔹literal(fast::clamp(1.0)→unknown | 🔸operator(*)→unknown | 🔹literal(_Block1.cCISnowData.w, 0.0, 1.0)))→unknown | 🔹literal(max(half(fast::clamp((fast::max(_9505, _9443.w)→unknown | 🔸type_cast(float)→float | 🔸operator(*)→float | 🔹literal(_9510))→unknown | 🔹literal(fast::max(_9519, fast::clamp(_9510)→unknown | 🔸operator(/)→unknown | 🔸operator(-)→float | 🔸operator(-)→float | 🔹literal(0.1500000059604644775390625, 0.0, 1.0))→unknown | 🔹literal(_9510), 0.0, 1.0)), half(fast::clamp((fast::max(_9505, _9443.z))→unknown | 🔹literal(_9557))→unknown | 🔹literal(fast::max(_9519, (1.5)→unknown | 🔸operator(/)→unknown | 🔹literal(_9556))→unknown | 🔹literal(_9557), 0.0, 1.0)))))→unknown | 🔸operator(-)→unknown | 🔸operator(-)→unknown | 🔸operator(-)→unknown | 🔸operator(-)→unknown | 🔸operator(+)→float | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">181</div>
                <div class="line-content">
                    half _9588 = _9199 - _9585;
                    <div class="node-info">🔹declaration(half _9588)→half | 🔹variable(_9199)→half | 🔹variable(_9585)→half | 🔸operator(-)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">182</div>
                <div class="line-content">
                    float _9603 = float(_9585);
                    <div class="node-info">🔹declaration(float _9603)→float | 🔹variable(_9585)→half | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">183</div>
                <div class="line-content">
                    _18272 = half(mix(float(_9772), 1.0, _9603));
                    <div class="node-info">🔹variable(_18272)→unknown | 🔹variable(_9772)→half | 🔸type_cast(float)→float | 🔹literal(1.0)→float | 🔹variable(_9603)→float | 🔸function(mix)→float | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">184</div>
                <div class="line-content">
                    _18268 = _9145 * _9588;
                    <div class="node-info">🔹variable(_18268)→unknown | 🔹variable(_9145)→half3 | 🔹variable(_9588)→half | 🔸operator(*)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">185</div>
                <div class="line-content">
                    _18236 = half(mix(_9100, 1.0, _9603));
                    <div class="node-info">🔹variable(_18236)→unknown | 🔹variable(_9100)→float | 🔹literal(1.0)→float | 🔹variable(_9603)→float | 🔸function(mix)→float | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">186</div>
                <div class="line-content">
                    _18234 = _9079 * _9588;
                    <div class="node-info">🔹variable(_18234)→unknown | 🔹variable(_9079)→half | 🔹variable(_9588)→half | 🔸operator(*)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">187</div>
                <div class="line-content">
                    _18225 = mix(_9761, half3(half(0.61000001430511474609375), half(0.660000026226043701171875), half(0.790000021457672119140625)), half3(_9585));
                    <div class="node-info">🔹variable(_18225)→unknown | 🔹variable(_9761)→half3 | 🔹literal(0.61000001430511474609375), half(0.660000026226043701171875), half(0.790000021457672119140625)→unknown | 🔸type_cast(half)→half | 🔸type_cast(half3)→half3 | 🔹variable(_9585)→half | 🔸type_cast(half3)→half3 | 🔸function(mix)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">191</div>
                <div class="line-content">
                    _18272 = _9772;
                    <div class="node-info">🔹variable(_18272)→unknown | 🔹variable(_9772)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">192</div>
                <div class="line-content">
                    _18268 = _9145;
                    <div class="node-info">🔹variable(_18268)→unknown | 🔹variable(_9145)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">193</div>
                <div class="line-content">
                    _18236 = _9096;
                    <div class="node-info">🔹variable(_18236)→unknown | 🔹variable(_9096)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">194</div>
                <div class="line-content">
                    _18234 = _9079;
                    <div class="node-info">🔹variable(_18234)→unknown | 🔹variable(_9079)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">195</div>
                <div class="line-content">
                    _18225 = _9761;
                    <div class="node-info">🔹variable(_18225)→unknown | 🔹variable(_9761)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">197</div>
                <div class="line-content">
                    half _8295 = half(fast::clamp(1.0 - _Block1.HexRenderOptionData[0].x, 0.0, 1.0));
                    <div class="node-info">🔹declaration(half _8295)→half | 🔹literal(fast::clamp(1.0)→unknown | 🔹literal(_Block1.HexRenderOptionData[0].x, 0.0, 1.0))→unknown | 🔸operator(-)→unknown | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion precision-issue ">
                <div class="line-number">198</div>
                <div class="line-content">
                    float _8298 = float(_8939.w * half((_8991 * float(min(_8994, _8996))) * powr(float(max(in.IN_TintColor.x, half(9.9956989288330078125e-05))), _Block1.cFurFadeInt)));
                    <div class="node-info">🔹declaration(float _8298)→float | 🔹member_access(_8939.w)→half | 🔹literal(half((_8991)→unknown | 🔹variable(_8994)→half | 🔹literal(_8996))→unknown | 🔸function(min)→half | 🔸type_cast(float)→float | 🔹literal(powr(float(max(in.IN_TintColor.x, half(9.9956989288330078125e)→unknown | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔹literal(05))), _Block1.cFurFadeInt)))→unknown | 🔸operator(-)→float | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">199</div>
                <div class="line-content">
                    half3 _8303 = half3(half(0.2125999927520751953125), half(0.715200006961822509765625), half(0.072200000286102294921875));
                    <div class="node-info">🔹declaration(half3 _8303)→half3 | 🔹literal(0.2125999927520751953125), half(0.715200006961822509765625), half(0.072200000286102294921875)→unknown | 🔸type_cast(half)→half | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">200</div>
                <div class="line-content">
                    half3 _8315 = mix(half3(dot(_18225, _8303)), _18225, half3(half(_Block1.cSaturation)));
                    <div class="node-info">🔹declaration(half3 _8315)→half3 | 🔹variable(_18225)→unknown | 🔹variable(_8303)→half3 | 🔸function(dot)→float | 🔸type_cast(half3)→half3 | 🔹variable(_18225)→unknown | 🔹member_access(_Block1.cSaturation)→unknown | 🔸type_cast(half)→half | 🔸type_cast(half3)→half3 | 🔸function(mix)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">201</div>
                <div class="line-content">
                    half _9787 = half(_8298);
                    <div class="node-info">🔹declaration(half _9787)→half | 🔹variable(_8298)→float | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">204</div>
                <div class="line-content">
                    if (_Block1.eDynamicFresnelIntensity > 0.0)
                    <div class="node-info">🔹member_access(_Block1.eDynamicFresnelIntensity)→unknown | 🔹literal(0.0)→float | 🔸operator(>)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">206</div>
                <div class="line-content">
                    float _9806 = abs(dot(_9109, -_8921));
                    <div class="node-info">🔹declaration(float _9806)→float | 🔹variable(_9109)→float3 | 🔹literal(-_8921)→unknown | 🔸function(dot)→float | 🔸function(abs)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">207</div>
                <div class="line-content">
                    float _9813 = abs(_Block1.eFresnelPower);
                    <div class="node-info">🔹declaration(float _9813)→float | 🔹member_access(_Block1.eFresnelPower)→unknown | 🔸function(abs)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">208</div>
                <div class="line-content">
                    float _9831 = fast::max(_Block1.eFresnelMinIntensity, (_Block1.eFresnelPower < 0.0) ? powr(_9806, _9813) : powr(1.0 - fast::min(float(_9199 - half(9.9956989288330078125e-05)), _9806), _9813));
                    <div class="node-info">🔹declaration(float _9831)→float | 🔹literal(fast::max(_Block1.eFresnelMinIntensity, (_Block1.eFresnelPower)→unknown | 🔹literal(0.0) ? powr(_9806, _9813) : powr(1.0)→unknown | 🔸operator(<)→bool | 🔹literal(fast::min(float(_9199)→unknown | 🔹literal(9.9956989288330078125e)→unknown | 🔹literal(05)), _9806), _9813))→unknown | 🔸operator(-)→unknown | 🔸type_cast(half)→half | 🔸operator(-)→half | 🔸operator(-)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">209</div>
                <div class="line-content">
                    float _9846 = float(_9787 * half(_9831));
                    <div class="node-info">🔹declaration(float _9846)→float | 🔹variable(_9787)→half | 🔹variable(_9831)→float | 🔸type_cast(half)→half | 🔸operator(*)→half | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">210</div>
                <div class="line-content">
                    _18255 = _9179 + half3((((float3(_Block1.eFresnelColor) * _9831) * _Block1.eFresnelIntensity) * 1.0) * _9846);
                    <div class="node-info">🔹variable(_18255)→unknown | 🔹variable(_9179)→half3 | 🔹literal((((float3(_Block1.eFresnelColor))→unknown | 🔹literal(_9831))→unknown | 🔹literal(_Block1.eFresnelIntensity))→unknown | 🔹literal(1.0))→unknown | 🔹variable(_9846)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸type_cast(half3)→half3 | 🔸operator(+)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">211</div>
                <div class="line-content">
                    _18230 = clamp(half(fast::max(fast::clamp(_Block1.eFresnelAlphaAdd, 0.0, 1.0) * _8298, _9846)), half(0.0), half(1.0));
                    <div class="node-info">🔹variable(_18230)→unknown | 🔹literal(fast::max(fast::clamp(_Block1.eFresnelAlphaAdd, 0.0, 1.0))→unknown | 🔹literal(_8298, _9846))→unknown | 🔸operator(*)→unknown | 🔸type_cast(half)→half | 🔹literal(0.0)→float | 🔸type_cast(half)→half | 🔹literal(1.0)→float | 🔸type_cast(half)→half | 🔸function(clamp)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">215</div>
                <div class="line-content">
                    _18255 = _9179;
                    <div class="node-info">🔹variable(_18255)→unknown | 🔹variable(_9179)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">216</div>
                <div class="line-content">
                    _18230 = _9787;
                    <div class="node-info">🔹variable(_18230)→unknown | 🔹variable(_9787)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">218</div>
                <div class="line-content">
                    float _9868 = float(_18230);
                    <div class="node-info">🔹declaration(float _9868)→float | 🔹variable(_18230)→unknown | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">219</div>
                <div class="line-content">
                    half _8346 = _18236 * half(_Block1.SHAOParam.w);
                    <div class="node-info">🔹declaration(half _8346)→half | 🔹variable(_18236)→unknown | 🔹literal(_Block1.SHAOParam.w)→unknown | 🔸type_cast(half)→half | 🔸operator(*)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">220</div>
                <div class="line-content">
                    float4 _9926 = float4((in.IN_WorldPosition.xyz + (_8925 * _Block1.cBiasFarAwayShadow)) - _Block1.CameraPos.xyz, 1.0) * _Block1.ShadowViewProjTexs0;
                    <div class="node-info">🔹declaration(float4 _9926)→float4 | 🔹literal(float4((in.IN_WorldPosition.xyz)→unknown | 🔹literal((_8925)→unknown | 🔹literal(_Block1.cBiasFarAwayShadow)))→unknown | 🔸operator(*)→unknown | 🔹literal(_Block1.CameraPos.xyz, 1.0))→unknown | 🔹member_access(_Block1.ShadowViewProjTexs0)→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">221</div>
                <div class="line-content">
                    float4 _17899 = _9926;
                    <div class="node-info">🔹declaration(float4 _17899)→float4 | 🔹variable(_9926)→float4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">222</div>
                <div class="line-content">
                    _17899.z = _9926.z - _Block1.CSMShadowBiases.x;
                    <div class="node-info">🔹literal(_17899.z = _9926.z)→unknown | 🔹literal(_Block1.CSMShadowBiases.x)→unknown | 🔸operator(-)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">223</div>
                <div class="line-content">
                    float4 _9942 = float4(in.IN_WorldPosition.xyz, 1.0);
                    <div class="node-info">🔹declaration(float4 _9942)→float4 | 🔹literal(in.IN_WorldPosition.xyz, 1.0)→unknown | 🔸type_cast(float4)→float4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">224</div>
                <div class="line-content">
                    float4 _9945 = _9942 * _Block1.ShadowViewProjTexs1;
                    <div class="node-info">🔹declaration(float4 _9945)→float4 | 🔹variable(_9942)→float4 | 🔹member_access(_Block1.ShadowViewProjTexs1)→unknown | 🔸operator(*)→float4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">225</div>
                <div class="line-content">
                    float4 _17902 = _9945;
                    <div class="node-info">🔹declaration(float4 _17902)→float4 | 🔹variable(_9945)→float4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">226</div>
                <div class="line-content">
                    _17902.z = _9945.z - _Block1.CSMShadowBiases.y;
                    <div class="node-info">🔹literal(_17902.z = _9945.z)→unknown | 🔹literal(_Block1.CSMShadowBiases.y)→unknown | 🔸operator(-)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">228</div>
                <div class="line-content">
                    if (_Block1.CSMCacheIndexs.z > 0.0)
                    <div class="node-info">🔹literal(_Block1.CSMCacheIndexs.z)→unknown | 🔹literal(0.0)→float | 🔸operator(>)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">230</div>
                <div class="line-content">
                    float4 _9971 = _9942 * _Block1.ShadowViewProjTexs2;
                    <div class="node-info">🔹declaration(float4 _9971)→float4 | 🔹variable(_9942)→float4 | 🔹member_access(_Block1.ShadowViewProjTexs2)→unknown | 🔸operator(*)→float4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">231</div>
                <div class="line-content">
                    _9971.z = _9971.z - _Block1.CSMShadowBiases.z;
                    <div class="node-info">🔹literal(_9971.z = _9971.z)→unknown | 🔹literal(_Block1.CSMShadowBiases.z)→unknown | 🔸operator(-)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">232</div>
                <div class="line-content">
                    _18237 = _9971;
                    <div class="node-info">🔹variable(_18237)→unknown | 🔹variable(_9971)→float4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">236</div>
                <div class="line-content">
                    _18237 = float4(0.0, 0.0, 0.0, 1.0);
                    <div class="node-info">🔹variable(_18237)→unknown | 🔹literal(0.0, 0.0, 0.0, 1.0)→unknown | 🔸type_cast(float4)→float4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">238</div>
                <div class="line-content">
                    float3 _10033 = _17902.xyz / float3(_9945.w);
                    <div class="node-info">🔹declaration(float3 _10033)→float3 | 🔹member_access(_17902.xyz)→float3 | 🔹member_access(_9945.w)→float | 🔸type_cast(float3)→float3 | 🔸operator(/)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">239</div>
                <div class="line-content">
                    float3 _10040 = _18237.xyz / float3(_18237.w);
                    <div class="node-info">🔹declaration(float3 _10040)→float3 | 🔹member_access(_18237.xyz)→unknown | 🔹member_access(_18237.w)→unknown | 🔸type_cast(float3)→float3 | 🔸operator(/)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">240</div>
                <div class="line-content">
                    float3 _10077 = _10040 * (step(-0.100000001490116119384765625, _Block1.CSMShadowBiases.z) * float(all(_10040 > float3(0.0)) && all(_10040 < float3(1.0))));
                    <div class="node-info">🔹declaration(float3 _10077)→float3 | 🔹variable(_10040)→float3 | 🔹literal((step()→unknown | 🔸operator(*)→float3 | 🔹literal(0.100000001490116119384765625, _Block1.CSMShadowBiases.z))→unknown | 🔹variable(_10040)→float3 | 🔹literal(float3(0.0)→unknown | 🔸operator(>)→bool | 🔸function(all)→unknown | 🔸type_cast(float)→float | 🔸operator(*)→float | 🔸operator(-)→float3 | 🔹variable(_10040)→float3 | 🔹literal(1.0)))→unknown | 🔸type_cast(float3)→float3 | 🔸operator(<)→bool | 🔸function(all)→unknown | 🔸operator(&&)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">241</div>
                <div class="line-content">
                    float _21135 = step(-0.100000001490116119384765625, _Block1.CSMShadowBiases.y) * float(all(_10033 > float3(0.0)) && all(_10033 < float3(1.0)));
                    <div class="node-info">🔹declaration(float _21135)→float | 🔹literal(-0.100000001490116119384765625)→unknown | 🔹literal(_Block1.CSMShadowBiases.y))→unknown | 🔹variable(_10033)→float3 | 🔹literal(float3(0.0)→unknown | 🔸operator(>)→bool | 🔸function(all)→unknown | 🔸type_cast(float)→float | 🔸operator(*)→float | 🔹variable(_10033)→float3 | 🔹literal(1.0)→float | 🔸type_cast(float3)→float3 | 🔸operator(<)→bool | 🔸function(all)→unknown | 🔸operator(&&)→bool | 🔸function(step)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">242</div>
                <div class="line-content">
                    float3 _21138 = _10077 + ((_10033 - _10077) * _21135);
                    <div class="node-info">🔹declaration(float3 _21138)→float3 | 🔹variable(_10077)→float3 | 🔹literal(((_10033)→unknown | 🔹literal(_10077))→unknown | 🔹literal(_21135))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔸operator(+)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">243</div>
                <div class="line-content">
                    float _10113 = _21138.z;
                    <div class="node-info">🔹declaration(float _10113)→float | 🔹member_access(_21138.z)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">244</div>
                <div class="line-content">
                    float2 _10120 = float2(_Block1.cShadowBias.w);
                    <div class="node-info">🔹declaration(float2 _10120)→float2 | 🔹literal(_Block1.cShadowBias.w)→unknown | 🔸type_cast(float2)→float2 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">245</div>
                <div class="line-content">
                    float2 _10167 = (_21138.xy / _10120) - float2(0.5);
                    <div class="node-info">🔹declaration(float2 _10167)→float2 | 🔹literal((_21138.xy)→unknown | 🔹literal(_10120))→unknown | 🔸operator(/)→unknown | 🔹literal(0.5)→float | 🔸type_cast(float2)→float2 | 🔸operator(-)→float2 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">246</div>
                <div class="line-content">
                    float2 _10169 = fract(_10167);
                    <div class="node-info">🔹declaration(float2 _10169)→float2 | 🔹variable(_10167)→float2 | 🔸function(fract)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">247</div>
                <div class="line-content">
                    float2 _10171 = floor(_10167);
                    <div class="node-info">🔹declaration(float2 _10171)→float2 | 🔹variable(_10167)→float2 | 🔸function(floor)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">248</div>
                <div class="line-content">
                    float2 _10177 = float2(2.0) - _10169;
                    <div class="node-info">🔹declaration(float2 _10177)→float2 | 🔹literal(2.0)→float | 🔸type_cast(float2)→float2 | 🔹variable(_10169)→float2 | 🔸operator(-)→float2 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">249</div>
                <div class="line-content">
                    float2 _10181 = _10169 + float2(1.0);
                    <div class="node-info">🔹declaration(float2 _10181)→float2 | 🔹variable(_10169)→float2 | 🔹literal(1.0)→float | 🔸type_cast(float2)→float2 | 🔸operator(+)→float2 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">250</div>
                <div class="line-content">
                    float2 _10184 = float2(1.0) / _10177;
                    <div class="node-info">🔹declaration(float2 _10184)→float2 | 🔹literal(1.0)→float | 🔸type_cast(float2)→float2 | 🔹variable(_10177)→float2 | 🔸operator(/)→float2 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">251</div>
                <div class="line-content">
                    float2 _10187 = _10169 / _10181;
                    <div class="node-info">🔹declaration(float2 _10187)→float2 | 🔹variable(_10169)→float2 | 🔹variable(_10181)→float2 | 🔸operator(/)→float2 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">252</div>
                <div class="line-content">
                    float _10205 = float(int(_Block1.CSMCacheIndexs[int(2.0 + ((-1.0) * _21135))]));
                    <div class="node-info">🔹declaration(float _10205)→float | 🔹literal(_Block1.CSMCacheIndexs[int(2.0)→unknown | 🔹literal((()→unknown | 🔹literal(1.0))→unknown | 🔹literal(_21135))])→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔸operator(+)→unknown | 🔸function(int)→unknown | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">253</div>
                <div class="line-content">
                    float3 _10208 = float3(((_10171 + float2(-0.5)) + _10184) * _10120, _10205);
                    <div class="node-info">🔹declaration(float3 _10208)→float3 | 🔹literal(((_10171)→unknown | 🔹literal(-0.5))→unknown | 🔸type_cast(float2)→float2 | 🔹literal(_10184))→unknown | 🔹literal(_10120, _10205)→unknown | 🔸operator(*)→unknown | 🔸operator(+)→float2 | 🔸operator(+)→float2 | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">254</div>
                <div class="line-content">
                    float3 _10231 = float3(((_10171 + float2(1.5, -0.5)) + float2(_10187.x, _10184.y)) * _10120, _10205);
                    <div class="node-info">🔹declaration(float3 _10231)→float3 | 🔹literal(((_10171)→unknown | 🔹literal(1.5,)→unknown | 🔹literal(0.5))→unknown | 🔸operator(-)→unknown | 🔸type_cast(float2)→float2 | 🔹literal(_10187.x, _10184.y))→unknown | 🔸type_cast(float2)→float2 | 🔹literal(_10120, _10205)→unknown | 🔸operator(*)→float2 | 🔸operator(+)→float2 | 🔸operator(+)→float2 | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">255</div>
                <div class="line-content">
                    float3 _10254 = float3(((_10171 + float2(-0.5, 1.5)) + float2(_10184.x, _10187.y)) * _10120, _10205);
                    <div class="node-info">🔹declaration(float3 _10254)→float3 | 🔹literal(((_10171)→unknown | 🔹literal(-0.5, 1.5))→unknown | 🔸type_cast(float2)→float2 | 🔹literal(_10184.x, _10187.y))→unknown | 🔸type_cast(float2)→float2 | 🔹literal(_10120, _10205)→unknown | 🔸operator(*)→float2 | 🔸operator(+)→float2 | 🔸operator(+)→float2 | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">256</div>
                <div class="line-content">
                    float3 _10276 = float3(((_10171 + float2(1.5)) + _10187) * _10120, _10205);
                    <div class="node-info">🔹declaration(float3 _10276)→float3 | 🔹literal(((_10171)→unknown | 🔹literal(1.5))→unknown | 🔸type_cast(float2)→float2 | 🔹literal(_10187))→unknown | 🔹literal(_10120, _10205)→unknown | 🔸operator(*)→unknown | 🔸operator(+)→float2 | 🔸operator(+)→float2 | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">257</div>
                <div class="line-content">
                    float _10282 = _10177.x;
                    <div class="node-info">🔹declaration(float _10282)→float | 🔹member_access(_10177.x)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">258</div>
                <div class="line-content">
                    float _10289 = _10181.x;
                    <div class="node-info">🔹declaration(float _10289)→float | 🔹member_access(_10181.x)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">259</div>
                <div class="line-content">
                    float _10300 = _10181.y;
                    <div class="node-info">🔹declaration(float _10300)→float | 🔹member_access(_10181.y)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">260</div>
                <div class="line-content">
                    float3 _9997 = _17899.xyz / float3(_9926.w);
                    <div class="node-info">🔹declaration(float3 _9997)→float3 | 🔹member_access(_17899.xyz)→float3 | 🔹member_access(_9926.w)→float | 🔸type_cast(float3)→float3 | 🔸operator(/)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">261</div>
                <div class="line-content">
                    float _10004 = fast::min(1.0 - float(half(10) * half(9.9956989288330078125e-05)), _9997.z);
                    <div class="node-info">🔹declaration(float _10004)→float | 🔹literal(fast::min(1.0)→unknown | 🔹literal(10)→int | 🔸type_cast(half)→half | 🔹literal(half(9.9956989288330078125e)→unknown | 🔸operator(*)→half | 🔹literal(05)), _9997.z)→unknown | 🔸operator(-)→half | 🔸type_cast(float)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">262</div>
                <div class="line-content">
                    float3 _17928 = _9997;
                    <div class="node-info">🔹declaration(float3 _17928)→float3 | 🔹variable(_9997)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">263</div>
                <div class="line-content">
                    _17928.z = _10004;
                    <div class="node-info">🔹literal(_17928.z = _10004)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">264</div>
                <div class="line-content">
                    float2 _10378 = (_17928.xy / _10120) - float2(0.5);
                    <div class="node-info">🔹declaration(float2 _10378)→float2 | 🔹literal((_17928.xy)→unknown | 🔹literal(_10120))→unknown | 🔸operator(/)→unknown | 🔹literal(0.5)→float | 🔸type_cast(float2)→float2 | 🔸operator(-)→float2 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">265</div>
                <div class="line-content">
                    float2 _10380 = fract(_10378);
                    <div class="node-info">🔹declaration(float2 _10380)→float2 | 🔹variable(_10378)→float2 | 🔸function(fract)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">266</div>
                <div class="line-content">
                    float2 _10382 = floor(_10378);
                    <div class="node-info">🔹declaration(float2 _10382)→float2 | 🔹variable(_10378)→float2 | 🔸function(floor)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">267</div>
                <div class="line-content">
                    float2 _10388 = float2(2.0) - _10380;
                    <div class="node-info">🔹declaration(float2 _10388)→float2 | 🔹literal(2.0)→float | 🔸type_cast(float2)→float2 | 🔹variable(_10380)→float2 | 🔸operator(-)→float2 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">268</div>
                <div class="line-content">
                    float2 _10392 = _10380 + float2(1.0);
                    <div class="node-info">🔹declaration(float2 _10392)→float2 | 🔹variable(_10380)→float2 | 🔹literal(1.0)→float | 🔸type_cast(float2)→float2 | 🔸operator(+)→float2 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">269</div>
                <div class="line-content">
                    float2 _10395 = float2(1.0) / _10388;
                    <div class="node-info">🔹declaration(float2 _10395)→float2 | 🔹literal(1.0)→float | 🔸type_cast(float2)→float2 | 🔹variable(_10388)→float2 | 🔸operator(/)→float2 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">270</div>
                <div class="line-content">
                    float2 _10398 = _10380 / _10392;
                    <div class="node-info">🔹declaration(float2 _10398)→float2 | 🔹variable(_10380)→float2 | 🔹variable(_10392)→float2 | 🔸operator(/)→float2 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">271</div>
                <div class="line-content">
                    float _10416 = float(int(_Block1.CSMCacheIndexs.x));
                    <div class="node-info">🔹declaration(float _10416)→float | 🔹literal(_Block1.CSMCacheIndexs.x)→unknown | 🔸function(int)→unknown | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">272</div>
                <div class="line-content">
                    float3 _10419 = float3(((_10382 + float2(-0.5)) + _10395) * _10120, _10416);
                    <div class="node-info">🔹declaration(float3 _10419)→float3 | 🔹literal(((_10382)→unknown | 🔹literal(-0.5))→unknown | 🔸type_cast(float2)→float2 | 🔹literal(_10395))→unknown | 🔹literal(_10120, _10416)→unknown | 🔸operator(*)→unknown | 🔸operator(+)→float2 | 🔸operator(+)→float2 | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">273</div>
                <div class="line-content">
                    float3 _10442 = float3(((_10382 + float2(1.5, -0.5)) + float2(_10398.x, _10395.y)) * _10120, _10416);
                    <div class="node-info">🔹declaration(float3 _10442)→float3 | 🔹literal(((_10382)→unknown | 🔹literal(1.5,)→unknown | 🔹literal(0.5))→unknown | 🔸operator(-)→unknown | 🔸type_cast(float2)→float2 | 🔹literal(_10398.x, _10395.y))→unknown | 🔸type_cast(float2)→float2 | 🔹literal(_10120, _10416)→unknown | 🔸operator(*)→float2 | 🔸operator(+)→float2 | 🔸operator(+)→float2 | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">274</div>
                <div class="line-content">
                    float3 _10465 = float3(((_10382 + float2(-0.5, 1.5)) + float2(_10395.x, _10398.y)) * _10120, _10416);
                    <div class="node-info">🔹declaration(float3 _10465)→float3 | 🔹literal(((_10382)→unknown | 🔹literal(-0.5, 1.5))→unknown | 🔸type_cast(float2)→float2 | 🔹literal(_10395.x, _10398.y))→unknown | 🔸type_cast(float2)→float2 | 🔹literal(_10120, _10416)→unknown | 🔸operator(*)→float2 | 🔸operator(+)→float2 | 🔸operator(+)→float2 | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">275</div>
                <div class="line-content">
                    float3 _10487 = float3(((_10382 + float2(1.5)) + _10398) * _10120, _10416);
                    <div class="node-info">🔹declaration(float3 _10487)→float3 | 🔹literal(((_10382)→unknown | 🔹literal(1.5))→unknown | 🔸type_cast(float2)→float2 | 🔹literal(_10398))→unknown | 🔹literal(_10120, _10416)→unknown | 🔸operator(*)→unknown | 🔸operator(+)→float2 | 🔸operator(+)→float2 | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">276</div>
                <div class="line-content">
                    float _10493 = _10388.x;
                    <div class="node-info">🔹declaration(float _10493)→float | 🔹member_access(_10388.x)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">277</div>
                <div class="line-content">
                    float _10500 = _10392.x;
                    <div class="node-info">🔹declaration(float _10500)→float | 🔹member_access(_10392.x)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">278</div>
                <div class="line-content">
                    float _10511 = _10392.y;
                    <div class="node-info">🔹declaration(float _10511)→float | 🔹member_access(_10392.y)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">279</div>
                <div class="line-content">
                    half _8351 = max(half(mix(0.0, 1.0 - fast::clamp((abs(dot(_9109, _Block1.SunDirection.xyz)) + ((2.0 * _9100) * _9100)) - 1.0, 0.0, 1.0), _Block1.cMicroShadow)), max(half(((((_10388.y * ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10419.xy, uint(rint(_10419.z)), _10004, level(0.0)) * _10493) + (sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10442.xy, uint(rint(_10442.z)), _10004, level(0.0)) * _10500))) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10465.xy, uint(rint(_10465.z)), _10004, level(0.0)) * _10493) * _10511)) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10487.xy, uint(rint(_10487.z)), _10004, level(0.0)) * _10500) * _10511)) * 0.111111097037792205810546875) * float(all(_17928 > float3(0.0)) && all(_17928 < float3(1.0)))), half(fast::min(1.0, float(half(((((_10177.y * ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10208.xy, uint(rint(_10208.z)), _10113, level(0.0)) * _10282) + (sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10231.xy, uint(rint(_10231.z)), _10113, level(0.0)) * _10289))) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10254.xy, uint(rint(_10254.z)), _10113, level(0.0)) * _10282) * _10300)) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10276.xy, uint(rint(_10276.z)), _10113, level(0.0)) * _10289) * _10300)) * 0.111111097037792205810546875) * float(all(_21138 > float3(0.0)) && all(_21138 < float3(1.0)))))))));
                    <div class="node-info">🔹declaration(half _8351)→half | 🔹literal(0.0)→float | 🔹literal(1.0)→float | 🔹literal(fast::clamp((abs(dot(_9109, _Block1.SunDirection.xyz)))→unknown | 🔸operator(-)→float | 🔹literal(((2.0)→unknown | 🔹literal(_9100))→unknown | 🔹literal(_9100)))→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(1.0, 0.0, 1.0))→unknown | 🔸operator(-)→unknown | 🔸operator(+)→float | 🔹member_access(_Block1.cMicroShadow)→unknown | 🔸function(mix)→float | 🔸type_cast(half)→half | 🔹literal(((((_10388.y)→unknown | 🔹literal(((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10419.xy, uint(rint(_10419.z)), _10004, level(0.0)))→unknown | 🔹literal(_10493))→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10442.xy, uint(rint(_10442.z)), _10004, level(0.0)))→unknown | 🔹literal(_10500))))→unknown | 🔸operator(*)→unknown | 🔹literal(((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10465.xy, uint(rint(_10465.z)), _10004, level(0.0)))→unknown | 🔹literal(_10493))→unknown | 🔹literal(_10511)))→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10487.xy, uint(rint(_10487.z)), _10004, level(0.0)))→unknown | 🔹literal(_10500))→unknown | 🔹literal(_10511)))→unknown | 🔹literal(0.111111097037792205810546875))→unknown | 🔹variable(_17928)→float3 | 🔹literal(float3(0.0)→unknown | 🔸operator(>)→bool | 🔸function(all)→unknown | 🔸type_cast(float)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(+)→float | 🔸operator(+)→float | 🔸operator(+)→float | 🔹variable(_17928)→float3 | 🔹literal(1.0))→unknown | 🔸type_cast(float3)→float3 | 🔸operator(<)→bool | 🔸function(all)→unknown | 🔸operator(&&)→bool | 🔸type_cast(half)→half | 🔹literal(fast::min(1.0, float(half(((((_10177.y)→unknown | 🔹literal(((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10208.xy, uint(rint(_10208.z)), _10113, level(0.0)))→unknown | 🔹literal(_10282))→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10231.xy, uint(rint(_10231.z)), _10113, level(0.0)))→unknown | 🔹literal(_10289))))→unknown | 🔸operator(*)→unknown | 🔹literal(((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10254.xy, uint(rint(_10254.z)), _10113, level(0.0)))→unknown | 🔹literal(_10282))→unknown | 🔹literal(_10300)))→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10276.xy, uint(rint(_10276.z)), _10113, level(0.0)))→unknown | 🔹literal(_10289))→unknown | 🔹literal(_10300)))→unknown | 🔹literal(0.111111097037792205810546875))→unknown | 🔹variable(_21138)→float3 | 🔹literal(float3(0.0)→unknown | 🔸operator(>)→bool | 🔸function(all)→unknown | 🔸type_cast(float)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(+)→float | 🔸operator(+)→float | 🔸operator(+)→float | 🔹variable(_21138)→float3 | 🔹literal(1.0)))))→unknown | 🔸type_cast(float3)→float3 | 🔸operator(<)→bool | 🔸function(all)→unknown | 🔸operator(&&)→bool | 🔸type_cast(half)→half | 🔸function(max)→half | 🔸function(max)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">280</div>
                <div class="line-content">
                    float3 _8370 = in.IN_WorldPosition.xyz - _Block1.CameraPos.xyz;
                    <div class="node-info">🔹declaration(float3 _8370)→float3 | 🔹literal(in.IN_WorldPosition.xyz)→unknown | 🔹literal(_Block1.CameraPos.xyz)→unknown | 🔸operator(-)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">281</div>
                <div class="line-content">
                    float3 _8373 = fast::normalize(-_8370);
                    <div class="node-info">🔹declaration(float3 _8373)→float3 | 🔹literal(fast::normalize()→unknown | 🔹literal(_8370))→unknown | 🔸operator(-)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">282</div>
                <div class="line-content">
                    float _8378 = dot(_9109, _8373);
                    <div class="node-info">🔹declaration(float _8378)→float | 🔹variable(_9109)→float3 | 🔹variable(_8373)→float3 | 🔸function(dot)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">283</div>
                <div class="line-content">
                    half3 _10557 = mix(half3(half(0.039999999105930328369140625)), _8315, half3(_18234));
                    <div class="node-info">🔹declaration(half3 _10557)→half3 | 🔹literal(0.039999999105930328369140625)→float | 🔸type_cast(half)→half | 🔸type_cast(half3)→half3 | 🔹variable(_8315)→half3 | 🔹variable(_18234)→unknown | 🔸type_cast(half3)→half3 | 🔸function(mix)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">284</div>
                <div class="line-content">
                    half3 _10569 = half3(float3(_8315 - (_8315 * _18234)) * float3(0.3183098733425140380859375));
                    <div class="node-info">🔹declaration(half3 _10569)→half3 | 🔹variable(_8315)→half3 | 🔹literal((_8315)→unknown | 🔹literal(_18234)))→unknown | 🔹literal(float3(0.3183098733425140380859375)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(-)→half3 | 🔸type_cast(float3)→float3 | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">285</div>
                <div class="line-content">
                    float3 _10588 = float3(_Block1.EnvInfo.z);
                    <div class="node-info">🔹declaration(float3 _10588)→float3 | 🔹literal(_Block1.EnvInfo.z)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">286</div>
                <div class="line-content">
                    half3 _8393 = half3(half(0.0));
                    <div class="node-info">🔹declaration(half3 _8393)→half3 | 🔹literal(0.0)→float | 🔸type_cast(half)→half | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">287</div>
                <div class="line-content">
                    uint _8397 = as_type<uint>(_Block1.SHGIParam.w);
                    <div class="node-info">🔹declaration(uint _8397)→uint | 🔹variable(as_type)→unknown | 🔹variable(uint)→unknown | 🔹literal((_Block1.SHGIParam.w))→unknown | 🔸operator(>)→bool | 🔸operator(<)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">288</div>
                <div class="line-content">
                    bool _8401 = (_8397 & 63u) > 0u;
                    <div class="node-info">🔹declaration(bool _8401)→bool | 🔹literal((_8397 & 63u))→unknown | 🔹literal(0u)→unknown | 🔸operator(>)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">290</div>
                <div class="line-content">
                    if (_8401)
                    <div class="node-info">🔹variable(_8401)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">292</div>
                <div class="line-content">
                    float3 _8435 = select(_Block1.PlayerPos.xyz, _Block1.CameraPos.xyz, bool3((_8397 & 524288u) > 0u));
                    <div class="node-info">🔹declaration(float3 _8435)→float3 | 🔹literal(_Block1.PlayerPos.xyz)→unknown | 🔹literal(_Block1.CameraPos.xyz)→unknown | 🔹literal((_8397 & 524288u))→unknown | 🔹literal(0u)→unknown | 🔸operator(>)→bool | 🔸function(bool3)→unknown | 🔸function(select)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">294</div>
                <div class="line-content">
                    if (_8401)
                    <div class="node-info">🔹variable(_8401)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">297</div>
                <div class="line-content">
                    if ((_8397 & 8u) != 0u)
                    <div class="node-info">🔹literal((_8397 & 8u))→unknown | 🔹literal(0u)→unknown | 🔸operator(!=)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">299</div>
                <div class="line-content">
                    float3 _10686 = (in.IN_WorldPosition.xyz + (_8373 * 0.100000001490116119384765625)) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125);
                    <div class="node-info">🔹declaration(float3 _10686)→float3 | 🔹literal((in.IN_WorldPosition.xyz)→unknown | 🔹literal((_8373)→unknown | 🔹literal(0.100000001490116119384765625)))→unknown | 🔹literal(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)→unknown | 🔸type_cast(float3)→float3 | 🔸operator(*)→float3 | 🔸operator(*)→float3 | 🔸operator(+)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">300</div>
                <div class="line-content">
                    float3 _10762 = _10686 - floor(_10686);
                    <div class="node-info">🔹declaration(float3 _10762)→float3 | 🔹variable(_10686)→float3 | 🔹variable(_10686)→float3 | 🔸function(floor)→unknown | 🔸operator(-)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">301</div>
                <div class="line-content">
                    float3 _10789 = _8435 * float3(0.125);
                    <div class="node-info">🔹declaration(float3 _10789)→float3 | 🔹variable(_8435)→float3 | 🔹literal(0.125)→float | 🔸type_cast(float3)→float3 | 🔸operator(*)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">302</div>
                <div class="line-content">
                    float _10797 = _10789.x;
                    <div class="node-info">🔹declaration(float _10797)→float | 🔹member_access(_10789.x)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">303</div>
                <div class="line-content">
                    float _10799 = floor(_10797);
                    <div class="node-info">🔹declaration(float _10799)→float | 🔹variable(_10797)→float | 🔸function(floor)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">305</div>
                <div class="line-content">
                    _21191.x = _10799 - 15.0;
                    <div class="node-info">🔹literal(_21191.x = _10799)→unknown | 🔹literal(15.0)→float | 🔸operator(-)→float</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">307</div>
                <div class="line-content">
                    if ((_10797 - _10799) > 0.5)
                    <div class="node-info">🔹literal((_10797)→unknown | 🔹literal(_10799))→unknown | 🔹literal(0.5)→float | 🔸operator(>)→bool | 🔸operator(-)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">309</div>
                <div class="line-content">
                    float3 _21194 = _21191;
                    <div class="node-info">🔹declaration(float3 _21194)→float3 | 🔹variable(_21191)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">310</div>
                <div class="line-content">
                    _21194.x = _10799 + (-14.0);
                    <div class="node-info">🔹literal(_21194.x = _10799)→unknown | 🔹literal(()→unknown | 🔹literal(14.0))→unknown | 🔸operator(-)→unknown | 🔸operator(+)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">311</div>
                <div class="line-content">
                    _21235 = _21194;
                    <div class="node-info">🔹variable(_21235)→unknown | 🔹variable(_21194)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">315</div>
                <div class="line-content">
                    _21235 = _21191;
                    <div class="node-info">🔹variable(_21235)→unknown | 🔹variable(_21191)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">317</div>
                <div class="line-content">
                    float _21078 = _10789.y;
                    <div class="node-info">🔹declaration(float _21078)→float | 🔹member_access(_10789.y)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">318</div>
                <div class="line-content">
                    float _21079 = floor(_21078);
                    <div class="node-info">🔹declaration(float _21079)→float | 🔹variable(_21078)→float | 🔸function(floor)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">319</div>
                <div class="line-content">
                    float3 _21198 = _21235;
                    <div class="node-info">🔹declaration(float3 _21198)→float3 | 🔹variable(_21235)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">320</div>
                <div class="line-content">
                    _21198.y = _21079 - 8.0;
                    <div class="node-info">🔹literal(_21198.y = _21079)→unknown | 🔹literal(8.0)→float | 🔸operator(-)→float</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">322</div>
                <div class="line-content">
                    if ((_21078 - _21079) > 0.5)
                    <div class="node-info">🔹literal((_21078)→unknown | 🔹literal(_21079))→unknown | 🔹literal(0.5)→float | 🔸operator(>)→bool | 🔸operator(-)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">324</div>
                <div class="line-content">
                    float3 _21201 = _21198;
                    <div class="node-info">🔹declaration(float3 _21201)→float3 | 🔹variable(_21198)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">325</div>
                <div class="line-content">
                    _21201.y = _21079 + (-7.0);
                    <div class="node-info">🔹literal(_21201.y = _21079)→unknown | 🔹literal(()→unknown | 🔹literal(7.0))→unknown | 🔸operator(-)→unknown | 🔸operator(+)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">326</div>
                <div class="line-content">
                    _21236 = _21201;
                    <div class="node-info">🔹variable(_21236)→unknown | 🔹variable(_21201)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">330</div>
                <div class="line-content">
                    _21236 = _21198;
                    <div class="node-info">🔹variable(_21236)→unknown | 🔹variable(_21198)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">332</div>
                <div class="line-content">
                    float _21100 = _10789.z;
                    <div class="node-info">🔹declaration(float _21100)→float | 🔹member_access(_10789.z)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">333</div>
                <div class="line-content">
                    float _21101 = floor(_21100);
                    <div class="node-info">🔹declaration(float _21101)→float | 🔹variable(_21100)→float | 🔸function(floor)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">334</div>
                <div class="line-content">
                    float3 _21205 = _21236;
                    <div class="node-info">🔹declaration(float3 _21205)→float3 | 🔹variable(_21236)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">335</div>
                <div class="line-content">
                    _21205.z = _21101 - 15.0;
                    <div class="node-info">🔹literal(_21205.z = _21101)→unknown | 🔹literal(15.0)→float | 🔸operator(-)→float</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">337</div>
                <div class="line-content">
                    if ((_21100 - _21101) > 0.5)
                    <div class="node-info">🔹literal((_21100)→unknown | 🔹literal(_21101))→unknown | 🔹literal(0.5)→float | 🔸operator(>)→bool | 🔸operator(-)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">339</div>
                <div class="line-content">
                    float3 _21208 = _21205;
                    <div class="node-info">🔹declaration(float3 _21208)→float3 | 🔹variable(_21205)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">340</div>
                <div class="line-content">
                    _21208.z = _21101 + (-14.0);
                    <div class="node-info">🔹literal(_21208.z = _21101)→unknown | 🔹literal(()→unknown | 🔹literal(14.0))→unknown | 🔸operator(-)→unknown | 🔸operator(+)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">341</div>
                <div class="line-content">
                    _21237 = _21208;
                    <div class="node-info">🔹variable(_21237)→unknown | 🔹variable(_21208)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">345</div>
                <div class="line-content">
                    _21237 = _21205;
                    <div class="node-info">🔹variable(_21237)→unknown | 🔹variable(_21205)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">347</div>
                <div class="line-content">
                    float3 _10822 = _21237 * 8.0;
                    <div class="node-info">🔹declaration(float3 _10822)→float3 | 🔹variable(_21237)→unknown | 🔹literal(8.0)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">349</div>
                <div class="line-content">
                    if (all(in.IN_WorldPosition.xyz >= _10822) && all(in.IN_WorldPosition.xyz < (_10822 + float3(240.0, 128.0, 240.0))))
                    <div class="node-info">🔹literal(in.IN_WorldPosition.xyz)→unknown | 🔹literal(_10822))→unknown | 🔹literal(in.IN_WorldPosition.xyz)→unknown | 🔹literal((_10822)→unknown | 🔸operator(<)→bool | 🔹literal(240.0, 128.0, 240.0)→unknown | 🔸type_cast(float3)→float3 | 🔸operator(+)→float3 | 🔸function(all)→unknown | 🔸operator(&&)→bool | 🔸operator(>=)→bool | 🔸function(all)→unknown | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">351</div>
                <div class="line-content">
                    uint _10704 = (_8397 & 251658240u) >> 24u;
                    <div class="node-info">🔹declaration(uint _10704)→uint | 🔹literal((_8397 & 251658240u))→unknown | 🔹literal(> 24u)→unknown | 🔸operator(>)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">352</div>
                <div class="line-content">
                    float _10887 = 3.0 - float((_8397 & 458752u) >> 16u);
                    <div class="node-info">🔹declaration(float _10887)→float | 🔹literal(3.0)→float | 🔹literal((_8397 & 458752u))→unknown | 🔹literal(> 16u)→unknown | 🔸operator(>)→bool | 🔸type_cast(float)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">354</div>
                <div class="line-content">
                    if (_10704 <= 3u)
                    <div class="node-info">🔹variable(_10704)→uint | 🔹literal(3u)→unknown | 🔸operator(<=)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">356</div>
                <div class="line-content">
                    float _10900 = 3.0 - float(_10704);
                    <div class="node-info">🔹declaration(float _10900)→float | 🔹literal(3.0)→float | 🔹variable(_10704)→uint | 🔸type_cast(float)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">357</div>
                <div class="line-content">
                    float2 _10994 = ((_10762.xz - float2(0.5)) * 0.9375) + float2(0.5);
                    <div class="node-info">🔹declaration(float2 _10994)→float2 | 🔹literal(((_10762.xz)→unknown | 🔹literal(0.5)))→unknown | 🔹literal(0.9375)→float | 🔸operator(*)→float | 🔸type_cast(float2)→float2 | 🔸operator(-)→float2 | 🔹literal(0.5)→float | 🔸type_cast(float2)→float2 | 🔸operator(+)→float2 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">358</div>
                <div class="line-content">
                    float _11001 = _10822.x * 0.0041666668839752674102783203125;
                    <div class="node-info">🔹declaration(float _11001)→float | 🔹member_access(_10822.x)→float | 🔹literal(0.0041666668839752674102783203125)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">359</div>
                <div class="line-content">
                    float _11005 = ((_11001 - floor(_11001)) - 0.5) * 0.9375;
                    <div class="node-info">🔹declaration(float _11005)→float | 🔹literal(((_11001)→unknown | 🔹literal(_11001))→unknown | 🔸function(floor)→unknown | 🔹literal(0.5))→unknown | 🔹literal(0.9375)→float | 🔸operator(*)→float | 🔸operator(-)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">360</div>
                <div class="line-content">
                    float _11011 = _10822.z * 0.0041666668839752674102783203125;
                    <div class="node-info">🔹declaration(float _11011)→float | 🔹member_access(_10822.z)→float | 🔹literal(0.0041666668839752674102783203125)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">361</div>
                <div class="line-content">
                    float _11015 = ((_11011 - floor(_11011)) - 0.5) * 0.9375;
                    <div class="node-info">🔹declaration(float _11015)→float | 🔹literal(((_11011)→unknown | 🔹literal(_11011))→unknown | 🔸function(floor)→unknown | 🔹literal(0.5))→unknown | 🔹literal(0.9375)→float | 🔸operator(*)→float | 🔸operator(-)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">362</div>
                <div class="line-content">
                    float _11020 = _10994.x;
                    <div class="node-info">🔹declaration(float _11020)→float | 🔹member_access(_10994.x)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">364</div>
                <div class="line-content">
                    _17954.x = (_11020 < (_11005 + 0.5)) ? fast::min(_11020, _11005 + 0.49609375) : fast::max(_11020, _11005 + 0.50390625);
                    <div class="node-info">🔹literal(_17954.x = (_11020)→unknown | 🔹literal((_11005)→unknown | 🔸operator(<)→bool | 🔹literal(0.5)) ? fast::min(_11020, _11005)→unknown | 🔹literal(0.49609375) : fast::max(_11020, _11005)→unknown | 🔹literal(0.50390625))→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→bool</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">365</div>
                <div class="line-content">
                    float _11038 = _10994.y;
                    <div class="node-info">🔹declaration(float _11038)→float | 🔹member_access(_10994.y)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">366</div>
                <div class="line-content">
                    _17954.z = (_11038 < (_11015 + 0.5)) ? fast::min(_11038, _11015 + 0.49609375) : fast::max(_11038, _11015 + 0.50390625);
                    <div class="node-info">🔹literal(_17954.z = (_11038)→unknown | 🔹literal((_11015)→unknown | 🔸operator(<)→bool | 🔹literal(0.5)) ? fast::min(_11038, _11015)→unknown | 🔹literal(0.49609375) : fast::max(_11038, _11015)→unknown | 🔹literal(0.50390625))→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→bool</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">367</div>
                <div class="line-content">
                    float _11059 = (_10762.y * 64.0) - 0.5;
                    <div class="node-info">🔹declaration(float _11059)→float | 🔹literal((_10762.y)→unknown | 🔹literal(64.0))→unknown | 🔸operator(*)→unknown | 🔹literal(0.5)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">368</div>
                <div class="line-content">
                    float _11064 = floor(_11059);
                    <div class="node-info">🔹declaration(float _11064)→float | 🔹variable(_11059)→float | 🔸function(floor)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">369</div>
                <div class="line-content">
                    uint _11067 = (_11059 < 0.0) ? 63u : uint(_11064);
                    <div class="node-info">🔹declaration(uint _11067)→uint | 🔹literal((_11059)→unknown | 🔹literal(0.0) ? 63u : uint(_11064))→unknown | 🔸operator(<)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">370</div>
                <div class="line-content">
                    uint _11070 = _11067 + 1u;
                    <div class="node-info">🔹declaration(uint _11070)→uint | 🔹variable(_11067)→uint | 🔹literal(1u)→unknown | 🔸operator(+)→uint | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">371</div>
                <div class="line-content">
                    uint _21301 = (_11070 >= 64u) ? 0u : _11070;
                    <div class="node-info">🔹declaration(uint _21301)→uint | 🔹literal((_11070)→unknown | 🔹literal(64u) ? 0u : _11070)→unknown | 🔸operator(>=)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">372</div>
                <div class="line-content">
                    float2 _11097 = (float2(float(_11067 & 7u), float(_11067 >> 3u)) + _17954.xz) * 0.125;
                    <div class="node-info">🔹declaration(float2 _11097)→float2 | 🔹literal((float2(float(_11067 & 7u), float(_11067)→unknown | 🔹literal(> 3u)))→unknown | 🔸operator(>)→bool | 🔹literal(_17954.xz))→unknown | 🔹literal(0.125)→float | 🔸operator(*)→float | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">373</div>
                <div class="line-content">
                    float _11100 = _11097.x;
                    <div class="node-info">🔹declaration(float _11100)→float | 🔹member_access(_11097.x)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">374</div>
                <div class="line-content">
                    float3 _11102 = float3(_11100, _11097.y, _10887);
                    <div class="node-info">🔹declaration(float3 _11102)→float3 | 🔹literal(_11100, _11097.y, _10887)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">376</div>
                <div class="line-content">
                    _17962.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11102.xy, uint(rint(_11102.z)), level(0.0)).x);
                    <div class="node-info">🔹literal(_17962.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11102.xy, uint(rint(_11102.z)), level(0.0)).x))→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">377</div>
                <div class="line-content">
                    float3 _11113 = float3(_11100, _11097.y, _10900);
                    <div class="node-info">🔹declaration(float3 _11113)→float3 | 🔹literal(_11100, _11097.y, _10900)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">378</div>
                <div class="line-content">
                    half3 _11118 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11113.xy, uint(rint(_11113.z)), level(0.0)).xyz);
                    <div class="node-info">🔹declaration(half3 _11118)→half3 | 🔹literal(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11113.xy, uint(rint(_11113.z)), level(0.0)).xyz)→unknown | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">379</div>
                <div class="line-content">
                    float2 _11135 = (float2(float(_21301 & 7u), float(_21301 >> 3u)) + _17954.xz) * 0.125;
                    <div class="node-info">🔹declaration(float2 _11135)→float2 | 🔹literal((float2(float(_21301 & 7u), float(_21301)→unknown | 🔹literal(> 3u)))→unknown | 🔸operator(>)→bool | 🔹literal(_17954.xz))→unknown | 🔹literal(0.125)→float | 🔸operator(*)→float | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">380</div>
                <div class="line-content">
                    float _11138 = _11135.x;
                    <div class="node-info">🔹declaration(float _11138)→float | 🔹member_access(_11135.x)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">381</div>
                <div class="line-content">
                    float3 _11140 = float3(_11138, _11135.y, _10887);
                    <div class="node-info">🔹declaration(float3 _11140)→float3 | 🔹literal(_11138, _11135.y, _10887)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">383</div>
                <div class="line-content">
                    _17964.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11140.xy, uint(rint(_11140.z)), level(0.0)).x);
                    <div class="node-info">🔹literal(_17964.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11140.xy, uint(rint(_11140.z)), level(0.0)).x))→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">384</div>
                <div class="line-content">
                    float3 _11151 = float3(_11138, _11135.y, _10900);
                    <div class="node-info">🔹declaration(float3 _11151)→float3 | 🔹literal(_11138, _11135.y, _10900)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">385</div>
                <div class="line-content">
                    half3 _11156 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11151.xy, uint(rint(_11151.z)), level(0.0)).xyz);
                    <div class="node-info">🔹declaration(half3 _11156)→half3 | 🔹literal(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11151.xy, uint(rint(_11151.z)), level(0.0)).xyz)→unknown | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">386</div>
                <div class="line-content">
                    half4 _11163 = mix(half4(_11118.x, _11118.y, _11118.z, _17962.w), half4(_11156.x, _11156.y, _11156.z, _17964.w), half4(half(fast::clamp(_11059 - _11064, 0.0, 1.0))));
                    <div class="node-info">🔹declaration(half4 _11163)→half4 | 🔹literal(_11118.x, _11118.y, _11118.z, _17962.w)→unknown | 🔸type_cast(half4)→half4 | 🔹literal(_11156.x, _11156.y, _11156.z, _17964.w)→unknown | 🔸type_cast(half4)→half4 | 🔹literal(fast::clamp(_11059)→unknown | 🔹literal(_11064, 0.0, 1.0))→unknown | 🔸operator(-)→unknown | 🔸type_cast(half)→half | 🔸type_cast(half4)→half4 | 🔸function(mix)→half4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">387</div>
                <div class="line-content">
                    _18297 = clamp((_11163.w * half(32.0)) + half(float(dot(half3(_8373), half3((float3(_11163.xyz) * float3(2.0)) - float3(1.0)))) * 2.0), half(0.0), half(1.0));
                    <div class="node-info">🔹variable(_18297)→unknown | 🔹literal((_11163.w)→unknown | 🔹literal(32.0))→unknown | 🔸type_cast(half)→half | 🔸operator(*)→half | 🔹variable(_8373)→float3 | 🔸type_cast(half3)→half3 | 🔹literal((float3(_11163.xyz)→unknown | 🔸type_cast(half3)→half3 | 🔹literal(float3(2.0)→unknown | 🔸operator(*)→half3 | 🔸function(dot)→float | 🔸type_cast(float)→float | 🔹literal(1.0))))→unknown | 🔸type_cast(float3)→float3 | 🔹literal(2.0)→float | 🔸operator(*)→float3 | 🔸operator(-)→float | 🔸type_cast(half)→half | 🔸operator(+)→half | 🔹literal(0.0)→float | 🔸type_cast(half)→half | 🔹literal(1.0)→float | 🔸type_cast(half)→half | 🔸function(clamp)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">391</div>
                <div class="line-content">
                    float2 _11233 = ((_10762.xz - float2(0.5)) * 0.9375) + float2(0.5);
                    <div class="node-info">🔹declaration(float2 _11233)→float2 | 🔹literal(((_10762.xz)→unknown | 🔹literal(0.5)))→unknown | 🔹literal(0.9375)→float | 🔸operator(*)→float | 🔸type_cast(float2)→float2 | 🔸operator(-)→float2 | 🔹literal(0.5)→float | 🔸type_cast(float2)→float2 | 🔸operator(+)→float2 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">392</div>
                <div class="line-content">
                    float _11240 = _10822.x * 0.0041666668839752674102783203125;
                    <div class="node-info">🔹declaration(float _11240)→float | 🔹member_access(_10822.x)→float | 🔹literal(0.0041666668839752674102783203125)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">393</div>
                <div class="line-content">
                    float _11244 = ((_11240 - floor(_11240)) - 0.5) * 0.9375;
                    <div class="node-info">🔹declaration(float _11244)→float | 🔹literal(((_11240)→unknown | 🔹literal(_11240))→unknown | 🔸function(floor)→unknown | 🔹literal(0.5))→unknown | 🔹literal(0.9375)→float | 🔸operator(*)→float | 🔸operator(-)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">394</div>
                <div class="line-content">
                    float _11250 = _10822.z * 0.0041666668839752674102783203125;
                    <div class="node-info">🔹declaration(float _11250)→float | 🔹member_access(_10822.z)→float | 🔹literal(0.0041666668839752674102783203125)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">395</div>
                <div class="line-content">
                    float _11254 = ((_11250 - floor(_11250)) - 0.5) * 0.9375;
                    <div class="node-info">🔹declaration(float _11254)→float | 🔹literal(((_11250)→unknown | 🔹literal(_11250))→unknown | 🔸function(floor)→unknown | 🔹literal(0.5))→unknown | 🔹literal(0.9375)→float | 🔸operator(*)→float | 🔸operator(-)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">396</div>
                <div class="line-content">
                    float _11259 = _11233.x;
                    <div class="node-info">🔹declaration(float _11259)→float | 🔹member_access(_11233.x)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">398</div>
                <div class="line-content">
                    _17977.x = (_11259 < (_11244 + 0.5)) ? fast::min(_11259, _11244 + 0.49609375) : fast::max(_11259, _11244 + 0.50390625);
                    <div class="node-info">🔹literal(_17977.x = (_11259)→unknown | 🔹literal((_11244)→unknown | 🔸operator(<)→bool | 🔹literal(0.5)) ? fast::min(_11259, _11244)→unknown | 🔹literal(0.49609375) : fast::max(_11259, _11244)→unknown | 🔹literal(0.50390625))→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→bool</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">399</div>
                <div class="line-content">
                    float _11277 = _11233.y;
                    <div class="node-info">🔹declaration(float _11277)→float | 🔹member_access(_11233.y)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">400</div>
                <div class="line-content">
                    _17977.z = (_11277 < (_11254 + 0.5)) ? fast::min(_11277, _11254 + 0.49609375) : fast::max(_11277, _11254 + 0.50390625);
                    <div class="node-info">🔹literal(_17977.z = (_11277)→unknown | 🔹literal((_11254)→unknown | 🔸operator(<)→bool | 🔹literal(0.5)) ? fast::min(_11277, _11254)→unknown | 🔹literal(0.49609375) : fast::max(_11277, _11254)→unknown | 🔹literal(0.50390625))→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→bool</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">401</div>
                <div class="line-content">
                    float _11298 = (_10762.y * 64.0) - 0.5;
                    <div class="node-info">🔹declaration(float _11298)→float | 🔹literal((_10762.y)→unknown | 🔹literal(64.0))→unknown | 🔸operator(*)→unknown | 🔹literal(0.5)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">402</div>
                <div class="line-content">
                    float _11303 = floor(_11298);
                    <div class="node-info">🔹declaration(float _11303)→float | 🔹variable(_11298)→float | 🔸function(floor)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">403</div>
                <div class="line-content">
                    uint _11306 = (_11298 < 0.0) ? 63u : uint(_11303);
                    <div class="node-info">🔹declaration(uint _11306)→uint | 🔹literal((_11298)→unknown | 🔹literal(0.0) ? 63u : uint(_11303))→unknown | 🔸operator(<)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">404</div>
                <div class="line-content">
                    uint _11309 = _11306 + 1u;
                    <div class="node-info">🔹declaration(uint _11309)→uint | 🔹variable(_11306)→uint | 🔹literal(1u)→unknown | 🔸operator(+)→uint | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">405</div>
                <div class="line-content">
                    uint _21300 = (_11309 >= 64u) ? 0u : _11309;
                    <div class="node-info">🔹declaration(uint _21300)→uint | 🔹literal((_11309)→unknown | 🔹literal(64u) ? 0u : _11309)→unknown | 🔸operator(>=)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">406</div>
                <div class="line-content">
                    float3 _11340 = float3((float2(float(_11306 & 7u), float(_11306 >> 3u)) + _17977.xz) * 0.125, _10887);
                    <div class="node-info">🔹declaration(float3 _11340)→float3 | 🔹literal((float2(float(_11306 & 7u), float(_11306)→unknown | 🔹literal(> 3u)))→unknown | 🔸operator(>)→bool | 🔹literal(_17977.xz))→unknown | 🔹literal(0.125, _10887)→unknown | 🔸operator(*)→unknown | 🔸operator(+)→bool | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">407</div>
                <div class="line-content">
                    float3 _11365 = float3((float2(float(_21300 & 7u), float(_21300 >> 3u)) + _17977.xz) * 0.125, _10887);
                    <div class="node-info">🔹declaration(float3 _11365)→float3 | 🔹literal((float2(float(_21300 & 7u), float(_21300)→unknown | 🔹literal(> 3u)))→unknown | 🔸operator(>)→bool | 🔹literal(_17977.xz))→unknown | 🔹literal(0.125, _10887)→unknown | 🔸operator(*)→unknown | 🔸operator(+)→bool | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">408</div>
                <div class="line-content">
                    _18297 = half(mix(float(half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11340.xy, uint(rint(_11340.z)), level(0.0)).x)), float(half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11365.xy, uint(rint(_11365.z)), level(0.0)).x)), fast::clamp(_11298 - _11303, 0.0, 1.0))) * half(32.0);
                    <div class="node-info">🔹variable(_18297)→unknown | 🔹literal(mix(float(half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11340.xy, uint(rint(_11340.z)), level(0.0)).x)), float(half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11365.xy, uint(rint(_11365.z)), level(0.0)).x)), fast::clamp(_11298)→unknown | 🔹literal(_11303, 0.0, 1.0))))→unknown | 🔹literal(half(32.0)→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">410</div>
                <div class="line-content">
                    float3 _11404 = (((in.IN_WorldPosition.xyz - _10822) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)) * 2.0) - float3(1.0);
                    <div class="node-info">🔹declaration(float3 _11404)→float3 | 🔹literal((((in.IN_WorldPosition.xyz)→unknown | 🔹literal(_10822))→unknown | 🔹literal(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)))→unknown | 🔹literal(2.0)→float | 🔸operator(*)→float | 🔸type_cast(float3)→float3 | 🔸operator(*)→float3 | 🔹literal(1.0)→float | 🔸type_cast(float3)→float3 | 🔸operator(-)→float3 | 🔸operator(-)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">411</div>
                <div class="line-content">
                    float3 _11407 = _11404 * _11404;
                    <div class="node-info">🔹declaration(float3 _11407)→float3 | 🔹variable(_11404)→float3 | 🔹variable(_11404)→float3 | 🔸operator(*)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">412</div>
                <div class="line-content">
                    float3 _11410 = _11407 * _11407;
                    <div class="node-info">🔹declaration(float3 _11410)→float3 | 🔹variable(_11407)→float3 | 🔹variable(_11407)→float3 | 🔸operator(*)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">414</div>
                <div class="line-content">
                    if ((!((_8397 & 4u) != 0u)) && ((_8397 & 32768u) > 0u))
                    <div class="node-info">🔹literal((!((_8397 & 4u))→unknown | 🔹literal(0u)))→unknown | 🔹literal(((_8397 & 32768u))→unknown | 🔹literal(0u))→unknown | 🔸operator(>)→bool | 🔸operator(&&)→bool | 🔸operator(!=)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">416</div>
                <div class="line-content">
                    _18303 = half(mix(float(_18297), 1.0, fast::clamp(fast::max(_11410.x, fast::max(_11410.y, _11410.z)), 0.0, 1.0)));
                    <div class="node-info">🔹variable(_18303)→unknown | 🔹variable(_18297)→unknown | 🔸type_cast(float)→float | 🔹literal(1.0)→float | 🔹literal(fast::clamp(fast::max(_11410.x, fast::max(_11410.y, _11410.z)), 0.0, 1.0))→unknown | 🔸function(mix)→float | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">420</div>
                <div class="line-content">
                    _18303 = _18297;
                    <div class="node-info">🔹variable(_18303)→unknown | 🔹variable(_18297)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">422</div>
                <div class="line-content">
                    _18305 = _18303;
                    <div class="node-info">🔹variable(_18305)→unknown | 🔹variable(_18303)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">426</div>
                <div class="line-content">
                    _18305 = _9019;
                    <div class="node-info">🔹variable(_18305)→unknown | 🔹variable(_9019)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">428</div>
                <div class="line-content">
                    _18304 = _18305;
                    <div class="node-info">🔹variable(_18304)→unknown | 🔹variable(_18305)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">432</div>
                <div class="line-content">
                    _18304 = _9019;
                    <div class="node-info">🔹variable(_18304)→unknown | 🔹variable(_9019)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">434</div>
                <div class="line-content">
                    float _11467 = _Block1.SHAOParam.z * _Block1.SHAOParam.z;
                    <div class="node-info">🔹declaration(float _11467)→float | 🔹literal(_Block1.SHAOParam.z)→unknown | 🔹literal(_Block1.SHAOParam.z)→unknown | 🔸operator(*)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">435</div>
                <div class="line-content">
                    float3 _11470 = in.IN_WorldPosition.xyz - _8435;
                    <div class="node-info">🔹declaration(float3 _11470)→float3 | 🔹literal(in.IN_WorldPosition.xyz)→unknown | 🔹variable(_8435)→float3 | 🔸operator(-)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">436</div>
                <div class="line-content">
                    float _11479 = fast::clamp((_11467 - dot(_11470, _11470)) / _11467, 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _11479)→float | 🔹literal(fast::clamp((_11467)→unknown | 🔹variable(_11470)→float3 | 🔹literal(_11470)))→unknown | 🔹literal(_11467, 0.0, 1.0)→unknown | 🔸operator(/)→unknown | 🔸function(dot)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">437</div>
                <div class="line-content">
                    _18306 = half(1.0 - fast::clamp((1.0 - float(half(fast::clamp(1.0 - float(half(powr(float(half(fast::clamp(1.0 - float(_18304), 0.0, 1.0))), fast::max(mix(6.0, 0.0, fast::clamp(_Block1.SHGIParam2.w, 0.0, 1.0)), 0.5)))), 0.0, 1.0)))) * float(half(mix(_Block1.SHAOParam.x, _Block1.SHAOParam.y, 1.0 - (_11479 * _11479)))), 0.0, 1.0));
                    <div class="node-info">🔹variable(_18306)→unknown | 🔹literal(1.0)→float | 🔹literal(fast::clamp((1.0)→unknown | 🔹literal(half(fast::clamp(1.0)→unknown | 🔹literal(float(half(powr(float(half(fast::clamp(1.0)→unknown | 🔹literal(_18304), 0.0, 1.0))), fast::max(mix(6.0, 0.0, fast::clamp(_Block1.SHGIParam2.w, 0.0, 1.0)), 0.5)))), 0.0, 1.0))))→unknown | 🔸type_cast(float)→float | 🔹literal(float(half(mix(_Block1.SHAOParam.x, _Block1.SHAOParam.y, 1.0)→unknown | 🔸operator(*)→float | 🔹literal((_11479)→unknown | 🔹literal(_11479)))), 0.0, 1.0)→unknown | 🔸operator(*)→unknown | 🔸operator(-)→float | 🔸operator(-)→float | 🔸operator(-)→float | 🔸type_cast(float)→float | 🔸operator(-)→float | 🔸operator(-)→float | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">441</div>
                <div class="line-content">
                    _18306 = _9019;
                    <div class="node-info">🔹variable(_18306)→unknown | 🔹variable(_9019)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">444</div>
                <div class="line-content">
                    if (!((_8397 & 64u) > 0u))
                    <div class="node-info">🔹literal(!((_8397 & 64u))→unknown | 🔹literal(0u))→unknown | 🔸operator(>)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">446</div>
                <div class="line-content">
                    _18330 = _8346 * _18306;
                    <div class="node-info">🔹variable(_18330)→unknown | 🔹variable(_8346)→half | 🔹variable(_18306)→unknown | 🔸operator(*)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">450</div>
                <div class="line-content">
                    _18330 = _8346;
                    <div class="node-info">🔹variable(_18330)→unknown | 🔹variable(_8346)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">452</div>
                <div class="line-content">
                    _18329 = _18330;
                    <div class="node-info">🔹variable(_18329)→unknown | 🔹variable(_18330)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">456</div>
                <div class="line-content">
                    _18329 = _8346;
                    <div class="node-info">🔹variable(_18329)→unknown | 🔹variable(_8346)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">458</div>
                <div class="line-content">
                    float3 _11517 = float3(half3(_8373));
                    <div class="node-info">🔹declaration(float3 _11517)→float3 | 🔹variable(_8373)→float3 | 🔸type_cast(half3)→half3 | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">459</div>
                <div class="line-content">
                    float3 _11600 = _Block1.CameraPos.xyz + (fast::normalize(float3(_Block1.SunDirection.x, fast::min(_Block1.SunDirection.y, 1.0), _Block1.SunDirection.z)) * 200000.0);
                    <div class="node-info">🔹declaration(float3 _11600)→float3 | 🔹literal(_Block1.CameraPos.xyz)→unknown | 🔹literal((fast::normalize(float3(_Block1.SunDirection.x, fast::min(_Block1.SunDirection.y, 1.0), _Block1.SunDirection.z)))→unknown | 🔹literal(200000.0))→unknown | 🔸operator(*)→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">460</div>
                <div class="line-content">
                    float3 _11604 = reflect(-_11517, _9109);
                    <div class="node-info">🔹declaration(float3 _11604)→float3 | 🔹literal(-_11517)→unknown | 🔹variable(_9109)→float3 | 🔸function(reflect)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">461</div>
                <div class="line-content">
                    float3 _11611 = (_11604 * dot(_11600, _11604)) - _11600;
                    <div class="node-info">🔹declaration(float3 _11611)→float3 | 🔹literal((_11604)→unknown | 🔹variable(_11600)→float3 | 🔹literal(_11604))→unknown | 🔸function(dot)→float | 🔸operator(*)→float | 🔹variable(_11600)→float3 | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">462</div>
                <div class="line-content">
                    float3 _11622 = fast::normalize(_11600 + (_11611 * fast::clamp(4500.0 / length(_11611), 0.0, 1.0)));
                    <div class="node-info">🔹declaration(float3 _11622)→float3 | 🔹literal(fast::normalize(_11600)→unknown | 🔹literal((_11611)→unknown | 🔹literal(fast::clamp(4500.0)→unknown | 🔹literal(_11611), 0.0, 1.0)))→unknown | 🔸function(length)→unknown | 🔸operator(/)→unknown | 🔸operator(*)→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">463</div>
                <div class="line-content">
                    half _11536 = clamp(half(dot(_9109, _11622)), half(0.0), half(1.0));
                    <div class="node-info">🔹declaration(half _11536)→half | 🔹variable(_9109)→float3 | 🔹variable(_11622)→float3 | 🔸function(dot)→float | 🔸type_cast(half)→half | 🔹literal(0.0)→float | 🔸type_cast(half)→half | 🔹literal(1.0)→float | 🔸type_cast(half)→half | 🔸function(clamp)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">464</div>
                <div class="line-content">
                    float _11560 = float(half(fast::max(0.119999997317790985107421875, float(_18272))));
                    <div class="node-info">🔹declaration(float _11560)→float | 🔹literal(fast::max(0.119999997317790985107421875, float(_18272)))→unknown | 🔸type_cast(half)→half | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">465</div>
                <div class="line-content">
                    float _11629 = fast::max(0.00999999977648258209228515625, fast::clamp((4.125 * _11560) - 0.319999992847442626953125, 0.0, 1.0));
                    <div class="node-info">🔹declaration(float _11629)→float | 🔹literal(fast::max(0.00999999977648258209228515625, fast::clamp((4.125)→unknown | 🔹literal(_11560))→unknown | 🔸operator(*)→unknown | 🔹literal(0.319999992847442626953125, 0.0, 1.0)))→unknown | 🔸operator(-)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">466</div>
                <div class="line-content">
                    float _11919 = float(_11536);
                    <div class="node-info">🔹declaration(float _11919)→float | 🔹variable(_11536)→half | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">467</div>
                <div class="line-content">
                    float _11925 = dot(_9109, _11517);
                    <div class="node-info">🔹declaration(float _11925)→float | 🔹variable(_9109)→float3 | 🔹variable(_11517)→float3 | 🔸function(dot)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">468</div>
                <div class="line-content">
                    float3 _11940 = fast::normalize(_11517 + _11622);
                    <div class="node-info">🔹declaration(float3 _11940)→float3 | 🔹literal(fast::normalize(_11517)→unknown | 🔹literal(_11622))→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">469</div>
                <div class="line-content">
                    float _11945 = fast::clamp(dot(_9109, _11940), 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _11945)→float | 🔹literal(fast::clamp(dot(_9109, _11940), 0.0, 1.0))→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">470</div>
                <div class="line-content">
                    float _11951 = fast::clamp(dot(_11517, _11940), 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _11951)→float | 🔹literal(fast::clamp(dot(_11517, _11940), 0.0, 1.0))→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">471</div>
                <div class="line-content">
                    float _11736 = fast::clamp(abs(fast::clamp(_11925, 0.0, 1.0)) + 9.9999997473787516355514526367188e-06, 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _11736)→float | 🔹literal(fast::clamp(abs(fast::clamp(_11925, 0.0, 1.0)))→unknown | 🔹literal(9.9999997473787516355514526367188e)→unknown | 🔹literal(06, 0.0, 1.0))→unknown | 🔸operator(-)→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">472</div>
                <div class="line-content">
                    half4 _11959 = half4(half(0.60000002384185791015625));
                    <div class="node-info">🔹declaration(half4 _11959)→half4 | 🔹literal(0.60000002384185791015625)→float | 🔸type_cast(half)→half | 🔸type_cast(half4)→half4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">473</div>
                <div class="line-content">
                    half4 _11967 = _11959 * _11959;
                    <div class="node-info">🔹declaration(half4 _11967)→half4 | 🔹variable(_11959)→half4 | 🔹variable(_11959)→half4 | 🔸operator(*)→half4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">474</div>
                <div class="line-content">
                    half4 _11970 = half4(half(1.0)) - _11967;
                    <div class="node-info">🔹declaration(half4 _11970)→half4 | 🔹literal(1.0)→float | 🔸type_cast(half)→half | 🔸type_cast(half4)→half4 | 🔹variable(_11967)→half4 | 🔸operator(-)→half4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">475</div>
                <div class="line-content">
                    half4 _11973 = half4(half(1.0)) + _11967;
                    <div class="node-info">🔹declaration(half4 _11973)→half4 | 🔹literal(1.0)→float | 🔸type_cast(half)→half | 🔸type_cast(half4)→half4 | 🔹variable(_11967)→half4 | 🔸operator(+)→half4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">476</div>
                <div class="line-content">
                    half4 _11975 = _11959 * half(2.0);
                    <div class="node-info">🔹declaration(half4 _11975)→half4 | 🔹variable(_11959)→half4 | 🔹literal(2.0)→float | 🔸type_cast(half)→half | 🔸operator(*)→half4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">477</div>
                <div class="line-content">
                    half4 _11982 = half4(half(1.5));
                    <div class="node-info">🔹declaration(half4 _11982)→half4 | 🔹literal(1.5)→float | 🔸type_cast(half)→half | 🔸type_cast(half4)→half4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">478</div>
                <div class="line-content">
                    float _11756 = exp2((((-5.554729938507080078125) * _11951) - 6.9831600189208984375) * _11951);
                    <div class="node-info">🔹declaration(float _11756)→float | 🔹literal(((()→unknown | 🔹literal(5.554729938507080078125))→unknown | 🔹literal(_11951))→unknown | 🔸operator(*)→unknown | 🔹literal(6.9831600189208984375))→unknown | 🔹variable(_11951)→float | 🔸operator(*)→float | 🔸operator(-)→float | 🔸operator(-)→float | 🔸function(exp2)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">479</div>
                <div class="line-content">
                    float _11763 = _11756 + ((1.0 - _11756) * 0.039999999105930328369140625);
                    <div class="node-info">🔹declaration(float _11763)→float | 🔹variable(_11756)→float | 🔹literal(((1.0)→unknown | 🔹literal(_11756))→unknown | 🔹literal(0.039999999105930328369140625))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">480</div>
                <div class="line-content">
                    half _11764 = half(0.699999988079071044921875);
                    <div class="node-info">🔹declaration(half _11764)→half | 🔹literal(0.699999988079071044921875)→float | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">481</div>
                <div class="line-content">
                    half _11768 = half(float(_11764) + 0.100000001490116119384765625);
                    <div class="node-info">🔹declaration(half _11768)→half | 🔹variable(_11764)→half | 🔸type_cast(float)→float | 🔹literal(0.100000001490116119384765625)→float | 🔸operator(+)→float | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">482</div>
                <div class="line-content">
                    half _11772 = _9199 - _8351;
                    <div class="node-info">🔹declaration(half _11772)→half | 🔹variable(_9199)→half | 🔹variable(_8351)→half | 🔸operator(-)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">483</div>
                <div class="line-content">
                    half _11777 = _9199 + _11768;
                    <div class="node-info">🔹declaration(half _11777)→half | 🔹variable(_9199)→half | 🔹variable(_11768)→half | 🔸operator(+)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">484</div>
                <div class="line-content">
                    half _11790 = _9199 + _11764;
                    <div class="node-info">🔹declaration(half _11790)→half | 🔹variable(_9199)→half | 🔹variable(_11764)→half | 🔸operator(+)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">485</div>
                <div class="line-content">
                    half3 _11812 = half3((float3(half3(_Block1.SunColor.xyz)) * _11919) * float(clamp(((_11772 + _11768) / _11777) * _11777, half(0.0), half(1.0)) * clamp(((_11536 + _11764) / _11790) * _11790, half(0.0), half(1.0))));
                    <div class="node-info">🔹declaration(half3 _11812)→half3 | 🔹literal((float3(half3(_Block1.SunColor.xyz)))→unknown | 🔹literal(_11919))→unknown | 🔹literal(float(clamp(((_11772)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(_11768))→unknown | 🔹literal(_11777))→unknown | 🔸operator(/)→unknown | 🔹literal(_11777, half(0.0), half(1.0)))→unknown | 🔹literal(clamp(((_11536)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(_11764))→unknown | 🔹literal(_11790))→unknown | 🔸operator(/)→unknown | 🔹literal(_11790, half(0.0), half(1.0))))→unknown | 🔸operator(*)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">486</div>
                <div class="line-content">
                    half _11815 = half(10.0);
                    <div class="node-info">🔹declaration(half _11815)→half | 🔹literal(10.0)→float | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">487</div>
                <div class="line-content">
                    half _11827 = clamp(dot(half3(fast::normalize(_11622 + _11517)), _9064), half(0.0), half(1.0));
                    <div class="node-info">🔹declaration(half _11827)→half | 🔹literal(fast::normalize(_11622)→unknown | 🔹literal(_11517))→unknown | 🔸operator(+)→unknown | 🔸type_cast(half3)→half3 | 🔹variable(_9064)→half3 | 🔸function(dot)→float | 🔹literal(0.0)→float | 🔸type_cast(half)→half | 🔹literal(1.0)→float | 🔸type_cast(half)→half | 🔸function(clamp)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">488</div>
                <div class="line-content">
                    float _11858 = float(_11815) * 0.5;
                    <div class="node-info">🔹declaration(float _11858)→float | 🔹variable(_11815)→half | 🔸type_cast(float)→float | 🔹literal(0.5)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">489</div>
                <div class="line-content">
                    float _11862 = float(_9251 + _11815);
                    <div class="node-info">🔹declaration(float _11862)→float | 🔹variable(_9251)→half | 🔹variable(_11815)→half | 🔸operator(+)→half | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">490</div>
                <div class="line-content">
                    float _12049 = _11560 * _11560;
                    <div class="node-info">🔹declaration(float _12049)→float | 🔹variable(_11560)→float | 🔹variable(_11560)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">491</div>
                <div class="line-content">
                    float _12062 = _12049 / (((((_11945 * _12049) * _12049) - _11945) * _11945) + 1.0);
                    <div class="node-info">🔹declaration(float _12062)→float | 🔹variable(_12049)→float | 🔹literal((((((_11945)→unknown | 🔸operator(/)→float | 🔹literal(_12049))→unknown | 🔹literal(_12049))→unknown | 🔸operator(*)→unknown | 🔸operator(*)→float | 🔹literal(_11945))→unknown | 🔹literal(_11945))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→float | 🔹literal(1.0))→unknown | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">492</div>
                <div class="line-content">
                    float _12080 = _12049 * _12049;
                    <div class="node-info">🔹declaration(float _12080)→float | 🔹variable(_12049)→float | 🔹variable(_12049)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">493</div>
                <div class="line-content">
                    float _12108 = float(half(9.9956989288330078125e-05));
                    <div class="node-info">🔹declaration(float _12108)→float | 🔹literal(9.9956989288330078125e)→unknown | 🔹literal(05)→int | 🔸operator(-)→int | 🔸type_cast(half)→half | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">494</div>
                <div class="line-content">
                    float3 _12025 = float3(_10557);
                    <div class="node-info">🔹declaration(float3 _12025)→float3 | 🔹variable(_10557)→half3 | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">495</div>
                <div class="line-content">
                    float3 _12120 = float3(fast::clamp(50.0 * _12025.y, 0.0, 1.0)) - _12025;
                    <div class="node-info">🔹declaration(float3 _12120)→float3 | 🔹literal(fast::clamp(50.0)→unknown | 🔹literal(_12025.y, 0.0, 1.0))→unknown | 🔸operator(*)→unknown | 🔸type_cast(float3)→float3 | 🔹variable(_12025)→float3 | 🔸operator(-)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion precision-issue ">
                <div class="line-number">496</div>
                <div class="line-content">
                    float3 _8521 = ((_9698 + (float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_11827 * _11827)), 0.0078125))), _11858)) * 0.15915493667125701904296875) * float(half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05)), _11973 - (_11975 * (-half(dot(_11517, _11622))))), _11982)) * float4(0.0795769989490509033203125)).x))), half3(half(fast::clamp((_11763 * _11763) * float(dot(_11812, _8303)), 0.0, 1.0)))) * _11812) * float3(_11629))) + (((float3(_11812) * ((_12025 + (_12120 * _11756)) * (fast::min(1000.0, (_12062 * _12062) * 0.3183098733425140380859375) * (1.0 / fast::max((_11736 + sqrt((_11736 * (_11736 - (_11736 * _12080))) + _12080)) * (_11919 + sqrt((_11919 * (_11919 - (_11919 * _12080))) + _12080)), _12108))))) * _11629) * float(_11772))) * _10588;
                    <div class="node-info">🔹declaration(float3 _8521)→float3 | 🔹literal(((_9698)→unknown | 🔹literal((float3(mix(_9179, half3(half(((_11862)→unknown | 🔹literal(powr(float(half(fast::max(float(_9199)→unknown | 🔸operator(*)→unknown | 🔹literal((_11827)→unknown | 🔹literal(_11827)), 0.0078125))), _11858)))→unknown | 🔹literal(0.15915493667125701904296875))→unknown | 🔹literal(float(half4(float4(_11970)→unknown | 🔹literal(powr(max(half4(half(9.9956989288330078125e)→unknown | 🔸operator(/)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(05)), _11973)→unknown | 🔹literal((_11975)→unknown | 🔹literal(()→unknown | 🔸operator(*)→unknown | 🔹variable(_11517)→float3 | 🔹literal(_11622))))), _11982)))→unknown | 🔹literal(0.0795769989490509033203125)).x))), half3(half(fast::clamp((_11763)→unknown | 🔹literal(_11763))→unknown | 🔹variable(_11812)→half3 | 🔹literal(_8303)), 0.0, 1.0)))→unknown | 🔸function(dot)→float | 🔸type_cast(float)→float | 🔹literal(_11812))→unknown | 🔹literal(float3(_11629)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸type_cast(float4)→float4 | 🔸operator(*)→float4 | 🔸function(dot)→float | 🔸type_cast(half)→half | 🔸operator(-)→half | 🔸operator(-)→half | 🔸operator(-)→half | 🔸operator(-)→half | 🔹literal((((float3(_11812))→unknown | 🔹literal(((_12025)→unknown | 🔸operator(*)→unknown | 🔹literal((_12120)→unknown | 🔹literal(_11756)))→unknown | 🔹literal((fast::min(1000.0, (_12062)→unknown | 🔹literal(_12062))→unknown | 🔹literal(0.3183098733425140380859375))→unknown | 🔹literal((1.0)→unknown | 🔹literal(fast::max((_11736)→unknown | 🔸operator(/)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal((_11736)→unknown | 🔹literal((_11736)→unknown | 🔸operator(*)→unknown | 🔹literal((_11736)→unknown | 🔹literal(_12080)))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔸function(sqrt)→unknown | 🔹literal(_12080)))→unknown | 🔹literal((_11919)→unknown | 🔸operator(*)→unknown | 🔹literal((_11919)→unknown | 🔹literal((_11919)→unknown | 🔸operator(*)→unknown | 🔹literal((_11919)→unknown | 🔹literal(_12080)))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔸function(sqrt)→unknown | 🔹literal(_12080)), _12108))))))→unknown | 🔹literal(_11629))→unknown | 🔹literal(_11772)))→unknown | 🔸type_cast(float)→float | 🔹variable(_10588)→float3 | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(+)→float | 🔸operator(+)→float | 🔸operator(+)→float | 🔸operator(+)→float | 🔸operator(+)→float | 🔸operator(+)→float | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">497</div>
                <div class="line-content">
                    uint _12171 = uint(_Block1.LightDataBuffer[0].x);
                    <div class="node-info">🔹declaration(uint _12171)→uint | 🔹literal(_Block1.LightDataBuffer[0].x)→unknown | 🔸function(uint)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">504</div>
                <div class="line-content">
                    _19825 = _18526;
                    <div class="node-info">🔹variable(_19825)→unknown | 🔹variable(_18526)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">505</div>
                <div class="line-content">
                    _19755 = _19185;
                    <div class="node-info">🔹variable(_19755)→unknown | 🔹variable(_19185)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">506</div>
                <div class="line-content">
                    _19720 = _19185;
                    <div class="node-info">🔹variable(_19720)→unknown | 🔹variable(_19185)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">507</div>
                <div class="line-content">
                    _19685 = _19185;
                    <div class="node-info">🔹variable(_19685)→unknown | 🔹variable(_19185)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">508</div>
                <div class="line-content">
                    _18431 = _9698;
                    <div class="node-info">🔹variable(_18431)→unknown | 🔹variable(_9698)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">509</div>
                <div class="line-content">
                    _18429 = _9179;
                    <div class="node-info">🔹variable(_18429)→unknown | 🔹variable(_9179)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">524</div>
                <div class="line-content">
                    for (uint _18428 = 0u; _18428 < _12171; _19825 = _20953, _19790 = _20938, _19755 = _20923, _19720 = _20908, _19685 = _20893, _19613 = _20863, _19577 = _20848, _19485 = _20833, _18431 = _19965, _18429 = _19923, _18428++)
                    <div class="node-info">🔹literal(uint _18428 = 0u; _18428)→unknown | 🔹literal(_12171; _19825 = _20953)→unknown | 🔸operator(<)→bool | 🔹literal(_19790 = _20938)→unknown | 🔹literal(_19755 = _20923)→unknown | 🔹literal(_19720 = _20908)→unknown | 🔹literal(_19685 = _20893)→unknown | 🔹literal(_19613 = _20863)→unknown | 🔹literal(_19577 = _20848)→unknown | 🔹literal(_19485 = _20833)→unknown | 🔹literal(_18431 = _19965)→unknown | 🔹literal(_18429 = _19923)→unknown | 🔹variable(_18428)→unknown | 🔹literal(+)→unknown | 🔸operator(+)→unknown | 🔸function(for)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">526</div>
                <div class="line-content">
                    uint _12181 = _18428 * 4u;
                    <div class="node-info">🔹declaration(uint _12181)→uint | 🔹variable(_18428)→unknown | 🔹literal(4u)→unknown | 🔸operator(*)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">527</div>
                <div class="line-content">
                    int _12188 = int(_12181 + 1u);
                    <div class="node-info">🔹declaration(int _12188)→int | 🔹variable(_12181)→uint | 🔹literal(1u)→unknown | 🔸operator(+)→uint | 🔸function(int)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">528</div>
                <div class="line-content">
                    int _12195 = int(_12181 + 2u);
                    <div class="node-info">🔹declaration(int _12195)→int | 🔹variable(_12181)→uint | 🔹literal(2u)→unknown | 🔸operator(+)→uint | 🔸function(int)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">529</div>
                <div class="line-content">
                    int _12202 = int(_12181 + 3u);
                    <div class="node-info">🔹declaration(int _12202)→int | 🔹variable(_12181)→uint | 🔹literal(3u)→unknown | 🔸operator(+)→uint | 🔸function(int)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">530</div>
                <div class="line-content">
                    int _12209 = int(_12181 + 4u);
                    <div class="node-info">🔹declaration(int _12209)→int | 🔹variable(_12181)→uint | 🔹literal(4u)→unknown | 🔸operator(+)→uint | 🔸function(int)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">531</div>
                <div class="line-content">
                    uint _12298 = as_type<uint>(_Block1.LightDataBuffer[_12209].x);
                    <div class="node-info">🔹declaration(uint _12298)→uint | 🔹variable(as_type)→unknown | 🔹variable(uint)→unknown | 🔹literal((_Block1.LightDataBuffer[_12209].x))→unknown | 🔸operator(>)→bool | 🔸operator(<)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">532</div>
                <div class="line-content">
                    if (!((_12298 & 2097152u) == 2097152u))
                    <div class="node-info">🔹literal(!((_12298 & 2097152u))→unknown | 🔹literal(2097152u))→unknown | 🔸operator(==)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">534</div>
                <div class="line-content">
                    _20953 = _19825;
                    <div class="node-info">🔹variable(_20953)→unknown | 🔹variable(_19825)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">535</div>
                <div class="line-content">
                    _20938 = _19790;
                    <div class="node-info">🔹variable(_20938)→unknown | 🔹variable(_19790)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">536</div>
                <div class="line-content">
                    _20923 = _19755;
                    <div class="node-info">🔹variable(_20923)→unknown | 🔹variable(_19755)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">537</div>
                <div class="line-content">
                    _20908 = _19720;
                    <div class="node-info">🔹variable(_20908)→unknown | 🔹variable(_19720)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">538</div>
                <div class="line-content">
                    _20893 = _19685;
                    <div class="node-info">🔹variable(_20893)→unknown | 🔹variable(_19685)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">539</div>
                <div class="line-content">
                    _20863 = _19613;
                    <div class="node-info">🔹variable(_20863)→unknown | 🔹variable(_19613)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">540</div>
                <div class="line-content">
                    _20848 = _19577;
                    <div class="node-info">🔹variable(_20848)→unknown | 🔹variable(_19577)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">541</div>
                <div class="line-content">
                    _20833 = _19485;
                    <div class="node-info">🔹variable(_20833)→unknown | 🔹variable(_19485)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">542</div>
                <div class="line-content">
                    _19965 = _18431;
                    <div class="node-info">🔹variable(_19965)→unknown | 🔹variable(_18431)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">543</div>
                <div class="line-content">
                    _19923 = _18429;
                    <div class="node-info">🔹variable(_19923)→unknown | 🔹variable(_18429)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">546</div>
                <div class="line-content">
                    uint _12309 = _12298 & 196608u;
                    <div class="node-info">🔹declaration(uint _12309)→uint | 🔹literal(_12298 & 196608u)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">555</div>
                <div class="line-content">
                    if (_12309 == 196608u)
                    <div class="node-info">🔹variable(_12309)→uint | 🔹literal(196608u)→unknown | 🔸operator(==)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">557</div>
                <div class="line-content">
                    float3 _12360 = -_Block1.LightDataBuffer[_12202].xyz;
                    <div class="node-info">🔹declaration(float3 _12360)→float3 | 🔹literal(-_Block1.LightDataBuffer[_12202].xyz)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">558</div>
                <div class="line-content">
                    float3 _12378 = in.IN_WorldPosition.xyz - (_Block1.LightDataBuffer[_12188].xyz + (_12360 * (dot(in.IN_WorldPosition.xyz - _Block1.LightDataBuffer[_12188].xyz, _12360) / dot(_12360, _12360))));
                    <div class="node-info">🔹declaration(float3 _12378)→float3 | 🔹literal(in.IN_WorldPosition.xyz)→unknown | 🔹literal((_Block1.LightDataBuffer[_12188].xyz)→unknown | 🔸operator(-)→unknown | 🔹literal((_12360)→unknown | 🔹literal((dot(in.IN_WorldPosition.xyz)→unknown | 🔸operator(*)→unknown | 🔹literal(_Block1.LightDataBuffer[_12188].xyz, _12360))→unknown | 🔹variable(_12360)→float3 | 🔹literal(_12360))))→unknown | 🔸function(dot)→float | 🔸operator(/)→float | 🔸operator(-)→float | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">559</div>
                <div class="line-content">
                    float _12381 = dot(_12378, _12378);
                    <div class="node-info">🔹declaration(float _12381)→float | 🔹variable(_12378)→float3 | 🔹variable(_12378)→float3 | 🔸function(dot)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">561</div>
                <div class="line-content">
                    if (_12381 > (_Block1.LightDataBuffer[_12209].y * _Block1.LightDataBuffer[_12209].y))
                    <div class="node-info">🔹variable(_12381)→float | 🔹literal((_Block1.LightDataBuffer[_12209].y)→unknown | 🔸operator(>)→bool | 🔹literal(_Block1.LightDataBuffer[_12209].y))→unknown | 🔸operator(*)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">563</div>
                <div class="line-content">
                    float _12392 = sqrt(_12381) - _Block1.LightDataBuffer[_12209].y;
                    <div class="node-info">🔹declaration(float _12392)→float | 🔹variable(_12381)→float | 🔸function(sqrt)→float | 🔹literal(_Block1.LightDataBuffer[_12209].y)→unknown | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">564</div>
                <div class="line-content">
                    float _12395 = _12392 * _12392;
                    <div class="node-info">🔹declaration(float _12395)→float | 🔹variable(_12392)→float | 🔹variable(_12392)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">565</div>
                <div class="line-content">
                    float _12398 = _12395 * abs(_Block1.LightDataBuffer[_12188].w);
                    <div class="node-info">🔹declaration(float _12398)→float | 🔹variable(_12395)→float | 🔹literal(_Block1.LightDataBuffer[_12188].w)→unknown | 🔸function(abs)→unknown | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">566</div>
                <div class="line-content">
                    float _12404 = fast::clamp(1.0 - (_12398 * _12398), 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _12404)→float | 🔹literal(fast::clamp(1.0)→unknown | 🔹literal((_12398)→unknown | 🔹literal(_12398), 0.0, 1.0))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">567</div>
                <div class="line-content">
                    _19350 = fast::min(100.0, (_12404 * _12404) / (_12395 + 1.0));
                    <div class="node-info">🔹variable(_19350)→unknown | 🔹literal(fast::min(100.0, (_12404)→unknown | 🔹literal(_12404))→unknown | 🔹literal((_12395)→unknown | 🔸operator(/)→unknown | 🔸operator(*)→unknown | 🔹literal(1.0)))→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">571</div>
                <div class="line-content">
                    _19350 = 1.0;
                    <div class="node-info">🔹variable(_19350)→unknown | 🔹literal(1.0)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">573</div>
                <div class="line-content">
                    _19821 = half3(_Block1.LightDataBuffer[_12195].xyz);
                    <div class="node-info">🔹variable(_19821)→unknown | 🔹literal(_Block1.LightDataBuffer[_12195].xyz)→unknown | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">574</div>
                <div class="line-content">
                    _19786 = _19350;
                    <div class="node-info">🔹variable(_19786)→unknown | 🔹variable(_19350)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">575</div>
                <div class="line-content">
                    _19751 = _12360;
                    <div class="node-info">🔹variable(_19751)→unknown | 🔹variable(_12360)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">576</div>
                <div class="line-content">
                    _19716 = _11517;
                    <div class="node-info">🔹variable(_19716)→unknown | 🔹variable(_11517)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">577</div>
                <div class="line-content">
                    _19681 = _9109;
                    <div class="node-info">🔹variable(_19681)→unknown | 🔹variable(_9109)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">578</div>
                <div class="line-content">
                    _19609 = 0;
                    <div class="node-info">🔹variable(_19609)→unknown | 🔹literal(0)→int | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">579</div>
                <div class="line-content">
                    _19573 = abs(_Block1.LightDataBuffer[_12195].w);
                    <div class="node-info">🔹variable(_19573)→unknown | 🔹literal(_Block1.LightDataBuffer[_12195].w)→unknown | 🔸function(abs)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">580</div>
                <div class="line-content">
                    _19481 = clamp(half(dot(_9109, _12360)), half(0.0), half(1.0));
                    <div class="node-info">🔹variable(_19481)→unknown | 🔹variable(_9109)→float3 | 🔹variable(_12360)→float3 | 🔸function(dot)→float | 🔸type_cast(half)→half | 🔹literal(0.0)→float | 🔸type_cast(half)→half | 🔹literal(1.0)→float | 🔸type_cast(half)→half | 🔸function(clamp)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">592</div>
                <div class="line-content">
                    if (_12309 == 0u)
                    <div class="node-info">🔹variable(_12309)→uint | 🔹literal(0u)→unknown | 🔸operator(==)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">594</div>
                <div class="line-content">
                    uint _12741 = as_type<uint>(_Block1.LightDataBuffer[_12195].w);
                    <div class="node-info">🔹declaration(uint _12741)→uint | 🔹variable(as_type)→unknown | 🔹variable(uint)→unknown | 🔹literal((_Block1.LightDataBuffer[_12195].w))→unknown | 🔸operator(>)→bool | 🔸operator(<)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">595</div>
                <div class="line-content">
                    float _12858 = float((_12741 >> 0u) & 65535u) * 0.0001525902189314365386962890625;
                    <div class="node-info">🔹declaration(float _12858)→float | 🔹literal((_12741)→unknown | 🔹literal(> 0u) & 65535u)→unknown | 🔸operator(>)→bool | 🔸type_cast(float)→float | 🔹literal(0.0001525902189314365386962890625)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">596</div>
                <div class="line-content">
                    float3 _12599 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;
                    <div class="node-info">🔹declaration(float3 _12599)→float3 | 🔹literal(_Block1.LightDataBuffer[_12188].xyz)→unknown | 🔹literal(in.IN_WorldPosition.xyz)→unknown | 🔸operator(-)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">597</div>
                <div class="line-content">
                    float _12602 = dot(_12599, _12599);
                    <div class="node-info">🔹declaration(float _12602)→float | 🔹variable(_12599)→float3 | 🔹variable(_12599)→float3 | 🔸function(dot)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">598</div>
                <div class="line-content">
                    float3 _12606 = _12599 * rsqrt(_12602);
                    <div class="node-info">🔹declaration(float3 _12606)→float3 | 🔹variable(_12599)→float3 | 🔹variable(_12602)→float | 🔸function(rsqrt)→unknown | 🔸operator(*)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">599</div>
                <div class="line-content">
                    float _12613 = _12602 * abs(_Block1.LightDataBuffer[_12188].w);
                    <div class="node-info">🔹declaration(float _12613)→float | 🔹variable(_12602)→float | 🔹literal(_Block1.LightDataBuffer[_12188].w)→unknown | 🔸function(abs)→unknown | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">600</div>
                <div class="line-content">
                    float _12620 = fast::clamp(1.0 - (_12613 * _12613), 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _12620)→float | 🔹literal(fast::clamp(1.0)→unknown | 🔹literal((_12613)→unknown | 🔹literal(_12613), 0.0, 1.0))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">601</div>
                <div class="line-content">
                    float _12631 = _12620 * _12620;
                    <div class="node-info">🔹declaration(float _12631)→float | 🔹variable(_12620)→float | 🔹variable(_12620)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">603</div>
                <div class="line-content">
                    if ((_12298 & 16777216u) == 16777216u)
                    <div class="node-info">🔹literal((_12298 & 16777216u))→unknown | 🔹literal(16777216u)→unknown | 🔸operator(==)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">605</div>
                <div class="line-content">
                    float _12641 = _12631 / ((_12602 * _Block1.LightDataBuffer[_12202].w) + 9.9999997473787516355514526367188e-05);
                    <div class="node-info">🔹declaration(float _12641)→float | 🔹variable(_12631)→float | 🔹literal(((_12602)→unknown | 🔸operator(/)→float | 🔹literal(_Block1.LightDataBuffer[_12202].w))→unknown | 🔸operator(*)→float | 🔹literal(9.9999997473787516355514526367188e)→unknown | 🔹literal(05))→unknown | 🔸operator(-)→unknown | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">607</div>
                <div class="line-content">
                    if (_12858 > 0.00999999977648258209228515625)
                    <div class="node-info">🔹variable(_12858)→float | 🔹literal(0.00999999977648258209228515625)→float | 🔸operator(>)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">609</div>
                <div class="line-content">
                    _19211 = fast::min(_12641, _12858);
                    <div class="node-info">🔹variable(_19211)→unknown | 🔹literal(fast::min(_12641, _12858))→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">613</div>
                <div class="line-content">
                    _19211 = _12641;
                    <div class="node-info">🔹variable(_19211)→unknown | 🔹variable(_12641)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">615</div>
                <div class="line-content">
                    _19213 = fast::min(100.0, _19211);
                    <div class="node-info">🔹variable(_19213)→unknown | 🔹literal(fast::min(100.0, _19211))→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">619</div>
                <div class="line-content">
                    _19213 = _12631 * 0.100000001490116119384765625;
                    <div class="node-info">🔹variable(_19213)→unknown | 🔹variable(_12631)→float | 🔹literal(0.100000001490116119384765625)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">621</div>
                <div class="line-content">
                    float _12677 = fast::clamp((dot(_Block1.LightDataBuffer[_12202].xyz, -_12606) - _Block1.LightDataBuffer[_12209].z) * _Block1.LightDataBuffer[_12209].y, 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _12677)→float | 🔹literal(fast::clamp((dot(_Block1.LightDataBuffer[_12202].xyz,)→unknown | 🔹literal(_12606))→unknown | 🔹literal(_Block1.LightDataBuffer[_12209].z))→unknown | 🔹literal(_Block1.LightDataBuffer[_12209].y, 0.0, 1.0))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔸operator(-)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">622</div>
                <div class="line-content">
                    _19822 = half3(fast::min(float3(3000.0), _Block1.LightDataBuffer[_12195].xyz * _19213));
                    <div class="node-info">🔹variable(_19822)→unknown | 🔹literal(fast::min(float3(3000.0), _Block1.LightDataBuffer[_12195].xyz)→unknown | 🔹literal(_19213))→unknown | 🔸operator(*)→unknown | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">623</div>
                <div class="line-content">
                    _19787 = _12677 * _12677;
                    <div class="node-info">🔹variable(_19787)→unknown | 🔹variable(_12677)→float | 🔹variable(_12677)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">624</div>
                <div class="line-content">
                    _19752 = _12606;
                    <div class="node-info">🔹variable(_19752)→unknown | 🔹variable(_12606)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">625</div>
                <div class="line-content">
                    _19717 = _11517;
                    <div class="node-info">🔹variable(_19717)→unknown | 🔹variable(_11517)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">626</div>
                <div class="line-content">
                    _19682 = _9109;
                    <div class="node-info">🔹variable(_19682)→unknown | 🔹variable(_9109)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">627</div>
                <div class="line-content">
                    _19610 = 0;
                    <div class="node-info">🔹variable(_19610)→unknown | 🔹literal(0)→int | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">628</div>
                <div class="line-content">
                    _19574 = float((_12741 >> 16u) & 65535u) * 0.001525902189314365386962890625;
                    <div class="node-info">🔹variable(_19574)→unknown | 🔹literal((_12741)→unknown | 🔹literal(> 16u) & 65535u)→unknown | 🔸operator(>)→bool | 🔸type_cast(float)→float | 🔹literal(0.001525902189314365386962890625)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">629</div>
                <div class="line-content">
                    _19482 = clamp(half(dot(_9109, _12606)), half(0.0), half(1.0));
                    <div class="node-info">🔹variable(_19482)→unknown | 🔹variable(_9109)→float3 | 🔹variable(_12606)→float3 | 🔸function(dot)→float | 🔸type_cast(half)→half | 🔹literal(0.0)→float | 🔸type_cast(half)→half | 🔹literal(1.0)→float | 🔸type_cast(half)→half | 🔸function(clamp)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">641</div>
                <div class="line-content">
                    if (_12309 == 65536u)
                    <div class="node-info">🔹variable(_12309)→uint | 🔹literal(65536u)→unknown | 🔸operator(==)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">643</div>
                <div class="line-content">
                    uint _13098 = as_type<uint>(_Block1.LightDataBuffer[_12195].w);
                    <div class="node-info">🔹declaration(uint _13098)→uint | 🔹variable(as_type)→unknown | 🔹variable(uint)→unknown | 🔹literal((_Block1.LightDataBuffer[_12195].w))→unknown | 🔸operator(>)→bool | 🔸operator(<)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">644</div>
                <div class="line-content">
                    float3 _12933 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;
                    <div class="node-info">🔹declaration(float3 _12933)→float3 | 🔹literal(_Block1.LightDataBuffer[_12188].xyz)→unknown | 🔹literal(in.IN_WorldPosition.xyz)→unknown | 🔸operator(-)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">645</div>
                <div class="line-content">
                    float _12936 = dot(_12933, _12933);
                    <div class="node-info">🔹declaration(float _12936)→float | 🔹variable(_12933)→float3 | 🔹variable(_12933)→float3 | 🔸function(dot)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">646</div>
                <div class="line-content">
                    float3 _12942 = _12933 / float3(sqrt(_12936));
                    <div class="node-info">🔹declaration(float3 _12942)→float3 | 🔹variable(_12933)→float3 | 🔹variable(_12936)→float | 🔸function(sqrt)→float | 🔸type_cast(float3)→float3 | 🔸operator(/)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">647</div>
                <div class="line-content">
                    float _12950 = _12936 * abs(_Block1.LightDataBuffer[_12188].w);
                    <div class="node-info">🔹declaration(float _12950)→float | 🔹variable(_12936)→float | 🔹literal(_Block1.LightDataBuffer[_12188].w)→unknown | 🔸function(abs)→unknown | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">648</div>
                <div class="line-content">
                    float _12956 = fast::clamp(1.0 - (_12950 * _12950), 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _12956)→float | 🔹literal(fast::clamp(1.0)→unknown | 🔹literal((_12950)→unknown | 🔹literal(_12950), 0.0, 1.0))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">649</div>
                <div class="line-content">
                    float _12972 = fast::min(100.0, (_12956 * _12956) / ((_12936 * _Block1.LightDataBuffer[_12209].w) + 9.9999997473787516355514526367188e-05));
                    <div class="node-info">🔹declaration(float _12972)→float | 🔹literal(fast::min(100.0, (_12956)→unknown | 🔹literal(_12956))→unknown | 🔹literal(((_12936)→unknown | 🔸operator(/)→unknown | 🔹literal(_Block1.LightDataBuffer[_12209].w))→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(9.9999997473787516355514526367188e)→unknown | 🔹literal(05)))→unknown | 🔸operator(-)→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">650</div>
                <div class="line-content">
                    float _13202 = float((_13098 >> 0u) & 65535u) * 0.0001525902189314365386962890625;
                    <div class="node-info">🔹declaration(float _13202)→float | 🔹literal((_13098)→unknown | 🔹literal(> 0u) & 65535u)→unknown | 🔸operator(>)→bool | 🔸type_cast(float)→float | 🔹literal(0.0001525902189314365386962890625)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">652</div>
                <div class="line-content">
                    if (_13202 > 0.00999999977648258209228515625)
                    <div class="node-info">🔹variable(_13202)→float | 🔹literal(0.00999999977648258209228515625)→float | 🔸operator(>)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">654</div>
                <div class="line-content">
                    _19070 = fast::min(_12972, _13202);
                    <div class="node-info">🔹variable(_19070)→unknown | 🔹literal(fast::min(_12972, _13202))→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">658</div>
                <div class="line-content">
                    _19070 = _12972;
                    <div class="node-info">🔹variable(_19070)→unknown | 🔹variable(_12972)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">660</div>
                <div class="line-content">
                    _19823 = half3(fast::min(float3(3000.0), _Block1.LightDataBuffer[_12195].xyz * _19070) * (((_12298 & 16777216u) == 16777216u) ? _Block1.TimeOfDayInfos.y : 1.0));
                    <div class="node-info">🔹variable(_19823)→unknown | 🔹literal(fast::min(float3(3000.0), _Block1.LightDataBuffer[_12195].xyz)→unknown | 🔹literal(_19070))→unknown | 🔹literal((((_12298 & 16777216u))→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(16777216u) ? _Block1.TimeOfDayInfos.y : 1.0))→unknown | 🔸operator(==)→bool | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">661</div>
                <div class="line-content">
                    _19788 = 1.0;
                    <div class="node-info">🔹variable(_19788)→unknown | 🔹literal(1.0)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">662</div>
                <div class="line-content">
                    _19753 = _12942;
                    <div class="node-info">🔹variable(_19753)→unknown | 🔹variable(_12942)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">663</div>
                <div class="line-content">
                    _19718 = _11517;
                    <div class="node-info">🔹variable(_19718)→unknown | 🔹variable(_11517)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">664</div>
                <div class="line-content">
                    _19683 = _9109;
                    <div class="node-info">🔹variable(_19683)→unknown | 🔹variable(_9109)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">665</div>
                <div class="line-content">
                    _19611 = 0;
                    <div class="node-info">🔹variable(_19611)→unknown | 🔹literal(0)→int | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">666</div>
                <div class="line-content">
                    _19575 = float((_13098 >> 16u) & 65535u) * 0.001525902189314365386962890625;
                    <div class="node-info">🔹variable(_19575)→unknown | 🔹literal((_13098)→unknown | 🔹literal(> 16u) & 65535u)→unknown | 🔸operator(>)→bool | 🔸type_cast(float)→float | 🔹literal(0.001525902189314365386962890625)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">667</div>
                <div class="line-content">
                    _19483 = ((_12298 & 262144u) == 262144u) ? _9019 : half(fast::clamp(dot(_9109, _12942), 0.0, 1.0));
                    <div class="node-info">🔹variable(_19483)→unknown | 🔹literal(((_12298 & 262144u))→unknown | 🔹literal(262144u) ? _9019 : half(fast::clamp(dot(_9109, _12942), 0.0, 1.0)))→unknown | 🔸operator(==)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">671</div>
                <div class="line-content">
                    bool _13270 = _12309 == 131072u;
                    <div class="node-info">🔹declaration(bool _13270)→bool | 🔹variable(_12309)→uint | 🔹literal(131072u)→unknown | 🔸operator(==)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">677</div>
                <div class="line-content">
                    if (_13270)
                    <div class="node-info">🔹variable(_13270)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">679</div>
                <div class="line-content">
                    float3 _13339 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;
                    <div class="node-info">🔹declaration(float3 _13339)→float3 | 🔹literal(_Block1.LightDataBuffer[_12188].xyz)→unknown | 🔹literal(in.IN_WorldPosition.xyz)→unknown | 🔸operator(-)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">680</div>
                <div class="line-content">
                    float _13342 = dot(_13339, _13339);
                    <div class="node-info">🔹declaration(float _13342)→float | 🔹variable(_13339)→float3 | 🔹variable(_13339)→float3 | 🔸function(dot)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">681</div>
                <div class="line-content">
                    float _13348 = _13342 * abs(_Block1.LightDataBuffer[_12188].w);
                    <div class="node-info">🔹declaration(float _13348)→float | 🔹variable(_13342)→float | 🔹literal(_Block1.LightDataBuffer[_12188].w)→unknown | 🔸function(abs)→unknown | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">682</div>
                <div class="line-content">
                    float _13356 = fast::clamp(1.0 - (_13348 * _13348), 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _13356)→float | 🔹literal(fast::clamp(1.0)→unknown | 🔹literal((_13348)→unknown | 🔹literal(_13348), 0.0, 1.0))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">683</div>
                <div class="line-content">
                    float3 _13433 = fast::normalize(_11517 - (_9109 * _11925));
                    <div class="node-info">🔹declaration(float3 _13433)→float3 | 🔹literal(fast::normalize(_11517)→unknown | 🔹literal((_9109)→unknown | 🔹literal(_11925)))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">684</div>
                <div class="line-content">
                    float3x3 _13459 = float3x3(_13433, cross(_9109, _13433), _9109);
                    <div class="node-info">🔹declaration(float3x3 _13459)→float3x3 | 🔹variable(_13433)→float3 | 🔹variable(_9109)→float3 | 🔹variable(_13433)→float3 | 🔸function(cross)→float3 | 🔹variable(_9109)→float3 | 🔸function(float3x3)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">685</div>
                <div class="line-content">
                    float3 _13466 = float3(_Block1.LightDataBuffer[_12202].xyz) * _Block1.LightDataBuffer[_12195].w;
                    <div class="node-info">🔹declaration(float3 _13466)→float3 | 🔹literal(_Block1.LightDataBuffer[_12202].xyz)→unknown | 🔸type_cast(float3)→float3 | 🔹literal(_Block1.LightDataBuffer[_12195].w)→unknown | 🔸operator(*)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">686</div>
                <div class="line-content">
                    float3 _13467 = _13339 - _13466;
                    <div class="node-info">🔹declaration(float3 _13467)→float3 | 🔹variable(_13339)→float3 | 🔹variable(_13466)→float3 | 🔸operator(-)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">687</div>
                <div class="line-content">
                    float3 _13472 = float3(_Block1.LightDataBuffer[_12209].yzw) * _Block1.LightDataBuffer[_12202].w;
                    <div class="node-info">🔹declaration(float3 _13472)→float3 | 🔹literal(_Block1.LightDataBuffer[_12209].yzw)→unknown | 🔸type_cast(float3)→float3 | 🔹literal(_Block1.LightDataBuffer[_12202].w)→unknown | 🔸operator(*)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">688</div>
                <div class="line-content">
                    float3 _13484 = _13339 + _13466;
                    <div class="node-info">🔹declaration(float3 _13484)→float3 | 🔹variable(_13339)→float3 | 🔹variable(_13466)→float3 | 🔸operator(+)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">689</div>
                <div class="line-content">
                    float3 _13657 = fast::normalize((_13467 - _13472) * _13459);
                    <div class="node-info">🔹declaration(float3 _13657)→float3 | 🔹literal(fast::normalize((_13467)→unknown | 🔹literal(_13472))→unknown | 🔹literal(_13459))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">690</div>
                <div class="line-content">
                    float3 _13660 = fast::normalize((_13484 - _13472) * _13459);
                    <div class="node-info">🔹declaration(float3 _13660)→float3 | 🔹literal(fast::normalize((_13484)→unknown | 🔹literal(_13472))→unknown | 🔹literal(_13459))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">691</div>
                <div class="line-content">
                    float3 _13663 = fast::normalize((_13484 + _13472) * _13459);
                    <div class="node-info">🔹declaration(float3 _13663)→float3 | 🔹literal(fast::normalize((_13484)→unknown | 🔹literal(_13472))→unknown | 🔹literal(_13459))→unknown | 🔸operator(*)→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">692</div>
                <div class="line-content">
                    float3 _13666 = fast::normalize((_13467 + _13472) * _13459);
                    <div class="node-info">🔹declaration(float3 _13666)→float3 | 🔹literal(fast::normalize((_13467)→unknown | 🔹literal(_13472))→unknown | 🔹literal(_13459))→unknown | 🔸operator(*)→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">693</div>
                <div class="line-content">
                    float _13712 = dot(_13657, _13660);
                    <div class="node-info">🔹declaration(float _13712)→float | 🔹variable(_13657)→float3 | 🔹variable(_13660)→float3 | 🔸function(dot)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">694</div>
                <div class="line-content">
                    float _13714 = abs(_13712);
                    <div class="node-info">🔹declaration(float _13714)→float | 🔹variable(_13712)→float | 🔸function(abs)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">695</div>
                <div class="line-content">
                    float _13728 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13714)) * _13714)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13714) * _13714));
                    <div class="node-info">🔹declaration(float _13728)→float | 🔹literal((0.8543984889984130859375)→unknown | 🔹literal(((0.4965155124664306640625)→unknown | 🔹literal((0.01452060043811798095703125)→unknown | 🔹literal(_13714)))→unknown | 🔹literal(_13714)))→unknown | 🔹literal((3.41759395599365234375)→unknown | 🔸operator(/)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(((4.1616725921630859375)→unknown | 🔹literal(_13714))→unknown | 🔹literal(_13714)))→unknown | 🔸operator(*)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">696</div>
                <div class="line-content">
                    float _13753 = dot(_13660, _13663);
                    <div class="node-info">🔹declaration(float _13753)→float | 🔹variable(_13660)→float3 | 🔹variable(_13663)→float3 | 🔸function(dot)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">697</div>
                <div class="line-content">
                    float _13755 = abs(_13753);
                    <div class="node-info">🔹declaration(float _13755)→float | 🔹variable(_13753)→float | 🔸function(abs)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">698</div>
                <div class="line-content">
                    float _13769 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13755)) * _13755)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13755) * _13755));
                    <div class="node-info">🔹declaration(float _13769)→float | 🔹literal((0.8543984889984130859375)→unknown | 🔹literal(((0.4965155124664306640625)→unknown | 🔹literal((0.01452060043811798095703125)→unknown | 🔹literal(_13755)))→unknown | 🔹literal(_13755)))→unknown | 🔹literal((3.41759395599365234375)→unknown | 🔸operator(/)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(((4.1616725921630859375)→unknown | 🔹literal(_13755))→unknown | 🔹literal(_13755)))→unknown | 🔸operator(*)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">699</div>
                <div class="line-content">
                    float _13794 = dot(_13663, _13666);
                    <div class="node-info">🔹declaration(float _13794)→float | 🔹variable(_13663)→float3 | 🔹variable(_13666)→float3 | 🔸function(dot)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">700</div>
                <div class="line-content">
                    float _13796 = abs(_13794);
                    <div class="node-info">🔹declaration(float _13796)→float | 🔹variable(_13794)→float | 🔸function(abs)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">701</div>
                <div class="line-content">
                    float _13810 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13796)) * _13796)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13796) * _13796));
                    <div class="node-info">🔹declaration(float _13810)→float | 🔹literal((0.8543984889984130859375)→unknown | 🔹literal(((0.4965155124664306640625)→unknown | 🔹literal((0.01452060043811798095703125)→unknown | 🔹literal(_13796)))→unknown | 🔹literal(_13796)))→unknown | 🔹literal((3.41759395599365234375)→unknown | 🔸operator(/)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(((4.1616725921630859375)→unknown | 🔹literal(_13796))→unknown | 🔹literal(_13796)))→unknown | 🔸operator(*)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">702</div>
                <div class="line-content">
                    float _13835 = dot(_13666, _13657);
                    <div class="node-info">🔹declaration(float _13835)→float | 🔹variable(_13666)→float3 | 🔹variable(_13657)→float3 | 🔸function(dot)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">703</div>
                <div class="line-content">
                    float _13837 = abs(_13835);
                    <div class="node-info">🔹declaration(float _13837)→float | 🔹variable(_13835)→float | 🔸function(abs)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">704</div>
                <div class="line-content">
                    float _13851 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13837)) * _13837)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13837) * _13837));
                    <div class="node-info">🔹declaration(float _13851)→float | 🔹literal((0.8543984889984130859375)→unknown | 🔹literal(((0.4965155124664306640625)→unknown | 🔹literal((0.01452060043811798095703125)→unknown | 🔹literal(_13837)))→unknown | 🔹literal(_13837)))→unknown | 🔹literal((3.41759395599365234375)→unknown | 🔸operator(/)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(((4.1616725921630859375)→unknown | 🔹literal(_13837))→unknown | 🔹literal(_13837)))→unknown | 🔸operator(*)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">705</div>
                <div class="line-content">
                    float3 _13700 = cross(_13660, (_13657 * (-((_13712 > 0.0) ? _13728 : ((0.5 * rsqrt(fast::max(1.0 - (_13712 * _13712), 1.0000000116860974230803549289703e-07))) - _13728)))) + (_13663 * ((_13753 > 0.0) ? _13769 : ((0.5 * rsqrt(fast::max(1.0 - (_13753 * _13753), 1.0000000116860974230803549289703e-07))) - _13769)))) + cross(_13666, (_13657 * ((_13835 > 0.0) ? _13851 : ((0.5 * rsqrt(fast::max(1.0 - (_13835 * _13835), 1.0000000116860974230803549289703e-07))) - _13851))) + (_13663 * (-((_13794 > 0.0) ? _13810 : ((0.5 * rsqrt(fast::max(1.0 - (_13794 * _13794), 1.0000000116860974230803549289703e-07))) - _13810)))));
                    <div class="node-info">🔹declaration(float3 _13700)→float3 | 🔹variable(_13660)→float3 | 🔹literal((_13657)→unknown | 🔹literal(()→unknown | 🔸operator(*)→unknown | 🔹literal(((_13712)→unknown | 🔹literal(0.0) ? _13728 : ((0.5)→unknown | 🔸operator(>)→bool | 🔹literal(rsqrt(fast::max(1.0)→unknown | 🔸operator(*)→bool | 🔹literal((_13712)→unknown | 🔹literal(_13712), 1.0000000116860974230803549289703e)→unknown | 🔸operator(*)→unknown | 🔹literal(07))))→unknown | 🔹literal(_13728)))))→unknown | 🔸operator(-)→unknown | 🔸operator(-)→unknown | 🔸operator(-)→bool | 🔸operator(-)→bool | 🔹literal((_13663)→unknown | 🔹literal(((_13753)→unknown | 🔹literal(0.0) ? _13769 : ((0.5)→unknown | 🔸operator(>)→bool | 🔹literal(rsqrt(fast::max(1.0)→unknown | 🔸operator(*)→bool | 🔸operator(*)→bool | 🔹literal((_13753)→unknown | 🔹literal(_13753), 1.0000000116860974230803549289703e)→unknown | 🔸operator(*)→unknown | 🔹literal(07))))→unknown | 🔹literal(_13769)))))→unknown | 🔸operator(-)→unknown | 🔸operator(-)→unknown | 🔸operator(-)→bool | 🔹literal(cross(_13666)→unknown | 🔸operator(+)→bool | 🔸operator(+)→bool | 🔹literal((_13657)→unknown | 🔹literal(((_13835)→unknown | 🔹literal(0.0) ? _13851 : ((0.5)→unknown | 🔸operator(>)→bool | 🔹literal(rsqrt(fast::max(1.0)→unknown | 🔸operator(*)→bool | 🔸operator(*)→bool | 🔹literal((_13835)→unknown | 🔹literal(_13835), 1.0000000116860974230803549289703e)→unknown | 🔸operator(*)→unknown | 🔹literal(07))))→unknown | 🔹literal(_13851))))→unknown | 🔸operator(-)→unknown | 🔸operator(-)→unknown | 🔸operator(-)→bool | 🔹literal((_13663)→unknown | 🔹literal(()→unknown | 🔸operator(*)→unknown | 🔹literal(((_13794)→unknown | 🔹literal(0.0) ? _13810 : ((0.5)→unknown | 🔸operator(>)→bool | 🔹literal(rsqrt(fast::max(1.0)→unknown | 🔸operator(*)→bool | 🔹literal((_13794)→unknown | 🔹literal(_13794), 1.0000000116860974230803549289703e)→unknown | 🔸operator(*)→unknown | 🔹literal(07))))→unknown | 🔹literal(_13810)))))→unknown | 🔸operator(-)→unknown | 🔸operator(-)→unknown | 🔸operator(-)→bool | 🔸operator(-)→bool | 🔸operator(+)→bool | 🔸function(cross)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">706</div>
                <div class="line-content">
                    float _13531 = length(_13700);
                    <div class="node-info">🔹declaration(float _13531)→float | 🔹variable(_13700)→float3 | 🔸function(length)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">707</div>
                <div class="line-content">
                    float _13539 = step(0.0, dot(cross(_Block1.LightDataBuffer[_12202].xyz, _Block1.LightDataBuffer[_12209].yzw), _13339));
                    <div class="node-info">🔹declaration(float _13539)→float | 🔹literal(0.0)→float | 🔹literal(_Block1.LightDataBuffer[_12202].xyz)→unknown | 🔹literal(_Block1.LightDataBuffer[_12209].yzw)→unknown | 🔸function(cross)→float3 | 🔹variable(_13339)→float3 | 🔸function(dot)→float | 🔸function(step)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">708</div>
                <div class="line-content">
                    _19824 = half3(_Block1.LightDataBuffer[_12195].xyz * (_13356 * _13356));
                    <div class="node-info">🔹variable(_19824)→unknown | 🔹literal(_Block1.LightDataBuffer[_12195].xyz)→unknown | 🔹literal((_13356)→unknown | 🔹literal(_13356))→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">709</div>
                <div class="line-content">
                    _19789 = ((!((_12298 & 67108864u) == 67108864u)) && (_13539 > 0.0)) ? 0.0 : _13531;
                    <div class="node-info">🔹variable(_19789)→unknown | 🔹literal(((!((_12298 & 67108864u))→unknown | 🔹literal(67108864u)))→unknown | 🔹literal((_13539)→unknown | 🔹literal(0.0)) ? 0.0 : _13531)→unknown | 🔸operator(>)→bool | 🔸operator(&&)→bool | 🔸operator(==)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">710</div>
                <div class="line-content">
                    _19754 = _13339 * rsqrt(_13342);
                    <div class="node-info">🔹variable(_19754)→unknown | 🔹variable(_13339)→float3 | 🔹variable(_13342)→float | 🔸function(rsqrt)→unknown | 🔸operator(*)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">711</div>
                <div class="line-content">
                    _19684 = _9109;
                    <div class="node-info">🔹variable(_19684)→unknown | 🔹variable(_9109)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">712</div>
                <div class="line-content">
                    _19484 = half(fast::max(((_13531 * _13531) + ((_13700 / float3(_13531)).z * ((_13539 * 2.0) - 1.0))) / (_13531 + 1.0), 0.0));
                    <div class="node-info">🔹variable(_19484)→unknown | 🔹literal(fast::max(((_13531)→unknown | 🔹literal(_13531))→unknown | 🔸operator(*)→unknown | 🔹literal(((_13700)→unknown | 🔹literal(float3(_13531)).z)→unknown | 🔸operator(/)→unknown | 🔹literal(((_13539)→unknown | 🔹literal(2.0))→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(1.0))))→unknown | 🔹literal((_13531)→unknown | 🔸operator(/)→unknown | 🔸operator(-)→unknown | 🔹literal(1.0), 0.0))→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">716</div>
                <div class="line-content">
                    _19824 = _19825;
                    <div class="node-info">🔹variable(_19824)→unknown | 🔹variable(_19825)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">717</div>
                <div class="line-content">
                    _19789 = _19790;
                    <div class="node-info">🔹variable(_19789)→unknown | 🔹variable(_19790)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">718</div>
                <div class="line-content">
                    _19754 = _19755;
                    <div class="node-info">🔹variable(_19754)→unknown | 🔹variable(_19755)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">719</div>
                <div class="line-content">
                    _19684 = _19685;
                    <div class="node-info">🔹variable(_19684)→unknown | 🔹variable(_19685)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">720</div>
                <div class="line-content">
                    _19484 = _19485;
                    <div class="node-info">🔹variable(_19484)→unknown | 🔹variable(_19485)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">722</div>
                <div class="line-content">
                    _19823 = _19824;
                    <div class="node-info">🔹variable(_19823)→unknown | 🔹variable(_19824)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">723</div>
                <div class="line-content">
                    _19788 = _19789;
                    <div class="node-info">🔹variable(_19788)→unknown | 🔹variable(_19789)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">724</div>
                <div class="line-content">
                    _19753 = _19754;
                    <div class="node-info">🔹variable(_19753)→unknown | 🔹variable(_19754)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">725</div>
                <div class="line-content">
                    _19718 = select(_19720, _11517, bool3(_13270));
                    <div class="node-info">🔹variable(_19718)→unknown | 🔹variable(_19720)→unknown | 🔹variable(_11517)→float3 | 🔹variable(_13270)→bool | 🔸function(bool3)→unknown | 🔸function(select)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">726</div>
                <div class="line-content">
                    _19683 = _19684;
                    <div class="node-info">🔹variable(_19683)→unknown | 🔹variable(_19684)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">727</div>
                <div class="line-content">
                    _19611 = _13270 ? 0 : _19613;
                    <div class="node-info">🔹variable(_19611)→unknown | 🔹literal(_13270 ? 0 : _19613)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">728</div>
                <div class="line-content">
                    _19575 = _13270 ? 1.0 : _19577;
                    <div class="node-info">🔹variable(_19575)→unknown | 🔹literal(_13270 ? 1.0 : _19577)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">729</div>
                <div class="line-content">
                    _19483 = _19484;
                    <div class="node-info">🔹variable(_19483)→unknown | 🔹variable(_19484)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">731</div>
                <div class="line-content">
                    _19822 = _19823;
                    <div class="node-info">🔹variable(_19822)→unknown | 🔹variable(_19823)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">732</div>
                <div class="line-content">
                    _19787 = _19788;
                    <div class="node-info">🔹variable(_19787)→unknown | 🔹variable(_19788)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">733</div>
                <div class="line-content">
                    _19752 = _19753;
                    <div class="node-info">🔹variable(_19752)→unknown | 🔹variable(_19753)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">734</div>
                <div class="line-content">
                    _19717 = _19718;
                    <div class="node-info">🔹variable(_19717)→unknown | 🔹variable(_19718)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">735</div>
                <div class="line-content">
                    _19682 = _19683;
                    <div class="node-info">🔹variable(_19682)→unknown | 🔹variable(_19683)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">736</div>
                <div class="line-content">
                    _19610 = _19611;
                    <div class="node-info">🔹variable(_19610)→unknown | 🔹variable(_19611)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">737</div>
                <div class="line-content">
                    _19574 = _19575;
                    <div class="node-info">🔹variable(_19574)→unknown | 🔹variable(_19575)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">738</div>
                <div class="line-content">
                    _19482 = _19483;
                    <div class="node-info">🔹variable(_19482)→unknown | 🔹variable(_19483)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">740</div>
                <div class="line-content">
                    _19821 = _19822;
                    <div class="node-info">🔹variable(_19821)→unknown | 🔹variable(_19822)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">741</div>
                <div class="line-content">
                    _19786 = _19787;
                    <div class="node-info">🔹variable(_19786)→unknown | 🔹variable(_19787)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">742</div>
                <div class="line-content">
                    _19751 = _19752;
                    <div class="node-info">🔹variable(_19751)→unknown | 🔹variable(_19752)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">743</div>
                <div class="line-content">
                    _19716 = _19717;
                    <div class="node-info">🔹variable(_19716)→unknown | 🔹variable(_19717)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">744</div>
                <div class="line-content">
                    _19681 = _19682;
                    <div class="node-info">🔹variable(_19681)→unknown | 🔹variable(_19682)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">745</div>
                <div class="line-content">
                    _19609 = _19610;
                    <div class="node-info">🔹variable(_19609)→unknown | 🔹variable(_19610)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">746</div>
                <div class="line-content">
                    _19573 = _19574;
                    <div class="node-info">🔹variable(_19573)→unknown | 🔹variable(_19574)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">747</div>
                <div class="line-content">
                    _19481 = _19482;
                    <div class="node-info">🔹variable(_19481)→unknown | 🔹variable(_19482)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">749</div>
                <div class="line-content">
                    float _14176 = float(_19481);
                    <div class="node-info">🔹declaration(float _14176)→float | 🔹variable(_19481)→unknown | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">750</div>
                <div class="line-content">
                    float3 _14197 = fast::normalize(_19716 + _19751);
                    <div class="node-info">🔹declaration(float3 _14197)→float3 | 🔹literal(fast::normalize(_19716)→unknown | 🔹literal(_19751))→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">751</div>
                <div class="line-content">
                    float _14202 = fast::clamp(dot(_19681, _14197), 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _14202)→float | 🔹literal(fast::clamp(dot(_19681, _14197), 0.0, 1.0))→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">752</div>
                <div class="line-content">
                    float _14208 = fast::clamp(dot(_19716, _14197), 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _14208)→float | 🔹literal(fast::clamp(dot(_19716, _14197), 0.0, 1.0))→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">753</div>
                <div class="line-content">
                    float _13993 = fast::clamp(abs(fast::clamp(dot(_19681, _19716), 0.0, 1.0)) + 9.9999997473787516355514526367188e-06, 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _13993)→float | 🔹literal(fast::clamp(abs(fast::clamp(dot(_19681, _19716), 0.0, 1.0)))→unknown | 🔹literal(9.9999997473787516355514526367188e)→unknown | 🔹literal(06, 0.0, 1.0))→unknown | 🔸operator(-)→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">754</div>
                <div class="line-content">
                    float _14013 = exp2((((-5.554729938507080078125) * _14208) - 6.9831600189208984375) * _14208);
                    <div class="node-info">🔹declaration(float _14013)→float | 🔹literal(((()→unknown | 🔹literal(5.554729938507080078125))→unknown | 🔹literal(_14208))→unknown | 🔸operator(*)→unknown | 🔹literal(6.9831600189208984375))→unknown | 🔹variable(_14208)→float | 🔸operator(*)→float | 🔸operator(-)→float | 🔸operator(-)→float | 🔸function(exp2)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">755</div>
                <div class="line-content">
                    float _14020 = _14013 + ((1.0 - _14013) * 0.039999999105930328369140625);
                    <div class="node-info">🔹declaration(float _14020)→float | 🔹variable(_14013)→float | 🔹literal(((1.0)→unknown | 🔹literal(_14013))→unknown | 🔹literal(0.039999999105930328369140625))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">756</div>
                <div class="line-content">
                    half _14029 = _9199 - (_9199 - _19481);
                    <div class="node-info">🔹declaration(half _14029)→half | 🔹variable(_9199)→half | 🔹literal((_9199)→unknown | 🔹literal(_19481))→unknown | 🔸operator(-)→unknown | 🔸operator(-)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">757</div>
                <div class="line-content">
                    half3 _14069 = half3(((float3(_19821) * float3(_19786)) * _14176) * float(clamp(((_14029 + _11768) / _11777) * _11777, half(0.0), half(1.0)) * clamp(((_19481 + _11764) / _11790) * _11790, half(0.0), half(1.0))));
                    <div class="node-info">🔹declaration(half3 _14069)→half3 | 🔹literal(((float3(_19821))→unknown | 🔹literal(_19786))→unknown | 🔸type_cast(float3)→float3 | 🔹literal(_14176))→unknown | 🔹literal(float(clamp(((_14029)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→float3 | 🔸operator(*)→float3 | 🔹literal(_11768))→unknown | 🔹literal(_11777))→unknown | 🔸operator(/)→unknown | 🔹literal(_11777, half(0.0), half(1.0)))→unknown | 🔹literal(clamp(((_19481)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(_11764))→unknown | 🔹literal(_11790))→unknown | 🔸operator(/)→unknown | 🔹literal(_11790, half(0.0), half(1.0))))→unknown | 🔸operator(*)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→float3 | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">758</div>
                <div class="line-content">
                    half _14084 = clamp(dot(half3(fast::normalize(_19751 + _19716)), _9064), half(0.0), half(1.0));
                    <div class="node-info">🔹declaration(half _14084)→half | 🔹literal(fast::normalize(_19751)→unknown | 🔹literal(_19716))→unknown | 🔸operator(+)→unknown | 🔸type_cast(half3)→half3 | 🔹variable(_9064)→half3 | 🔸function(dot)→float | 🔹literal(0.0)→float | 🔸type_cast(half)→half | 🔹literal(1.0)→float | 🔸type_cast(half)→half | 🔸function(clamp)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">762</div>
                <div class="line-content">
                    if (_19609 >= 1)
                    <div class="node-info">🔹variable(_19609)→unknown | 🔹literal(1)→int | 🔸operator(>=)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">764</div>
                <div class="line-content">
                    float _14319 = _12049 / (((((_14202 * _12049) * _12049) - _14202) * _14202) + 1.0);
                    <div class="node-info">🔹declaration(float _14319)→float | 🔹variable(_12049)→float | 🔹literal((((((_14202)→unknown | 🔸operator(/)→float | 🔹literal(_12049))→unknown | 🔹literal(_12049))→unknown | 🔸operator(*)→unknown | 🔸operator(*)→float | 🔹literal(_14202))→unknown | 🔹literal(_14202))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→float | 🔹literal(1.0))→unknown | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">765</div>
                <div class="line-content">
                    _19883 = (_12025 + (_12120 * _14013)) * (fast::min(1000.0, (_14319 * _14319) * 0.3183098733425140380859375) * (1.0 / fast::max((_13993 + sqrt((_13993 * (_13993 - (_13993 * _12080))) + _12080)) * (_14176 + sqrt((_14176 * (_14176 - (_14176 * _12080))) + _12080)), _12108)));
                    <div class="node-info">🔹variable(_19883)→unknown | 🔹literal((_12025)→unknown | 🔹literal((_12120)→unknown | 🔹literal(_14013)))→unknown | 🔹literal((fast::min(1000.0, (_14319)→unknown | 🔹literal(_14319))→unknown | 🔹literal(0.3183098733425140380859375))→unknown | 🔹literal((1.0)→unknown | 🔹literal(fast::max((_13993)→unknown | 🔸operator(/)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal((_13993)→unknown | 🔹literal((_13993)→unknown | 🔸operator(*)→unknown | 🔹literal((_13993)→unknown | 🔹literal(_12080))))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔹literal(_12080)))→unknown | 🔹literal((_14176)→unknown | 🔸operator(*)→unknown | 🔹literal((_14176)→unknown | 🔹literal((_14176)→unknown | 🔸operator(*)→unknown | 🔹literal((_14176)→unknown | 🔹literal(_12080))))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔹literal(_12080)), _12108))→unknown | 🔸operator(+)→unknown | 🔸function(sqrt)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸function(sqrt)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">770</div>
                <div class="line-content">
                    _19883 = float3(0.0);
                    <div class="node-info">🔹variable(_19883)→unknown | 🔹literal(0.0)→float | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">773</div>
                <div class="line-content">
                    break; // unreachable workaround
                    <div class="node-info">🔹literal(break;)→unknown | 🔹literal(/ unreachable workaround)→unknown | 🔸operator(/)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">774</div>
                <div class="line-content">
                    } while(false);
                    <div class="node-info">🔹literal(} while(false))→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">775</div>
                <div class="line-content">
                    _20953 = _19821;
                    <div class="node-info">🔹variable(_20953)→unknown | 🔹variable(_19821)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">776</div>
                <div class="line-content">
                    _20938 = _19786;
                    <div class="node-info">🔹variable(_20938)→unknown | 🔹variable(_19786)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">777</div>
                <div class="line-content">
                    _20923 = _19751;
                    <div class="node-info">🔹variable(_20923)→unknown | 🔹variable(_19751)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">778</div>
                <div class="line-content">
                    _20908 = _19716;
                    <div class="node-info">🔹variable(_20908)→unknown | 🔹variable(_19716)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">779</div>
                <div class="line-content">
                    _20893 = _19681;
                    <div class="node-info">🔹variable(_20893)→unknown | 🔹variable(_19681)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">780</div>
                <div class="line-content">
                    _20863 = _19609;
                    <div class="node-info">🔹variable(_20863)→unknown | 🔹variable(_19609)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">781</div>
                <div class="line-content">
                    _20848 = _19573;
                    <div class="node-info">🔹variable(_20848)→unknown | 🔹variable(_19573)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">782</div>
                <div class="line-content">
                    _20833 = _19481;
                    <div class="node-info">🔹variable(_20833)→unknown | 🔹variable(_19481)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion precision-issue ">
                <div class="line-number">783</div>
                <div class="line-content">
                    _19965 = (_18431 + (float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_14084 * _14084)), 0.0078125))), _11858)) * 0.15915493667125701904296875) * float(half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05)), _11973 - (_11975 * (-half(dot(_19716, _19751))))), _11982)) * float4(0.0795769989490509033203125)).x))), half3(half(fast::clamp((_14020 * _14020) * float(dot(_14069, _8303)), 0.0, 1.0)))) * _14069) * float3(_19573))) + (((float3(_14069) * _19883) * _19573) * float(_14029));
                    <div class="node-info">🔹variable(_19965)→unknown | 🔹literal((_18431)→unknown | 🔹literal((float3(mix(_9179, half3(half(((_11862)→unknown | 🔹literal(powr(float(half(fast::max(float(_9199)→unknown | 🔸operator(*)→unknown | 🔹literal((_14084)→unknown | 🔹literal(_14084)), 0.0078125))), _11858)))→unknown | 🔹literal(0.15915493667125701904296875))→unknown | 🔹literal(float(half4(float4(_11970)→unknown | 🔹literal(powr(max(half4(half(9.9956989288330078125e)→unknown | 🔸operator(/)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(05)), _11973)→unknown | 🔹literal((_11975)→unknown | 🔹literal(()→unknown | 🔸operator(*)→unknown | 🔹variable(_19716)→unknown | 🔹literal(_19751))))), _11982)))→unknown | 🔹literal(0.0795769989490509033203125)).x))), half3(half(fast::clamp((_14020)→unknown | 🔹literal(_14020))→unknown | 🔹variable(_14069)→half3 | 🔹literal(_8303)), 0.0, 1.0)))→unknown | 🔸function(dot)→float | 🔸type_cast(float)→float | 🔹literal(_14069))→unknown | 🔹literal(float3(_19573)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸type_cast(float4)→float4 | 🔸operator(*)→float4 | 🔸function(dot)→float | 🔸type_cast(half)→half | 🔸operator(-)→half | 🔸operator(-)→half | 🔸operator(-)→half | 🔸operator(-)→half | 🔹literal((((float3(_14069))→unknown | 🔹literal(_19883))→unknown | 🔹literal(_19573))→unknown | 🔹literal(_14029))→unknown | 🔸type_cast(float)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(+)→float | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">784</div>
                <div class="line-content">
                    _19923 = _18429 + (_14069 * _14029);
                    <div class="node-info">🔹variable(_19923)→unknown | 🔹variable(_18429)→unknown | 🔹literal((_14069)→unknown | 🔹literal(_14029))→unknown | 🔸operator(*)→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">786</div>
                <div class="line-content">
                    half3 _8560 = (_9179 + (((_9179 + (_11812 * _11772)) * _9254) * half3(half(_Block1.EnvInfo.z)))) + _18429;
                    <div class="node-info">🔹declaration(half3 _8560)→half3 | 🔹literal((_9179)→unknown | 🔹literal((((_9179)→unknown | 🔹literal((_11812)→unknown | 🔹literal(_11772)))→unknown | 🔹literal(_9254))→unknown | 🔹literal(_Block1.EnvInfo.z)))→unknown | 🔸type_cast(half)→half | 🔸type_cast(half3)→half3 | 🔸operator(*)→half3 | 🔸operator(*)→half3 | 🔸operator(*)→half3 | 🔹variable(_18429)→unknown | 🔸operator(+)→half3 | 🔸operator(+)→half3 | 🔸operator(+)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">787</div>
                <div class="line-content">
                    float3 _8565 = (_9698 + _8521) + _18431;
                    <div class="node-info">🔹declaration(float3 _8565)→float3 | 🔹literal((_9698)→unknown | 🔹literal(_8521))→unknown | 🔹variable(_18431)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">788</div>
                <div class="line-content">
                    bool _8573 = (_8397 & 16128u) > 0u;
                    <div class="node-info">🔹declaration(bool _8573)→bool | 🔹literal((_8397 & 16128u))→unknown | 🔹literal(0u)→unknown | 🔸operator(>)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">791</div>
                <div class="line-content">
                    if (_8573)
                    <div class="node-info">🔹variable(_8573)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">793</div>
                <div class="line-content">
                    bool _8590 = (_8397 & 16384u) > 0u;
                    <div class="node-info">🔹declaration(bool _8590)→bool | 🔹literal((_8397 & 16384u))→unknown | 🔹literal(0u)→unknown | 🔸operator(>)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">796</div>
                <div class="line-content">
                    if (_8573)
                    <div class="node-info">🔹variable(_8573)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">798</div>
                <div class="line-content">
                    uint _14451 = (_8397 & 458752u) >> 16u;
                    <div class="node-info">🔹declaration(uint _14451)→uint | 🔹literal((_8397 & 458752u))→unknown | 🔹literal(> 16u)→unknown | 🔸operator(>)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">799</div>
                <div class="line-content">
                    uint _14469 = (_8397 & 4026531840u) >> 28u;
                    <div class="node-info">🔹declaration(uint _14469)→uint | 🔹literal((_8397 & 4026531840u))→unknown | 🔹literal(> 28u)→unknown | 🔸operator(>)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">800</div>
                <div class="line-content">
                    float _14482 = fast::clamp((_Block1.CameraInfo.y * in.IN_LinearZ) / fast::max(_Block1.SHGIParam2.x, 0.001000000047497451305389404296875), 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _14482)→float | 🔹literal(fast::clamp((_Block1.CameraInfo.y)→unknown | 🔹literal(in.IN_LinearZ))→unknown | 🔹literal(fast::max(_Block1.SHGIParam2.x, 0.001000000047497451305389404296875), 0.0, 1.0))→unknown | 🔸operator(/)→unknown | 🔸operator(*)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">803</div>
                <div class="line-content">
                    if ((_8397 & 2048u) != 0u)
                    <div class="node-info">🔹literal((_8397 & 2048u))→unknown | 🔹literal(0u)→unknown | 🔸operator(!=)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">805</div>
                <div class="line-content">
                    float3 _14684 = select(_Block1.PlayerPos.xyz, _Block1.CameraPos.xyz, bool3((_8397 & 524288u) > 0u)) * float3(0.125);
                    <div class="node-info">🔹declaration(float3 _14684)→float3 | 🔹literal(_Block1.PlayerPos.xyz)→unknown | 🔹literal(_Block1.CameraPos.xyz)→unknown | 🔹literal((_8397 & 524288u))→unknown | 🔹literal(0u))→unknown | 🔸operator(>)→bool | 🔸function(bool3)→unknown | 🔹literal(float3(0.125)→unknown | 🔸operator(*)→unknown | 🔸function(select)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">806</div>
                <div class="line-content">
                    float _14692 = _14684.x;
                    <div class="node-info">🔹declaration(float _14692)→float | 🔹member_access(_14684.x)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">807</div>
                <div class="line-content">
                    float _14694 = floor(_14692);
                    <div class="node-info">🔹declaration(float _14694)→float | 🔹variable(_14692)→float | 🔸function(floor)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">809</div>
                <div class="line-content">
                    _21212.x = _14694 - 15.0;
                    <div class="node-info">🔹literal(_21212.x = _14694)→unknown | 🔹literal(15.0)→float | 🔸operator(-)→float</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">811</div>
                <div class="line-content">
                    if ((_14692 - _14694) > 0.5)
                    <div class="node-info">🔹literal((_14692)→unknown | 🔹literal(_14694))→unknown | 🔹literal(0.5)→float | 🔸operator(>)→bool | 🔸operator(-)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">813</div>
                <div class="line-content">
                    float3 _21215 = _21212;
                    <div class="node-info">🔹declaration(float3 _21215)→float3 | 🔹variable(_21212)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">814</div>
                <div class="line-content">
                    _21215.x = _14694 + (-14.0);
                    <div class="node-info">🔹literal(_21215.x = _14694)→unknown | 🔹literal(()→unknown | 🔹literal(14.0))→unknown | 🔸operator(-)→unknown | 🔸operator(+)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">815</div>
                <div class="line-content">
                    _21268 = _21215;
                    <div class="node-info">🔹variable(_21268)→unknown | 🔹variable(_21215)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">819</div>
                <div class="line-content">
                    _21268 = _21212;
                    <div class="node-info">🔹variable(_21268)→unknown | 🔹variable(_21212)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">821</div>
                <div class="line-content">
                    float _21034 = _14684.y;
                    <div class="node-info">🔹declaration(float _21034)→float | 🔹member_access(_14684.y)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">822</div>
                <div class="line-content">
                    float _21035 = floor(_21034);
                    <div class="node-info">🔹declaration(float _21035)→float | 🔹variable(_21034)→float | 🔸function(floor)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">823</div>
                <div class="line-content">
                    float3 _21219 = _21268;
                    <div class="node-info">🔹declaration(float3 _21219)→float3 | 🔹variable(_21268)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">824</div>
                <div class="line-content">
                    _21219.y = _21035 - 8.0;
                    <div class="node-info">🔹literal(_21219.y = _21035)→unknown | 🔹literal(8.0)→float | 🔸operator(-)→float</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">826</div>
                <div class="line-content">
                    if ((_21034 - _21035) > 0.5)
                    <div class="node-info">🔹literal((_21034)→unknown | 🔹literal(_21035))→unknown | 🔹literal(0.5)→float | 🔸operator(>)→bool | 🔸operator(-)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">828</div>
                <div class="line-content">
                    float3 _21222 = _21219;
                    <div class="node-info">🔹declaration(float3 _21222)→float3 | 🔹variable(_21219)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">829</div>
                <div class="line-content">
                    _21222.y = _21035 + (-7.0);
                    <div class="node-info">🔹literal(_21222.y = _21035)→unknown | 🔹literal(()→unknown | 🔹literal(7.0))→unknown | 🔸operator(-)→unknown | 🔸operator(+)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">830</div>
                <div class="line-content">
                    _21269 = _21222;
                    <div class="node-info">🔹variable(_21269)→unknown | 🔹variable(_21222)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">834</div>
                <div class="line-content">
                    _21269 = _21219;
                    <div class="node-info">🔹variable(_21269)→unknown | 🔹variable(_21219)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">836</div>
                <div class="line-content">
                    float _21056 = _14684.z;
                    <div class="node-info">🔹declaration(float _21056)→float | 🔹member_access(_14684.z)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">837</div>
                <div class="line-content">
                    float _21057 = floor(_21056);
                    <div class="node-info">🔹declaration(float _21057)→float | 🔹variable(_21056)→float | 🔸function(floor)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">838</div>
                <div class="line-content">
                    float3 _21226 = _21269;
                    <div class="node-info">🔹declaration(float3 _21226)→float3 | 🔹variable(_21269)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">839</div>
                <div class="line-content">
                    _21226.z = _21057 - 15.0;
                    <div class="node-info">🔹literal(_21226.z = _21057)→unknown | 🔹literal(15.0)→float | 🔸operator(-)→float</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">841</div>
                <div class="line-content">
                    if ((_21056 - _21057) > 0.5)
                    <div class="node-info">🔹literal((_21056)→unknown | 🔹literal(_21057))→unknown | 🔹literal(0.5)→float | 🔸operator(>)→bool | 🔸operator(-)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">843</div>
                <div class="line-content">
                    float3 _21229 = _21226;
                    <div class="node-info">🔹declaration(float3 _21229)→float3 | 🔹variable(_21226)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">844</div>
                <div class="line-content">
                    _21229.z = _21057 + (-14.0);
                    <div class="node-info">🔹literal(_21229.z = _21057)→unknown | 🔹literal(()→unknown | 🔹literal(14.0))→unknown | 🔸operator(-)→unknown | 🔸operator(+)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">845</div>
                <div class="line-content">
                    _21270 = _21229;
                    <div class="node-info">🔹variable(_21270)→unknown | 🔹variable(_21229)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">849</div>
                <div class="line-content">
                    _21270 = _21226;
                    <div class="node-info">🔹variable(_21270)→unknown | 🔹variable(_21226)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">851</div>
                <div class="line-content">
                    float3 _14717 = _21270 * 8.0;
                    <div class="node-info">🔹declaration(float3 _14717)→float3 | 🔹variable(_21270)→unknown | 🔹literal(8.0)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">854</div>
                <div class="line-content">
                    if (all(in.IN_WorldPosition.xyz >= _14717) && all(in.IN_WorldPosition.xyz < (_14717 + float3(240.0, 128.0, 240.0))))
                    <div class="node-info">🔹literal(in.IN_WorldPosition.xyz)→unknown | 🔹literal(_14717))→unknown | 🔹literal(in.IN_WorldPosition.xyz)→unknown | 🔹literal((_14717)→unknown | 🔸operator(<)→bool | 🔹literal(240.0, 128.0, 240.0)→unknown | 🔸type_cast(float3)→float3 | 🔸operator(+)→float3 | 🔸function(all)→unknown | 🔸operator(&&)→bool | 🔸operator(>=)→bool | 🔸function(all)→unknown | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">856</div>
                <div class="line-content">
                    float3 _14534 = (in.IN_WorldPosition.xyz + (_9109 * 0.100000001490116119384765625)) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125);
                    <div class="node-info">🔹declaration(float3 _14534)→float3 | 🔹literal((in.IN_WorldPosition.xyz)→unknown | 🔹literal((_9109)→unknown | 🔹literal(0.100000001490116119384765625)))→unknown | 🔹literal(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)→unknown | 🔸type_cast(float3)→float3 | 🔸operator(*)→float3 | 🔸operator(*)→float3 | 🔸operator(+)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">857</div>
                <div class="line-content">
                    float3 _14747 = _14534 - floor(_14534);
                    <div class="node-info">🔹declaration(float3 _14747)→float3 | 🔹variable(_14534)→float3 | 🔹variable(_14534)→float3 | 🔸function(floor)→unknown | 🔸operator(-)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">858</div>
                <div class="line-content">
                    half3 _14556 = half3(_9109);
                    <div class="node-info">🔹declaration(half3 _14556)→half3 | 🔹variable(_9109)→float3 | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">859</div>
                <div class="line-content">
                    float _14788 = float((_8397 & 15728640u) >> 20u);
                    <div class="node-info">🔹declaration(float _14788)→float | 🔹literal((_8397 & 15728640u))→unknown | 🔹literal(> 20u)→unknown | 🔸operator(>)→bool | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">860</div>
                <div class="line-content">
                    float _14797 = float(_14451);
                    <div class="node-info">🔹declaration(float _14797)→float | 🔹variable(_14451)→uint | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">861</div>
                <div class="line-content">
                    float _14827 = (_14788 - _14797) + ((3.0 - _14797) * 3.0);
                    <div class="node-info">🔹declaration(float _14827)→float | 🔹literal((_14788)→unknown | 🔹literal(_14797))→unknown | 🔸operator(-)→unknown | 🔹literal(((3.0)→unknown | 🔹literal(_14797))→unknown | 🔹literal(3.0))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">862</div>
                <div class="line-content">
                    float _14831 = _14827 + 1.0;
                    <div class="node-info">🔹declaration(float _14831)→float | 🔹variable(_14827)→float | 🔹literal(1.0)→float | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">863</div>
                <div class="line-content">
                    float _14833 = _14827 + 2.0;
                    <div class="node-info">🔹declaration(float _14833)→float | 🔹variable(_14827)→float | 🔹literal(2.0)→float | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">866</div>
                <div class="line-content">
                    if (3 >= int(_14469))
                    <div class="node-info">🔹literal(3)→int | 🔹variable(_14469)→uint | 🔸function(int)→unknown | 🔸operator(>=)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">868</div>
                <div class="line-content">
                    float _14850 = (_14788 - float((_8397 & 251658240u) >> 24u)) + ((3.0 - float(_14469)) * 3.0);
                    <div class="node-info">🔹declaration(float _14850)→float | 🔹literal((_14788)→unknown | 🔹literal((_8397 & 251658240u))→unknown | 🔹literal(> 24u))→unknown | 🔸operator(>)→bool | 🔸type_cast(float)→float | 🔸operator(-)→float | 🔹literal(((3.0)→unknown | 🔹literal(_14469)))→unknown | 🔹literal(3.0)→float | 🔸operator(*)→float | 🔸type_cast(float)→float | 🔸operator(-)→float | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">869</div>
                <div class="line-content">
                    float _14854 = _14850 + 1.0;
                    <div class="node-info">🔹declaration(float _14854)→float | 🔹variable(_14850)→float | 🔹literal(1.0)→float | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">870</div>
                <div class="line-content">
                    float _14856 = _14850 + 2.0;
                    <div class="node-info">🔹declaration(float _14856)→float | 🔹variable(_14850)→float | 🔹literal(2.0)→float | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">871</div>
                <div class="line-content">
                    float2 _14956 = ((_14747.xz - float2(0.5)) * 0.9375) + float2(0.5);
                    <div class="node-info">🔹declaration(float2 _14956)→float2 | 🔹literal(((_14747.xz)→unknown | 🔹literal(0.5)))→unknown | 🔹literal(0.9375)→float | 🔸operator(*)→float | 🔸type_cast(float2)→float2 | 🔸operator(-)→float2 | 🔹literal(0.5)→float | 🔸type_cast(float2)→float2 | 🔸operator(+)→float2 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">872</div>
                <div class="line-content">
                    float _14963 = _14717.x * 0.0041666668839752674102783203125;
                    <div class="node-info">🔹declaration(float _14963)→float | 🔹member_access(_14717.x)→float | 🔹literal(0.0041666668839752674102783203125)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">873</div>
                <div class="line-content">
                    float _14967 = ((_14963 - floor(_14963)) - 0.5) * 0.9375;
                    <div class="node-info">🔹declaration(float _14967)→float | 🔹literal(((_14963)→unknown | 🔹literal(_14963))→unknown | 🔸function(floor)→unknown | 🔹literal(0.5))→unknown | 🔹literal(0.9375)→float | 🔸operator(*)→float | 🔸operator(-)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">874</div>
                <div class="line-content">
                    float _14973 = _14717.z * 0.0041666668839752674102783203125;
                    <div class="node-info">🔹declaration(float _14973)→float | 🔹member_access(_14717.z)→float | 🔹literal(0.0041666668839752674102783203125)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">875</div>
                <div class="line-content">
                    float _14977 = ((_14973 - floor(_14973)) - 0.5) * 0.9375;
                    <div class="node-info">🔹declaration(float _14977)→float | 🔹literal(((_14973)→unknown | 🔹literal(_14973))→unknown | 🔸function(floor)→unknown | 🔹literal(0.5))→unknown | 🔹literal(0.9375)→float | 🔸operator(*)→float | 🔸operator(-)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">876</div>
                <div class="line-content">
                    float _14982 = _14956.x;
                    <div class="node-info">🔹declaration(float _14982)→float | 🔹member_access(_14956.x)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">878</div>
                <div class="line-content">
                    _18095.x = (_14982 < (_14967 + 0.5)) ? fast::min(_14982, _14967 + 0.49609375) : fast::max(_14982, _14967 + 0.50390625);
                    <div class="node-info">🔹literal(_18095.x = (_14982)→unknown | 🔹literal((_14967)→unknown | 🔸operator(<)→bool | 🔹literal(0.5)) ? fast::min(_14982, _14967)→unknown | 🔹literal(0.49609375) : fast::max(_14982, _14967)→unknown | 🔹literal(0.50390625))→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→bool</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">879</div>
                <div class="line-content">
                    float _15000 = _14956.y;
                    <div class="node-info">🔹declaration(float _15000)→float | 🔹member_access(_14956.y)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">880</div>
                <div class="line-content">
                    _18095.z = (_15000 < (_14977 + 0.5)) ? fast::min(_15000, _14977 + 0.49609375) : fast::max(_15000, _14977 + 0.50390625);
                    <div class="node-info">🔹literal(_18095.z = (_15000)→unknown | 🔹literal((_14977)→unknown | 🔸operator(<)→bool | 🔹literal(0.5)) ? fast::min(_15000, _14977)→unknown | 🔹literal(0.49609375) : fast::max(_15000, _14977)→unknown | 🔹literal(0.50390625))→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→bool</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">881</div>
                <div class="line-content">
                    float _15021 = (_14747.y * 64.0) - 0.5;
                    <div class="node-info">🔹declaration(float _15021)→float | 🔹literal((_14747.y)→unknown | 🔹literal(64.0))→unknown | 🔸operator(*)→unknown | 🔹literal(0.5)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">882</div>
                <div class="line-content">
                    float _15026 = floor(_15021);
                    <div class="node-info">🔹declaration(float _15026)→float | 🔹variable(_15021)→float | 🔸function(floor)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">883</div>
                <div class="line-content">
                    uint _15029 = (_15021 < 0.0) ? 63u : uint(_15026);
                    <div class="node-info">🔹declaration(uint _15029)→uint | 🔹literal((_15021)→unknown | 🔹literal(0.0) ? 63u : uint(_15026))→unknown | 🔸operator(<)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">884</div>
                <div class="line-content">
                    uint _15032 = _15029 + 1u;
                    <div class="node-info">🔹declaration(uint _15032)→uint | 🔹variable(_15029)→uint | 🔹literal(1u)→unknown | 🔸operator(+)→uint | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">885</div>
                <div class="line-content">
                    uint _21309 = (_15032 >= 64u) ? 0u : _15032;
                    <div class="node-info">🔹declaration(uint _21309)→uint | 🔹literal((_15032)→unknown | 🔹literal(64u) ? 0u : _15032)→unknown | 🔸operator(>=)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">886</div>
                <div class="line-content">
                    float2 _15059 = (float2(float(_15029 & 7u), float(_15029 >> 3u)) + _18095.xz) * 0.125;
                    <div class="node-info">🔹declaration(float2 _15059)→float2 | 🔹literal((float2(float(_15029 & 7u), float(_15029)→unknown | 🔹literal(> 3u)))→unknown | 🔸operator(>)→bool | 🔹literal(_18095.xz))→unknown | 🔹literal(0.125)→float | 🔸operator(*)→float | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">887</div>
                <div class="line-content">
                    float2 _15074 = (float2(float(_21309 & 7u), float(_21309 >> 3u)) + _18095.xz) * 0.125;
                    <div class="node-info">🔹declaration(float2 _15074)→float2 | 🔹literal((float2(float(_21309 & 7u), float(_21309)→unknown | 🔹literal(> 3u)))→unknown | 🔸operator(>)→bool | 🔹literal(_18095.xz))→unknown | 🔹literal(0.125)→float | 🔸operator(*)→float | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">888</div>
                <div class="line-content">
                    float _15078 = _15059.x;
                    <div class="node-info">🔹declaration(float _15078)→float | 🔹member_access(_15059.x)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">889</div>
                <div class="line-content">
                    float3 _15080 = float3(_15078, _15059.y, _14827);
                    <div class="node-info">🔹declaration(float3 _15080)→float3 | 🔹literal(_15078, _15059.y, _14827)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">891</div>
                <div class="line-content">
                    _18103.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15080.xy, uint(rint(_15080.z)), level(0.0)).x);
                    <div class="node-info">🔹literal(_18103.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15080.xy, uint(rint(_15080.z)), level(0.0)).x))→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">892</div>
                <div class="line-content">
                    float3 _15092 = float3(_15078, _15059.y, _14850);
                    <div class="node-info">🔹declaration(float3 _15092)→float3 | 🔹literal(_15078, _15059.y, _14850)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">893</div>
                <div class="line-content">
                    half3 _15097 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15092.xy, uint(rint(_15092.z)), level(0.0)).xyz);
                    <div class="node-info">🔹declaration(half3 _15097)→half3 | 🔹literal(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15092.xy, uint(rint(_15092.z)), level(0.0)).xyz)→unknown | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">894</div>
                <div class="line-content">
                    float _15104 = _15074.x;
                    <div class="node-info">🔹declaration(float _15104)→float | 🔹member_access(_15074.x)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">895</div>
                <div class="line-content">
                    float3 _15106 = float3(_15104, _15074.y, _14827);
                    <div class="node-info">🔹declaration(float3 _15106)→float3 | 🔹literal(_15104, _15074.y, _14827)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">897</div>
                <div class="line-content">
                    _18105.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15106.xy, uint(rint(_15106.z)), level(0.0)).x);
                    <div class="node-info">🔹literal(_18105.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15106.xy, uint(rint(_15106.z)), level(0.0)).x))→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">898</div>
                <div class="line-content">
                    float3 _15118 = float3(_15104, _15074.y, _14850);
                    <div class="node-info">🔹declaration(float3 _15118)→float3 | 🔹literal(_15104, _15074.y, _14850)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">899</div>
                <div class="line-content">
                    half3 _15123 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15118.xy, uint(rint(_15118.z)), level(0.0)).xyz);
                    <div class="node-info">🔹declaration(half3 _15123)→half3 | 🔹literal(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15118.xy, uint(rint(_15118.z)), level(0.0)).xyz)→unknown | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">900</div>
                <div class="line-content">
                    half4 _15132 = half4(half(fast::clamp(_15021 - _15026, 0.0, 1.0)));
                    <div class="node-info">🔹declaration(half4 _15132)→half4 | 🔹literal(fast::clamp(_15021)→unknown | 🔹literal(_15026, 0.0, 1.0))→unknown | 🔸operator(-)→unknown | 🔸type_cast(half)→half | 🔸type_cast(half4)→half4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">901</div>
                <div class="line-content">
                    half4 _15133 = mix(half4(_15097.x, _15097.y, _15097.z, _18103.w), half4(_15123.x, _15123.y, _15123.z, _18105.w), _15132);
                    <div class="node-info">🔹declaration(half4 _15133)→half4 | 🔹literal(_15097.x, _15097.y, _15097.z, _18103.w)→unknown | 🔸type_cast(half4)→half4 | 🔹literal(_15123.x, _15123.y, _15123.z, _18105.w)→unknown | 🔸type_cast(half4)→half4 | 🔹variable(_15132)→half4 | 🔸function(mix)→half4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">902</div>
                <div class="line-content">
                    float3 _15140 = float3(_15078, _15059.y, _14831);
                    <div class="node-info">🔹declaration(float3 _15140)→float3 | 🔹literal(_15078, _15059.y, _14831)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">904</div>
                <div class="line-content">
                    _18107.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15140.xy, uint(rint(_15140.z)), level(0.0)).x);
                    <div class="node-info">🔹literal(_18107.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15140.xy, uint(rint(_15140.z)), level(0.0)).x))→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">905</div>
                <div class="line-content">
                    float3 _15152 = float3(_15078, _15059.y, _14854);
                    <div class="node-info">🔹declaration(float3 _15152)→float3 | 🔹literal(_15078, _15059.y, _14854)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">906</div>
                <div class="line-content">
                    half3 _15157 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15152.xy, uint(rint(_15152.z)), level(0.0)).xyz);
                    <div class="node-info">🔹declaration(half3 _15157)→half3 | 🔹literal(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15152.xy, uint(rint(_15152.z)), level(0.0)).xyz)→unknown | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">907</div>
                <div class="line-content">
                    float3 _15166 = float3(_15104, _15074.y, _14831);
                    <div class="node-info">🔹declaration(float3 _15166)→float3 | 🔹literal(_15104, _15074.y, _14831)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">909</div>
                <div class="line-content">
                    _18109.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15166.xy, uint(rint(_15166.z)), level(0.0)).x);
                    <div class="node-info">🔹literal(_18109.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15166.xy, uint(rint(_15166.z)), level(0.0)).x))→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">910</div>
                <div class="line-content">
                    float3 _15178 = float3(_15104, _15074.y, _14854);
                    <div class="node-info">🔹declaration(float3 _15178)→float3 | 🔹literal(_15104, _15074.y, _14854)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">911</div>
                <div class="line-content">
                    half3 _15183 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15178.xy, uint(rint(_15178.z)), level(0.0)).xyz);
                    <div class="node-info">🔹declaration(half3 _15183)→half3 | 🔹literal(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15178.xy, uint(rint(_15178.z)), level(0.0)).xyz)→unknown | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">912</div>
                <div class="line-content">
                    half4 _15193 = mix(half4(_15157.x, _15157.y, _15157.z, _18107.w), half4(_15183.x, _15183.y, _15183.z, _18109.w), _15132);
                    <div class="node-info">🔹declaration(half4 _15193)→half4 | 🔹literal(_15157.x, _15157.y, _15157.z, _18107.w)→unknown | 🔸type_cast(half4)→half4 | 🔹literal(_15183.x, _15183.y, _15183.z, _18109.w)→unknown | 🔸type_cast(half4)→half4 | 🔹variable(_15132)→half4 | 🔸function(mix)→half4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">913</div>
                <div class="line-content">
                    float3 _15200 = float3(_15078, _15059.y, _14833);
                    <div class="node-info">🔹declaration(float3 _15200)→float3 | 🔹literal(_15078, _15059.y, _14833)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">915</div>
                <div class="line-content">
                    _18111.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15200.xy, uint(rint(_15200.z)), level(0.0)).x);
                    <div class="node-info">🔹literal(_18111.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15200.xy, uint(rint(_15200.z)), level(0.0)).x))→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">916</div>
                <div class="line-content">
                    float3 _15212 = float3(_15078, _15059.y, _14856);
                    <div class="node-info">🔹declaration(float3 _15212)→float3 | 🔹literal(_15078, _15059.y, _14856)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">917</div>
                <div class="line-content">
                    half3 _15217 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15212.xy, uint(rint(_15212.z)), level(0.0)).xyz);
                    <div class="node-info">🔹declaration(half3 _15217)→half3 | 🔹literal(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15212.xy, uint(rint(_15212.z)), level(0.0)).xyz)→unknown | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">918</div>
                <div class="line-content">
                    float3 _15226 = float3(_15104, _15074.y, _14833);
                    <div class="node-info">🔹declaration(float3 _15226)→float3 | 🔹literal(_15104, _15074.y, _14833)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">920</div>
                <div class="line-content">
                    _18113.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15226.xy, uint(rint(_15226.z)), level(0.0)).x);
                    <div class="node-info">🔹literal(_18113.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15226.xy, uint(rint(_15226.z)), level(0.0)).x))→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">921</div>
                <div class="line-content">
                    float3 _15238 = float3(_15104, _15074.y, _14856);
                    <div class="node-info">🔹declaration(float3 _15238)→float3 | 🔹literal(_15104, _15074.y, _14856)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">922</div>
                <div class="line-content">
                    half3 _15243 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15238.xy, uint(rint(_15238.z)), level(0.0)).xyz);
                    <div class="node-info">🔹declaration(half3 _15243)→half3 | 🔹literal(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15238.xy, uint(rint(_15238.z)), level(0.0)).xyz)→unknown | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">923</div>
                <div class="line-content">
                    half4 _15253 = mix(half4(_15217.x, _15217.y, _15217.z, _18111.w), half4(_15243.x, _15243.y, _15243.z, _18113.w), _15132);
                    <div class="node-info">🔹declaration(half4 _15253)→half4 | 🔹literal(_15217.x, _15217.y, _15217.z, _18111.w)→unknown | 🔸type_cast(half4)→half4 | 🔹literal(_15243.x, _15243.y, _15243.z, _18113.w)→unknown | 🔸type_cast(half4)→half4 | 🔹variable(_15132)→half4 | 🔸function(mix)→half4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">924</div>
                <div class="line-content">
                    half _15255 = half(32.0);
                    <div class="node-info">🔹declaration(half _15255)→half | 🔹literal(32.0)→float | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">925</div>
                <div class="line-content">
                    half _15258 = _15133.w * _15255;
                    <div class="node-info">🔹declaration(half _15258)→half | 🔹member_access(_15133.w)→half | 🔹variable(_15255)→half | 🔸operator(*)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">926</div>
                <div class="line-content">
                    half _15262 = _15193.w * _15255;
                    <div class="node-info">🔹declaration(half _15262)→half | 🔹member_access(_15193.w)→half | 🔹variable(_15255)→half | 🔸operator(*)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">927</div>
                <div class="line-content">
                    half _15266 = _15253.w * _15255;
                    <div class="node-info">🔹declaration(half _15266)→half | 🔹member_access(_15253.w)→half | 🔹variable(_15255)→half | 🔸operator(*)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion precision-issue ">
                <div class="line-number">928</div>
                <div class="line-content">
                    half3 _15343 = half3(((float3(_15133.xyz) * float3(2.0)) - float3(1.0)) * float(_15258)).xyz;
                    <div class="node-info">🔹declaration(half3 _15343)→half3 | 🔹literal(((float3(_15133.xyz))→unknown | 🔹literal(2.0)→float | 🔸type_cast(float3)→float3 | 🔸operator(*)→float3 | 🔸type_cast(half3)→half3 | 🔹literal(1.0))→unknown | 🔸type_cast(float3)→float3 | 🔹literal(float(_15258)).xyz)→unknown | 🔸operator(*)→float3 | 🔸operator(-)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">930</div>
                <div class="line-content">
                    _18130.x = half(float(dot(_14556, _15343)) * 2.0);
                    <div class="node-info">🔹literal(_18130.x = half(float(dot(_14556, _15343)))→unknown | 🔹literal(2.0))→unknown | 🔸operator(*)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion precision-issue ">
                <div class="line-number">931</div>
                <div class="line-content">
                    half3 _15352 = half3(((float3(_15193.xyz) * float3(2.0)) - float3(1.0)) * float(_15262)).xyz;
                    <div class="node-info">🔹declaration(half3 _15352)→half3 | 🔹literal(((float3(_15193.xyz))→unknown | 🔹literal(2.0)→float | 🔸type_cast(float3)→float3 | 🔸operator(*)→float3 | 🔸type_cast(half3)→half3 | 🔹literal(1.0))→unknown | 🔸type_cast(float3)→float3 | 🔹literal(float(_15262)).xyz)→unknown | 🔸operator(*)→float3 | 🔸operator(-)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">932</div>
                <div class="line-content">
                    _18130.y = half(float(dot(_14556, _15352)) * 2.0);
                    <div class="node-info">🔹literal(_18130.y = half(float(dot(_14556, _15352)))→unknown | 🔹literal(2.0))→unknown | 🔸operator(*)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion precision-issue ">
                <div class="line-number">933</div>
                <div class="line-content">
                    half3 _15361 = half3(((float3(_15253.xyz) * float3(2.0)) - float3(1.0)) * float(_15266)).xyz;
                    <div class="node-info">🔹declaration(half3 _15361)→half3 | 🔹literal(((float3(_15253.xyz))→unknown | 🔹literal(2.0)→float | 🔸type_cast(float3)→float3 | 🔸operator(*)→float3 | 🔸type_cast(half3)→half3 | 🔹literal(1.0))→unknown | 🔸type_cast(float3)→float3 | 🔹literal(float(_15266)).xyz)→unknown | 🔸operator(*)→float3 | 🔸operator(-)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">934</div>
                <div class="line-content">
                    _18130.z = half(float(dot(_14556, _15361)) * 2.0);
                    <div class="node-info">🔹literal(_18130.z = half(float(dot(_14556, _15361)))→unknown | 🔹literal(2.0))→unknown | 🔸operator(*)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">936</div>
                <div class="line-content">
                    if (_8590)
                    <div class="node-info">🔹variable(_8590)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">938</div>
                <div class="line-content">
                    _18819 = half3(((float3(_15343) * float3(0.21199999749660491943359375)) + (float3(_15352) * float3(0.714999973773956298828125))) + (float3(_15361) * float3(0.0719999969005584716796875)));
                    <div class="node-info">🔹variable(_18819)→unknown | 🔹literal(((float3(_15343))→unknown | 🔹literal(0.21199999749660491943359375))→unknown | 🔸type_cast(float3)→float3 | 🔸operator(*)→float3 | 🔹literal((float3(_15352))→unknown | 🔹literal(0.714999973773956298828125)))→unknown | 🔸type_cast(float3)→float3 | 🔸operator(*)→float3 | 🔹literal((float3(_15361))→unknown | 🔹literal(0.0719999969005584716796875))→unknown | 🔸type_cast(float3)→float3 | 🔸operator(*)→float3 | 🔸operator(+)→float3 | 🔸operator(+)→float3 | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">942</div>
                <div class="line-content">
                    _18819 = _8393;
                    <div class="node-info">🔹variable(_18819)→unknown | 🔹variable(_8393)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">944</div>
                <div class="line-content">
                    _18818 = _18819;
                    <div class="node-info">🔹variable(_18818)→unknown | 🔹variable(_18819)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">945</div>
                <div class="line-content">
                    _18806 = max(half3(_15258, _15262, _15266) + (_18130 * half(mix(_Block1.SHGIParam2.z, 1.0, _14482))), _8393);
                    <div class="node-info">🔹variable(_18806)→unknown | 🔹literal(_15258, _15262, _15266))→unknown | 🔹literal((_18130)→unknown | 🔹literal(_Block1.SHGIParam2.z)→unknown | 🔹literal(1.0)→float | 🔹variable(_14482)→float | 🔸function(mix)→float | 🔸type_cast(half)→half | 🔸operator(*)→half | 🔸operator(+)→half | 🔸type_cast(half3)→half3 | 🔹variable(_8393)→half3 | 🔸function(max)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">949</div>
                <div class="line-content">
                    float2 _15427 = ((_14747.xz - float2(0.5)) * 0.9375) + float2(0.5);
                    <div class="node-info">🔹declaration(float2 _15427)→float2 | 🔹literal(((_14747.xz)→unknown | 🔹literal(0.5)))→unknown | 🔹literal(0.9375)→float | 🔸operator(*)→float | 🔸type_cast(float2)→float2 | 🔸operator(-)→float2 | 🔹literal(0.5)→float | 🔸type_cast(float2)→float2 | 🔸operator(+)→float2 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">950</div>
                <div class="line-content">
                    float _15434 = _14717.x * 0.0041666668839752674102783203125;
                    <div class="node-info">🔹declaration(float _15434)→float | 🔹member_access(_14717.x)→float | 🔹literal(0.0041666668839752674102783203125)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">951</div>
                <div class="line-content">
                    float _15438 = ((_15434 - floor(_15434)) - 0.5) * 0.9375;
                    <div class="node-info">🔹declaration(float _15438)→float | 🔹literal(((_15434)→unknown | 🔹literal(_15434))→unknown | 🔸function(floor)→unknown | 🔹literal(0.5))→unknown | 🔹literal(0.9375)→float | 🔸operator(*)→float | 🔸operator(-)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">952</div>
                <div class="line-content">
                    float _15444 = _14717.z * 0.0041666668839752674102783203125;
                    <div class="node-info">🔹declaration(float _15444)→float | 🔹member_access(_14717.z)→float | 🔹literal(0.0041666668839752674102783203125)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">953</div>
                <div class="line-content">
                    float _15448 = ((_15444 - floor(_15444)) - 0.5) * 0.9375;
                    <div class="node-info">🔹declaration(float _15448)→float | 🔹literal(((_15444)→unknown | 🔹literal(_15444))→unknown | 🔸function(floor)→unknown | 🔹literal(0.5))→unknown | 🔹literal(0.9375)→float | 🔸operator(*)→float | 🔸operator(-)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">954</div>
                <div class="line-content">
                    float _15453 = _15427.x;
                    <div class="node-info">🔹declaration(float _15453)→float | 🔹member_access(_15427.x)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">956</div>
                <div class="line-content">
                    _18143.x = (_15453 < (_15438 + 0.5)) ? fast::min(_15453, _15438 + 0.49609375) : fast::max(_15453, _15438 + 0.50390625);
                    <div class="node-info">🔹literal(_18143.x = (_15453)→unknown | 🔹literal((_15438)→unknown | 🔸operator(<)→bool | 🔹literal(0.5)) ? fast::min(_15453, _15438)→unknown | 🔹literal(0.49609375) : fast::max(_15453, _15438)→unknown | 🔹literal(0.50390625))→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→bool</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">957</div>
                <div class="line-content">
                    float _15471 = _15427.y;
                    <div class="node-info">🔹declaration(float _15471)→float | 🔹member_access(_15427.y)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">958</div>
                <div class="line-content">
                    _18143.z = (_15471 < (_15448 + 0.5)) ? fast::min(_15471, _15448 + 0.49609375) : fast::max(_15471, _15448 + 0.50390625);
                    <div class="node-info">🔹literal(_18143.z = (_15471)→unknown | 🔹literal((_15448)→unknown | 🔸operator(<)→bool | 🔹literal(0.5)) ? fast::min(_15471, _15448)→unknown | 🔹literal(0.49609375) : fast::max(_15471, _15448)→unknown | 🔹literal(0.50390625))→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→bool</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">959</div>
                <div class="line-content">
                    float _15492 = (_14747.y * 64.0) - 0.5;
                    <div class="node-info">🔹declaration(float _15492)→float | 🔹literal((_14747.y)→unknown | 🔹literal(64.0))→unknown | 🔸operator(*)→unknown | 🔹literal(0.5)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">960</div>
                <div class="line-content">
                    float _15497 = floor(_15492);
                    <div class="node-info">🔹declaration(float _15497)→float | 🔹variable(_15492)→float | 🔸function(floor)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">961</div>
                <div class="line-content">
                    uint _15500 = (_15492 < 0.0) ? 63u : uint(_15497);
                    <div class="node-info">🔹declaration(uint _15500)→uint | 🔹literal((_15492)→unknown | 🔹literal(0.0) ? 63u : uint(_15497))→unknown | 🔸operator(<)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">962</div>
                <div class="line-content">
                    uint _15503 = _15500 + 1u;
                    <div class="node-info">🔹declaration(uint _15503)→uint | 🔹variable(_15500)→uint | 🔹literal(1u)→unknown | 🔸operator(+)→uint | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">963</div>
                <div class="line-content">
                    uint _21308 = (_15503 >= 64u) ? 0u : _15503;
                    <div class="node-info">🔹declaration(uint _21308)→uint | 🔹literal((_15503)→unknown | 🔹literal(64u) ? 0u : _15503)→unknown | 🔸operator(>=)→bool | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">964</div>
                <div class="line-content">
                    float2 _15530 = (float2(float(_15500 & 7u), float(_15500 >> 3u)) + _18143.xz) * 0.125;
                    <div class="node-info">🔹declaration(float2 _15530)→float2 | 🔹literal((float2(float(_15500 & 7u), float(_15500)→unknown | 🔹literal(> 3u)))→unknown | 🔸operator(>)→bool | 🔹literal(_18143.xz))→unknown | 🔹literal(0.125)→float | 🔸operator(*)→float | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">965</div>
                <div class="line-content">
                    float2 _15545 = (float2(float(_21308 & 7u), float(_21308 >> 3u)) + _18143.xz) * 0.125;
                    <div class="node-info">🔹declaration(float2 _15545)→float2 | 🔹literal((float2(float(_21308 & 7u), float(_21308)→unknown | 🔹literal(> 3u)))→unknown | 🔸operator(>)→bool | 🔹literal(_18143.xz))→unknown | 🔹literal(0.125)→float | 🔸operator(*)→float | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">966</div>
                <div class="line-content">
                    float _15549 = _15530.x;
                    <div class="node-info">🔹declaration(float _15549)→float | 🔹member_access(_15530.x)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">967</div>
                <div class="line-content">
                    float3 _15551 = float3(_15549, _15530.y, _14827);
                    <div class="node-info">🔹declaration(float3 _15551)→float3 | 🔹literal(_15549, _15530.y, _14827)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">969</div>
                <div class="line-content">
                    _18151.x = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15551.xy, uint(rint(_15551.z)), level(0.0)).x);
                    <div class="node-info">🔹literal(_18151.x = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15551.xy, uint(rint(_15551.z)), level(0.0)).x))→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">970</div>
                <div class="line-content">
                    float _15561 = _15545.x;
                    <div class="node-info">🔹declaration(float _15561)→float | 🔹member_access(_15545.x)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">971</div>
                <div class="line-content">
                    float3 _15563 = float3(_15561, _15545.y, _14827);
                    <div class="node-info">🔹declaration(float3 _15563)→float3 | 🔹literal(_15561, _15545.y, _14827)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">973</div>
                <div class="line-content">
                    _18153.x = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15563.xy, uint(rint(_15563.z)), level(0.0)).x);
                    <div class="node-info">🔹literal(_18153.x = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15563.xy, uint(rint(_15563.z)), level(0.0)).x))→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">974</div>
                <div class="line-content">
                    float3 _15575 = float3(_15549, _15530.y, _14831);
                    <div class="node-info">🔹declaration(float3 _15575)→float3 | 🔹literal(_15549, _15530.y, _14831)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">975</div>
                <div class="line-content">
                    _18151.y = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15575.xy, uint(rint(_15575.z)), level(0.0)).x);
                    <div class="node-info">🔹literal(_18151.y = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15575.xy, uint(rint(_15575.z)), level(0.0)).x))→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">976</div>
                <div class="line-content">
                    float3 _15587 = float3(_15561, _15545.y, _14831);
                    <div class="node-info">🔹declaration(float3 _15587)→float3 | 🔹literal(_15561, _15545.y, _14831)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">977</div>
                <div class="line-content">
                    _18153.y = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15587.xy, uint(rint(_15587.z)), level(0.0)).x);
                    <div class="node-info">🔹literal(_18153.y = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15587.xy, uint(rint(_15587.z)), level(0.0)).x))→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">978</div>
                <div class="line-content">
                    float3 _15599 = float3(_15549, _15530.y, _14833);
                    <div class="node-info">🔹declaration(float3 _15599)→float3 | 🔹literal(_15549, _15530.y, _14833)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">979</div>
                <div class="line-content">
                    _18151.z = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15599.xy, uint(rint(_15599.z)), level(0.0)).x);
                    <div class="node-info">🔹literal(_18151.z = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15599.xy, uint(rint(_15599.z)), level(0.0)).x))→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">980</div>
                <div class="line-content">
                    float3 _15611 = float3(_15561, _15545.y, _14833);
                    <div class="node-info">🔹declaration(float3 _15611)→float3 | 🔹literal(_15561, _15545.y, _14833)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">981</div>
                <div class="line-content">
                    _18153.z = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15611.xy, uint(rint(_15611.z)), level(0.0)).x);
                    <div class="node-info">🔹literal(_18153.z = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15611.xy, uint(rint(_15611.z)), level(0.0)).x))→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">982</div>
                <div class="line-content">
                    half3 _15622 = mix(_18151, _18153, half3(half(fast::clamp(_15492 - _15497, 0.0, 1.0))));
                    <div class="node-info">🔹declaration(half3 _15622)→half3 | 🔹variable(_18151)→unknown | 🔹variable(_18153)→unknown | 🔹literal(fast::clamp(_15492)→unknown | 🔹literal(_15497, 0.0, 1.0))→unknown | 🔸operator(-)→unknown | 🔸type_cast(half)→half | 🔸type_cast(half3)→half3 | 🔸function(mix)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">983</div>
                <div class="line-content">
                    half _15623 = half(32.0);
                    <div class="node-info">🔹declaration(half _15623)→half | 🔹literal(32.0)→float | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">985</div>
                <div class="line-content">
                    _18164.x = _15622.x * _15623;
                    <div class="node-info">🔹literal(_18164.x = _15622.x)→unknown | 🔹variable(_15623)→half | 🔸operator(*)→half</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">986</div>
                <div class="line-content">
                    _18164.y = _15622.y * _15623;
                    <div class="node-info">🔹literal(_18164.y = _15622.y)→unknown | 🔹variable(_15623)→half | 🔸operator(*)→half</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">987</div>
                <div class="line-content">
                    _18164.z = _15622.z * _15623;
                    <div class="node-info">🔹literal(_18164.z = _15622.z)→unknown | 🔹variable(_15623)→half | 🔸operator(*)→half</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">988</div>
                <div class="line-content">
                    _18818 = _8393;
                    <div class="node-info">🔹variable(_18818)→unknown | 🔹variable(_8393)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">989</div>
                <div class="line-content">
                    _18806 = _18164;
                    <div class="node-info">🔹variable(_18806)→unknown | 🔹variable(_18164)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">991</div>
                <div class="line-content">
                    float3 _15658 = (((in.IN_WorldPosition.xyz - _14717) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)) * 2.0) - float3(1.0);
                    <div class="node-info">🔹declaration(float3 _15658)→float3 | 🔹literal((((in.IN_WorldPosition.xyz)→unknown | 🔹literal(_14717))→unknown | 🔹literal(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)))→unknown | 🔹literal(2.0)→float | 🔸operator(*)→float | 🔸type_cast(float3)→float3 | 🔸operator(*)→float3 | 🔹literal(1.0)→float | 🔸type_cast(float3)→float3 | 🔸operator(-)→float3 | 🔸operator(-)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">992</div>
                <div class="line-content">
                    float3 _15661 = _15658 * _15658;
                    <div class="node-info">🔹declaration(float3 _15661)→float3 | 🔹variable(_15658)→float3 | 🔹variable(_15658)→float3 | 🔸operator(*)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">993</div>
                <div class="line-content">
                    float3 _15664 = _15661 * _15661;
                    <div class="node-info">🔹declaration(float3 _15664)→float3 | 🔹variable(_15661)→float3 | 🔹variable(_15661)→float3 | 🔸operator(*)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">996</div>
                <div class="line-content">
                    if ((max(int(_14451), 2) == 3) && ((_8397 & 32768u) > 0u))
                    <div class="node-info">🔹literal((max(int(_14451), 2))→unknown | 🔹literal(3))→unknown | 🔹literal(((_8397 & 32768u))→unknown | 🔹literal(0u))→unknown | 🔸operator(>)→bool | 🔸operator(&&)→bool | 🔸operator(==)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">998</div>
                <div class="line-content">
                    half3 _14921 = half3(half(1.0 - fast::clamp(fast::max(_15664.x, fast::max(_15664.y, _15664.z)), 0.0, 1.0)));
                    <div class="node-info">🔹declaration(half3 _14921)→half3 | 🔹literal(1.0)→float | 🔹literal(fast::clamp(fast::max(_15664.x, fast::max(_15664.y, _15664.z)), 0.0, 1.0))→unknown | 🔸operator(-)→float | 🔸type_cast(half)→half | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">999</div>
                <div class="line-content">
                    _18823 = _18818 * _14921;
                    <div class="node-info">🔹variable(_18823)→unknown | 🔹variable(_18818)→unknown | 🔹variable(_14921)→half3 | 🔸operator(*)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1000</div>
                <div class="line-content">
                    _18822 = _18806 * _14921;
                    <div class="node-info">🔹variable(_18822)→unknown | 🔹variable(_18806)→unknown | 🔹variable(_14921)→half3 | 🔸operator(*)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1004</div>
                <div class="line-content">
                    _18823 = _18818;
                    <div class="node-info">🔹variable(_18823)→unknown | 🔹variable(_18818)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1005</div>
                <div class="line-content">
                    _18822 = _18806;
                    <div class="node-info">🔹variable(_18822)→unknown | 🔹variable(_18806)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1007</div>
                <div class="line-content">
                    _18827 = _18823;
                    <div class="node-info">🔹variable(_18827)→unknown | 🔹variable(_18823)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1008</div>
                <div class="line-content">
                    _18825 = _18822;
                    <div class="node-info">🔹variable(_18825)→unknown | 🔹variable(_18822)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1012</div>
                <div class="line-content">
                    _18827 = _8393;
                    <div class="node-info">🔹variable(_18827)→unknown | 🔹variable(_8393)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1013</div>
                <div class="line-content">
                    _18825 = _8393;
                    <div class="node-info">🔹variable(_18825)→unknown | 🔹variable(_8393)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1015</div>
                <div class="line-content">
                    _18826 = _18827;
                    <div class="node-info">🔹variable(_18826)→unknown | 🔹variable(_18827)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1016</div>
                <div class="line-content">
                    _18824 = _18825;
                    <div class="node-info">🔹variable(_18824)→unknown | 🔹variable(_18825)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1020</div>
                <div class="line-content">
                    _18826 = _8393;
                    <div class="node-info">🔹variable(_18826)→unknown | 🔹variable(_8393)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1021</div>
                <div class="line-content">
                    _18824 = _8393;
                    <div class="node-info">🔹variable(_18824)→unknown | 🔹variable(_8393)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1023</div>
                <div class="line-content">
                    half3 _14565 = half3(float3(0.0));
                    <div class="node-info">🔹declaration(half3 _14565)→half3 | 🔹literal(0.0)→float | 🔸type_cast(float3)→float3 | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1025</div>
                <div class="line-content">
                    if (_8590)
                    <div class="node-info">🔹variable(_8590)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1027</div>
                <div class="line-content">
                    float3 _14569 = float3(_18824);
                    <div class="node-info">🔹declaration(float3 _14569)→float3 | 🔹variable(_18824)→unknown | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1028</div>
                <div class="line-content">
                    float _14575 = float(dot(_18826, _18826));
                    <div class="node-info">🔹declaration(float _14575)→float | 🔹variable(_18826)→unknown | 🔹variable(_18826)→unknown | 🔸function(dot)→float | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1030</div>
                <div class="line-content">
                    if ((float(half(((0.21267099678516387939453125 * _14569.x) + (0.71516001224517822265625 * _14569.y)) + (0.072168998420238494873046875 * _14569.z))) > 0.001000000047497451305389404296875) && (_14575 > 9.9999999747524270787835121154785e-07))
                    <div class="node-info">🔹literal((float(half(((0.21267099678516387939453125)→unknown | 🔹literal(_14569.x))→unknown | 🔸operator(*)→unknown | 🔹literal((0.71516001224517822265625)→unknown | 🔹literal(_14569.y)))→unknown | 🔸operator(*)→unknown | 🔹literal((0.072168998420238494873046875)→unknown | 🔹literal(_14569.z))))→unknown | 🔹literal(0.001000000047497451305389404296875))→unknown | 🔸operator(>)→bool | 🔸operator(*)→bool | 🔸operator(+)→bool | 🔸operator(+)→bool | 🔹literal((_14575)→unknown | 🔹literal(9.9999999747524270787835121154785e)→unknown | 🔸operator(>)→bool | 🔹literal(07))→unknown | 🔸operator(-)→bool | 🔸operator(&&)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1032</div>
                <div class="line-content">
                    float _14592 = fast::clamp(_9109.y, 0.0, 1.0) * 0.75;
                    <div class="node-info">🔹declaration(float _14592)→float | 🔹literal(fast::clamp(_9109.y, 0.0, 1.0))→unknown | 🔹literal(0.75)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1033</div>
                <div class="line-content">
                    float3 _14600 = (float3(_18826) / float3(sqrt(_14575))) * float3(fast::clamp(1.0 - (_14592 * _14592), 0.0, 1.0));
                    <div class="node-info">🔹declaration(float3 _14600)→float3 | 🔹literal((float3(_18826))→unknown | 🔹literal(_14575))→unknown | 🔸function(sqrt)→unknown | 🔸type_cast(float3)→float3 | 🔸operator(/)→float3 | 🔹literal(float3(fast::clamp(1.0)→unknown | 🔸operator(*)→float3 | 🔹literal((_14592)→unknown | 🔹literal(_14592), 0.0, 1.0)))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1034</div>
                <div class="line-content">
                    float _14604 = mix(_11560, 1.0, 0.25);
                    <div class="node-info">🔹declaration(float _14604)→float | 🔹variable(_11560)→float | 🔹literal(1.0)→float | 🔹literal(0.25)→float | 🔸function(mix)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1035</div>
                <div class="line-content">
                    float _14612 = fast::clamp(dot(_9109, fast::normalize(_8373 + _14600)), 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _14612)→float | 🔹literal(fast::clamp(dot(_9109, fast::normalize(_8373)→unknown | 🔹literal(_14600)), 0.0, 1.0))→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1036</div>
                <div class="line-content">
                    float _15697 = _14604 * _14604;
                    <div class="node-info">🔹declaration(float _15697)→float | 🔹variable(_14604)→float | 🔹variable(_14604)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1037</div>
                <div class="line-content">
                    float _15710 = _15697 / (((((_14612 * _15697) * _15697) - _14612) * _14612) + 1.0);
                    <div class="node-info">🔹declaration(float _15710)→float | 🔹variable(_15697)→float | 🔹literal((((((_14612)→unknown | 🔸operator(/)→float | 🔹literal(_15697))→unknown | 🔹literal(_15697))→unknown | 🔸operator(*)→unknown | 🔸operator(*)→float | 🔹literal(_14612))→unknown | 🔹literal(_14612))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→float | 🔹literal(1.0))→unknown | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1038</div>
                <div class="line-content">
                    _18831 = half3(float3(_10557 * _18824) * float3((fast::min(1000.0, (_15710 * _15710) * 0.3183098733425140380859375) * fast::clamp(dot(_9109, _14600), 0.0, 1.0)) * _Block1.SHGIParam.y));
                    <div class="node-info">🔹variable(_18831)→unknown | 🔹variable(_10557)→half3 | 🔹literal(_18824))→unknown | 🔹literal(float3((fast::min(1000.0, (_15710)→unknown | 🔹literal(_15710))→unknown | 🔹literal(0.3183098733425140380859375))→unknown | 🔹literal(fast::clamp(dot(_9109, _14600), 0.0, 1.0)))→unknown | 🔹literal(_Block1.SHGIParam.y)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→half3 | 🔸type_cast(float3)→float3 | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1042</div>
                <div class="line-content">
                    _18831 = _14565;
                    <div class="node-info">🔹variable(_18831)→unknown | 🔹variable(_14565)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1044</div>
                <div class="line-content">
                    _18830 = _18831;
                    <div class="node-info">🔹variable(_18830)→unknown | 🔹variable(_18831)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1048</div>
                <div class="line-content">
                    _18830 = _14565;
                    <div class="node-info">🔹variable(_18830)→unknown | 🔹variable(_14565)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1050</div>
                <div class="line-content">
                    float _14641 = float(half(mix(_Block1.SHGIParam2.y, 1.0, _14482)));
                    <div class="node-info">🔹declaration(float _14641)→float | 🔹literal(_Block1.SHGIParam2.y)→unknown | 🔹literal(1.0)→float | 🔹variable(_14482)→float | 🔸function(mix)→float | 🔸type_cast(half)→half | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1051</div>
                <div class="line-content">
                    _18859 = _18830 * half3(half(_Block1.SHGIParam.y * _14641));
                    <div class="node-info">🔹variable(_18859)→unknown | 🔹variable(_18830)→unknown | 🔹literal(_Block1.SHGIParam.y)→unknown | 🔹variable(_14641)→float | 🔸operator(*)→float | 🔸type_cast(half)→half | 🔸type_cast(half3)→half3 | 🔸operator(*)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1052</div>
                <div class="line-content">
                    _18832 = _18824 * half3(half(_Block1.SHGIParam.x * _14641));
                    <div class="node-info">🔹variable(_18832)→unknown | 🔹variable(_18824)→unknown | 🔹literal(_Block1.SHGIParam.x)→unknown | 🔹variable(_14641)→float | 🔸operator(*)→float | 🔸type_cast(half)→half | 🔸type_cast(half3)→half3 | 🔸operator(*)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1056</div>
                <div class="line-content">
                    _18859 = _8393;
                    <div class="node-info">🔹variable(_18859)→unknown | 🔹variable(_8393)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1057</div>
                <div class="line-content">
                    _18832 = _8393;
                    <div class="node-info">🔹variable(_18832)→unknown | 🔹variable(_8393)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1059</div>
                <div class="line-content">
                    _19028 = _8560 + (_18832 * _18329);
                    <div class="node-info">🔹variable(_19028)→unknown | 🔹variable(_8560)→half3 | 🔹literal((_18832)→unknown | 🔹literal(_18329))→unknown | 🔸operator(*)→unknown | 🔸operator(+)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1060</div>
                <div class="line-content">
                    _18989 = _8565 + float3(_18859 * _18329);
                    <div class="node-info">🔹variable(_18989)→unknown | 🔹variable(_8565)→float3 | 🔹variable(_18859)→unknown | 🔹variable(_18329)→unknown | 🔸operator(*)→unknown | 🔸type_cast(float3)→float3 | 🔸operator(+)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1064</div>
                <div class="line-content">
                    _19028 = _8560;
                    <div class="node-info">🔹variable(_19028)→unknown | 🔹variable(_8560)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1065</div>
                <div class="line-content">
                    _18989 = _8565;
                    <div class="node-info">🔹variable(_18989)→unknown | 🔹variable(_8565)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1067</div>
                <div class="line-content">
                    float _15792 = fast::clamp(dot(_9109, fast::normalize(_8373 + _8373)), 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _15792)→float | 🔹literal(fast::clamp(dot(_9109, fast::normalize(_8373)→unknown | 🔹literal(_8373)), 0.0, 1.0))→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1068</div>
                <div class="line-content">
                    float _15839 = _12049 / (((((_15792 * _12049) * _12049) - _15792) * _15792) + 1.0);
                    <div class="node-info">🔹declaration(float _15839)→float | 🔹variable(_12049)→float | 🔹literal((((((_15792)→unknown | 🔸operator(/)→float | 🔹literal(_12049))→unknown | 🔹literal(_12049))→unknown | 🔸operator(*)→unknown | 🔸operator(*)→float | 🔹literal(_15792))→unknown | 🔹literal(_15792))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→float | 🔹literal(1.0))→unknown | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1069</div>
                <div class="line-content">
                    float _15805 = float(half(fast::clamp(_8378, 0.0, 1.0)));
                    <div class="node-info">🔹declaration(float _15805)→float | 🔹literal(fast::clamp(_8378, 0.0, 1.0))→unknown | 🔸type_cast(half)→half | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1070</div>
                <div class="line-content">
                    float _15854 = _12049 * 0.5;
                    <div class="node-info">🔹declaration(float _15854)→float | 🔹variable(_12049)→float | 🔹literal(0.5)→float | 🔸operator(*)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1071</div>
                <div class="line-content">
                    float _15858 = 1.0 - _15854;
                    <div class="node-info">🔹declaration(float _15858)→float | 🔹literal(1.0)→float | 🔹variable(_15854)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1072</div>
                <div class="line-content">
                    float _15861 = (_15805 * _15858) + _15854;
                    <div class="node-info">🔹declaration(float _15861)→float | 🔹literal((_15805)→unknown | 🔹literal(_15858))→unknown | 🔸operator(*)→unknown | 🔹variable(_15854)→float | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1073</div>
                <div class="line-content">
                    half3 _15813 = half3(_12025);
                    <div class="node-info">🔹declaration(half3 _15813)→half3 | 🔹variable(_12025)→float3 | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1074</div>
                <div class="line-content">
                    float3 _15762 = float3(_10569);
                    <div class="node-info">🔹declaration(float3 _15762)→float3 | 🔹variable(_10569)→half3 | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1075</div>
                <div class="line-content">
                    float3 _15920 = _Block1.cLocalVirtualLitPos.xyz + _Block1.cVirtualLitParam.xyz;
                    <div class="node-info">🔹declaration(float3 _15920)→float3 | 🔹literal(_Block1.cLocalVirtualLitPos.xyz)→unknown | 🔹literal(_Block1.cVirtualLitParam.xyz)→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1076</div>
                <div class="line-content">
                    float3 _15960 = ((((fast::normalize(cross(_8373, float3(0.0, 1.0, 0.0))) * _15920.x) + float3(0.0, _15920.y, 0.0)) + (_8373 * _15920.z)) + (float4(0.0, 0.0, 0.0, 1.0) * _Block1.World)) - in.IN_WorldPosition.xyz;
                    <div class="node-info">🔹declaration(float3 _15960)→float3 | 🔹literal(((((fast::normalize(cross(_8373, float3(0.0, 1.0, 0.0))))→unknown | 🔹literal(_15920.x))→unknown | 🔸operator(*)→unknown | 🔹literal(0.0, _15920.y, 0.0))→unknown | 🔸type_cast(float3)→float3 | 🔹literal((_8373)→unknown | 🔹literal(_15920.z)))→unknown | 🔸operator(*)→unknown | 🔹literal((float4(0.0, 0.0, 0.0, 1.0))→unknown | 🔹literal(_Block1.World)))→unknown | 🔸operator(*)→unknown | 🔹literal(in.IN_WorldPosition.xyz)→unknown | 🔸operator(-)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→float3 | 🔸operator(+)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1077</div>
                <div class="line-content">
                    float3 _15970 = mix(_15960, fast::normalize(_15960), float3(step(0.0, _Block1.cLocalVirtualLitColor.w)));
                    <div class="node-info">🔹declaration(float3 _15970)→float3 | 🔹variable(_15960)→float3 | 🔹literal(fast::normalize(_15960))→unknown | 🔹literal(0.0)→float | 🔹literal(_Block1.cLocalVirtualLitColor.w)→unknown | 🔸function(step)→unknown | 🔸type_cast(float3)→float3 | 🔸function(mix)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1078</div>
                <div class="line-content">
                    half _15974 = half(dot(_15970, _15970));
                    <div class="node-info">🔹declaration(half _15974)→half | 🔹variable(_15970)→float3 | 🔹variable(_15970)→float3 | 🔸function(dot)→float | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1079</div>
                <div class="line-content">
                    float3 _15976 = fast::normalize(_15970);
                    <div class="node-info">🔹declaration(float3 _15976)→float3 | 🔹literal(fast::normalize(_15970))→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1080</div>
                <div class="line-content">
                    half _15979 = _15974 * half(1.0 / (_Block1.cLocalVirtualLitCustom.x * _Block1.cLocalVirtualLitCustom.x));
                    <div class="node-info">🔹declaration(half _15979)→half | 🔹variable(_15974)→half | 🔹literal(1.0)→float | 🔹literal((_Block1.cLocalVirtualLitCustom.x)→unknown | 🔸operator(/)→float | 🔹literal(_Block1.cLocalVirtualLitCustom.x))→unknown | 🔸operator(*)→float | 🔸type_cast(half)→half | 🔸operator(*)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1081</div>
                <div class="line-content">
                    float _15986 = fast::clamp(1.0 - float(_15979 * _15979), 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _15986)→float | 🔹literal(fast::clamp(1.0)→unknown | 🔹variable(_15979)→half | 🔹literal(_15979), 0.0, 1.0)→unknown | 🔸operator(*)→half | 🔸type_cast(float)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1082</div>
                <div class="line-content">
                    half _16031 = half((fast::clamp(dot(_9109, _15976), 0.0, 1.0) * _Block1.cLocalVirtualLitCustom.z) + (1.0 - _Block1.cLocalVirtualLitCustom.z));
                    <div class="node-info">🔹declaration(half _16031)→half | 🔹literal((fast::clamp(dot(_9109, _15976), 0.0, 1.0))→unknown | 🔹literal(_Block1.cLocalVirtualLitCustom.z))→unknown | 🔸operator(*)→unknown | 🔹literal((1.0)→unknown | 🔹literal(_Block1.cLocalVirtualLitCustom.z))→unknown | 🔸operator(-)→unknown | 🔸operator(+)→unknown | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1083</div>
                <div class="line-content">
                    float _16112 = fast::clamp(dot(_9109, fast::normalize(_8373 + _15976)), 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _16112)→float | 🔹literal(fast::clamp(dot(_9109, fast::normalize(_8373)→unknown | 🔹literal(_15976)), 0.0, 1.0))→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1084</div>
                <div class="line-content">
                    float _16159 = _12049 / (((((_16112 * _12049) * _12049) - _16112) * _16112) + 1.0);
                    <div class="node-info">🔹declaration(float _16159)→float | 🔹variable(_12049)→float | 🔹literal((((((_16112)→unknown | 🔸operator(/)→float | 🔹literal(_12049))→unknown | 🔹literal(_12049))→unknown | 🔸operator(*)→unknown | 🔸operator(*)→float | 🔹literal(_16112))→unknown | 🔹literal(_16112))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→float | 🔹literal(1.0))→unknown | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1085</div>
                <div class="line-content">
                    float _8657 = float(_18329);
                    <div class="node-info">🔹declaration(float _8657)→float | 🔹variable(_18329)→unknown | 🔸type_cast(float)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1086</div>
                <div class="line-content">
                    half _8696 = half(fast::min(_8657, mix(abs(_Block1.cVisibilitySH[0].w), 1.0, _Block1.WorldProbeInfo.x)));
                    <div class="node-info">🔹declaration(half _8696)→half | 🔹literal(fast::min(_8657, mix(abs(_Block1.cVisibilitySH[0].w), 1.0, _Block1.WorldProbeInfo.x)))→unknown | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1087</div>
                <div class="line-content">
                    half _16335 = half(-1.023326873779296875);
                    <div class="node-info">🔹declaration(half _16335)→half | 🔹literal(-1.023326873779296875)→unknown | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1088</div>
                <div class="line-content">
                    half _16336 = half(1.023326873779296875);
                    <div class="node-info">🔹declaration(half _16336)→half | 🔹literal(1.023326873779296875)→float | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1089</div>
                <div class="line-content">
                    half _16342 = _9064.y;
                    <div class="node-info">🔹declaration(half _16342)→half | 🔹member_access(_9064.y)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1090</div>
                <div class="line-content">
                    half _16351 = half(-0.858085691928863525390625);
                    <div class="node-info">🔹declaration(half _16351)→half | 🔹literal(-0.858085691928863525390625)→unknown | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1091</div>
                <div class="line-content">
                    half4 _16356 = half4(_16351, half(0.7431240081787109375), _16351, half(0.4290428459644317626953125));
                    <div class="node-info">🔹declaration(half4 _16356)→half4 | 🔹literal(_16351, half(0.7431240081787109375), _16351, half(0.4290428459644317626953125))→unknown | 🔸type_cast(half4)→half4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1092</div>
                <div class="line-content">
                    half _16361 = _9064.z;
                    <div class="node-info">🔹declaration(half _16361)→half | 🔹member_access(_9064.z)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1093</div>
                <div class="line-content">
                    half _16369 = _9064.x;
                    <div class="node-info">🔹declaration(half _16369)→half | 🔹member_access(_9064.x)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1094</div>
                <div class="line-content">
                    half4 _16385 = _16356 * half4(_16342 * _16361, _16361 * _16361, _16369 * _16361, (_16369 * _16369) - (_16342 * _16342));
                    <div class="node-info">🔹declaration(half4 _16385)→half4 | 🔹variable(_16356)→half4 | 🔹variable(_16342)→half | 🔹literal(_16361, _16361)→unknown | 🔹literal(_16361, _16369)→unknown | 🔹literal(_16361, (_16369)→unknown | 🔹variable(_16369)→half | 🔸operator(*)→half | 🔸operator(*)→half | 🔸operator(*)→half | 🔸operator(*)→half | 🔸type_cast(half4)→half4 | 🔸operator(*)→half4 | 🔹literal((_16342)→unknown | 🔹literal(_16342)))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→half4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1095</div>
                <div class="line-content">
                    half _16387 = half(-0.2477079927921295166015625);
                    <div class="node-info">🔹declaration(half _16387)→half | 🔹literal(-0.2477079927921295166015625)→unknown | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1096</div>
                <div class="line-content">
                    _16385.y = _16385.y + _16387;
                    <div class="node-info">🔹literal(_16385.y = _16385.y)→unknown | 🔹variable(_16387)→half | 🔸operator(+)→half</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1097</div>
                <div class="line-content">
                    half3 _16279 = half3(_Block1.cSHCoefficients[0].xyz * float3(half3(half(0.886226952075958251953125))));
                    <div class="node-info">🔹declaration(half3 _16279)→half3 | 🔹literal(_Block1.cSHCoefficients[0].xyz)→unknown | 🔹literal(0.886226952075958251953125)→float | 🔸type_cast(half)→half | 🔸type_cast(half3)→half3 | 🔸type_cast(float3)→float3 | 🔸operator(*)→float3 | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1098</div>
                <div class="line-content">
                    float4 _16284 = float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16342))) * _9064.yzxx);
                    <div class="node-info">🔹declaration(float4 _16284)→float4 | 🔹literal(half4(_16335, _16336, _16335, half(0.858085691928863525390625)→unknown | 🔹literal(_16342)))→unknown | 🔸type_cast(float)→float | 🔹member_access(_9064.yzxx)→unknown | 🔸operator(*)→float | 🔸operator(*)→float | 🔸type_cast(float4)→float4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1099</div>
                <div class="line-content">
                    float4 _16306 = float4(_16385);
                    <div class="node-info">🔹declaration(float4 _16306)→float4 | 🔹variable(_16385)→half4 | 🔸type_cast(float4)→float4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1100</div>
                <div class="line-content">
                    half3 _16397 = half3(float3(0.081409998238086700439453125, 0.74361002445220947265625, -0.66364002227783203125));
                    <div class="node-info">🔹declaration(half3 _16397)→half3 | 🔹literal(0.081409998238086700439453125, 0.74361002445220947265625,)→unknown | 🔹literal(0.66364002227783203125)→float | 🔸operator(-)→float | 🔸type_cast(float3)→float3 | 🔸type_cast(half3)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1101</div>
                <div class="line-content">
                    half _16509 = _16397.y;
                    <div class="node-info">🔹declaration(half _16509)→half | 🔹member_access(_16397.y)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1102</div>
                <div class="line-content">
                    half _16528 = _16397.z;
                    <div class="node-info">🔹declaration(half _16528)→half | 🔹member_access(_16397.z)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1103</div>
                <div class="line-content">
                    half _16536 = _16397.x;
                    <div class="node-info">🔹declaration(half _16536)→half | 🔹member_access(_16397.x)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1104</div>
                <div class="line-content">
                    half4 _16552 = _16356 * half4(_16509 * _16528, _16528 * _16528, _16536 * _16528, (_16536 * _16536) - (_16509 * _16509));
                    <div class="node-info">🔹declaration(half4 _16552)→half4 | 🔹variable(_16356)→half4 | 🔹variable(_16509)→half | 🔹literal(_16528, _16528)→unknown | 🔹literal(_16528, _16536)→unknown | 🔹literal(_16528, (_16536)→unknown | 🔹variable(_16536)→half | 🔸operator(*)→half | 🔸operator(*)→half | 🔸operator(*)→half | 🔸operator(*)→half | 🔸type_cast(half4)→half4 | 🔸operator(*)→half4 | 🔹literal((_16509)→unknown | 🔹literal(_16509)))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→half4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1105</div>
                <div class="line-content">
                    _16552.y = _16552.y + _16387;
                    <div class="node-info">🔹literal(_16552.y = _16552.y)→unknown | 🔹variable(_16387)→half | 🔸operator(+)→half</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1106</div>
                <div class="line-content">
                    float4 _16451 = float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16509))) * _16397.yzxx);
                    <div class="node-info">🔹declaration(float4 _16451)→float4 | 🔹literal(half4(_16335, _16336, _16335, half(0.858085691928863525390625)→unknown | 🔹literal(_16509)))→unknown | 🔸type_cast(float)→float | 🔹member_access(_16397.yzxx)→unknown | 🔸operator(*)→float | 🔸operator(*)→float | 🔸type_cast(float4)→float4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1107</div>
                <div class="line-content">
                    float4 _16473 = float4(_16552);
                    <div class="node-info">🔹declaration(float4 _16473)→float4 | 🔹variable(_16552)→half4 | 🔸type_cast(float4)→float4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion precision-issue ">
                <div class="line-number">1108</div>
                <div class="line-content">
                    half3 _16258 = ((((max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16284)), half(dot(_Block1.cSHCoefficients[3], _16284)), half(dot(_Block1.cSHCoefficients[5], _16284))) + half3(half(dot(_Block1.cSHCoefficients[2], _16306)), half(dot(_Block1.cSHCoefficients[4], _16306)), half(dot(_Block1.cSHCoefficients[6], _16306)))), _9179) * half3(half(0.699999988079071044921875 + (0.300000011920928955078125 * _Block1.EnvInfo.z)))) * half3(half(float(_8295 * _18329) * mix(1.0, _Block1.WorldProbeInfo.y, step(0.0, _Block1.cVisibilitySH[0].w))))) * half3(half(_Block1.GIInfo.z))) + half3(float3(max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16451)), half(dot(_Block1.cSHCoefficients[3], _16451)), half(dot(_Block1.cSHCoefficients[5], _16451))) + half3(half(dot(_Block1.cSHCoefficients[2], _16473)), half(dot(_Block1.cSHCoefficients[4], _16473)), half(dot(_Block1.cSHCoefficients[6], _16473)))), _9179)) * float3(((3.1415927410125732421875 * float(clamp(dot(_9064, _16397), half(0.0), half(1.0)))) * _Block1.WorldProbeInfo.w) * float(half(float(_8295 * _18236)) * half(0.5))))) * half3(half(_Block1.cSHCoefficients[0].w));
                    <div class="node-info">🔹declaration(half3 _16258)→half3 | 🔹literal(((((max(_16279)→unknown | 🔹literal((half3(half(dot(_Block1.cSHCoefficients[1], _16284)), half(dot(_Block1.cSHCoefficients[3], _16284)), half(dot(_Block1.cSHCoefficients[5], _16284))))→unknown | 🔹literal(_Block1.cSHCoefficients[2])→unknown | 🔹literal(_16306)), half(dot(_Block1.cSHCoefficients[4])→unknown | 🔹literal(_16306)), half(dot(_Block1.cSHCoefficients[6])→unknown | 🔹literal(_16306)))), _9179)→unknown | 🔸function(dot)→float | 🔹literal(half3(half(0.699999988079071044921875)→unknown | 🔸operator(*)→float | 🔹literal((0.300000011920928955078125)→unknown | 🔹literal(_Block1.EnvInfo.z)))))→unknown | 🔹variable(_8295)→half | 🔹literal(_18329))→unknown | 🔹literal(1.0)→float | 🔹literal(_Block1.WorldProbeInfo.y)→unknown | 🔹literal(0.0)→float | 🔹literal(_Block1.cVisibilitySH[0].w))))→unknown | 🔸function(step)→unknown | 🔸function(mix)→float | 🔹literal(half3(half(_Block1.GIInfo.z)→unknown | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸type_cast(float)→float | 🔸type_cast(half)→half | 🔸type_cast(half3)→half3 | 🔸operator(*)→half3 | 🔸operator(*)→half3 | 🔹literal(half3(float3(max(_16279)→unknown | 🔹literal((half3(half(dot(_Block1.cSHCoefficients[1], _16451)), half(dot(_Block1.cSHCoefficients[3], _16451)), half(dot(_Block1.cSHCoefficients[5], _16451))))→unknown | 🔹literal(dot(_Block1.cSHCoefficients[2], _16473)), half(dot(_Block1.cSHCoefficients[4], _16473)), half(dot(_Block1.cSHCoefficients[6], _16473)))), _9179)→unknown | 🔸type_cast(half)→half | 🔸type_cast(half3)→half3 | 🔹literal(float3(((3.1415927410125732421875)→unknown | 🔹variable(_9064)→half3 | 🔹variable(_16397)→half3 | 🔸function(dot)→float | 🔹literal(0.0)→float | 🔸type_cast(half)→half | 🔹literal(1.0))→unknown | 🔸type_cast(half)→half | 🔸function(clamp)→float | 🔸type_cast(float)→float | 🔹literal(_Block1.WorldProbeInfo.w))→unknown | 🔹literal(float(half(float(_8295)→unknown | 🔹literal(_18236)))→unknown | 🔹literal(0.5)))))→unknown | 🔸type_cast(half)→half | 🔹literal(half3(half(_Block1.cSHCoefficients[0].w)→unknown | 🔸operator(*)→half | 🔸operator(*)→half | 🔸operator(*)→half | 🔸operator(*)→half | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(+)→float | 🔸operator(+)→float | 🔸operator(+)→float | 🔸operator(+)→float | 🔸type_cast(half)→half | 🔸type_cast(half3)→half3 | 🔸operator(+)→half3 | 🔸operator(+)→half3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion precision-issue ">
                <div class="line-number">1109</div>
                <div class="line-content">
                    half3 _8776 = ((_18255 + (half3((float3(_15813 * (half(fast::min(1000.0, (_15839 * _15839) * 0.3183098733425140380859375)) * half(0.25 / fast::max(_15861 * _15861, _12108)))) * _Block1.cVirtualLitColor.xyz) * _15805) + half3((_Block1.cVirtualLitColor.xyz * abs(_8378)) * _15762))) + half3(fast::min(float3(8192.0), ((((float3((_15813 * (half(fast::min(1000.0, (_16159 * _16159) * 0.3183098733425140380859375)) * half(0.25 / fast::max(_15861 * ((float(_16031) * _15858) + _15854), _12108)))) * _16031) * float3(_Block1.cLocalVirtualLitPos.w)) + float3(_10569 * _16031)) * float(half(fast::min(float(half((_15986 * _15986) / ((float(_15974) * abs(_Block1.cLocalVirtualLitCustom.y)) + 9.9999997473787516355514526367188e-05))), _Block1.cLocalVirtualLitCustom.w)))) * (_Block1.cLocalVirtualLitColor.xyz * abs(_Block1.cLocalVirtualLitColor.w))) * _Block1.DiyLightingInfo.z))) * _8696;
                    <div class="node-info">🔹declaration(half3 _8776)→half3 | 🔹literal(((_18255)→unknown | 🔹literal((half3((float3(_15813)→unknown | 🔹literal((half(fast::min(1000.0, (_15839)→unknown | 🔹literal(_15839))→unknown | 🔹literal(0.3183098733425140380859375)))→unknown | 🔹literal(0.25)→float | 🔹literal(fast::max(_15861)→unknown | 🔸operator(/)→float | 🔹literal(_15861, _12108)))))→unknown | 🔹literal(_Block1.cVirtualLitColor.xyz))→unknown | 🔹variable(_15805)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸type_cast(half)→half | 🔸operator(*)→half | 🔸operator(*)→half | 🔸operator(*)→half | 🔸operator(*)→half | 🔹literal((_Block1.cVirtualLitColor.xyz)→unknown | 🔹literal(_8378)))→unknown | 🔹literal(_15762))→unknown | 🔸operator(*)→unknown | 🔸function(abs)→unknown | 🔸operator(*)→unknown | 🔸type_cast(half3)→half3 | 🔹literal(fast::min(float3(8192.0), ((((float3((_15813)→unknown | 🔹literal((half(fast::min(1000.0, (_16159)→unknown | 🔹literal(_16159))→unknown | 🔹literal(0.3183098733425140380859375)))→unknown | 🔹literal(half(0.25)→unknown | 🔹literal(fast::max(_15861)→unknown | 🔸operator(/)→unknown | 🔹literal(((float(_16031))→unknown | 🔹variable(_15858)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸type_cast(half3)→half3 | 🔹literal(_15854), _12108)))))→unknown | 🔹literal(_16031))→unknown | 🔹literal(_Block1.cLocalVirtualLitPos.w))→unknown | 🔸type_cast(float3)→float3 | 🔸operator(*)→float3 | 🔸operator(*)→float3 | 🔹variable(_10569)→half3 | 🔹literal(_16031)))→unknown | 🔹literal(half(fast::min(float(half((_15986)→unknown | 🔹literal(_15986))→unknown | 🔹literal(((float(_15974))→unknown | 🔸operator(/)→unknown | 🔹literal(abs(_Block1.cLocalVirtualLitCustom.y)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸type_cast(float)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸type_cast(float3)→float3 | 🔹literal(9.9999997473787516355514526367188e)→unknown | 🔹literal(05))), _Block1.cLocalVirtualLitCustom.w)))))→unknown | 🔹literal((_Block1.cLocalVirtualLitColor.xyz)→unknown | 🔹literal(_Block1.cLocalVirtualLitColor.w)))→unknown | 🔸function(abs)→unknown | 🔹literal(_Block1.DiyLightingInfo.z))))→unknown | 🔹variable(_8696)→half | 🔸operator(*)→half | 🔸operator(*)→half | 🔸operator(*)→half | 🔸operator(*)→half | 🔸operator(-)→half | 🔸operator(+)→float3 | 🔸operator(+)→float3 | 🔸operator(+)→float3 | 🔸operator(+)→float3 | 🔸operator(+)→float3 | 🔸operator(+)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1110</div>
                <div class="line-content">
                    float _16622 = length(_8370);
                    <div class="node-info">🔹declaration(float _16622)→float | 🔹variable(_8370)→float3 | 🔸function(length)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1111</div>
                <div class="line-content">
                    float _16645 = _8370.y;
                    <div class="node-info">🔹declaration(float _16645)→float | 🔹member_access(_8370.y)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1112</div>
                <div class="line-content">
                    float _16657 = (_16645 + _12108) + float((_9251 * half(9.9956989288330078125e-05)) * half(int(sign(_16645))));
                    <div class="node-info">🔹declaration(float _16657)→float | 🔹literal((_16645)→unknown | 🔹literal(_12108))→unknown | 🔹literal((_9251)→unknown | 🔹literal(half(9.9956989288330078125e)→unknown | 🔸operator(*)→unknown | 🔹literal(05)))→unknown | 🔹variable(_16645)→float | 🔸function(sign)→unknown | 🔸function(int)→unknown | 🔸type_cast(half)→half | 🔸operator(*)→half | 🔸operator(-)→half | 🔸type_cast(float)→float | 🔸operator(+)→float | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1113</div>
                <div class="line-content">
                    float2 _16682 = fast::max(float2(0.0), float2(_Block1.FogInfo.w, exp((-_Block1.AerialPerspectiveMie.y) * _Block1.CameraPos.y) * _Block1.AerialPerspectiveMie.z) * ((float2(1.0) - exp(-fast::min(float2(_Block1.FogInfo.z, _Block1.AerialPerspectiveMie.y) * _16657, float2(10.0)))) / float2(_16657)));
                    <div class="node-info">🔹declaration(float2 _16682)→float2 | 🔹literal(fast::max(float2(0.0), float2(_Block1.FogInfo.w, exp(()→unknown | 🔹literal(_Block1.AerialPerspectiveMie.y))→unknown | 🔹literal(_Block1.CameraPos.y))→unknown | 🔹literal(_Block1.AerialPerspectiveMie.z))→unknown | 🔹literal(((float2(1.0))→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(-fast::min(float2(_Block1.FogInfo.z, _Block1.AerialPerspectiveMie.y))→unknown | 🔹literal(_16657, float2(10.0)))))→unknown | 🔹literal(_16657))→unknown | 🔸type_cast(float2)→float2 | 🔸operator(/)→float2 | 🔸operator(*)→float2 | 🔸function(exp)→unknown | 🔸operator(-)→unknown | 🔸operator(-)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1114</div>
                <div class="line-content">
                    float3 _16688 = fast::max(float3(_12108), _Block1.AerialPerspectiveExt.xyz);
                    <div class="node-info">🔹declaration(float3 _16688)→float3 | 🔹literal(fast::max(float3(_12108), _Block1.AerialPerspectiveExt.xyz))→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1115</div>
                <div class="line-content">
                    float3 _16698 = float3(_8303);
                    <div class="node-info">🔹declaration(float3 _16698)→float3 | 🔹variable(_8303)→half3 | 🔸type_cast(float3)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1116</div>
                <div class="line-content">
                    float3 _16715 = exp(-(_16688 * ((_16622 * (_Block1.FogColor.w + ((1.0 - _Block1.FogColor.w) * fast::clamp(_16622 / _Block1.FogInfo.x, 0.0, 1.0)))) * ((_16682.x / dot(_16688, _16698)) + ((_16682.y * fast::max(9.9999999747524270787835121154785e-07, _Block1.AerialPerspectiveRay.w * 0.0005000000237487256526947021484375)) * 5.0)))));
                    <div class="node-info">🔹declaration(float3 _16715)→float3 | 🔹literal(-(_16688)→unknown | 🔹literal(((_16622)→unknown | 🔹literal((_Block1.FogColor.w)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(((1.0)→unknown | 🔹literal(_Block1.FogColor.w))→unknown | 🔹literal(fast::clamp(_16622)→unknown | 🔹literal(_Block1.FogInfo.x, 0.0, 1.0)))))→unknown | 🔸operator(/)→unknown | 🔹literal(((_16682.x)→unknown | 🔹variable(_16688)→float3 | 🔹literal(_16698))→unknown | 🔸function(dot)→float | 🔸operator(/)→float | 🔸operator(*)→float | 🔸operator(*)→float | 🔸operator(-)→float | 🔹literal(((_16682.y)→unknown | 🔹literal(fast::max(9.9999999747524270787835121154785e)→unknown | 🔸operator(*)→unknown | 🔹literal(07, _Block1.AerialPerspectiveRay.w)→unknown | 🔹literal(0.0005000000237487256526947021484375)))→unknown | 🔹literal(5.0)))))→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔸operator(+)→float | 🔸operator(+)→float | 🔸function(exp)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1117</div>
                <div class="line-content">
                    float3 _16602 = fast::normalize(_8370);
                    <div class="node-info">🔹declaration(float3 _16602)→float3 | 🔹literal(fast::normalize(_8370))→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1118</div>
                <div class="line-content">
                    float _16756 = fast::clamp(dot(_16602, _Block1.OriginSunDir.xyz), 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _16756)→float | 🔹literal(fast::clamp(dot(_16602, _Block1.OriginSunDir.xyz), 0.0, 1.0))→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1119</div>
                <div class="line-content">
                    float _16759 = fast::max(0.0, _16602.y);
                    <div class="node-info">🔹declaration(float _16759)→float | 🔹literal(fast::max(0.0, _16602.y))→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1120</div>
                <div class="line-content">
                    float _16820 = fast::clamp((_16622 - 80.0) / fast::max(_12108, 520.0), 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _16820)→float | 🔹literal(fast::clamp((_16622)→unknown | 🔹literal(80.0))→unknown | 🔹literal(fast::max(_12108, 520.0), 0.0, 1.0))→unknown | 🔸operator(/)→unknown | 🔸operator(-)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1121</div>
                <div class="line-content">
                    float _16778 = 1.0 - (_16759 * _16759);
                    <div class="node-info">🔹declaration(float _16778)→float | 🔹literal(1.0)→float | 🔹literal((_16759)→unknown | 🔹literal(_16759))→unknown | 🔸operator(*)→unknown | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1122</div>
                <div class="line-content">
                    float3 _16785 = float3(((1.0 - (_Block1.AerialPerspectiveExt.w * _Block1.AerialPerspectiveExt.w)) / (12.56637096405029296875 * powr(fast::max(1.0 + (_Block1.AerialPerspectiveExt.w * (_Block1.AerialPerspectiveExt.w - (2.0 * _16756))), _12108), 1.5))) * (_16820 * _16820)) * _Block1.SunFogColor.xyz;
                    <div class="node-info">🔹declaration(float3 _16785)→float3 | 🔹literal(float3(((1.0)→unknown | 🔹literal((_Block1.AerialPerspectiveExt.w)→unknown | 🔹literal(_Block1.AerialPerspectiveExt.w)))→unknown | 🔹literal((12.56637096405029296875)→unknown | 🔸operator(/)→unknown | 🔹literal(powr(fast::max(1.0)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔹literal((_Block1.AerialPerspectiveExt.w)→unknown | 🔹literal((_Block1.AerialPerspectiveExt.w)→unknown | 🔸operator(*)→unknown | 🔹literal((2.0)→unknown | 🔹literal(_16756))), _12108), 1.5))))→unknown | 🔹literal((_16820)→unknown | 🔹literal(_16820)))→unknown | 🔹literal(_Block1.SunFogColor.xyz)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1123</div>
                <div class="line-content">
                    half _16793 = half(_16756);
                    <div class="node-info">🔹declaration(half _16793)→half | 🔹variable(_16756)→float | 🔸type_cast(half)→half | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1124</div>
                <div class="line-content">
                    float3 _16805 = ((_Block1.AerialPerspectiveRay.xyz * float(half(1.0) + (_16793 * _16793))) + (_16785 * _Block1.AerialPerspectiveMie.x)) + ((_Block1.FogColor.xyz * (0.0596831031143665313720703125 * (1.0 + (_16778 * _16778)))) + _16785);
                    <div class="node-info">🔹declaration(float3 _16805)→float3 | 🔹literal(((_Block1.AerialPerspectiveRay.xyz)→unknown | 🔹literal(half(1.0)→unknown | 🔸type_cast(float)→float | 🔸operator(*)→float | 🔹literal((_16793)→unknown | 🔹literal(_16793))))→unknown | 🔸operator(*)→unknown | 🔹literal((_16785)→unknown | 🔹literal(_Block1.AerialPerspectiveMie.x)))→unknown | 🔸operator(*)→unknown | 🔹literal(((_Block1.FogColor.xyz)→unknown | 🔹literal((0.0596831031143665313720703125)→unknown | 🔹literal((1.0)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal((_16778)→unknown | 🔹literal(_16778)))))→unknown | 🔸operator(*)→unknown | 🔹literal(_16785))→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→unknown | 🔸operator(+)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion precision-issue ">
                <div class="line-number">1125</div>
                <div class="line-content">
                    float3 _16862 = (((((((((((_Block1.cVisibilitySH[0].xyz * _8657) * _15762) * _Block1.AmbientColor.w) * _Block1.ReflectionProbeBBMin.w) * float3(_Block1.cSHCoefficients[0].w)).xyz + float3(_18268)).xyz + (_18989 + float3(((_9179 * _9254) + _8393) * _8696))).xyz + float3((_19028 + half3(float3(_16258) * float3((_15805 * 0.5) + 0.5))) * _10569)).xyz + float3((half3(_9698 * _10588) * _11772) + _8776)).xyz * dot(_16715, _16698)) + (_16805 - (_16805 * _16715))).xyz;
                    <div class="node-info">🔹declaration(float3 _16862)→float3 | 🔹literal((((((((((((_Block1.cVisibilitySH[0].xyz)→unknown | 🔹literal(_8657))→unknown | 🔹literal(_15762))→unknown | 🔹literal(_Block1.AmbientColor.w))→unknown | 🔹literal(_Block1.ReflectionProbeBBMin.w))→unknown | 🔹literal(float3(_Block1.cSHCoefficients[0].w)).xyz)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔹literal(float3(_18268)).xyz)→unknown | 🔹literal((_18989)→unknown | 🔹literal(((_9179)→unknown | 🔹variable(_9254)→half3 | 🔸operator(*)→half3 | 🔸type_cast(float3)→float3 | 🔹literal(_8393))→unknown | 🔹literal(_8696))).xyz)→unknown | 🔸operator(*)→unknown | 🔹literal(float3((_19028)→unknown | 🔹variable(_16258)→half3 | 🔸type_cast(float3)→float3 | 🔹literal(float3((_15805)→unknown | 🔹literal(0.5)→float | 🔸operator(*)→float | 🔸operator(*)→float3 | 🔸type_cast(half3)→half3 | 🔹literal(0.5))))→unknown | 🔹literal(_10569)).xyz)→unknown | 🔸operator(*)→unknown | 🔹literal((half3(_9698)→unknown | 🔹literal(_10588))→unknown | 🔹variable(_11772)→half | 🔸operator(*)→half | 🔸operator(*)→half | 🔸type_cast(float3)→float3 | 🔹literal(_8776)).xyz)→unknown | 🔹variable(_16715)→float3 | 🔹literal(_16698))→unknown | 🔸function(dot)→float | 🔸operator(*)→float | 🔹literal((_16805)→unknown | 🔹literal((_16805)→unknown | 🔹literal(_16715))).xyz)→unknown | 🔸operator(*)→unknown | 🔸operator(-)→unknown | 🔸operator(+)→float | 🔸operator(+)→float3 | 🔸operator(+)→float3 | 🔸operator(+)→float3 | 🔸operator(+)→float3 | 🔸operator(+)→float3 | 🔸operator(+)→float3 | 🔸operator(+)→float3 | 🔸operator(+)→float3 | 🔸operator(+)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1126</div>
                <div class="line-content">
                    float3 _8804 = (_16862 * _9868).xyz;
                    <div class="node-info">🔹declaration(float3 _8804)→float3 | 🔹literal((_16862)→unknown | 🔹literal(_9868).xyz)→unknown | 🔸operator(*)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1128</div>
                <div class="line-content">
                    if (_Block1.eIsPlayerOverride < 0.5)
                    <div class="node-info">🔹member_access(_Block1.eIsPlayerOverride)→unknown | 🔹literal(0.5)→float | 🔸operator(<)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1131</div>
                <div class="line-content">
                    if ((_Block1.ScreenMotionGray.x * _Block1.ScreenMotionGray.x) > _12108)
                    <div class="node-info">🔹literal((_Block1.ScreenMotionGray.x)→unknown | 🔹literal(_Block1.ScreenMotionGray.x))→unknown | 🔹variable(_12108)→float | 🔸operator(>)→bool | 🔸operator(*)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1133</div>
                <div class="line-content">
                    float _16911 = fast::clamp((_Block1.CameraPos.w - _Block1.ScreenMotionGray.w) / fast::max(_12108, (_Block1.ScreenMotionGray.w + abs(_Block1.ScreenMotionGray.x)) - _Block1.ScreenMotionGray.w), 0.0, 1.0);
                    <div class="node-info">🔹declaration(float _16911)→float | 🔹literal(fast::clamp((_Block1.CameraPos.w)→unknown | 🔹literal(_Block1.ScreenMotionGray.w))→unknown | 🔹literal(fast::max(_12108, (_Block1.ScreenMotionGray.w)→unknown | 🔸operator(/)→unknown | 🔸operator(-)→unknown | 🔹literal(_Block1.ScreenMotionGray.x)))→unknown | 🔹literal(_Block1.ScreenMotionGray.w), 0.0, 1.0)→unknown | 🔸operator(-)→unknown | 🔸function(abs)→unknown | 🔸operator(+)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1135</div>
                <div class="line-content">
                    if (_Block1.ScreenMotionGray.x > 0.001000000047497451305389404296875)
                    <div class="node-info">🔹literal(_Block1.ScreenMotionGray.x)→unknown | 🔹literal(0.001000000047497451305389404296875)→float | 🔸operator(>)→bool | 🔸function(if)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1137</div>
                <div class="line-content">
                    _19029 = 1.0 - _16911;
                    <div class="node-info">🔹variable(_19029)→unknown | 🔹literal(1.0)→float | 🔹variable(_16911)→float | 🔸operator(-)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1141</div>
                <div class="line-content">
                    _19029 = _16911;
                    <div class="node-info">🔹variable(_19029)→unknown | 🔹variable(_16911)→float | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line type-conversion ">
                <div class="line-number">1143</div>
                <div class="line-content">
                    _19032 = mix(_8804, float3(dot(_8804, _16698) * (0.00999999977648258209228515625 * floor(_Block1.ScreenMotionGray.z))), float3(_19029 * fract(_Block1.ScreenMotionGray.z)));
                    <div class="node-info">🔹variable(_19032)→unknown | 🔹variable(_8804)→float3 | 🔹variable(_8804)→float3 | 🔹literal(_16698))→unknown | 🔹literal((0.00999999977648258209228515625)→unknown | 🔹literal(_Block1.ScreenMotionGray.z)→unknown | 🔸function(floor)→unknown | 🔸operator(*)→unknown | 🔸operator(*)→unknown | 🔸function(dot)→float | 🔸type_cast(float3)→float3 | 🔹variable(_19029)→unknown | 🔹literal(_Block1.ScreenMotionGray.z)→unknown | 🔸function(fract)→unknown | 🔸operator(*)→unknown | 🔸type_cast(float3)→float3 | 🔸function(mix)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1147</div>
                <div class="line-content">
                    _19032 = _8804;
                    <div class="node-info">🔹variable(_19032)→unknown | 🔹variable(_8804)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1149</div>
                <div class="line-content">
                    _19031 = _19032;
                    <div class="node-info">🔹variable(_19031)→unknown | 🔹variable(_19032)→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1153</div>
                <div class="line-content">
                    _19031 = _8804;
                    <div class="node-info">🔹variable(_19031)→unknown | 🔹variable(_8804)→float3 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1155</div>
                <div class="line-content">
                    float4 _8808 = float4(_19031.x, _19031.y, _19031.z, float4(0.0).w);
                    <div class="node-info">🔹declaration(float4 _8808)→float4 | 🔹literal(_19031.x, _19031.y, _19031.z, float4(0.0).w)→unknown | 🔸type_cast(float4)→float4 | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1156</div>
                <div class="line-content">
                    _8808.w = _9868;
                    <div class="node-info">🔹literal(_8808.w = _9868)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1157</div>
                <div class="line-content">
                    float3 _8816 = fast::min(_8808.xyz, float3(10000.0));
                    <div class="node-info">🔹declaration(float3 _8816)→float3 | 🔹literal(fast::min(_8808.xyz, float3(10000.0)))→unknown | 🔹assignment(=)→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1158</div>
                <div class="line-content">
                    out._Ret = float4(_8816.x, _8816.y, _8816.z, _8808.w);
                    <div class="node-info">🔹literal(out._Ret = float4(_8816.x, _8816.y, _8816.z, _8808.w))→unknown</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">1159</div>
                <div class="line-content">
                    return out;
                    <div class="node-info">🔹literal(return out)→unknown</div>
                </div>
            </div>

        </div>
    </div>
</body>
</html>
