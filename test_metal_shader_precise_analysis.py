#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Metal着色器的精确类型分析
"""

import sys
import os
import webbrowser
from pathlib import Path

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def find_metal_shader_file():
    """查找metal_shader_ps文件"""
    # 可能的文件路径
    possible_paths = [
        "metal_shader_ps",
        "metal_shader_ps.metal",
        "metal_shader_ps.txt",
        "Shaders/metal_shader_ps",
        "Shaders/metal_shader_ps.metal",
        "TestData/metal_shader_ps",
        "TestData/metal_shader_ps.metal",
        "../metal_shader_ps",
        "../metal_shader_ps.metal",
    ]
    
    current_dir = Path(".")
    
    # 在当前目录及其子目录中搜索
    for pattern in ["metal_shader_ps*", "*metal_shader*"]:
        for file_path in current_dir.rglob(pattern):
            if file_path.is_file():
                print(f"找到着色器文件: {file_path}")
                return str(file_path)
    
    # 检查可能的路径
    for path in possible_paths:
        if os.path.exists(path):
            print(f"找到着色器文件: {path}")
            return path
    
    return None

def read_shader_content(file_path):
    """读取着色器文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"成功读取着色器文件，内容长度: {len(content)} 字符")
        return content
    except UnicodeDecodeError:
        # 尝试其他编码
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read()
            print(f"使用GBK编码读取着色器文件，内容长度: {len(content)} 字符")
            return content
        except Exception as e:
            print(f"读取文件失败: {str(e)}")
            return None
    except Exception as e:
        print(f"读取文件失败: {str(e)}")
        return None

def analyze_shader_with_precise_types(shader_content):
    """使用精确类型分析方法分析着色器"""
    print("开始精确类型分析...")
    
    try:
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        processor = ShaderAnalysisProcessor()
        
        # 使用精确类型分析
        result = processor.analyze_shader_with_precise_types(
            shader_content, 
            save_reports=True, 
            base_filename="metal_shader_precise_analysis"
        )
        
        print("✅ 精确类型分析完成！")
        return result
        
    except Exception as e:
        print(f"❌ 精确类型分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def display_analysis_results(result):
    """显示分析结果"""
    if not result:
        return
    
    print("\n" + "=" * 60)
    print("📊 精确类型分析结果")
    print("=" * 60)
    
    # 显示基本信息
    print(f"分析方法: {result.get('analysis_method', 'unknown')}")
    
    # 显示摘要
    if 'summary' in result:
        print("\n📋 分析摘要:")
        print("-" * 40)
        summary_lines = result['summary'].split('\n')
        for line in summary_lines[:20]:  # 只显示前20行摘要
            if line.strip():
                print(line)
    
    # 显示统计信息
    if 'analysis' in result and 'precise_analysis' in result['analysis']:
        precise_data = result['analysis']['precise_analysis']
        overall_stats = precise_data['overall_statistics']
        
        print(f"\n📈 详细统计:")
        print("-" * 40)
        print(f"总代码行: {overall_stats['total_lines']}")
        print(f"总节点数: {overall_stats['total_nodes']}")
        print(f"变量声明: {overall_stats['total_variables']}")
        print(f"中间结果: {overall_stats['total_intermediate_results']}")
        print(f"类型转换: {overall_stats['total_type_conversions']}")
        print(f"精度问题: {overall_stats['total_precision_issues']}")
        print(f"准确性评分: {overall_stats['precision_accuracy_score']:.1f}%")
        
        # 显示类型分布
        if overall_stats['type_distribution']:
            print(f"\n🎨 类型分布:")
            for type_name, count in sorted(overall_stats['type_distribution'].items()):
                percentage = count / overall_stats['total_nodes'] * 100
                print(f"  {type_name}: {count} ({percentage:.1f}%)")
        
        # 显示对比结果
        if 'comparison' in result['analysis']:
            comparison = result['analysis']['comparison']
            improvement = comparison.get('operation_count_improvement', {})
            if improvement:
                print(f"\n📊 分析改进:")
                print(f"  原始方法: {improvement['original']} 个操作")
                print(f"  精确方法: {improvement['precise']} 个节点")
                print(f"  精度提升: +{improvement['improvement']} 个节点 ({improvement['improvement_percentage']:.1f}%)")
    
    # 显示生成的文件
    if 'files' in result:
        print(f"\n📄 生成的报告文件:")
        print("-" * 40)
        for report_type, file_path in result['files'].items():
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"  {report_type}: {file_path} ({file_size} bytes)")
            else:
                print(f"  {report_type}: {file_path} (文件不存在)")

def open_html_report(result):
    """打开HTML报告"""
    if not result or 'files' not in result:
        print("❌ 没有找到报告文件")
        return False
    
    html_file = result['files'].get('html')
    if not html_file or not os.path.exists(html_file):
        print("❌ HTML报告文件不存在")
        return False
    
    try:
        # 获取绝对路径
        abs_path = os.path.abspath(html_file)
        file_url = f'file:///{abs_path.replace(os.sep, "/")}'
        
        print(f"🌐 正在打开HTML报告: {abs_path}")
        
        # 尝试用默认浏览器打开
        webbrowser.open(file_url)
        
        print("✅ HTML报告已在浏览器中打开")
        return True
        
    except Exception as e:
        print(f"❌ 打开HTML报告失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 Metal着色器精确类型分析测试")
    print("=" * 60)
    
    # 1. 查找着色器文件
    print("1. 查找metal_shader_ps文件...")
    shader_file = find_metal_shader_file()
    
    if not shader_file:
        print("❌ 未找到metal_shader_ps文件")
        print("请确保文件存在于以下位置之一:")
        print("  - 当前目录")
        print("  - Shaders/目录")
        print("  - TestData/目录")
        print("  - 上级目录")
        return False
    
    # 2. 读取着色器内容
    print(f"\n2. 读取着色器文件: {shader_file}")
    shader_content = read_shader_content(shader_file)
    
    if not shader_content:
        print("❌ 读取着色器文件失败")
        return False
    
    # 显示着色器内容预览
    lines = shader_content.split('\n')
    print(f"着色器文件包含 {len(lines)} 行代码")
    print("前10行预览:")
    for i, line in enumerate(lines[:10], 1):
        print(f"  {i:2d}: {line[:80]}{'...' if len(line) > 80 else ''}")
    
    # 3. 执行精确类型分析
    print(f"\n3. 执行精确类型分析...")
    result = analyze_shader_with_precise_types(shader_content)
    
    if not result:
        print("❌ 精确类型分析失败")
        return False
    
    # 4. 显示分析结果
    print(f"\n4. 显示分析结果...")
    display_analysis_results(result)
    
    # 5. 打开HTML报告
    print(f"\n5. 打开HTML报告...")
    success = open_html_report(result)
    
    if success:
        print(f"\n🎉 测试完成！")
        print("HTML报告已在浏览器中打开，您可以查看详细的精确类型分析结果。")
    else:
        print(f"\n⚠️  测试部分完成，但HTML报告打开失败。")
        if 'files' in result and 'html' in result['files']:
            print(f"您可以手动打开HTML文件: {result['files']['html']}")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
