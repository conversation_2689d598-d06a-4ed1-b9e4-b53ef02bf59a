#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证着色器分析功能集成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_core_modules():
    """验证核心模块"""
    print("🔍 验证核心模块...")
    
    try:
        # 验证分析器核心
        from Process.Analysis.shader_analyzer_core import ShaderAnalyzerCore, OperationType, DataTypeInfo
        print("✅ ShaderAnalyzerCore 导入成功")
        
        # 验证报告生成器
        from Process.Analysis.report_generator import ReportGenerator
        print("✅ ReportGenerator 导入成功")
        
        # 验证处理器
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        print("✅ ShaderAnalysisProcessor 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 核心模块导入失败: {str(e)}")
        return False

def verify_ui_components():
    """验证UI组件"""
    print("\n🎨 验证UI组件...")
    
    try:
        # 验证分析组件
        from ui.shader_analysis_widget import ShaderAnalysisWidget, MetricCard, AnalysisWorker
        print("✅ ShaderAnalysisWidget 导入成功")
        
        # 验证主窗口集成
        from ui.main_window import MainWindow
        print("✅ MainWindow 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ UI组件导入失败: {str(e)}")
        return False

def verify_functionality():
    """验证功能完整性"""
    print("\n⚙️ 验证功能完整性...")
    
    try:
        # 创建测试内容
        test_shader = """
#include <metal_stdlib>
using namespace metal;

fragment float4 main0(float2 uv [[stage_in]]) {
    half3 color = half3(1.0, 0.5, 0.0);
    float intensity = 0.8;
    half4 result = half4(color * half(intensity), 1.0);
    return float4(result);
}
"""
        
        # 测试分析功能
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        processor = ShaderAnalysisProcessor()
        
        result = processor.analyze_shader(test_shader, save_reports=False)
        
        # 验证结果结构
        assert 'analysis' in result
        assert 'summary' in result
        assert 'statistics' in result['analysis']
        
        stats = result['analysis']['statistics']
        print(f"✅ 分析功能正常 - 发现 {stats['total_operations']} 个操作")
        
        # 验证关键指标
        metrics = processor.get_key_metrics(result['analysis'])
        print(f"✅ 指标计算正常 - 性能评分: {metrics['performance_score']}")
        
        # 验证优化建议
        suggestions = processor.get_optimization_suggestions(result['analysis'])
        print(f"✅ 建议生成正常 - {len(suggestions)} 条建议")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能验证失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def verify_file_structure():
    """验证文件结构"""
    print("\n📁 验证文件结构...")
    
    required_files = [
        'Process/Analysis/__init__.py',
        'Process/Analysis/shader_analyzer_core.py',
        'Process/Analysis/report_generator.py',
        'Process/Analysis/shader_analysis_processor.py',
        'ui/shader_analysis_widget.py',
        'ui/main_window.py'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - 文件不存在")
            all_exist = False
    
    return all_exist

def main():
    """主验证函数"""
    print("🔍 着色器分析功能集成验证")
    print("=" * 60)
    
    # 验证文件结构
    structure_ok = verify_file_structure()
    
    # 验证核心模块
    core_ok = verify_core_modules()
    
    # 验证UI组件
    ui_ok = verify_ui_components()
    
    # 验证功能完整性
    func_ok = verify_functionality()
    
    print("\n" + "=" * 60)
    print("📊 验证结果:")
    print(f"文件结构: {'✅ 完整' if structure_ok else '❌ 缺失文件'}")
    print(f"核心模块: {'✅ 正常' if core_ok else '❌ 异常'}")
    print(f"UI组件: {'✅ 正常' if ui_ok else '❌ 异常'}")
    print(f"功能测试: {'✅ 通过' if func_ok else '❌ 失败'}")
    
    all_ok = structure_ok and core_ok and ui_ok and func_ok
    
    if all_ok:
        print("\n🎉 所有验证通过! 着色器分析功能已成功集成")
        print("\n📖 使用方法:")
        print("1. 启动主程序: python main.py")
        print("2. 加载着色器文件到代码区域")
        print("3. 使用菜单 '处理' -> '🔍 着色器分析' 或按 Ctrl+Alt+A")
        print("4. 在右侧编译区域查看分析结果")
        print("5. 点击按钮查看详细HTML报告")
    else:
        print("\n⚠️ 部分验证失败，请检查错误信息并修复")
    
    return all_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
