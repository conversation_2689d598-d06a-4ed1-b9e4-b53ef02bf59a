#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整HTML报告生成
"""

import sys
import os
import webbrowser
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_complete_html_report():
    """测试完整HTML报告生成"""
    print("🌐 测试完整HTML报告生成")
    print("=" * 50)
    
    # 读取metal_shader_ps文件
    shader_file = "metal_shader_ps"
    if not os.path.exists(shader_file):
        print("❌ 未找到metal_shader_ps文件")
        return False
    
    try:
        with open(shader_file, 'r', encoding='utf-8') as f:
            shader_content = f.read()
        print(f"✅ 读取着色器文件成功 ({len(shader_content)} 字符)")
        print(f"📄 代码行数: {len(shader_content.split(chr(10)))}")
    except Exception as e:
        print(f"❌ 读取文件失败: {str(e)}")
        return False
    
    # 测试不同的HTML选项
    test_cases = [
        {
            'name': '完整报告 - 显示节点详情和类型颜色',
            'options': {
                'show_node_details': True,
                'show_type_colors': True,
                'show_all_lines': True
            },
            'filename': 'complete_with_details'
        },
        {
            'name': '简化报告 - 只显示运算统计',
            'options': {
                'show_node_details': False,
                'show_type_colors': True,
                'show_all_lines': True
            },
            'filename': 'complete_simple'
        },
        {
            'name': '无颜色标记报告',
            'options': {
                'show_node_details': True,
                'show_type_colors': False,
                'show_all_lines': True
            },
            'filename': 'complete_no_colors'
        }
    ]
    
    from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
    processor = ShaderAnalysisProcessor()
    
    generated_files = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['name']}")
        print("-" * 40)
        
        try:
            # 执行分析
            result = processor.analyze_shader_with_precise_types(
                shader_content,
                save_reports=True,
                base_filename=test_case['filename'],
                html_options=test_case['options']
            )
            
            if 'files' in result and 'html' in result['files']:
                html_file = result['files']['html']
                generated_files.append((test_case['name'], html_file))
                print(f"✅ HTML报告生成成功: {html_file}")
                
                # 检查文件大小
                file_size = os.path.getsize(html_file)
                print(f"📄 文件大小: {file_size:,} 字节")
                
                # 检查是否包含完整代码
                with open(html_file, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 统计HTML中的代码行数
                code_line_count = html_content.count('<div class="code-line')
                print(f"📝 HTML中代码行数: {code_line_count}")
                
                # 检查选项是否生效
                if test_case['options']['show_node_details']:
                    if '操作:' in html_content:
                        print("✅ 节点详情显示正常")
                    else:
                        print("⚠️  节点详情可能未完全显示")
                
                if test_case['options']['show_type_colors']:
                    if 'border-left:' in html_content:
                        print("✅ 类型颜色标记正常")
                    else:
                        print("⚠️  类型颜色标记可能未生效")
                
            else:
                print("❌ HTML报告生成失败")
                
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
    
    # 显示生成的文件列表
    if generated_files:
        print(f"\n📋 生成的HTML报告文件:")
        print("=" * 50)
        for name, file_path in generated_files:
            abs_path = os.path.abspath(file_path)
            print(f"• {name}")
            print(f"  文件: {abs_path}")
            print()
        
        # 打开第一个报告文件
        if generated_files:
            first_file = generated_files[0][1]
            abs_path = os.path.abspath(first_file)
            file_url = f'file:///{abs_path.replace(os.sep, "/")}'
            print(f"🌐 打开第一个HTML报告: {abs_path}")
            webbrowser.open(file_url)
    
    return len(generated_files) > 0

def test_ui_integration():
    """测试UI集成"""
    print(f"\n🔗 测试UI集成")
    print("=" * 50)
    
    try:
        # 测试UI组件导入
        sys.path.append('UI')
        from precise_type_analysis_widget import PreciseTypeAnalysisWidget
        
        print("✅ UI组件导入成功")
        
        # 测试HTML选项获取
        widget = PreciseTypeAnalysisWidget()
        
        # 模拟选项设置
        widget.show_node_details_cb.setChecked(True)
        widget.show_type_colors_cb.setChecked(False)
        
        # 获取选项
        html_options = {
            'show_node_details': widget.show_node_details_cb.isChecked(),
            'show_type_colors': widget.show_type_colors_cb.isChecked(),
            'show_all_lines': True
        }
        
        print(f"✅ HTML选项获取成功: {html_options}")
        
        return True
        
    except Exception as e:
        print(f"❌ UI集成测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🌐 完整HTML报告生成测试")
    print("=" * 60)
    print("测试内容:")
    print("• 📄 显示完整着色器代码（不只是运算代码）")
    print("• 🎨 HTML选项控制（节点详情、类型颜色）")
    print("• 🚫 移除最大行数限制，显示全部代码")
    print("• 🔗 UI组件与HTML生成的集成")
    print()
    
    # 测试HTML报告生成
    html_test = test_complete_html_report()
    
    # 测试UI集成
    ui_test = test_ui_integration()
    
    print(f"\n🎉 测试结果总结")
    print("=" * 60)
    
    if html_test:
        print("✅ HTML报告生成测试通过")
        print("   • 显示完整着色器代码")
        print("   • HTML选项控制生效")
        print("   • 无行数限制")
    else:
        print("❌ HTML报告生成测试失败")
    
    if ui_test:
        print("✅ UI集成测试通过")
        print("   • UI组件选项正确传递")
        print("   • HTML生成参数正确")
    else:
        print("❌ UI集成测试失败")
    
    if html_test and ui_test:
        print(f"\n🎊 所有测试通过！")
        print("现在HTML报告将:")
        print("• 📄 显示完整的着色器代码文件")
        print("• 🎨 根据UI选项控制显示内容")
        print("• 🌈 使用颜色标记不同类型的代码行")
        print("• 📊 为每行显示运算统计信息")
        print("• 🚫 不再有行数限制")
    else:
        print(f"\n❌ 部分测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
