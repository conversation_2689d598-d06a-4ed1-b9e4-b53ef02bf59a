#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试详细HTML报告生成
"""

import sys
import os
import tempfile
from datetime import datetime

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_detailed_html_report():
    """测试详细HTML报告生成"""
    print("🌐 测试详细HTML报告生成")
    print("=" * 50)
    
    try:
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        # 创建处理器
        processor = ShaderAnalysisProcessor()
        
        # 测试着色器代码（包含多行，便于测试每行分析）
        test_shader = """
float4 vertex_main(float3 position : POSITION, float2 uv : TEXCOORD0) : SV_POSITION {
    float4x4 mvp = mul(model_matrix, view_proj_matrix);
    float4 world_pos = mul(float4(position, 1.0), mvp);
    float3 scaled_pos = world_pos.xyz * 2.0;
    return float4(scaled_pos, world_pos.w);
}

float4 pixel_main(float2 uv : TEXCOORD0) : SV_Target {
    float4 base_color = tex2D(main_texture, uv);
    float3 normal = normalize(world_normal);
    float ndotl = dot(normal, light_direction);
    float3 diffuse = base_color.rgb * ndotl;
    return float4(diffuse, base_color.a);
}
"""
        
        print("   正在进行着色器分析...")
        result = processor.analyze_shader(test_shader, save_reports=False)
        
        # 检查分析结果
        if result and 'analysis' in result:
            analysis_data = result['analysis']
            precise_data = analysis_data.get('precise_analysis', {})
            
            if precise_data:
                print("   ✅ 获得精确分析数据")
                
                # 检查代码行数据
                code_lines = precise_data.get('code_lines', [])
                print(f"   📊 代码行数: {len(code_lines)}")
                
                # 检查精确分析数据
                precise_analyses = precise_data.get('precise_analyses', [])
                print(f"   📊 精确分析数: {len(precise_analyses)}")
                
                # 生成HTML报告
                print("\n   生成HTML报告...")
                html_content = processor._generate_html_report(result, test_shader)
                
                if html_content and len(html_content) > 5000:
                    print(f"   ✅ HTML报告生成成功 ({len(html_content)} 字符)")
                    
                    # 检查HTML内容是否包含每行代码分析
                    checks = [
                        ("代码行分析", "line-analysis" in html_content or "code-line" in html_content),
                        ("运算统计", "operation" in html_content.lower()),
                        ("类型转换", "conversion" in html_content.lower() or "转换" in html_content),
                        ("节点详情", "node" in html_content.lower() or "节点" in html_content),
                        ("性能级别", "performance" in html_content.lower() or "性能" in html_content),
                        ("代码内容", "vertex_main" in html_content and "pixel_main" in html_content),
                        ("行号显示", any(f"line-{i}" in html_content or f"第{i}行" in html_content for i in range(1, 6))),
                    ]
                    
                    passed_checks = 0
                    for check_name, check_result in checks:
                        if check_result:
                            print(f"   ✅ {check_name}: 包含")
                            passed_checks += 1
                        else:
                            print(f"   ❌ {check_name}: 缺失")
                    
                    # 保存HTML文件用于检查
                    temp_dir = tempfile.gettempdir()
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    html_filename = f"test_detailed_shader_analysis_{timestamp}.html"
                    html_path = os.path.join(temp_dir, html_filename)
                    
                    with open(html_path, 'w', encoding='utf-8') as f:
                        f.write(html_content)
                    
                    print(f"\n   📁 HTML文件已保存: {html_path}")
                    print(f"   📏 文件大小: {os.path.getsize(html_path)} 字节")
                    
                    # 检查HTML文件中的具体内容
                    print(f"\n   🔍 HTML内容检查:")
                    
                    # 检查是否包含代码行表格
                    if "<table" in html_content and "code" in html_content.lower():
                        print("   ✅ 包含代码表格")
                    else:
                        print("   ❌ 缺少代码表格")
                    
                    # 检查是否包含分析详情
                    if "分析" in html_content and ("运算" in html_content or "operation" in html_content.lower()):
                        print("   ✅ 包含分析详情")
                    else:
                        print("   ❌ 缺少分析详情")
                    
                    # 检查是否包含统计信息
                    overall_stats = precise_data.get('overall_statistics', {})
                    total_nodes = overall_stats.get('total_nodes', 0)
                    if str(total_nodes) in html_content and total_nodes > 0:
                        print(f"   ✅ 包含统计信息 (总节点数: {total_nodes})")
                    else:
                        print("   ❌ 缺少统计信息")
                    
                    return passed_checks >= 5  # 至少通过5个检查
                else:
                    print("   ❌ HTML报告生成失败或内容过短")
                    return False
            else:
                print("   ❌ 没有精确分析数据")
                return False
        else:
            print("   ❌ 分析失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_html_generation():
    """测试UI组件的HTML生成"""
    print("\n🎨 测试UI组件HTML生成")
    print("=" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from ui.shader_analysis_widget import ShaderAnalysisWidget
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        # 创建QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建组件和处理器
        widget = ShaderAnalysisWidget()
        processor = ShaderAnalysisProcessor()
        
        # 测试着色器
        test_shader = """
float3 calculate_lighting(float3 normal, float3 light_dir) {
    float ndotl = dot(normal, light_dir);
    return saturate(ndotl) * float3(1.0, 1.0, 1.0);
}
"""
        
        print("   正在进行分析...")
        result = processor.analyze_shader(test_shader, save_reports=False)
        
        # 设置分析结果到UI组件
        widget.analysis_result = result
        
        # 模拟HTML报告生成（不实际打开浏览器）
        print("   测试HTML报告生成方法...")
        
        # 检查分析结果是否正确设置
        if widget.analysis_result:
            analysis_data = widget.analysis_result.get('analysis', {})
            precise_data = analysis_data.get('precise_analysis', {})
            
            if precise_data:
                print("   ✅ UI组件获得分析数据")
                
                # 测试HTML生成逻辑（模拟open_html_report的核心部分）
                try:
                    shader_content = widget.analysis_result.get('shader_content', '')
                    html_content = processor._generate_html_report(widget.analysis_result, shader_content)
                    
                    if html_content and len(html_content) > 1000:
                        print("   ✅ UI组件HTML生成成功")
                        
                        # 检查是否包含测试代码
                        if "calculate_lighting" in html_content:
                            print("   ✅ HTML包含源代码")
                        else:
                            print("   ❌ HTML缺少源代码")
                            return False
                        
                        return True
                    else:
                        print("   ❌ UI组件HTML生成失败")
                        return False
                except Exception as e:
                    print(f"   ❌ HTML生成异常: {str(e)}")
                    return False
            else:
                print("   ❌ UI组件没有精确分析数据")
                return False
        else:
            print("   ❌ UI组件没有分析结果")
            return False
            
    except Exception as e:
        print(f"   ❌ UI测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 详细HTML报告测试")
    print("=" * 60)
    print("测试内容:")
    print("• 🌐 详细HTML报告生成测试")
    print("• 🎨 UI组件HTML生成测试")
    print("• 📊 每行代码分析检查")
    print("• 📁 HTML文件保存验证")
    print()
    
    # 执行测试
    detailed_ok = test_detailed_html_report()
    ui_ok = test_ui_html_generation()
    
    print(f"\n🎉 测试结果总结")
    print("=" * 60)
    
    if detailed_ok:
        print("✅ 详细HTML报告测试通过")
        print("   • HTML内容生成完整")
        print("   • 包含每行代码分析")
        print("   • 统计信息正确显示")
        print("   • 文件保存成功")
    else:
        print("❌ 详细HTML报告测试失败")
    
    if ui_ok:
        print("✅ UI组件HTML生成测试通过")
        print("   • UI组件集成正常")
        print("   • HTML生成方法正确")
        print("   • 源代码包含完整")
    else:
        print("❌ UI组件HTML生成测试失败")
    
    if detailed_ok and ui_ok:
        print(f"\n🎊 详细HTML报告功能正常！")
        print("现在HTML报告包含:")
        print("• 📊 完整的统计信息")
        print("• 📝 每行代码的详细分析")
        print("• 🎯 运算次数和类型转换")
        print("• 🎨 性能级别和优化建议")
        print("• 📈 可视化的分析结果")
        print("\n启动UI测试实际功能: python ui/main_window.py")
    else:
        print(f"\n❌ 详细HTML报告功能需要进一步检查")

if __name__ == "__main__":
    main()
