#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI修复和新功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PyQt5.QtCore import QTimer

def test_analysis_widget():
    """测试分析组件"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = QMainWindow()
    window.setWindowTitle("着色器分析组件测试")
    window.setGeometry(100, 100, 800, 600)
    
    # 创建中央组件
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 创建分析组件
    from ui.shader_analysis_widget import ShaderAnalysisWidget
    analysis_widget = ShaderAnalysisWidget()
    layout.addWidget(analysis_widget)
    
    # 添加测试按钮
    test_btn = QPushButton("开始测试分析")
    layout.addWidget(test_btn)
    
    # 测试着色器内容
    test_shader = """
#include <metal_stdlib>
using namespace metal;

struct VertexOut {
    float4 position [[position]];
    half2 texCoord;
    half3 normal;
};

fragment half4 main0(VertexOut in [[stage_in]], 
                     texture2d<half> baseTexture [[texture(0)]],
                     sampler baseSampler [[sampler(0)]]) {
    
    half4 baseColor = baseTexture.sample(baseSampler, float2(in.texCoord));
    half3 normalizedNormal = normalize(in.normal);
    
    float lightIntensity = 0.8;
    half3 finalColor = baseColor.rgb * half(lightIntensity);
    
    // 混合精度运算示例
    float3 worldPos = float3(1.0, 2.0, 3.0);
    half3 lightDir = half3(normalize(worldPos));
    
    half dotProduct = dot(normalizedNormal, lightDir);
    half3 diffuse = finalColor * max(dotProduct, half(0.0));
    
    return half4(diffuse, baseColor.a);
}
"""
    
    def start_test():
        print("开始测试分析...")
        analysis_widget.start_analysis(test_shader)
    
    test_btn.clicked.connect(start_test)
    
    # 显示窗口
    window.show()
    
    # 自动开始测试
    QTimer.singleShot(1000, start_test)
    
    print("测试窗口已启动")
    print("功能测试:")
    print("1. 查看指标卡片是否正确显示")
    print("2. 测试性能评分条")
    print("3. 点击'查看详细报告'按钮测试HTML报告")
    print("4. 点击'导出JSON数据'按钮测试JSON导出")
    print("5. 点击'导出CSV数据'按钮测试CSV导出")
    
    sys.exit(app.exec_())

def test_html_generation():
    """测试HTML报告生成"""
    print("测试HTML报告生成...")
    
    try:
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        test_shader = """
#include <metal_stdlib>
using namespace metal;

fragment half4 main0() {
    half3 color = half3(1.0, 0.5, 0.0);
    float intensity = 0.8;
    return half4(color * half(intensity), 1.0);
}
"""
        
        processor = ShaderAnalysisProcessor()
        result = processor.analyze_shader(test_shader, save_reports=False)
        
        # 生成HTML报告
        html_content = processor.generate_html_report(result['analysis'], test_shader)
        
        # 保存测试文件
        with open('test_report.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print("✅ HTML报告生成成功: test_report.html")
        print("请检查HTML文件是否包含逐行代码分析")
        
        return True
        
    except Exception as e:
        print(f"❌ HTML报告生成失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 测试UI修复和新功能")
    print("=" * 50)
    
    # 选择测试模式
    if len(sys.argv) > 1 and sys.argv[1] == 'html':
        # 只测试HTML生成
        test_html_generation()
    else:
        # 测试完整UI
        test_analysis_widget()

if __name__ == "__main__":
    main()
