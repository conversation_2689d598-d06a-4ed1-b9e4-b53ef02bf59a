#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
表达式模拟器 - 将复杂表达式拆分成多个模拟步骤
"""

import ast
from typing import Dict, List, Any, Optional, Tuple
from .temp_variable_manager import TempVariableManager, SimulationStep

class ExpressionSimulator:
    """表达式模拟器"""
    
    def __init__(self, temp_manager: TempVariableManager):
        self.temp_manager = temp_manager
        self.step_counter = 0
    
    def simulate_expression(self, node: ast.AST, line_number: int, context: str = "") -> str:
        """
        模拟表达式执行过程
        返回最终结果变量名
        """
        if isinstance(node, ast.BinOp):
            return self._simulate_binary_op(node, line_number, context)
        elif isinstance(node, ast.Call):
            return self._simulate_function_call(node, line_number, context)
        elif isinstance(node, ast.Name):
            return node.id
        elif isinstance(node, ast.Constant):
            return str(node.value)
        elif isinstance(node, ast.Num):  # Python < 3.8 兼容
            return str(node.n)
        elif isinstance(node, ast.Str):  # Python < 3.8 兼容
            return f'"{node.s}"'
        elif isinstance(node, ast.Attribute):
            return self._simulate_attribute_access(node, line_number, context)
        elif isinstance(node, ast.Subscript):
            return self._simulate_subscript(node, line_number, context)
        elif isinstance(node, ast.UnaryOp):
            return self._simulate_unary_op(node, line_number, context)
        else:
            # 对于其他类型的节点，返回一个临时变量
            temp_var = self.temp_manager.get_next_temp_var()
            step = SimulationStep(
                step_index=self.step_counter,
                operation="unknown",
                target_var=temp_var,
                operands=[],
                expression=f"<{type(node).__name__}>",
                node_type=type(node).__name__,
                line_number=line_number
            )
            self.temp_manager.add_simulation_step(step)
            self.step_counter += 1
            return temp_var
    
    def _simulate_binary_op(self, node: ast.BinOp, line_number: int, context: str) -> str:
        """模拟二元操作"""
        # 递归处理左右操作数
        left_var = self.simulate_expression(node.left, line_number, context)
        right_var = self.simulate_expression(node.right, line_number, context)
        
        # 确定操作类型
        op_map = {
            ast.Add: ('add', '+'),
            ast.Sub: ('subtract', '-'),
            ast.Mult: ('multiply', '*'),
            ast.Div: ('divide', '/'),
            ast.Mod: ('modulo', '%'),
            ast.Pow: ('power', '**'),
            ast.LShift: ('left_shift', '<<'),
            ast.RShift: ('right_shift', '>>'),
            ast.BitOr: ('bitwise_or', '|'),
            ast.BitXor: ('bitwise_xor', '^'),
            ast.BitAnd: ('bitwise_and', '&')
        }
        
        operation, symbol = op_map.get(type(node.op), ('unknown_op', '?'))
        
        # 创建临时变量存储结果
        temp_var = self.temp_manager.get_next_temp_var()
        
        # 创建模拟步骤
        step = SimulationStep(
            step_index=self.step_counter,
            operation=operation,
            target_var=temp_var,
            operands=[left_var, right_var],
            expression=f"{left_var} {symbol} {right_var}",
            node_type="BinOp",
            line_number=line_number
        )
        
        self.temp_manager.add_simulation_step(step)
        self.step_counter += 1
        
        return temp_var
    
    def _simulate_function_call(self, node: ast.Call, line_number: int, context: str) -> str:
        """模拟函数调用"""
        # 获取函数名
        if isinstance(node.func, ast.Name):
            func_name = node.func.id
        elif isinstance(node.func, ast.Attribute):
            func_name = self._get_attribute_name(node.func)
        else:
            func_name = "<function>"
        
        # 处理参数
        arg_vars = []
        for arg in node.args:
            arg_var = self.simulate_expression(arg, line_number, context)
            arg_vars.append(arg_var)
        
        # 创建临时变量存储结果
        temp_var = self.temp_manager.get_next_temp_var()
        
        # 构建表达式
        args_str = ", ".join(arg_vars)
        expression = f"{func_name}({args_str})"
        
        # 创建模拟步骤
        step = SimulationStep(
            step_index=self.step_counter,
            operation="function_call",
            target_var=temp_var,
            operands=arg_vars,
            expression=expression,
            node_type="Call",
            line_number=line_number
        )
        
        self.temp_manager.add_simulation_step(step)
        self.step_counter += 1
        
        return temp_var
    
    def _simulate_attribute_access(self, node: ast.Attribute, line_number: int, context: str) -> str:
        """模拟属性访问"""
        # 处理对象
        obj_var = self.simulate_expression(node.value, line_number, context)
        
        # 创建临时变量
        temp_var = self.temp_manager.get_next_temp_var()
        
        # 创建模拟步骤
        step = SimulationStep(
            step_index=self.step_counter,
            operation="attribute_access",
            target_var=temp_var,
            operands=[obj_var],
            expression=f"{obj_var}.{node.attr}",
            node_type="Attribute",
            line_number=line_number
        )
        
        self.temp_manager.add_simulation_step(step)
        self.step_counter += 1
        
        return temp_var
    
    def _simulate_subscript(self, node: ast.Subscript, line_number: int, context: str) -> str:
        """模拟下标访问"""
        # 处理对象和索引
        obj_var = self.simulate_expression(node.value, line_number, context)
        slice_var = self.simulate_expression(node.slice, line_number, context)
        
        # 创建临时变量
        temp_var = self.temp_manager.get_next_temp_var()
        
        # 创建模拟步骤
        step = SimulationStep(
            step_index=self.step_counter,
            operation="subscript_access",
            target_var=temp_var,
            operands=[obj_var, slice_var],
            expression=f"{obj_var}[{slice_var}]",
            node_type="Subscript",
            line_number=line_number
        )
        
        self.temp_manager.add_simulation_step(step)
        self.step_counter += 1
        
        return temp_var
    
    def _simulate_unary_op(self, node: ast.UnaryOp, line_number: int, context: str) -> str:
        """模拟一元操作"""
        # 处理操作数
        operand_var = self.simulate_expression(node.operand, line_number, context)
        
        # 确定操作类型
        op_map = {
            ast.UAdd: ('unary_plus', '+'),
            ast.USub: ('unary_minus', '-'),
            ast.Not: ('logical_not', '!'),
            ast.Invert: ('bitwise_not', '~')
        }
        
        operation, symbol = op_map.get(type(node.op), ('unknown_unary', '?'))
        
        # 创建临时变量
        temp_var = self.temp_manager.get_next_temp_var()
        
        # 创建模拟步骤
        step = SimulationStep(
            step_index=self.step_counter,
            operation=operation,
            target_var=temp_var,
            operands=[operand_var],
            expression=f"{symbol}{operand_var}",
            node_type="UnaryOp",
            line_number=line_number
        )
        
        self.temp_manager.add_simulation_step(step)
        self.step_counter += 1
        
        return temp_var
    
    def _get_attribute_name(self, node: ast.Attribute) -> str:
        """获取属性的完整名称"""
        if isinstance(node.value, ast.Name):
            return f"{node.value.id}.{node.attr}"
        elif isinstance(node.value, ast.Attribute):
            return f"{self._get_attribute_name(node.value)}.{node.attr}"
        else:
            return f"<obj>.{node.attr}"
    
    def simulate_assignment(self, target: str, value_node: ast.AST, line_number: int) -> List[SimulationStep]:
        """
        模拟赋值语句
        返回所有生成的步骤
        """
        steps_before = len(self.temp_manager.get_all_simulation_steps())
        
        # 模拟右侧表达式
        result_var = self.simulate_expression(value_node, line_number, "assignment")
        
        # 如果结果不是目标变量，添加最终赋值步骤
        if result_var != target:
            final_step = SimulationStep(
                step_index=self.step_counter,
                operation="assign",
                target_var=target,
                operands=[result_var],
                expression=f"{target} = {result_var}",
                node_type="Assign",
                line_number=line_number
            )
            
            self.temp_manager.add_simulation_step(final_step)
            self.step_counter += 1
        
        # 返回这次赋值生成的所有步骤
        all_steps = self.temp_manager.get_all_simulation_steps()
        return all_steps[steps_before:]
