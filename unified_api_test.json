{"analysis": {"precise_analysis": {"code_lines": ["CodeLineInfo(line_number=2, content='float3 worldPos = transform * localPos;', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['worldPos', 'transform', 'localPos'], conversion_details=[])", "CodeLineInfo(line_number=3, content='half4 baseColor = texture.sample(sampler, uv);', variables=4, float_vars=0, half_vars=1, conversions=0, has_conversion=False, all_variables=['baseColor', 'sampler', 'texture.sample', 'uv'], conversion_details=[])", "CodeLineInfo(line_number=4, content='float3 normal = normalize(normalMatrix * localNormal);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['normal', 'normalMatrix', 'localNormal'], conversion_details=[])", "CodeLineInfo(line_number=5, content='float dotNL = dot(normal, lightDir);', variables=3, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['dotNL', 'normal', 'lightDir'], conversion_details=[])", "CodeLineInfo(line_number=6, content='half3 diffuse = baseColor.rgb * half(max(dotNL, 0.0));', variables=3, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['diffuse', 'baseColor.rgb', 'dotNL'], conversion_details=[{'target_type': 'half', 'source_expression': 'max(dotNL, 0.0', 'line_position': 32}])", "CodeLineInfo(line_number=7, content='float3 specular = pow(max(dot(reflect(-lightDir, normal), viewDir), 0.0), shininess);', variables=5, float_vars=1, half_vars=0, conversions=0, has_conversion=False, all_variables=['specular', 'lightDir', 'normal', 'shininess', 'viewDir'], conversion_details=[])", "CodeLineInfo(line_number=8, content='half3 finalColor = diffuse + half3(specular);', variables=3, float_vars=0, half_vars=1, conversions=1, has_conversion=True, all_variables=['finalColor', 'specular', 'diffuse'], conversion_details=[{'target_type': 'half3', 'source_expression': 'specular', 'line_position': 29}])"], "syntax_trees": ["assignment(=:float3)", "assignment(=:half4)", "assignment(=:float3)", "assignment(=:float)", "assignment(=:half3)", "assignment(=:float3)", "assignment(=:half3)"], "precise_analyses": ["<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x0000027A0F60A780>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x0000027A0F810470>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x0000027A0F8102C0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x0000027A0F8106E0>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x0000027A0F810800>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x0000027A0F810A70>", "<Process.Analysis.tree_based_type_analyzer.TypeAnalysisResult object at 0x0000027A0F810F20>"], "global_type_table": {"worldPos": "DataType.FLOAT3", "baseColor": "DataType.HALF4", "normal": "DataType.FLOAT3", "dotNL": "DataType.FLOAT", "diffuse": "DataType.HALF3", "specular": "DataType.FLOAT3", "finalColor": "DataType.HALF3"}, "overall_statistics": {"total_lines": 7, "total_nodes": 44, "total_variables": 7, "total_intermediate_results": 13, "total_type_conversions": 3, "total_precision_issues": 0, "type_distribution": {"float3": 6, "unknown": 20, "half4": 1, "float": 9, "half3": 7, "half": 1}, "precision_accuracy_score": 36.36363636363637}}}, "files": {}, "analysis_method": "precise_tree_based"}