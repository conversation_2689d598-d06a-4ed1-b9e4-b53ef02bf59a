================================================================================
Metal着色器Half/Float运算分析报告
================================================================================

📊 统计概览:
  总运算操作数: 1873
  精度转换操作: 497
  混合精度运算: 60
  高性能影响操作: 91
  纹理采样操作: 31

🔍 运算类型分布:
  arithmetic          :  616 (32.9%)
  assignment          :  452 (24.1%)
  mixed_precision     :   60 (3.2%)
  type_conversion     :  437 (23.3%)
  function_call       :  277 (14.8%)
  texture_sample      :   31 (1.7%)

⚠️  关键运算行分析 (前20个高影响操作):
   1. 行 102: mixed_precision - fragment main0_out main0(main0_in in [[stage_in]], constant ...
      转换: 混合精度运算
   2. 行 112: texture_sample  - sBaseSampler.sample(sBaseSamplerSmplr, in.IN_TexCoord.xy)...
   3. 行 114: mixed_precision - half3 _8949 = half3(float3(_8941 * _8941) * float3(_Block1.c...
      转换: 混合精度运算
   4. 行 115: texture_sample  - sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy ...
   5. 行 117: texture_sample  - sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy ...
   6. 行 119: mixed_precision - float _8991 = fast::clamp(powr(float(max(in.IN_TintColor.w, ...
      转换: 混合精度运算
   7. 行 124: mixed_precision - if ((float((_8994 * _8996) - half(mix(0.0, 0.5, powr(float(m...
      转换: 混合精度运算
   8. 行 128: texture_sample  - sNormalSampler.sample(sNormalSamplerSmplr, in.IN_TexCoord.xy...
   9. 行 140: texture_sample  - sMixSampler.sample(sMixSamplerSmplr, in.IN_TexCoord.xy)...
  10. 行 142: mixed_precision - half _9096 = half(mix(1.0, float(mix(half3(_9019), half3(hal...
      转换: 混合精度运算
  11. 行 145: texture_sample  - sEmissionMapSampler.sample(sEmissionMapSamplerSmplr, in.IN_T...
  12. 行 146: mixed_precision - half3 _9145 = _9130.xyz * half3((float3(_Block1.cEmissionCol...
      转换: 混合精度运算
  13. 行 158: mixed_precision - float _9721 = fast::clamp(dot(_9286, float3(half3(_9176, _91...
      转换: 混合精度运算
  14. 行 159: mixed_precision - float _9734 = (_9721 * _9721) * float(half(fast::clamp((_Blo...
      转换: 混合精度运算
  15. 行 161: mixed_precision - half _9772 = half(mix(float(_9199 - _9074.x), 0.899999976158...
      转换: 混合精度运算
  16. 行 162: mixed_precision - float _9429 = (float(half(_9014)) * _9408) * fast::clamp(1.0...
      转换: 混合精度运算
  17. 行 163: texture_sample  - sCharInteractionSampler.sample(sCharInteractionSamplerSmplr,...
  18. 行 171: mixed_precision - float _9460 = float(half(fast::max(0.0, _Block1.EnvInfo.y)))...
      转换: 混合精度运算
  19. 行 175: mixed_precision - float _9510 = float(half(fast::clamp(((float2(0.800000011920...
      转换: 混合精度运算
  20. 行 176: mixed_precision - float _9519 = float(half(9.9956989288330078125e-05));...
      转换: 混合精度运算

🔄 混合精度运算详情:
   1. 行 102: fragment main0_out main0(main0_in in [[stage_in]], constant _Block1T& ...
   2. 行 114: half3 _8949 = half3(float3(_8941 * _8941) * float3(_Block1.cBaseColor)...
   3. 行 119: float _8991 = fast::clamp(powr(float(max(in.IN_TintColor.w, half(9.995...
   4. 行 124: if ((float((_8994 * _8996) - half(mix(0.0, 0.5, powr(float(max(_9199 -...
   5. 行 142: half _9096 = half(mix(1.0, float(mix(half3(_9019), half3(half(fast::cl...
   6. 行 146: half3 _9145 = _9130.xyz * half3((float3(_Block1.cEmissionColor) * _Blo...
   7. 行 158: float _9721 = fast::clamp(dot(_9286, float3(half3(_9176, _9176, _9199)...
   8. 行 159: float _9734 = (_9721 * _9721) * float(half(fast::clamp((_Block1.cCIMud...
   9. 行 161: half _9772 = half(mix(float(_9199 - _9074.x), 0.8999999761581420898437...
  10. 行 162: float _9429 = (float(half(_9014)) * _9408) * fast::clamp(1.0 + _Block1...

🔀 类型转换热点:
  行1100-1109:  35次转换
  行 930- 939:  17次转换
  行 270- 279:  14次转换
  行 380- 389:  14次转换
  行 480- 489:  14次转换
  行 490- 499:  12次转换
  行 130- 139:  11次转换
  行 180- 189:  10次转换
  行 750- 759:  10次转换
  行 780- 789:  10次转换

💡 性能优化建议:
  ⚠️  发现大量混合精度运算，建议统一使用half或float
  ⚠️  频繁的精度转换可能影响性能，考虑减少不必要的转换
  ⚠️  大量纹理采样操作，考虑优化纹理访问模式

================================================================================