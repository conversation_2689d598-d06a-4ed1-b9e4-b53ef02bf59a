🎯 精确类型分析摘要报告
==================================================
📊 基本统计:
  总代码行: 762
  总节点数: 4901
  变量声明: 474
  中间结果: 1646

🔍 类型分析:
  类型转换: 253
  精度问题: 16
  准确性评分: 27.9%

🎨 类型分布:
  bool: 166 (3.4%)
  float: 817 (16.7%)
  float2: 110 (2.2%)
  float3: 418 (8.5%)
  float3x3: 1 (0.0%)
  float4: 34 (0.7%)
  half: 299 (6.1%)
  half3: 172 (3.5%)
  half4: 63 (1.3%)
  int: 17 (0.3%)
  uint: 46 (0.9%)
  unknown: 2758 (56.3%)

📈 分析改进:
  原始方法识别: 2816 个操作
  精确方法识别: 4901 个节点
  精度提升: +2085 个节点 (74.0%)

💡 性能建议:
  ⚠️  发现 253 个类型转换，建议优化
  ⚠️  发现 16 个混合精度问题，建议统一精度