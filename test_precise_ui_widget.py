#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试精确类型分析UI组件
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'UI'))

def test_precise_ui_widget():
    """测试精确类型分析UI组件"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("🎯 精确类型分析UI测试")
    main_window.setGeometry(100, 100, 1000, 700)
    
    # 设置样式
    main_window.setStyleSheet("""
        QMainWindow {
            background-color: #f8f9fa;
        }
    """)
    
    # 创建中央组件
    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)
    
    layout = QVBoxLayout(central_widget)
    layout.setContentsMargins(20, 20, 20, 20)
    
    # 导入并创建精确类型分析组件
    try:
        from precise_type_analysis_widget import PreciseTypeAnalysisWidget
        
        precise_widget = PreciseTypeAnalysisWidget()
        layout.addWidget(precise_widget)
        
        # 测试着色器代码
        test_shader = """
float3 worldPos = transform * localPos;
half4 baseColor = texture.sample(sampler, uv);
float3 normal = normalize(normalMatrix * localNormal);
float dotNL = dot(normal, lightDir);
half3 diffuse = baseColor.rgb * half(max(dotNL, 0.0));
float3 specular = pow(max(dot(reflect(-lightDir, normal), viewDir), 0.0), shininess);
half3 finalColor = diffuse + half3(specular);
"""
        
        print("🚀 启动精确类型分析UI测试")
        print("=" * 50)
        print("功能测试:")
        print("• 📊 指标卡片显示")
        print("• 🔢 运算统计分析")
        print("• 🎯 精度分析评分")
        print("• 💡 性能优化建议")
        print("• 📄 HTML报告生成")
        print("• 💾 JSON数据导出")
        print()
        
        # 显示窗口
        main_window.show()
        
        # 自动开始分析（延迟启动以确保UI完全加载）
        from PyQt5.QtCore import QTimer
        def start_analysis():
            print("开始精确类型分析...")
            precise_widget.start_precise_analysis(test_shader)
        
        QTimer.singleShot(1000, start_analysis)  # 1秒后开始分析
        
        print("✅ UI组件已启动")
        print("窗口将在1秒后自动开始分析测试着色器代码")
        print("您可以观察分析过程和结果显示")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ 导入UI组件失败: {str(e)}")
        print("请确保PyQt5已正确安装")
        return False
    except Exception as e:
        print(f"❌ 创建UI组件失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_widget_components():
    """测试组件功能"""
    print("🧪 组件功能测试")
    print("=" * 50)
    
    try:
        from precise_type_analysis_widget import PreciseMetricCard, PreciseTypeAnalysisWidget
        
        # 测试指标卡片
        print("1. 测试指标卡片组件...")
        app = QApplication([])
        
        card = PreciseMetricCard("测试指标", "1,234", "#4fc3f7", "测试副标题")
        print("   ✅ 指标卡片创建成功")
        
        # 测试主组件
        print("2. 测试主分析组件...")
        widget = PreciseTypeAnalysisWidget()
        print("   ✅ 主分析组件创建成功")
        
        # 测试方法
        print("3. 测试组件方法...")
        widget.clear_results()
        print("   ✅ 清空结果方法正常")
        
        print("✅ 所有组件功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 组件功能测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🎯 精确类型分析UI组件测试")
    print("=" * 60)
    print("测试内容:")
    print("• 🎨 UI组件创建和布局")
    print("• 📊 指标卡片显示效果")
    print("• 🔄 分析流程和状态更新")
    print("• 📄 报告生成和打开功能")
    print("• 💾 数据导出功能")
    print()
    
    # 先测试组件功能
    component_test = test_widget_components()
    
    if component_test:
        print("\n" + "=" * 60)
        print("🚀 启动UI界面测试...")
        print("=" * 60)
        
        # 检查是否有显示环境
        if 'DISPLAY' not in os.environ and os.name != 'nt':
            print("⚠️  未检测到图形显示环境，跳过UI界面测试")
            print("组件功能测试已通过，UI组件可以正常使用")
            return True
        
        # 启动UI测试
        test_precise_ui_widget()
    else:
        print("\n❌ 组件功能测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    main()
