
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Metal着色器Half/Float运算分析报告</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007acc;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4fc3f7;
        }
        
        .stat-label {
            color: #cccccc;
            margin-top: 5px;
        }
        
        .chart-container {
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .bar-chart {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .bar-item {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .bar-label {
            width: 150px;
            text-align: right;
        }
        
        .bar {
            height: 25px;
            background: linear-gradient(90deg, #4fc3f7, #29b6f6);
            border-radius: 4px;
            display: flex;
            align-items: center;
            padding: 0 10px;
            color: white;
            font-weight: bold;
        }
        
        .suggestions {
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .suggestion-item {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 193, 7, 0.1);
            border-left: 4px solid #ffc107;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Metal着色器Half/Float运算分析报告</h1>
        <p>深度分析着色器中的数据类型转换和运算模式</p>
        <p>生成时间: 2025-08-01 17:36:04</p>
    </div>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">15</div>
            <div class="stat-label">总运算操作</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">2</div>
            <div class="stat-label">精度转换</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">0</div>
            <div class="stat-label">混合精度运算</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">0</div>
            <div class="stat-label">高影响操作</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">0</div>
            <div class="stat-label">纹理采样</div>
        </div>
    </div>
    
    <div class="chart-container">
        <h3>📊 运算类型分布</h3>
        <div class="bar-chart">

            <div class="bar-item">
                <div class="bar-label">arithmetic</div>
                <div class="bar" style="width: 213.33333333333334px;">
                    4 (26.7%)
                </div>
            </div>

            <div class="bar-item">
                <div class="bar-label">assignment</div>
                <div class="bar" style="width: 160.0px;">
                    3 (20.0%)
                </div>
            </div>

            <div class="bar-item">
                <div class="bar-label">variable_analysis</div>
                <div class="bar" style="width: 266.66666666666663px;">
                    5 (33.3%)
                </div>
            </div>

            <div class="bar-item">
                <div class="bar-label">function_call</div>
                <div class="bar" style="width: 53.333333333333336px;">
                    1 (6.7%)
                </div>
            </div>

            <div class="bar-item">
                <div class="bar-label">type_conversion</div>
                <div class="bar" style="width: 106.66666666666667px;">
                    2 (13.3%)
                </div>
            </div>

        </div>
    </div>
    
    <div class="suggestions">
        <h3>💡 性能优化建议</h3>

    </div>

    <div class="chart-container">
        <h3>🔍 逐行代码分析 (显示 7/7 行)</h3>
        <div style="max-height: 600px; overflow-y: auto; background: #1e1e1e; border-radius: 5px; padding: 10px;">

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">1</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;"><span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">2</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;"><span style="color: #ff9800; font-weight: bold;">half</span> <span style="color: #ff9800;">_9270</span> = _9255.z;<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"> [变量: 2 | float: 0 | half: 1 | 转换: 0]</span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">3</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;"><span style="color: #2196f3; font-weight: bold;">float3</span> <span style="color: #2196f3;">_8921</span> = fast::normalize(_Block1.CameraPos.xyz - in.IN_WorldPosition.xyz);<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"> [变量: 3 | float: 1 | half: 0 | 转换: 0]</span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">4</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">_18272 = _9772;<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"> [变量: 2 | float: 0 | half: 0 | 转换: 0]</span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; background-color: rgba(255, 193, 7, 0.2);">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">5</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">_18237 = float4(0.0, 0.0, 0.0, 1.0);<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"> [类型转换] [变量: 1 | float: 0 | half: 0 | 转换: 0]</span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; background-color: rgba(255, 193, 7, 0.2);">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">6</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;"><span style="color: #ff5722; font-weight: bold;">half3</span> <span style="color: #ff5722;">result</span> = color.rgb * <span style="background-color: rgba(255, 193, 7, 0.3); color: #ff9800; font-weight: bold;">half(intensity)</span>;<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"> [类型转换] [变量: 3 | float: 0 | half: 1 | 转换: 1]</span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">7</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;"><span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

        </div>

    </div>

    <div style="text-align: center; margin-top: 30px; color: #858585;">
        <p>报告由ShaderProfile工具自动生成</p>
    </div>
</body>
</html>
