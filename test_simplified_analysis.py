#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的精确类型分析测试 - 只显示运算统计
"""

import sys
import os
import webbrowser
from pathlib import Path

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_shader_operations(shader_content):
    """分析着色器运算统计"""
    print("开始运算统计分析...")
    
    try:
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        processor = ShaderAnalysisProcessor()
        
        # 使用精确类型分析
        result = processor.analyze_shader_with_precise_types(
            shader_content, 
            save_reports=True, 
            base_filename="simplified_analysis"
        )
        
        print("✅ 运算统计分析完成！")
        return result
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        return None

def display_operation_statistics(result):
    """显示运算统计信息"""
    if not result or 'analysis' not in result or 'precise_analysis' not in result['analysis']:
        print("❌ 没有找到精确分析数据")
        return
    
    precise_data = result['analysis']['precise_analysis']
    overall_stats = precise_data['overall_statistics']
    
    print("\n" + "=" * 60)
    print("📊 运算统计结果")
    print("=" * 60)
    
    # 计算总运算次数
    total_operations = 0
    total_conversions = overall_stats['total_type_conversions']
    total_precision_issues = overall_stats['total_precision_issues']
    
    # 统计各种运算类型
    operation_types = {
        'assignment': 0,    # 赋值运算
        'operator': 0,      # 算术运算
        'function': 0,      # 函数调用
    }
    
    if 'code_lines' in precise_data and 'precise_analyses' in precise_data:
        for code_line, analysis in zip(precise_data['code_lines'], precise_data['precise_analyses']):
            line_operations = 0
            
            for typed_node in analysis.typed_nodes:
                node_type = typed_node.node.node_type.value
                if node_type in operation_types:
                    operation_types[node_type] += 1
                    line_operations += 1
            
            total_operations += line_operations
    
    print(f"🔢 总运算次数: {total_operations}")
    print(f"   - 赋值运算: {operation_types['assignment']} 次")
    print(f"   - 算术运算: {operation_types['operator']} 次") 
    print(f"   - 函数调用: {operation_types['function']} 次")
    print()
    print(f"🔄 类型转换次数: {total_conversions}")
    print(f"⚠️  精度问题次数: {total_precision_issues}")
    
    # 计算运算效率
    if total_operations > 0:
        conversion_rate = (total_conversions / total_operations) * 100
        print(f"📈 类型转换率: {conversion_rate:.1f}%")
    
    print(f"\n📋 代码行统计:")
    print(f"   - 总代码行: {overall_stats['total_lines']}")
    print(f"   - 平均每行运算: {total_operations / overall_stats['total_lines']:.1f} 次")

def display_line_by_line_operations(result):
    """显示逐行运算统计"""
    if not result or 'analysis' not in result or 'precise_analysis' not in result['analysis']:
        return
    
    precise_data = result['analysis']['precise_analysis']
    
    if 'code_lines' not in precise_data or 'precise_analyses' not in precise_data:
        return
    
    print(f"\n📝 逐行运算统计 (前20行):")
    print("-" * 60)
    
    for i, (code_line, analysis) in enumerate(zip(precise_data['code_lines'], precise_data['precise_analyses'])):
        if i >= 20:  # 只显示前20行
            break
            
        # 统计运算次数
        operation_count = 0
        conversion_count = len(analysis.type_conversions)
        
        # 计算运算次数
        for typed_node in analysis.typed_nodes:
            node_type = typed_node.node.node_type.value
            if node_type in ['operator', 'function', 'assignment']:
                operation_count += 1
        
        # 显示统计信息
        stats = f"运算: {operation_count}次"
        if conversion_count > 0:
            stats += f" | 类型转换: {conversion_count}次"
        
        # 截断过长的代码行
        code_display = code_line.content
        if len(code_display) > 50:
            code_display = code_display[:47] + "..."
        
        print(f"行{code_line.line_number:3d}: {code_display:<50} [{stats}]")

def main():
    """主函数"""
    print("🚀 简化运算统计分析")
    print("=" * 60)
    
    # 读取metal_shader_ps文件
    shader_file = "metal_shader_ps"
    if not os.path.exists(shader_file):
        print("❌ 未找到metal_shader_ps文件")
        return False
    
    try:
        with open(shader_file, 'r', encoding='utf-8') as f:
            shader_content = f.read()
        print(f"✅ 读取着色器文件成功 ({len(shader_content)} 字符)")
    except Exception as e:
        print(f"❌ 读取文件失败: {str(e)}")
        return False
    
    # 执行分析
    result = analyze_shader_operations(shader_content)
    if not result:
        return False
    
    # 显示统计结果
    display_operation_statistics(result)
    display_line_by_line_operations(result)
    
    # 打开简化的HTML报告
    if 'files' in result and 'html' in result['files']:
        html_file = result['files']['html']
        if os.path.exists(html_file):
            abs_path = os.path.abspath(html_file)
            file_url = f'file:///{abs_path.replace(os.sep, "/")}'
            print(f"\n🌐 打开简化HTML报告: {abs_path}")
            webbrowser.open(file_url)
    
    print(f"\n🎉 简化分析完成！")
    return True

if __name__ == "__main__":
    main()
