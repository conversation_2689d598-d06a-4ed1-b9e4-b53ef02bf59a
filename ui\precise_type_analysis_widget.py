#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确类型分析UI组件
"""

import os
import webbrowser
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QTextEdit, QProgressBar, QFrame, QGridLayout,
                             QCheckBox, QSpinBox, QPushButton, QScrollArea)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont

class PreciseAnalysisWorker(QThread):
    """精确类型分析工作线程"""
    analysis_completed = pyqtSignal(dict)
    analysis_failed = pyqtSignal(str)

    def __init__(self, shader_content: str):
        super().__init__()
        self.shader_content = shader_content

    def run(self):
        try:
            from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
            processor = ShaderAnalysisProcessor()
            result = processor.analyze_shader(
                self.shader_content,
                save_reports=True
            )
            self.analysis_completed.emit(result)
        except Exception as e:
            self.analysis_failed.emit(str(e))

class PreciseMetricCard(QFrame):
    """精确分析指标卡片组件"""

    def __init__(self, title: str, value: str, color: str = "#4fc3f7", subtitle: str = ""):
        super().__init__()
        self.setFrameStyle(QFrame.StyledPanel)
        self.setFixedSize(120, 70)  # 稍大一些以容纳更多信息
        self.setStyleSheet(f"""
            PreciseMetricCard {{
                background-color: transparent;
                border: 1px solid #cccccc;
                border-radius: 6px;
                padding: 3px;
            }}
        """)

        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignCenter)
        layout.setContentsMargins(3, 3, 3, 3)
        layout.setSpacing(1)

        # 数值标签
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"""
            QLabel {{
                font-size: 16px;
                font-weight: bold;
                color: {color};
                margin: 0px;
            }}
        """)

        # 标题标签
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 9px;
                color: #333333;
                margin: 0px;
            }
        """)

        layout.addWidget(value_label)
        layout.addWidget(title_label)
        
        # 副标题（如果有）
        if subtitle:
            subtitle_label = QLabel(subtitle)
            subtitle_label.setAlignment(Qt.AlignCenter)
            subtitle_label.setStyleSheet("""
                QLabel {
                    font-size: 8px;
                    color: #666666;
                    margin: 0px;
                }
            """)
            layout.addWidget(subtitle_label)

class PreciseTypeAnalysisWidget(QWidget):
    """精确类型分析结果显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.analysis_result = None
        self.report_files = {}
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题
        title_label = QLabel("🎯 精确类型分析 - 基于语法树的运算过程模拟")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #333333;
                background: transparent;
                padding: 8px 15px;
                border-radius: 6px;
                margin-bottom: 8px;
            }
        """)
        layout.addWidget(title_label)
        
        # 指标卡片区域
        self.metrics_layout = QGridLayout()
        self.metrics_layout.setSpacing(8)
        self.metrics_layout.setContentsMargins(0, 0, 0, 0)
        self.metrics_frame = QFrame()
        self.metrics_frame.setLayout(self.metrics_layout)
        self.metrics_frame.setStyleSheet("""
            QFrame {
                background-color: transparent;
                margin: 2px;
            }
        """)
        layout.addWidget(self.metrics_frame)
        
        # 运算统计区域
        operation_stats_label = QLabel("🔢 运算统计")
        operation_stats_label.setStyleSheet("font-size: 12px; font-weight: bold; color: #333333; margin-top: 8px; margin-bottom: 3px;")
        layout.addWidget(operation_stats_label)
        
        self.operation_stats_text = QTextEdit()
        self.operation_stats_text.setMaximumHeight(100)
        self.operation_stats_text.setStyleSheet("""
            QTextEdit {
                background-color: transparent;
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 6px;
                color: #333333;
                font-family: 'Consolas', monospace;
                font-size: 11px;
            }
        """)
        layout.addWidget(self.operation_stats_text)
        
        # 精度分析区域
        precision_label = QLabel("🎯 精度分析")
        precision_label.setStyleSheet("font-size: 12px; font-weight: bold; color: #333333; margin-top: 8px; margin-bottom: 3px;")
        layout.addWidget(precision_label)

        self.precision_frame = QFrame()
        self.precision_frame.setStyleSheet("""
            QFrame {
                background-color: transparent;
                border: 1px solid #cccccc;
                border-radius: 6px;
                padding: 8px;
            }
        """)
        precision_layout = QVBoxLayout(self.precision_frame)
        precision_layout.setContentsMargins(8, 8, 8, 8)
        precision_layout.setSpacing(5)

        # 准确性评分
        accuracy_title = QLabel("类型推断准确性")
        accuracy_title.setStyleSheet("font-size: 12px; font-weight: bold; color: #333333; margin-bottom: 5px;")
        precision_layout.addWidget(accuracy_title)
        
        self.accuracy_bar = QProgressBar()
        self.accuracy_bar.setRange(0, 100)
        self.accuracy_bar.setFixedHeight(20)
        self.accuracy_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #cccccc;
                border-radius: 4px;
                text-align: center;
                font-weight: bold;
                font-size: 11px;
                color: #333333;
                background-color: transparent;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ff4444, stop:0.5 #ffaa00, stop:1 #00ff00);
                border-radius: 3px;
            }
        """)
        precision_layout.addWidget(self.accuracy_bar)

        self.accuracy_label = QLabel("等待分析...")
        self.accuracy_label.setAlignment(Qt.AlignCenter)
        self.accuracy_label.setStyleSheet("color: #333333; margin-top: 3px; font-size: 11px;")
        precision_layout.addWidget(self.accuracy_label)
        
        layout.addWidget(self.precision_frame)
        
        # 性能建议区域
        suggestions_label = QLabel("💡 性能优化建议")
        suggestions_label.setStyleSheet("font-size: 12px; font-weight: bold; color: #333333; margin-top: 8px; margin-bottom: 3px;")
        layout.addWidget(suggestions_label)

        self.suggestions_text = QTextEdit()
        self.suggestions_text.setMaximumHeight(80)
        self.suggestions_text.setStyleSheet("""
            QTextEdit {
                background-color: transparent;
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 6px;
                color: #333333;
                font-family: 'Consolas', monospace;
                font-size: 11px;
            }
        """)
        layout.addWidget(self.suggestions_text)
        
        # HTML报告说明
        html_info_label = QLabel("💡 HTML报告将包含完整代码和交互式显示选项")
        html_info_label.setStyleSheet("color: #666666; font-size: 11px; padding: 5px;")
        html_info_label.setWordWrap(True)
        layout.addWidget(html_info_label)
        
        # 按钮区域
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(8)
        buttons_layout.setContentsMargins(0, 8, 0, 0)

        self.open_html_btn = QPushButton("📄 打开HTML报告")
        self.open_html_btn.setEnabled(False)
        self.open_html_btn.clicked.connect(self.open_html_report)
        self.open_html_btn.setStyleSheet("""
            QPushButton {
                background-color: #4fc3f7;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 11px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #29b6f6;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        self.export_json_btn = QPushButton("💾 导出JSON数据")
        self.export_json_btn.setEnabled(False)
        self.export_json_btn.clicked.connect(self.export_json_data)
        self.export_json_btn.setStyleSheet("""
            QPushButton {
                background-color: #66bb6a;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 11px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4caf50;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        buttons_layout.addWidget(self.open_html_btn)
        buttons_layout.addWidget(self.export_json_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)
        
        # 状态标签
        self.status_label = QLabel("准备就绪")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 11px;
                padding: 5px;
                background-color: transparent;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.status_label)

    def start_analysis(self, shader_content: str):
        """开始精确类型分析"""
        self.status_label.setText("正在进行精确类型分析...")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #ff9800;
                font-size: 11px;
                padding: 5px;
                background-color: transparent;
                border-radius: 3px;
            }
        """)

        # 清空之前的结果
        self.clear_results()

        # 启动分析线程
        self.analysis_worker = PreciseAnalysisWorker(shader_content)
        self.analysis_worker.analysis_completed.connect(self.on_analysis_completed)
        self.analysis_worker.analysis_failed.connect(self.on_analysis_failed)
        self.analysis_worker.start()

    def clear_results(self):
        """清空分析结果"""
        # 清空指标卡片
        for i in reversed(range(self.metrics_layout.count())):
            child = self.metrics_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # 重置文本区域
        self.operation_stats_text.clear()
        self.suggestions_text.clear()

        # 重置进度条
        self.accuracy_bar.setValue(0)
        self.accuracy_label.setText("等待分析...")

        # 禁用按钮
        self.open_html_btn.setEnabled(False)
        self.export_json_btn.setEnabled(False)

    def on_analysis_completed(self, result):
        """分析完成处理"""
        self.analysis_result = result
        self.report_files = result.get('files', {})

        # 更新状态
        self.status_label.setText("精确类型分析完成")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #4caf50;
                font-size: 11px;
                padding: 5px;
                background-color: transparent;
                border-radius: 3px;
            }
        """)

        # 更新UI显示
        self.update_analysis_display()

        # 启用按钮
        self.open_html_btn.setEnabled(True)
        self.export_json_btn.setEnabled(True)

    def on_analysis_failed(self, error_message):
        """分析失败处理"""
        self.status_label.setText(f"分析失败: {error_message}")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #f44336;
                font-size: 11px;
                padding: 5px;
                background-color: transparent;
                border-radius: 3px;
            }
        """)

        self.suggestions_text.setText(f"分析过程中出现错误:\n{error_message}")

    def update_analysis_display(self):
        """更新分析结果显示"""
        if not self.analysis_result:
            return

        # 获取精确分析数据
        analysis_data = self.analysis_result.get('analysis', {})
        precise_data = analysis_data.get('precise_analysis', {})

        if not precise_data:
            self.suggestions_text.setText("未找到精确分析数据")
            return

        overall_stats = precise_data.get('overall_statistics', {})

        # 更新指标卡片
        self.update_metric_cards(overall_stats)

        # 更新运算统计
        self.update_operation_stats(overall_stats, precise_data)

        # 更新精度分析
        self.update_precision_analysis(overall_stats)

        # 更新优化建议
        self.update_suggestions(overall_stats, analysis_data)

    def update_metric_cards(self, stats):
        """更新指标卡片"""
        metrics = [
            ("总节点数", f"{stats.get('total_nodes', 0):,}", "#4fc3f7", "语法树节点"),
            ("运算次数", f"{stats.get('total_nodes', 0) - stats.get('total_variables', 0):,}", "#ff9800", "实际运算"),
            ("中间结果", f"{stats.get('total_intermediate_results', 0):,}", "#9c27b0", "临时计算"),
            ("类型转换", f"{stats.get('total_type_conversions', 0):,}", "#f44336", "性能影响"),
            ("变量声明", f"{stats.get('total_variables', 0):,}", "#4caf50", "变量定义"),
            ("精度问题", f"{stats.get('total_precision_issues', 0):,}", "#ff5722", "混合精度"),
        ]

        for i, (title, value, color, subtitle) in enumerate(metrics):
            card = PreciseMetricCard(title, value, color, subtitle)
            row = i // 3
            col = i % 3
            self.metrics_layout.addWidget(card, row, col)

    def update_operation_stats(self, stats, precise_data):
        """更新运算统计"""
        total_nodes = stats.get('total_nodes', 0)
        total_variables = stats.get('total_variables', 0)
        total_operations = total_nodes - total_variables
        total_lines = stats.get('total_lines', 0)

        stats_text = f"📊 运算密度分析:\n"
        stats_text += f"• 总代码行: {total_lines:,} 行\n"
        stats_text += f"• 有效运算: {total_operations:,} 次\n"
        stats_text += f"• 平均每行: {total_operations/total_lines:.2f} 次运算\n"

        # 类型分布
        type_dist = stats.get('type_distribution', {})
        if type_dist:
            stats_text += f"\n🎨 类型分布 (前5种):\n"
            sorted_types = sorted(type_dist.items(), key=lambda x: x[1], reverse=True)[:5]
            for type_name, count in sorted_types:
                percentage = (count / total_nodes) * 100
                stats_text += f"• {type_name}: {count:,} ({percentage:.1f}%)\n"

        self.operation_stats_text.setText(stats_text)

    def update_precision_analysis(self, stats):
        """更新精度分析"""
        accuracy_score = stats.get('precision_accuracy_score', 0)
        self.accuracy_bar.setValue(int(accuracy_score))

        if accuracy_score >= 80:
            level = "优秀"
            color = "#4caf50"
        elif accuracy_score >= 60:
            level = "良好"
            color = "#ff9800"
        else:
            level = "需要改进"
            color = "#f44336"

        self.accuracy_label.setText(f"{accuracy_score:.1f}% - {level}")
        self.accuracy_label.setStyleSheet(f"color: {color}; margin-top: 3px; font-size: 11px;")

    def update_suggestions(self, stats, analysis_data):
        """更新优化建议"""
        suggestions = []

        total_conversions = stats.get('total_type_conversions', 0)
        total_precision_issues = stats.get('total_precision_issues', 0)
        accuracy_score = stats.get('precision_accuracy_score', 0)

        if total_conversions > 100:
            suggestions.append(f"⚠️ 发现 {total_conversions} 个类型转换，建议统一变量类型")

        if total_precision_issues > 0:
            suggestions.append(f"❌ 发现 {total_precision_issues} 个混合精度问题，影响性能")

        if accuracy_score < 60:
            suggestions.append("💡 类型推断准确性较低，建议明确变量类型声明")

        # 对比分析建议
        comparison = analysis_data.get('comparison', {})
        if comparison:
            improvement = comparison.get('operation_count_improvement', {})
            if improvement and improvement.get('improvement_percentage', 0) > 20:
                suggestions.append(f"📈 精确分析识别了额外 {improvement.get('improvement', 0)} 个运算节点")

        if not suggestions:
            suggestions.append("✅ 代码类型使用良好，无明显性能问题")

        self.suggestions_text.setText("\n".join(suggestions))

    def open_html_report(self):
        """打开HTML报告"""
        if 'html' in self.report_files:
            html_file = self.report_files['html']
            if os.path.exists(html_file):
                webbrowser.open(f'file:///{os.path.abspath(html_file).replace(os.sep, "/")}')
            else:
                self.status_label.setText("HTML报告文件不存在")
        else:
            self.status_label.setText("未找到HTML报告")

    def export_json_data(self):
        """导出JSON数据"""
        if 'json' in self.report_files:
            json_file = self.report_files['json']
            if os.path.exists(json_file):
                self.status_label.setText(f"JSON数据已保存: {json_file}")
            else:
                self.status_label.setText("JSON文件不存在")
        else:
            self.status_label.setText("未找到JSON数据")
