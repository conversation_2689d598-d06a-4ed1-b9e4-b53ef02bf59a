#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复HTML模板中的大括号问题
"""

import os
import re

def fix_template_braces():
    """修复模板中的大括号"""
    template_file = "Process/Analysis/templates/html_template.py"
    
    if not os.path.exists(template_file):
        print(f"❌ 模板文件不存在: {template_file}")
        return False
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📁 读取模板文件: {template_file}")
        print(f"   原始长度: {len(content):,} 字符")
        
        # 计算需要修复的大括号数量
        single_braces = content.count('{') + content.count('}')
        format_placeholders = len(re.findall(r'\{[^{}]*\}', content))
        
        print(f"   单个大括号数量: {single_braces}")
        print(f"   格式化占位符数量: {format_placeholders}")
        
        # 保护格式化占位符
        placeholders = re.findall(r'\{[^{}]*\}', content)
        placeholder_map = {}
        
        for i, placeholder in enumerate(placeholders):
            temp_key = f"__PLACEHOLDER_{i}__"
            placeholder_map[temp_key] = placeholder
            content = content.replace(placeholder, temp_key, 1)
        
        print(f"   保护了 {len(placeholder_map)} 个格式化占位符")
        
        # 转义所有剩余的大括号
        content = content.replace('{', '{{').replace('}', '}}')
        
        # 恢复格式化占位符
        for temp_key, original in placeholder_map.items():
            content = content.replace(temp_key, original)
        
        # 写回文件
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 模板修复完成")
        print(f"   修复后长度: {len(content):,} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        return False

def test_template_format():
    """测试模板格式化"""
    print("\n🔍 测试模板格式化")
    print("=" * 50)
    
    try:
        from Process.Analysis.templates.html_template import HTML_TEMPLATE_HEADER, HTML_TEMPLATE_FOOTER
        
        # 测试格式化
        test_data = {
            'total_lines': 100,
            'analysis_time': '2024-01-01 12:00:00',
            'total_nodes': 500,
            'analyzed_lines': 80,
            'total_operations': 200,
            'total_conversions': 10,
            'total_precision_issues': 5,
            'precision_score': 85.5
        }
        
        formatted_header = HTML_TEMPLATE_HEADER.format(**test_data)
        print(f"✅ HTML_TEMPLATE_HEADER 格式化成功: {len(formatted_header):,} 字符")
        
        # 检查是否包含CSS样式
        if 'font-family' in formatted_header:
            print("✅ CSS样式正常")
        else:
            print("❌ CSS样式缺失")
        
        # 检查是否包含数据
        if '100' in formatted_header and '85.5%' in formatted_header:
            print("✅ 数据替换正常")
        else:
            print("❌ 数据替换失败")
        
        print(f"✅ HTML_TEMPLATE_FOOTER 长度: {len(HTML_TEMPLATE_FOOTER):,} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 模板格式化测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 修复HTML模板大括号问题")
    print("=" * 80)
    
    # 修复模板
    fix_ok = fix_template_braces()
    
    if fix_ok:
        # 测试修复结果
        test_ok = test_template_format()
        
        if test_ok:
            print(f"\n🎊 模板修复成功！")
            print("现在可以正常使用HTML模板了")
        else:
            print(f"\n❌ 模板修复后测试失败")
    else:
        print(f"\n❌ 模板修复失败")

if __name__ == "__main__":
    main()
