#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终功能验证脚本
"""

import sys
import os
import tempfile
import json
import csv
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_core_functionality():
    """测试核心功能"""
    print("🔍 测试核心分析功能...")
    
    try:
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        # 测试着色器内容
        test_shader = """
#include <metal_stdlib>
using namespace metal;

fragment half4 main0(float2 uv [[stage_in]], 
                     texture2d<half> baseTexture [[texture(0)]],
                     sampler baseSampler [[sampler(0)]]) {
    
    half4 baseColor = baseTexture.sample(baseSampler, uv);
    float intensity = 0.8;
    half3 finalColor = baseColor.rgb * half(intensity);
    
    return half4(finalColor, baseColor.a);
}
"""
        
        processor = ShaderAnalysisProcessor()
        result = processor.analyze_shader(test_shader, save_reports=False)
        
        # 验证结果结构
        assert 'analysis' in result
        assert 'summary' in result
        assert 'shader_content' in result
        
        stats = result['analysis']['statistics']
        print(f"✅ 分析完成 - 发现 {stats['total_operations']} 个操作")
        print(f"   - 精度转换: {stats['precision_conversions']}")
        print(f"   - 混合精度运算: {stats['mixed_precision_ops']}")
        print(f"   - 纹理采样: {stats['texture_samples']}")
        
        return result
        
    except Exception as e:
        print(f"❌ 核心功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_html_report_with_code_analysis(result):
    """测试HTML报告包含逐行代码分析"""
    print("\n📊 测试HTML报告生成...")
    
    try:
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        processor = ShaderAnalysisProcessor()
        shader_content = result['shader_content']
        html_content = processor.generate_html_report(result['analysis'], shader_content)
        
        # 检查HTML内容
        assert '逐行代码分析' in html_content
        assert '#include <metal_stdlib>' in html_content
        assert 'using namespace metal' in html_content
        
        # 保存测试文件
        with open('verification_report.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print("✅ HTML报告生成成功，包含逐行代码分析")
        print("   文件保存为: verification_report.html")
        
        return True
        
    except Exception as e:
        print(f"❌ HTML报告测试失败: {str(e)}")
        return False

def test_json_export(result):
    """测试JSON导出功能"""
    print("\n📄 测试JSON导出...")
    
    try:
        # 模拟导出JSON
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8')
        json.dump(result['analysis'], temp_file, ensure_ascii=False, indent=2)
        temp_file.close()

        # 验证文件
        with open(temp_file.name, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        assert 'statistics' in data
        assert 'operations' in data
        
        print(f"✅ JSON导出成功: {temp_file.name}")
        
        # 清理
        os.unlink(temp_file.name)
        
        return True
        
    except Exception as e:
        print(f"❌ JSON导出测试失败: {str(e)}")
        return False

def test_csv_export(result):
    """测试CSV导出功能"""
    print("\n📊 测试CSV导出...")
    
    try:
        # 模拟导出CSV
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, newline='', encoding='utf-8')

        operations = result['analysis']['operations']
        fieldnames = ['line', 'type', 'performance_impact', 'precision_conversion', 'output_type', 'detail']
        writer = csv.DictWriter(temp_file, fieldnames=fieldnames)
        
        writer.writeheader()
        for op in operations:
            writer.writerow({
                'line': op['line'],
                'type': op['type'],
                'performance_impact': op['performance_impact'],
                'precision_conversion': op['precision_conversion'] or '',
                'output_type': op['output_type'] or '',
                'detail': op['detail'][:100] + '...' if len(op['detail']) > 100 else op['detail']
            })
        
        temp_file.close()
        
        # 验证文件
        with open(temp_file.name, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
        
        assert len(rows) > 0
        assert 'line' in rows[0]
        assert 'type' in rows[0]
        
        print(f"✅ CSV导出成功: {temp_file.name}")
        print(f"   导出了 {len(rows)} 行数据")
        
        # 清理
        os.unlink(temp_file.name)
        
        return True
        
    except Exception as e:
        print(f"❌ CSV导出测试失败: {str(e)}")
        return False

def test_ui_components():
    """测试UI组件创建"""
    print("\n🎨 测试UI组件...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from ui.shader_analysis_widget import ShaderAnalysisWidget, MetricCard
        
        # 创建应用程序（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 测试MetricCard
        card = MetricCard("测试指标", "123", "#4fc3f7")
        assert card.width() == 120
        assert card.height() == 80
        
        # 测试ShaderAnalysisWidget
        widget = ShaderAnalysisWidget()
        assert widget.analysis_result is None
        
        print("✅ UI组件创建成功")
        print("   - MetricCard: 固定尺寸 120x80")
        print("   - ShaderAnalysisWidget: 初始化正常")
        
        return True
        
    except Exception as e:
        print(f"❌ UI组件测试失败: {str(e)}")
        return False

def main():
    """主验证函数"""
    print("🔧 着色器分析功能最终验证")
    print("=" * 60)
    
    # 测试核心功能
    result = test_core_functionality()
    if not result:
        print("\n❌ 核心功能测试失败，停止验证")
        return False
    
    # 测试各项功能
    tests = [
        ("HTML报告生成", lambda: test_html_report_with_code_analysis(result)),
        ("JSON导出功能", lambda: test_json_export(result)),
        ("CSV导出功能", lambda: test_csv_export(result)),
        ("UI组件创建", test_ui_components)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 验证结果汇总:")
    
    all_passed = True
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name:20}: {status}")
        if not success:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有功能验证通过!")
        print("\n📖 修复内容:")
        print("  ✅ 修复了UI显示问题（指标卡片固定尺寸）")
        print("  ✅ 添加了CSV导出功能")
        print("  ✅ JSON和CSV导出都支持文件选择对话框")
        print("  ✅ HTML报告包含逐行代码分析")
        print("  ✅ 优化了按钮布局和样式")
        
        print("\n🚀 使用方法:")
        print("  1. 在主程序中加载着色器文件")
        print("  2. 使用 Ctrl+Alt+A 或菜单执行分析")
        print("  3. 在分析结果窗口中:")
        print("     - 点击'查看详细报告'查看包含逐行分析的HTML报告")
        print("     - 点击'导出JSON数据'选择路径导出JSON文件")
        print("     - 点击'导出CSV数据'选择路径导出CSV文件")
    else:
        print("\n⚠️ 部分功能验证失败，请检查错误信息")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
