#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试语法树构建器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_syntax_tree():
    """测试基本语法树构建"""
    print("🌳 基本语法树构建测试")
    print("=" * 60)
    
    from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer
    
    # 测试用例1：基本赋值和运算
    test_shader_1 = """
float a = b + c;
half d = half(a);
float3 pos = normalize(worldPos);
"""
    
    analyzer = SyntaxTreeAnalyzer()
    result = analyzer.analyze_shader_with_syntax_trees(test_shader_1)
    
    print("测试用例1：基本赋值和运算")
    print("-" * 30)
    print(analyzer.format_analysis_result(result))
    
    return result

def test_complex_expressions():
    """测试复杂表达式"""
    print("\n" + "=" * 60)
    print("🔍 复杂表达式测试")
    print("=" * 60)
    
    from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer
    
    # 测试用例2：复杂表达式
    test_shader_2 = """
float3 _12378 = _12345.xyz;
float _12380 = _12378.x;
float _19350 = _12380 * 2.0;
half _inner2 = half(_inner1);
float result = max(a, b);
half4 color = texture.sample(sampler, uv);
"""
    
    analyzer = SyntaxTreeAnalyzer()
    result = analyzer.analyze_shader_with_syntax_trees(test_shader_2)
    
    print("测试用例2：复杂表达式")
    print("-" * 30)
    print(analyzer.format_analysis_result(result))
    
    return result

def test_type_inference():
    """测试类型推断"""
    print("\n" + "=" * 60)
    print("🎯 类型推断测试")
    print("=" * 60)
    
    from Process.Analysis.syntax_tree_builder import SyntaxTreeBuilder
    
    builder = SyntaxTreeBuilder()
    
    # 测试类型推断
    test_cases = [
        ("float a = 1.0", "变量声明"),
        ("b = a + c", "变量赋值"),
        ("half d = half(a)", "类型转换"),
        ("float3 pos = normalize(worldPos)", "函数调用"),
        ("result = obj.member", "成员访问"),
        ("value = max(a, b)", "函数调用with参数"),
    ]
    
    print("类型推断测试:")
    print("-" * 30)
    
    for i, (code, description) in enumerate(test_cases, 1):
        print(f"\n测试{i}: {description}")
        print(f"代码: {code}")
        
        try:
            tree = builder.parse_statement(code, i)
            print(f"语法树:")
            print(builder.print_tree(tree))
            
            # 分析类型信息
            analysis = builder.analyze_tree_types(tree)
            if analysis['type_conversions']:
                print(f"类型转换: {analysis['type_conversions']}")
            if analysis['precision_issues']:
                print(f"精度问题: {analysis['precision_issues']}")
                
        except Exception as e:
            print(f"解析错误: {str(e)}")

def test_type_table():
    """测试类型表功能"""
    print("\n" + "=" * 60)
    print("📋 类型表测试")
    print("=" * 60)
    
    from Process.Analysis.syntax_tree_builder import TypeTable, DataType
    
    type_table = TypeTable()
    
    # 测试变量声明和查询
    print("变量声明和查询测试:")
    print("-" * 30)
    
    # 声明一些变量
    type_table.declare_variable("pos", DataType.FLOAT3)
    type_table.declare_variable("color", DataType.HALF4)
    type_table.declare_variable("intensity", DataType.FLOAT)
    
    # 查询变量类型
    variables = ["pos", "color", "intensity", "unknown_var"]
    for var in variables:
        var_type = type_table.get_variable_type(var)
        print(f"  {var}: {var_type.value}")
    
    # 测试函数信息
    print(f"\n内置函数测试:")
    print("-" * 30)
    
    functions = ["normalize", "dot", "max", "sample", "unknown_func"]
    for func in functions:
        func_info = type_table.get_function_info(func)
        print(f"  {func}: 返回类型={func_info['return_type'].value}, 参数={func_info['params']}")

def test_mixed_precision_detection():
    """测试混合精度检测"""
    print("\n" + "=" * 60)
    print("⚠️  混合精度检测测试")
    print("=" * 60)
    
    from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer
    
    # 包含混合精度问题的着色器
    test_shader = """
float a = 1.0;
half b = 0.5;
float result1 = a + b;
half result2 = a * b;
float3 pos = float3(1.0, 2.0, 3.0);
half3 color = half3(0.5, 0.5, 0.5);
float3 mixed = pos + color;
"""
    
    analyzer = SyntaxTreeAnalyzer()
    result = analyzer.analyze_shader_with_syntax_trees(test_shader)
    
    print("混合精度检测结果:")
    print("-" * 30)
    print(analyzer.format_analysis_result(result))
    
    # 详细显示精度问题
    precision_issues = result['type_summary']['precision_issue_details']
    if precision_issues:
        print(f"\n发现 {len(precision_issues)} 个混合精度问题:")
        for issue in precision_issues:
            print(f"  行{issue['line']}: {issue['left_type']} {issue['operation']} {issue['right_type']}")
    else:
        print("未发现混合精度问题")

def test_integration_example():
    """集成示例测试"""
    print("\n" + "=" * 60)
    print("🔗 集成示例测试")
    print("=" * 60)
    
    from Process.Analysis.syntax_tree_builder import SyntaxTreeAnalyzer
    
    # 完整的着色器示例
    complete_shader = """
float3 _12378 = _12345.xyz;
float _12380 = _12378.x;
float _19350 = _12380 * 2.0;
if (_12381 > 0.5)
{
    float _inner1 = _19350 + 1.0;
    half _inner2 = half(_inner1);
    _result = _inner2 * 0.5;
}
else
{
    _result = 0.0;
}
half4 finalColor = texture.sample(sampler, uv);
float3 lighting = normalize(lightDir);
half3 diffuse = finalColor.rgb * half(dot(normal, lighting));
"""
    
    analyzer = SyntaxTreeAnalyzer()
    result = analyzer.analyze_shader_with_syntax_trees(complete_shader)
    
    print("完整着色器分析结果:")
    print("-" * 30)
    print(analyzer.format_analysis_result(result))
    
    # 生成性能建议
    type_summary = result['type_summary']
    print(f"\n🎯 性能分析建议:")
    print("-" * 30)
    
    if type_summary['type_conversions'] > 0:
        print(f"⚠️  发现 {type_summary['type_conversions']} 个类型转换，可能影响性能")
    
    if type_summary['precision_issues'] > 0:
        print(f"⚠️  发现 {type_summary['precision_issues']} 个混合精度问题，建议统一精度类型")
    
    float_vars = sum(1 for t in type_summary['declared_variables'].values() 
                    if t.value.startswith('float'))
    half_vars = sum(1 for t in type_summary['declared_variables'].values() 
                   if t.value.startswith('half'))
    
    print(f"📊 变量类型分布: Float类型 {float_vars} 个, Half类型 {half_vars} 个")
    
    if float_vars > half_vars * 2:
        print("💡 建议: 考虑使用更多half类型变量以提高性能")
    elif half_vars > float_vars * 2:
        print("💡 建议: 当前使用了较多half类型，性能较好")
    else:
        print("💡 建议: Float和Half类型使用较为均衡")

def main():
    """主函数"""
    print("🚀 语法树构建器测试套件")
    print("功能特点:")
    print("• 🌳 构建完整的抽象语法树")
    print("• 📋 维护全局变量类型表")
    print("• 🔍 智能类型推断和检测")
    print("• ⚠️  混合精度问题检测")
    print("• 🎯 性能分析和建议")
    print("• 🔗 与代码行分析器无缝集成")
    print()
    
    try:
        test_basic_syntax_tree()
        test_complex_expressions()
        test_type_inference()
        test_type_table()
        test_mixed_precision_detection()
        test_integration_example()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成！")
        print("语法树构建器已准备就绪，可以集成到着色器分析系统中。")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
