<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 精确类型分析报告</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #4fc3f7;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        .code-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .code-line {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            min-height: 24px;
            align-items: flex-start;
        }
        .line-number {
            background-color: #f8f9fa;
            color: #666;
            padding: 4px 12px;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            min-width: 60px;
            text-align: right;
            border-right: 1px solid #dee2e6;
        }
        .line-content {
            flex: 1;
            padding: 4px 12px;
            font-family: 'Consolas', monospace;
            font-size: 13px;
            white-space: pre;
        }
        .node-info {
            font-size: 11px;
            color: #666;
            margin-top: 2px;
            font-style: italic;
        }
        .performance-normal {
            border-left: 3px solid #4caf50;
        }
        .performance-intensive {
            border-left: 3px solid #ff9800;
        }
        .performance-conversion {
            border-left: 3px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 精确类型分析报告</h1>
            <p>基于语法树的Metal着色器运算过程分析</p>
            <p>总代码行数: 9 | 分析时间: 2025-08-04 12:48:25</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">44</div>
                <div class="stat-label">总节点数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">7</div>
                <div class="stat-label">有运算的行</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">18</div>
                <div class="stat-label">总运算次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">3</div>
                <div class="stat-label">类型转换</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">36.4%</div>
                <div class="stat-label">准确性评分</div>
            </div>
        </div>

        <div class="code-section">
            <h3 style="padding: 15px 20px; margin: 0; background: #f8f9fa; border-bottom: 1px solid #dee2e6;">📝 完整着色器代码分析</h3>

            <div class="code-line ">
                <div class="line-number">1</div>
                <div class="line-content">
                    
                    <div class="node-info"></div>
                </div>
            </div>

            <div class="code-line performance-normal">
                <div class="line-number">2</div>
                <div class="line-content">
                    float3 worldPos = transform * localPos;
                    <div class="node-info">运算: 2次 | 操作: =, *</div>
                </div>
            </div>

            <div class="code-line performance-normal">
                <div class="line-number">3</div>
                <div class="line-content">
                    half4 baseColor = texture.sample(sampler, uv);
                    <div class="node-info">运算: 1次 | 操作: =</div>
                </div>
            </div>

            <div class="code-line performance-normal">
                <div class="line-number">4</div>
                <div class="line-content">
                    float3 normal = normalize(normalMatrix * localNormal);
                    <div class="node-info">运算: 3次 | 操作: normalize, =, *</div>
                </div>
            </div>

            <div class="code-line performance-normal">
                <div class="line-number">5</div>
                <div class="line-content">
                    float dotNL = dot(normal, lightDir);
                    <div class="node-info">运算: 2次 | 操作: =, dot</div>
                </div>
            </div>

            <div class="code-line performance-conversion">
                <div class="line-number">6</div>
                <div class="line-content">
                    half3 diffuse = baseColor.rgb * half(max(dotNL, 0.0));
                    <div class="node-info">运算: 3次 | 类型转换: 1次 | 操作: max, =, *</div>
                </div>
            </div>

            <div class="code-line performance-conversion">
                <div class="line-number">7</div>
                <div class="line-content">
                    float3 specular = pow(max(dot(reflect(-lightDir, normal), viewDir), 0.0), shininess);
                    <div class="node-info">运算: 5次 | 类型转换: 1次 | 操作: max, reflect, dot, =, pow</div>
                </div>
            </div>

            <div class="code-line performance-conversion">
                <div class="line-number">8</div>
                <div class="line-content">
                    half3 finalColor = diffuse + half3(specular);
                    <div class="node-info">运算: 2次 | 类型转换: 1次 | 操作: +, =</div>
                </div>
            </div>

            <div class="code-line ">
                <div class="line-number">9</div>
                <div class="line-content">
                    
                    <div class="node-info"></div>
                </div>
            </div>

        </div>
    </div>
</body>
</html>
