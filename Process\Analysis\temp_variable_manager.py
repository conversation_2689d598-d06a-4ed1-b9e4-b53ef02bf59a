#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
临时变量管理器 - 管理语法树模拟过程中的临时变量
"""

from typing import Dict, List, Any
from dataclasses import dataclass

@dataclass
class SimulationStep:
    """模拟步骤数据结构"""
    step_index: int
    operation: str  # 操作类型：assign, add, multiply, function_call等
    target_var: str  # 目标变量名
    operands: List[str]  # 操作数
    expression: str  # 完整表达式
    node_type: str  # AST节点类型
    line_number: int  # 源代码行号
    precision_info: Dict[str, Any] = None  # 精度信息
    
    def __post_init__(self):
        if self.precision_info is None:
            self.precision_info = {}

class TempVariableManager:
    """临时变量管理器"""
    
    def __init__(self):
        self._temp_counter = 0
        self._variable_types = {}  # 变量类型映射
        self._simulation_steps = []  # 所有模拟步骤
        self._line_steps = {}  # 按行号分组的步骤
    
    def get_next_temp_var(self) -> str:
        """获取下一个临时变量名"""
        temp_var = f"tmp_{self._temp_counter}"
        self._temp_counter += 1
        return temp_var
    
    def reset_counter(self):
        """重置计数器（用于新的分析会话）"""
        self._temp_counter = 0
        self._variable_types.clear()
        self._simulation_steps.clear()
        self._line_steps.clear()
    
    def set_variable_type(self, var_name: str, var_type: str):
        """设置变量类型"""
        self._variable_types[var_name] = var_type
    
    def get_variable_type(self, var_name: str) -> str:
        """获取变量类型"""
        return self._variable_types.get(var_name, "unknown")
    
    def add_simulation_step(self, step: SimulationStep):
        """添加模拟步骤"""
        self._simulation_steps.append(step)
        
        # 按行号分组
        line_num = step.line_number
        if line_num not in self._line_steps:
            self._line_steps[line_num] = []
        self._line_steps[line_num].append(step)
    
    def get_line_simulation_steps(self, line_number: int) -> List[SimulationStep]:
        """获取指定行的模拟步骤"""
        return self._line_steps.get(line_number, [])
    
    def get_all_simulation_steps(self) -> List[SimulationStep]:
        """获取所有模拟步骤"""
        return self._simulation_steps.copy()
    
    def get_simulation_summary(self) -> Dict[str, Any]:
        """获取模拟摘要"""
        total_steps = len(self._simulation_steps)
        lines_with_steps = len(self._line_steps)
        
        # 统计操作类型
        operation_counts = {}
        for step in self._simulation_steps:
            op = step.operation
            operation_counts[op] = operation_counts.get(op, 0) + 1
        
        # 统计临时变量使用
        temp_vars_used = self._temp_counter
        
        return {
            'total_steps': total_steps,
            'lines_with_simulation': lines_with_steps,
            'temp_variables_used': temp_vars_used,
            'operation_counts': operation_counts,
            'variable_types': self._variable_types.copy()
        }
    
    def format_line_simulation(self, line_number: int) -> List[str]:
        """格式化指定行的模拟过程为字符串列表"""
        steps = self.get_line_simulation_steps(line_number)
        if not steps:
            return []
        
        formatted_steps = []
        for step in steps:
            if step.operation == 'assign':
                if len(step.operands) == 1:
                    formatted_steps.append(f"{step.target_var} = {step.operands[0]}")
                else:
                    formatted_steps.append(f"{step.target_var} = {step.expression}")
            elif step.operation in ['add', 'subtract', 'multiply', 'divide']:
                op_symbol = {
                    'add': '+',
                    'subtract': '-', 
                    'multiply': '*',
                    'divide': '/'
                }.get(step.operation, '?')
                
                if len(step.operands) >= 2:
                    formatted_steps.append(f"{step.target_var} = {step.operands[0]} {op_symbol} {step.operands[1]}")
                else:
                    formatted_steps.append(f"{step.target_var} = {step.expression}")
            elif step.operation == 'function_call':
                formatted_steps.append(f"{step.target_var} = {step.expression}")
            else:
                formatted_steps.append(f"{step.target_var} = {step.expression}")
        
        return formatted_steps
    
    def export_simulation_data(self) -> Dict[str, Any]:
        """导出模拟数据"""
        return {
            'temp_counter': self._temp_counter,
            'variable_types': self._variable_types.copy(),
            'simulation_steps': [
                {
                    'step_index': step.step_index,
                    'operation': step.operation,
                    'target_var': step.target_var,
                    'operands': step.operands.copy(),
                    'expression': step.expression,
                    'node_type': step.node_type,
                    'line_number': step.line_number,
                    'precision_info': step.precision_info.copy()
                }
                for step in self._simulation_steps
            ],
            'summary': self.get_simulation_summary()
        }
