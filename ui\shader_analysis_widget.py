#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
着色器分析结果显示组件
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTextEdit, QScrollArea, QFrame,
                            QGridLayout, QProgressBar, QMessageBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QUrl
from PyQt5.QtGui import QFont, QDesktopServices, QPalette, QColor
import os
import webbrowser
from typing import Dict, Optional

class AnalysisWorker(QThread):
    """分析工作线程"""
    analysis_completed = pyqtSignal(dict)
    analysis_failed = pyqtSignal(str)
    
    def __init__(self, shader_content: str):
        super().__init__()
        self.shader_content = shader_content
    
    def run(self):
        try:
            from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
            processor = ShaderAnalysisProcessor()
            result = processor.analyze_shader(self.shader_content)
            self.analysis_completed.emit(result)
        except Exception as e:
            self.analysis_failed.emit(str(e))

class MetricCard(QFrame):
    """指标卡片组件"""
    
    def __init__(self, title: str, value: str, color: str = "#4fc3f7"):
        super().__init__()
        self.setFrameStyle(QFrame.StyledPanel)
        self.setStyleSheet(f"""
            MetricCard {{
                background-color: #2d2d30;
                border: 1px solid #3c3c3c;
                border-radius: 8px;
                padding: 10px;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignCenter)
        
        # 数值标签
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: bold;
                color: {color};
                margin: 5px;
            }}
        """)
        
        # 标题标签
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #cccccc;
                margin: 2px;
            }
        """)
        
        layout.addWidget(value_label)
        layout.addWidget(title_label)

class ShaderAnalysisWidget(QWidget):
    """着色器分析结果显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.analysis_result = None
        self.report_files = {}
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("🔍 Metal着色器Half/Float运算分析")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # 指标卡片区域
        self.metrics_layout = QGridLayout()
        self.metrics_frame = QFrame()
        self.metrics_frame.setLayout(self.metrics_layout)
        layout.addWidget(self.metrics_frame)
        
        # 性能评分
        self.score_frame = QFrame()
        self.score_frame.setStyleSheet("""
            QFrame {
                background-color: #2d2d30;
                border: 1px solid #3c3c3c;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        score_layout = QVBoxLayout(self.score_frame)
        
        score_title = QLabel("性能评分")
        score_title.setStyleSheet("font-size: 14px; font-weight: bold; color: #ffffff; margin-bottom: 10px;")
        score_layout.addWidget(score_title)
        
        self.score_bar = QProgressBar()
        self.score_bar.setRange(0, 100)
        self.score_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #3c3c3c;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                color: white;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ff4444, stop:0.5 #ffaa00, stop:1 #00ff00);
                border-radius: 3px;
            }
        """)
        score_layout.addWidget(self.score_bar)
        
        self.score_label = QLabel("等待分析...")
        self.score_label.setAlignment(Qt.AlignCenter)
        self.score_label.setStyleSheet("color: #cccccc; margin-top: 5px;")
        score_layout.addWidget(self.score_label)
        
        layout.addWidget(self.score_frame)
        
        # 优化建议区域
        suggestions_label = QLabel("💡 优化建议")
        suggestions_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #ffffff; margin-top: 10px;")
        layout.addWidget(suggestions_label)
        
        self.suggestions_text = QTextEdit()
        self.suggestions_text.setMaximumHeight(120)
        self.suggestions_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                border: 1px solid #3c3c3c;
                border-radius: 5px;
                padding: 10px;
                color: #d4d4d4;
                font-family: 'Consolas', monospace;
            }
        """)
        layout.addWidget(self.suggestions_text)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.html_report_btn = QPushButton("📊 查看详细报告")
        self.html_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:disabled {
                background-color: #666666;
                color: #999999;
            }
        """)
        self.html_report_btn.clicked.connect(self.open_html_report)
        self.html_report_btn.setEnabled(False)
        
        self.json_report_btn = QPushButton("📄 导出JSON数据")
        self.json_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #666666;
                color: #999999;
            }
        """)
        self.json_report_btn.clicked.connect(self.open_json_report)
        self.json_report_btn.setEnabled(False)
        
        button_layout.addWidget(self.html_report_btn)
        button_layout.addWidget(self.json_report_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # 详细信息区域
        details_label = QLabel("📋 分析详情")
        details_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #ffffff; margin-top: 10px;")
        layout.addWidget(details_label)
        
        self.details_text = QTextEdit()
        self.details_text.setMaximumHeight(200)
        self.details_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                border: 1px solid #3c3c3c;
                border-radius: 5px;
                padding: 10px;
                color: #d4d4d4;
                font-family: 'Consolas', monospace;
                font-size: 11px;
            }
        """)
        layout.addWidget(self.details_text)
        
        layout.addStretch()
    
    def start_analysis(self, shader_content: str):
        """开始分析"""
        self.clear_results()
        self.set_loading_state(True)
        
        # 启动分析线程
        self.worker = AnalysisWorker(shader_content)
        self.worker.analysis_completed.connect(self.on_analysis_completed)
        self.worker.analysis_failed.connect(self.on_analysis_failed)
        self.worker.start()
    
    def on_analysis_completed(self, result: Dict):
        """分析完成回调"""
        self.analysis_result = result
        self.report_files = result.get('files', {})
        
        self.set_loading_state(False)
        self.update_display(result)
    
    def on_analysis_failed(self, error_msg: str):
        """分析失败回调"""
        self.set_loading_state(False)
        QMessageBox.critical(self, "分析失败", f"着色器分析失败:\n{error_msg}")
    
    def set_loading_state(self, loading: bool):
        """设置加载状态"""
        if loading:
            self.score_label.setText("正在分析...")
            self.suggestions_text.setText("正在分析着色器代码，请稍候...")
            self.details_text.setText("分析中...")
        
        self.html_report_btn.setEnabled(not loading and bool(self.report_files.get('html')))
        self.json_report_btn.setEnabled(not loading and bool(self.report_files.get('json')))
    
    def update_display(self, result: Dict):
        """更新显示内容"""
        analysis = result.get('analysis', {})
        stats = analysis.get('statistics', {})
        
        # 清除现有指标卡片
        for i in reversed(range(self.metrics_layout.count())):
            self.metrics_layout.itemAt(i).widget().setParent(None)
        
        # 添加指标卡片
        metrics = [
            ("总运算操作", str(stats.get('total_operations', 0)), "#4fc3f7"),
            ("精度转换", str(stats.get('precision_conversions', 0)), "#ff9800"),
            ("混合精度运算", str(stats.get('mixed_precision_ops', 0)), "#f44336"),
            ("高影响操作", str(stats.get('high_impact_ops', 0)), "#9c27b0"),
            ("纹理采样", str(stats.get('texture_samples', 0)), "#4caf50")
        ]
        
        for i, (title, value, color) in enumerate(metrics):
            card = MetricCard(title, value, color)
            row = i // 3
            col = i % 3
            self.metrics_layout.addWidget(card, row, col)
        
        # 更新性能评分
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        processor = ShaderAnalysisProcessor()
        key_metrics = processor.get_key_metrics(analysis)
        score = key_metrics['performance_score']
        
        self.score_bar.setValue(score)
        if score >= 80:
            score_text = f"优秀 ({score}/100)"
        elif score >= 60:
            score_text = f"良好 ({score}/100)"
        elif score >= 40:
            score_text = f"一般 ({score}/100)"
        else:
            score_text = f"需要优化 ({score}/100)"
        
        self.score_label.setText(score_text)
        
        # 更新优化建议
        suggestions = processor.get_optimization_suggestions(analysis)
        self.suggestions_text.setText('\n'.join(f"• {suggestion}" for suggestion in suggestions))
        
        # 更新详细信息
        summary = result.get('summary', '')
        self.details_text.setText(summary)
        
        # 启用按钮
        self.html_report_btn.setEnabled(bool(self.report_files.get('html')))
        self.json_report_btn.setEnabled(bool(self.report_files.get('json')))
    
    def clear_results(self):
        """清除结果"""
        self.analysis_result = None
        self.report_files = {}
        
        # 清除指标卡片
        for i in reversed(range(self.metrics_layout.count())):
            self.metrics_layout.itemAt(i).widget().setParent(None)
        
        self.score_bar.setValue(0)
        self.score_label.setText("等待分析...")
        self.suggestions_text.clear()
        self.details_text.clear()
        
        self.html_report_btn.setEnabled(False)
        self.json_report_btn.setEnabled(False)
    
    def open_html_report(self):
        """打开HTML报告"""
        html_path = self.report_files.get('html')
        if html_path and os.path.exists(html_path):
            try:
                # 使用系统默认浏览器打开
                QDesktopServices.openUrl(QUrl.fromLocalFile(html_path))
            except Exception as e:
                QMessageBox.warning(self, "打开失败", f"无法打开HTML报告:\n{str(e)}")
        else:
            QMessageBox.warning(self, "文件不存在", "HTML报告文件不存在或已被删除")
    
    def open_json_report(self):
        """打开JSON报告"""
        json_path = self.report_files.get('json')
        if json_path and os.path.exists(json_path):
            try:
                # 使用系统默认程序打开JSON文件
                QDesktopServices.openUrl(QUrl.fromLocalFile(json_path))
            except Exception as e:
                QMessageBox.warning(self, "打开失败", f"无法打开JSON报告:\n{str(e)}")
        else:
            QMessageBox.warning(self, "文件不存在", "JSON报告文件不存在或已被删除")
