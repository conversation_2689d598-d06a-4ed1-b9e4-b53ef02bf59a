#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
着色器分析结果显示组件
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QTextEdit, QScrollArea, QFrame,
                            QGridLayout, QProgressBar, QMessageBox, QFileDialog,
                            QSpinBox, QCheckBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QUrl
from PyQt5.QtGui import QFont, QDesktopServices, QPalette, QColor
import os
import webbrowser
import json
import csv
from typing import Dict, Optional

class AnalysisWorker(QThread):
    """分析工作线程"""
    analysis_completed = pyqtSignal(dict)
    analysis_failed = pyqtSignal(str)

    def __init__(self, shader_content: str):
        super().__init__()
        self.shader_content = shader_content
    
    def run(self):
        try:
            from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
            processor = ShaderAnalysisProcessor()
            result = processor.analyze_shader(self.shader_content)
            self.analysis_completed.emit(result)
        except Exception as e:
            self.analysis_failed.emit(str(e))

class MetricCard(QFrame):
    """指标卡片组件"""

    def __init__(self, title: str, value: str, color: str = "#4fc3f7"):
        super().__init__()
        self.setFrameStyle(QFrame.StyledPanel)
        self.setFixedSize(120, 80)  # 固定大小
        self.setStyleSheet(f"""
            MetricCard {{
                background-color: #2d2d30;
                border: 1px solid #3c3c3c;
                border-radius: 8px;
                padding: 5px;
            }}
        """)

        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignCenter)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(2)

        # 数值标签
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"""
            QLabel {{
                font-size: 18px;
                font-weight: bold;
                color: {color};
                margin: 0px;
            }}
        """)

        # 标题标签
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 10px;
                color: #cccccc;
                margin: 0px;
            }
        """)

        layout.addWidget(value_label)
        layout.addWidget(title_label)

class ShaderAnalysisWidget(QWidget):
    """着色器分析结果显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.analysis_result = None
        self.report_files = {}
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("🔍 Metal着色器Half/Float运算分析")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # 指标卡片区域
        self.metrics_layout = QGridLayout()
        self.metrics_layout.setSpacing(10)
        self.metrics_frame = QFrame()
        self.metrics_frame.setLayout(self.metrics_layout)
        self.metrics_frame.setStyleSheet("""
            QFrame {
                background-color: transparent;
                margin: 5px;
            }
        """)
        layout.addWidget(self.metrics_frame)
        
        # 性能评分
        self.score_frame = QFrame()
        self.score_frame.setStyleSheet("""
            QFrame {
                background-color: #2d2d30;
                border: 1px solid #3c3c3c;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        score_layout = QVBoxLayout(self.score_frame)
        
        score_title = QLabel("性能评分")
        score_title.setStyleSheet("font-size: 14px; font-weight: bold; color: #ffffff; margin-bottom: 10px;")
        score_layout.addWidget(score_title)
        
        self.score_bar = QProgressBar()
        self.score_bar.setRange(0, 100)
        self.score_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #3c3c3c;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                color: white;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ff4444, stop:0.5 #ffaa00, stop:1 #00ff00);
                border-radius: 3px;
            }
        """)
        score_layout.addWidget(self.score_bar)
        
        self.score_label = QLabel("等待分析...")
        self.score_label.setAlignment(Qt.AlignCenter)
        self.score_label.setStyleSheet("color: #cccccc; margin-top: 5px;")
        score_layout.addWidget(self.score_label)
        
        layout.addWidget(self.score_frame)
        
        # 优化建议区域
        suggestions_label = QLabel("💡 优化建议")
        suggestions_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #ffffff; margin-top: 10px;")
        layout.addWidget(suggestions_label)
        
        self.suggestions_text = QTextEdit()
        self.suggestions_text.setMaximumHeight(120)
        self.suggestions_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                border: 1px solid #3c3c3c;
                border-radius: 5px;
                padding: 10px;
                color: #d4d4d4;
                font-family: 'Consolas', monospace;
            }
        """)
        layout.addWidget(self.suggestions_text)
        
        # HTML报告选项
        html_options_layout = QHBoxLayout()

        self.show_all_lines_cb = QCheckBox("显示所有代码行")
        self.show_all_lines_cb.setStyleSheet("color: #cccccc; margin-right: 10px;")
        self.show_all_lines_cb.setToolTip("勾选后HTML报告将显示所有代码行（可能影响性能）")

        max_lines_label = QLabel("最大显示行数:")
        max_lines_label.setStyleSheet("color: #cccccc; margin-right: 5px;")

        self.max_lines_spinbox = QSpinBox()
        self.max_lines_spinbox.setRange(100, 5000)
        self.max_lines_spinbox.setValue(1000)
        self.max_lines_spinbox.setSuffix(" 行")
        self.max_lines_spinbox.setEnabled(False)  # 默认禁用，当取消勾选"显示所有行"时启用
        self.max_lines_spinbox.setStyleSheet("""
            QSpinBox {
                background-color: #2d2d30;
                border: 1px solid #3c3c3c;
                border-radius: 3px;
                padding: 5px;
                color: #d4d4d4;
                min-width: 80px;
            }
        """)

        # 连接复选框信号
        self.show_all_lines_cb.toggled.connect(lambda checked: self.max_lines_spinbox.setEnabled(not checked))

        html_options_layout.addWidget(self.show_all_lines_cb)
        html_options_layout.addWidget(max_lines_label)
        html_options_layout.addWidget(self.max_lines_spinbox)
        html_options_layout.addStretch()

        layout.addLayout(html_options_layout)

        # 按钮区域
        button_layout = QHBoxLayout()

        self.html_report_btn = QPushButton("📊 查看详细报告")
        self.html_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:disabled {
                background-color: #666666;
                color: #999999;
            }
        """)
        self.html_report_btn.clicked.connect(self.open_html_report)
        self.html_report_btn.setEnabled(False)
        
        self.json_report_btn = QPushButton("📄 导出JSON数据")
        self.json_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #666666;
                color: #999999;
            }
        """)
        self.json_report_btn.clicked.connect(self.export_json_report)
        self.json_report_btn.setEnabled(False)

        self.csv_report_btn = QPushButton("📊 导出CSV数据")
        self.csv_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
            QPushButton:disabled {
                background-color: #666666;
                color: #999999;
            }
        """)
        self.csv_report_btn.clicked.connect(self.export_csv_report)
        self.csv_report_btn.setEnabled(False)

        button_layout.addWidget(self.html_report_btn)
        button_layout.addWidget(self.json_report_btn)
        button_layout.addWidget(self.csv_report_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # 详细信息区域
        details_label = QLabel("📋 分析详情")
        details_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #ffffff; margin-top: 10px;")
        layout.addWidget(details_label)
        
        self.details_text = QTextEdit()
        self.details_text.setMaximumHeight(200)
        self.details_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                border: 1px solid #3c3c3c;
                border-radius: 5px;
                padding: 10px;
                color: #d4d4d4;
                font-family: 'Consolas', monospace;
                font-size: 11px;
            }
        """)
        layout.addWidget(self.details_text)
        
        layout.addStretch()
    
    def start_analysis(self, shader_content: str):
        """开始分析"""
        self.clear_results()
        self.set_loading_state(True)
        
        # 启动分析线程
        self.worker = AnalysisWorker(shader_content)
        self.worker.analysis_completed.connect(self.on_analysis_completed)
        self.worker.analysis_failed.connect(self.on_analysis_failed)
        self.worker.start()
    
    def on_analysis_completed(self, result: Dict):
        """分析完成回调"""
        self.analysis_result = result
        self.report_files = result.get('files', {})
        
        self.set_loading_state(False)
        self.update_display(result)
    
    def on_analysis_failed(self, error_msg: str):
        """分析失败回调"""
        self.set_loading_state(False)
        QMessageBox.critical(self, "分析失败", f"着色器分析失败:\n{error_msg}")
    
    def set_loading_state(self, loading: bool):
        """设置加载状态"""
        if loading:
            self.score_label.setText("正在分析...")
            self.suggestions_text.setText("正在分析着色器代码，请稍候...")
            self.details_text.setText("分析中...")
        
        self.html_report_btn.setEnabled(not loading and bool(self.report_files.get('html')))
        self.json_report_btn.setEnabled(not loading and bool(self.analysis_result))
        self.csv_report_btn.setEnabled(not loading and bool(self.analysis_result))
    
    def update_display(self, result: Dict):
        """更新显示内容"""
        analysis = result.get('analysis', {})
        stats = analysis.get('statistics', {})
        
        # 清除现有指标卡片
        for i in reversed(range(self.metrics_layout.count())):
            self.metrics_layout.itemAt(i).widget().setParent(None)
        
        # 添加指标卡片
        metrics = [
            ("总运算操作", str(stats.get('total_operations', 0)), "#4fc3f7"),
            ("精度转换", str(stats.get('precision_conversions', 0)), "#ff9800"),
            ("混合精度运算", str(stats.get('mixed_precision_ops', 0)), "#f44336"),
            ("高影响操作", str(stats.get('high_impact_ops', 0)), "#9c27b0"),
            ("纹理采样", str(stats.get('texture_samples', 0)), "#4caf50")
        ]
        
        for i, (title, value, color) in enumerate(metrics):
            card = MetricCard(title, value, color)
            row = i // 3
            col = i % 3
            self.metrics_layout.addWidget(card, row, col)
        
        # 更新性能评分
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        processor = ShaderAnalysisProcessor()
        key_metrics = processor.get_key_metrics(analysis)
        score = key_metrics['performance_score']
        
        self.score_bar.setValue(score)
        if score >= 80:
            score_text = f"优秀 ({score}/100)"
        elif score >= 60:
            score_text = f"良好 ({score}/100)"
        elif score >= 40:
            score_text = f"一般 ({score}/100)"
        else:
            score_text = f"需要优化 ({score}/100)"
        
        self.score_label.setText(score_text)
        
        # 更新优化建议
        suggestions = processor.get_optimization_suggestions(analysis)
        self.suggestions_text.setText('\n'.join(f"• {suggestion}" for suggestion in suggestions))
        
        # 更新详细信息
        summary = result.get('summary', '')
        self.details_text.setText(summary)
        
        # 启用按钮
        self.html_report_btn.setEnabled(bool(self.report_files.get('html')))
        self.json_report_btn.setEnabled(bool(self.analysis_result))
        self.csv_report_btn.setEnabled(bool(self.analysis_result))
    
    def clear_results(self):
        """清除结果"""
        self.analysis_result = None
        self.report_files = {}
        
        # 清除指标卡片
        for i in reversed(range(self.metrics_layout.count())):
            self.metrics_layout.itemAt(i).widget().setParent(None)
        
        self.score_bar.setValue(0)
        self.score_label.setText("等待分析...")
        self.suggestions_text.clear()
        self.details_text.clear()
        
        self.html_report_btn.setEnabled(False)
        self.json_report_btn.setEnabled(False)
        self.csv_report_btn.setEnabled(False)
    
    def open_html_report(self):
        """打开HTML报告"""
        if not self.analysis_result:
            QMessageBox.warning(self, "打开失败", "没有可显示的分析结果")
            return

        try:
            # 生成包含逐行分析的HTML报告
            from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
            processor = ShaderAnalysisProcessor()

            shader_content = self.analysis_result.get('shader_content', '')

            # 根据用户设置确定最大行数
            if self.show_all_lines_cb.isChecked():
                max_lines = None  # 显示所有行
            else:
                max_lines = self.max_lines_spinbox.value()

            html_content = processor.generate_html_report(
                self.analysis_result['analysis'],
                shader_content,
                max_lines
            )

            # 保存到临时文件
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_path = f.name

            # 使用系统默认浏览器打开
            QDesktopServices.openUrl(QUrl.fromLocalFile(temp_path))

        except Exception as e:
            QMessageBox.warning(self, "打开失败", f"无法生成HTML报告:\n{str(e)}")
            import traceback
            traceback.print_exc()
    
    def export_json_report(self):
        """导出JSON报告"""
        if not self.analysis_result:
            QMessageBox.warning(self, "导出失败", "没有可导出的分析数据")
            return

        # 选择保存路径
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出JSON分析数据",
            "shader_analysis.json",
            "JSON文件 (*.json);;所有文件 (*)"
        )

        if not file_path:
            return

        try:
            # 导出JSON数据
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.analysis_result['analysis'], f, ensure_ascii=False, indent=2)

            QMessageBox.information(self, "导出成功", f"JSON数据已导出到:\n{file_path}")

            # 询问是否打开文件
            reply = QMessageBox.question(
                self,
                "打开文件",
                "是否要打开导出的JSON文件？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))

        except Exception as e:
            QMessageBox.critical(self, "导出失败", f"导出JSON数据时出错:\n{str(e)}")

    def export_csv_report(self):
        """导出CSV报告"""
        if not self.analysis_result:
            QMessageBox.warning(self, "导出失败", "没有可导出的分析数据")
            return

        # 选择保存路径
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出CSV分析数据",
            "shader_analysis.csv",
            "CSV文件 (*.csv);;所有文件 (*)"
        )

        if not file_path:
            return

        try:
            # 准备CSV数据
            operations = self.analysis_result['analysis']['operations']

            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['line', 'type', 'performance_impact', 'precision_conversion', 'output_type', 'detail']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for op in operations:
                    writer.writerow({
                        'line': op['line'],
                        'type': op['type'],
                        'performance_impact': op['performance_impact'],
                        'precision_conversion': op['precision_conversion'] or '',
                        'output_type': op['output_type'] or '',
                        'detail': op['detail'][:100] + '...' if len(op['detail']) > 100 else op['detail']
                    })

            QMessageBox.information(self, "导出成功", f"CSV数据已导出到:\n{file_path}")

            # 询问是否打开文件
            reply = QMessageBox.question(
                self,
                "打开文件",
                "是否要打开导出的CSV文件？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))

        except Exception as e:
            QMessageBox.critical(self, "导出失败", f"导出CSV数据时出错:\n{str(e)}")
