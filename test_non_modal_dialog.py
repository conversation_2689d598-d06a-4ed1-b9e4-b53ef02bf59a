#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试非模态着色器分析弹窗
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication

def test_non_modal_dialog():
    """测试非模态弹窗"""
    app = QApplication(sys.argv)
    
    # 导入主窗口
    from ui.main_window import MainWindow
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    print("🔍 非模态着色器分析弹窗测试")
    print("=" * 50)
    print("修复内容:")
    print("1. ✅ 修复详细内容按钮点击问题")
    print("   - HTML报告按钮现在基于analysis_result启用")
    print("   - 不再依赖预先保存的HTML文件")
    print()
    print("2. ✅ 弹窗改为非模态")
    print("   - setModal(False) - 不阻塞主窗口")
    print("   - show() 替代 exec_() - 非阻塞显示")
    print("   - 弹窗保存为主窗口属性，避免垃圾回收")
    print()
    print("测试步骤:")
    print("1. 在主窗口中打开或创建着色器文件")
    print("2. 输入测试着色器代码")
    print("3. 使用 Ctrl+Alt+A 打开分析弹窗")
    print("4. 验证主窗口仍可操作（非模态）")
    print("5. 验证'查看详细报告'按钮可点击")
    print("6. 点击按钮应该打开HTML报告")
    print()
    print("测试用着色器代码:")
    print("""
#include <metal_stdlib>
using namespace metal;

fragment half4 main0(VertexOut in [[stage_in]]) {
    half4 color = half4(1.0, 0.5, 0.0, 1.0);
    float intensity = 0.8;
    half3 result = color.rgb * half(intensity);
    return half4(result, color.a);
}
""")
    print()
    print("预期行为:")
    print("✅ 弹窗显示后，主窗口仍可正常操作")
    print("✅ 分析完成后，所有按钮都应该可点击")
    print("✅ HTML报告按钮点击后打开浏览器")
    print("✅ 可以同时操作主窗口和分析弹窗")
    
    sys.exit(app.exec_())

def main():
    """主函数"""
    try:
        test_non_modal_dialog()
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
