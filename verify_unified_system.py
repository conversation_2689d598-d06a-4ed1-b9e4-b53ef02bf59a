#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证统一系统重构完成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_api_unification():
    """验证API统一"""
    print("🔄 验证API统一")
    print("=" * 50)
    
    try:
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        processor = ShaderAnalysisProcessor()
        
        # 检查主要方法
        main_methods = [
            'analyze_shader',
            'get_key_metrics', 
            'get_analysis_summary',
            'generate_html_report',
            'get_optimization_suggestions'
        ]
        
        for method in main_methods:
            if hasattr(processor, method):
                print(f"   ✅ {method}: 存在")
            else:
                print(f"   ❌ {method}: 缺失")
        
        # 检查是否移除了旧方法
        old_methods = [
            'analyze_shader_with_precise_types'
        ]
        
        for method in old_methods:
            if hasattr(processor, method):
                print(f"   ⚠️  {method}: 仍然存在（应该移除）")
            else:
                print(f"   ✅ {method}: 已移除")
        
        return True
        
    except Exception as e:
        print(f"❌ API验证失败: {str(e)}")
        return False

def verify_ui_integration():
    """验证UI集成"""
    print("\n🎨 验证UI集成")
    print("=" * 50)
    
    # 检查UI组件中的方法调用
    ui_files = [
        'UI/shader_analysis_widget.py',
        'UI/precise_type_analysis_widget.py'
    ]

    all_ui_ok = True

    for ui_file in ui_files:
        if os.path.exists(ui_file):
            with open(ui_file, 'r', encoding='utf-8') as f:
                content = f.read()

            print(f"检查 {ui_file}:")

            # 检查是否使用了统一的方法
            if 'analyze_shader(' in content:
                print(f"   ✅ 使用统一的 analyze_shader 方法")
            else:
                print(f"   ❌ 未使用统一的 analyze_shader 方法")
                all_ui_ok = False

            # 检查是否还有旧方法调用
            if 'analyze_shader_with_precise_types' in content:
                print(f"   ⚠️  仍包含旧方法调用")
                all_ui_ok = False
            else:
                print(f"   ✅ 已移除旧方法调用")
        else:
            print(f"❌ UI文件不存在: {ui_file}")
            all_ui_ok = False

    return all_ui_ok

def verify_data_structure():
    """验证数据结构"""
    print("\n📊 验证数据结构")
    print("=" * 50)
    
    try:
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        processor = ShaderAnalysisProcessor()
        
        # 测试简单代码
        test_code = "float a = b + c;"
        result = processor.analyze_shader(test_code, save_reports=False)
        
        # 验证返回结构
        expected_structure = {
            'analysis': {
                'precise_analysis': {
                    'overall_statistics': {},
                    'code_lines': [],
                    'precise_analyses': []
                }
            },
            'files': {},
            'analysis_method': 'precise_tree_based',
            'shader_content': test_code
        }
        
        def check_structure(actual, expected, path=""):
            """递归检查结构"""
            for key in expected:
                full_path = f"{path}.{key}" if path else key
                if key not in actual:
                    print(f"   ❌ 缺少键: {full_path}")
                    return False
                elif isinstance(expected[key], dict):
                    if not check_structure(actual[key], expected[key], full_path):
                        return False
                else:
                    print(f"   ✅ 包含键: {full_path}")
            return True
        
        if check_structure(result, expected_structure):
            print("   ✅ 数据结构验证通过")
            return True
        else:
            print("   ❌ 数据结构验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据结构验证失败: {str(e)}")
        return False

def verify_performance():
    """验证性能"""
    print("\n⚡ 验证性能")
    print("=" * 50)
    
    try:
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        import time
        
        processor = ShaderAnalysisProcessor()
        
        # 读取大型着色器文件
        shader_file = "metal_shader_ps"
        if os.path.exists(shader_file):
            with open(shader_file, 'r', encoding='utf-8') as f:
                shader_content = f.read()
            
            print(f"测试大型着色器文件: {len(shader_content)} 字符")
            
            # 测试分析性能
            start_time = time.time()
            result = processor.analyze_shader(shader_content, save_reports=False)
            end_time = time.time()
            
            analysis_time = end_time - start_time
            print(f"   ✅ 分析完成时间: {analysis_time:.2f} 秒")
            
            # 显示分析结果统计
            precise_data = result['analysis']['precise_analysis']
            overall_stats = precise_data['overall_statistics']
            
            print(f"   📊 分析结果:")
            print(f"      总节点数: {overall_stats['total_nodes']:,}")
            print(f"      类型转换: {overall_stats['total_type_conversions']:,}")
            print(f"      准确性评分: {overall_stats['precision_accuracy_score']:.1f}%")
            
            return True
        else:
            print("❌ 未找到测试文件")
            return False
            
    except Exception as e:
        print(f"❌ 性能验证失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🎯 统一系统重构验证")
    print("=" * 60)
    print("验证内容:")
    print("• 🔄 API接口统一 - 移除原始分析，只保留基于AST的精确分析")
    print("• 🎨 UI组件集成 - 所有组件使用统一的analyze_shader方法")
    print("• 📊 数据结构一致 - 返回结构标准化")
    print("• ⚡ 性能验证 - 大型文件分析性能")
    print()
    
    # 执行各项验证
    api_ok = verify_api_unification()
    ui_ok = verify_ui_integration()
    data_ok = verify_data_structure()
    perf_ok = verify_performance()
    
    print(f"\n🎉 验证结果总结")
    print("=" * 60)
    
    if api_ok:
        print("✅ API接口统一验证通过")
        print("   • 统一使用 analyze_shader 方法")
        print("   • 移除了旧的分析方法")
        print("   • 所有辅助方法正常工作")
    else:
        print("❌ API接口统一验证失败")
    
    if ui_ok:
        print("✅ UI组件集成验证通过")
        print("   • UI组件使用统一接口")
        print("   • 移除了旧方法调用")
    else:
        print("❌ UI组件集成验证失败")
    
    if data_ok:
        print("✅ 数据结构验证通过")
        print("   • 返回结构标准化")
        print("   • 包含完整的精确分析数据")
    else:
        print("❌ 数据结构验证失败")
    
    if perf_ok:
        print("✅ 性能验证通过")
        print("   • 大型文件分析正常")
        print("   • 分析速度满足要求")
    else:
        print("❌ 性能验证失败")
    
    if all([api_ok, ui_ok, data_ok, perf_ok]):
        print(f"\n🎊 系统重构完成！")
        print("现在整个系统统一使用基于AST的精确类型分析:")
        print("• 🔄 单一的分析入口: analyze_shader")
        print("• 🎯 基于语法树的精确类型推断")
        print("• 📊 完整的运算过程模拟")
        print("• 🌐 交互式HTML报告")
        print("• 💡 智能优化建议")
        print("• 📱 统一的UI接口")
    else:
        print(f"\n❌ 系统重构未完全完成，请检查失败的验证项")

if __name__ == "__main__":
    main()
