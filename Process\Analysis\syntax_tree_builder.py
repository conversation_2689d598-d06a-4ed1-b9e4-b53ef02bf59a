#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语法树构建器 - 构建着色器代码的语法树结构并进行类型分析
"""

import re
from typing import Dict, List, Optional, Union, Any
from dataclasses import dataclass
from enum import Enum

class NodeType(Enum):
    """节点类型枚举"""
    VARIABLE = "variable"           # 变量
    LITERAL = "literal"            # 字面量
    OPERATOR = "operator"          # 运算符
    FUNCTION = "function"          # 函数调用
    ASSIGNMENT = "assignment"      # 赋值
    DECLARATION = "declaration"    # 声明
    MEMBER_ACCESS = "member_access" # 成员访问
    TYPE_CAST = "type_cast"        # 类型转换

class DataType(Enum):
    """数据类型枚举"""
    FLOAT = "float"
    HALF = "half"
    INT = "int"
    UINT = "uint"
    BOOL = "bool"
    FLOAT2 = "float2"
    FLOAT3 = "float3"
    FLOAT4 = "float4"
    HALF2 = "half2"
    HALF3 = "half3"
    HALF4 = "half4"
    FLOAT3X3 = "float3x3"
    FLOAT4X4 = "float4x4"
    UNKNOWN = "unknown"

@dataclass
class ASTNode:
    """抽象语法树节点"""
    node_type: NodeType
    value: str                    # 节点值（变量名、运算符、函数名等）
    data_type: DataType          # 数据类型
    children: List['ASTNode']    # 子节点
    line_number: int             # 源代码行号
    position: int = 0            # 在行中的位置
    
    def add_child(self, child: 'ASTNode'):
        """添加子节点"""
        self.children.append(child)
    
    def __str__(self):
        return f"{self.node_type.value}({self.value}:{self.data_type.value})"

class TypeTable:
    """全局类型表"""
    
    def __init__(self):
        self.variables: Dict[str, DataType] = {}
        self.functions: Dict[str, Dict] = {}
        self._init_builtin_functions()
    
    def _init_builtin_functions(self):
        """初始化内置函数"""
        self.functions.update({
            'normalize': {'return_type': DataType.UNKNOWN, 'params': ['vector']},
            'dot': {'return_type': DataType.FLOAT, 'params': ['vector', 'vector']},
            'cross': {'return_type': DataType.UNKNOWN, 'params': ['vector', 'vector']},
            'max': {'return_type': DataType.UNKNOWN, 'params': ['any', 'any']},
            'min': {'return_type': DataType.UNKNOWN, 'params': ['any', 'any']},
            'clamp': {'return_type': DataType.UNKNOWN, 'params': ['any', 'any', 'any']},
            'mix': {'return_type': DataType.UNKNOWN, 'params': ['any', 'any', 'float']},
            'sqrt': {'return_type': DataType.UNKNOWN, 'params': ['any']},
            'pow': {'return_type': DataType.UNKNOWN, 'params': ['any', 'any']},
            'sin': {'return_type': DataType.UNKNOWN, 'params': ['any']},
            'cos': {'return_type': DataType.UNKNOWN, 'params': ['any']},
            'sample': {'return_type': DataType.HALF4, 'params': ['texture', 'sampler', 'coords']},
        })
    
    def declare_variable(self, name: str, data_type: DataType):
        """声明变量"""
        self.variables[name] = data_type
    
    def get_variable_type(self, name: str) -> DataType:
        """获取变量类型"""
        return self.variables.get(name, DataType.UNKNOWN)
    
    def get_function_info(self, name: str) -> Dict:
        """获取函数信息"""
        return self.functions.get(name, {'return_type': DataType.UNKNOWN, 'params': []})

class SyntaxTreeBuilder:
    """语法树构建器"""
    
    def __init__(self):
        self.type_table = TypeTable()
        self.operators = {
            '+': 'add', '-': 'sub', '*': 'mul', '/': 'div',
            '=': 'assign', '==': 'eq', '!=': 'ne',
            '<': 'lt', '>': 'gt', '<=': 'le', '>=': 'ge',
            '&&': 'and', '||': 'or', '!': 'not'
        }
    
    def parse_type(self, type_str: str) -> DataType:
        """解析类型字符串"""
        type_map = {
            'float': DataType.FLOAT, 'half': DataType.HALF,
            'int': DataType.INT, 'uint': DataType.UINT, 'bool': DataType.BOOL,
            'float2': DataType.FLOAT2, 'float3': DataType.FLOAT3, 'float4': DataType.FLOAT4,
            'half2': DataType.HALF2, 'half3': DataType.HALF3, 'half4': DataType.HALF4,
            'float3x3': DataType.FLOAT3X3, 'float4x4': DataType.FLOAT4X4
        }
        return type_map.get(type_str.lower(), DataType.UNKNOWN)
    
    def infer_literal_type(self, value: str) -> DataType:
        """推断字面量类型"""
        if re.match(r'^\d+\.\d*f?$', value):
            return DataType.FLOAT
        elif re.match(r'^\d+$', value):
            return DataType.INT
        elif value.lower() in ['true', 'false']:
            return DataType.BOOL
        return DataType.UNKNOWN
    
    def parse_expression(self, expr: str, line_number: int) -> ASTNode:
        """解析表达式"""
        expr = expr.strip()
        
        # 处理类型转换 float(x), half(x)
        type_cast_match = re.match(r'^(float|half|float2|float3|float4|half2|half3|half4)\s*\((.+)\)$', expr)
        if type_cast_match:
            target_type_str, inner_expr = type_cast_match.groups()
            target_type = self.parse_type(target_type_str)
            
            cast_node = ASTNode(
                node_type=NodeType.TYPE_CAST,
                value=target_type_str,
                data_type=target_type,
                children=[],
                line_number=line_number
            )
            
            inner_node = self.parse_expression(inner_expr, line_number)
            cast_node.add_child(inner_node)
            return cast_node
        
        # 处理函数调用 func(a, b, c)
        func_match = re.match(r'^([a-zA-Z_][a-zA-Z0-9_]*)\s*\((.+)\)$', expr)
        if func_match:
            func_name, params_str = func_match.groups()
            func_info = self.type_table.get_function_info(func_name)
            
            func_node = ASTNode(
                node_type=NodeType.FUNCTION,
                value=func_name,
                data_type=func_info['return_type'],
                children=[],
                line_number=line_number
            )
            
            # 解析参数
            if params_str.strip():
                params = self._split_parameters(params_str)
                for param in params:
                    param_node = self.parse_expression(param.strip(), line_number)
                    func_node.add_child(param_node)
            
            return func_node
        
        # 处理成员访问 obj.member
        member_match = re.match(r'^([a-zA-Z_][a-zA-Z0-9_]*)\s*\.\s*([a-zA-Z_][a-zA-Z0-9_]*)$', expr)
        if member_match:
            obj_name, member_name = member_match.groups()
            obj_type = self.type_table.get_variable_type(obj_name)
            
            member_node = ASTNode(
                node_type=NodeType.MEMBER_ACCESS,
                value=f"{obj_name}.{member_name}",
                data_type=self._infer_member_type(obj_type, member_name),
                children=[],
                line_number=line_number
            )
            
            return member_node
        
        # 处理二元运算符
        for op in ['==', '!=', '<=', '>=', '&&', '||', '+', '-', '*', '/', '<', '>']:
            if op in expr:
                parts = expr.split(op, 1)
                if len(parts) == 2:
                    left_expr, right_expr = parts
                    left_expr, right_expr = left_expr.strip(), right_expr.strip()
                    
                    if left_expr and right_expr:
                        op_node = ASTNode(
                            node_type=NodeType.OPERATOR,
                            value=op,
                            data_type=self._infer_operator_result_type(op, left_expr, right_expr),
                            children=[],
                            line_number=line_number
                        )
                        
                        left_node = self.parse_expression(left_expr, line_number)
                        right_node = self.parse_expression(right_expr, line_number)
                        
                        op_node.add_child(left_node)
                        op_node.add_child(right_node)
                        
                        return op_node
        
        # 处理变量或字面量
        if re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', expr):
            # 变量
            var_type = self.type_table.get_variable_type(expr)
            return ASTNode(
                node_type=NodeType.VARIABLE,
                value=expr,
                data_type=var_type,
                children=[],
                line_number=line_number
            )
        else:
            # 字面量
            literal_type = self.infer_literal_type(expr)
            return ASTNode(
                node_type=NodeType.LITERAL,
                value=expr,
                data_type=literal_type,
                children=[],
                line_number=line_number
            )
    
    def _split_parameters(self, params_str: str) -> List[str]:
        """分割函数参数（考虑嵌套括号）"""
        params = []
        current_param = ""
        paren_count = 0
        
        for char in params_str:
            if char == ',' and paren_count == 0:
                params.append(current_param.strip())
                current_param = ""
            else:
                if char == '(':
                    paren_count += 1
                elif char == ')':
                    paren_count -= 1
                current_param += char
        
        if current_param.strip():
            params.append(current_param.strip())
        
        return params
    
    def _infer_member_type(self, obj_type: DataType, member: str) -> DataType:
        """推断成员访问的类型"""
        if member in ['x', 'y', 'z', 'w', 'r', 'g', 'b', 'a']:
            return DataType.FLOAT if obj_type.value.startswith('float') else DataType.HALF
        elif member in ['xy', 'xyz', 'rgb', 'rg']:
            if member == 'xy' or member == 'rg':
                return DataType.FLOAT2 if obj_type.value.startswith('float') else DataType.HALF2
            elif member == 'xyz' or member == 'rgb':
                return DataType.FLOAT3 if obj_type.value.startswith('float') else DataType.HALF3
        return DataType.UNKNOWN
    
    def _infer_operator_result_type(self, op: str, left_expr: str, right_expr: str) -> DataType:
        """推断运算符结果类型"""
        if op in ['==', '!=', '<', '>', '<=', '>=', '&&', '||']:
            return DataType.BOOL
        # 对于算术运算符，需要更复杂的类型推断
        return DataType.UNKNOWN
    
    def parse_statement(self, statement: str, line_number: int) -> ASTNode:
        """解析语句"""
        statement = statement.strip().rstrip(';')
        
        # 处理变量声明和赋值 type var = expr
        decl_match = re.match(r'^(float|half|float2|float3|float4|half2|half3|half4|float3x3|float4x4|int|uint|bool)\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*(.+)$', statement)
        if decl_match:
            type_str, var_name, expr = decl_match.groups()
            var_type = self.parse_type(type_str)
            
            # 在类型表中声明变量
            self.type_table.declare_variable(var_name, var_type)
            
            # 创建声明节点
            decl_node = ASTNode(
                node_type=NodeType.DECLARATION,
                value=f"{type_str} {var_name}",
                data_type=var_type,
                children=[],
                line_number=line_number
            )
            
            # 解析右侧表达式
            expr_node = self.parse_expression(expr, line_number)
            decl_node.add_child(expr_node)
            
            return decl_node
        
        # 处理赋值 var = expr
        assign_match = re.match(r'^([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*(.+)$', statement)
        if assign_match:
            var_name, expr = assign_match.groups()
            var_type = self.type_table.get_variable_type(var_name)
            
            assign_node = ASTNode(
                node_type=NodeType.ASSIGNMENT,
                value=f"{var_name} =",
                data_type=var_type,
                children=[],
                line_number=line_number
            )
            
            expr_node = self.parse_expression(expr, line_number)
            assign_node.add_child(expr_node)
            
            return assign_node
        
        # 处理其他表达式
        return self.parse_expression(statement, line_number)
    
    def build_syntax_trees(self, code_lines: List[str]) -> List[ASTNode]:
        """为代码行列表构建语法树"""
        trees = []
        
        for i, line in enumerate(code_lines, 1):
            line = line.strip()
            if line and not line.startswith('//') and not line.startswith('#'):
                try:
                    tree = self.parse_statement(line, i)
                    trees.append(tree)
                except Exception as e:
                    # 创建错误节点
                    error_node = ASTNode(
                        node_type=NodeType.VARIABLE,
                        value=f"ERROR: {str(e)}",
                        data_type=DataType.UNKNOWN,
                        children=[],
                        line_number=i
                    )
                    trees.append(error_node)
        
        return trees

    def print_tree(self, node: ASTNode, indent: int = 0) -> str:
        """打印语法树结构"""
        result = "  " * indent + f"{node}\n"
        for child in node.children:
            result += self.print_tree(child, indent + 1)
        return result

    def analyze_tree_types(self, node: ASTNode) -> Dict[str, Any]:
        """分析语法树中的类型信息"""
        analysis = {
            'node_info': {
                'type': node.node_type.value,
                'value': node.value,
                'data_type': node.data_type.value,
                'line': node.line_number
            },
            'type_conversions': [],
            'precision_issues': [],
            'children_analysis': []
        }

        # 检查类型转换
        if node.node_type == NodeType.TYPE_CAST:
            if node.children:
                source_type = node.children[0].data_type
                target_type = node.data_type
                analysis['type_conversions'].append({
                    'from': source_type.value,
                    'to': target_type.value,
                    'line': node.line_number
                })

        # 检查精度问题
        if node.node_type == NodeType.OPERATOR and node.value in ['+', '-', '*', '/']:
            if len(node.children) >= 2:
                left_type = node.children[0].data_type
                right_type = node.children[1].data_type

                if self._is_mixed_precision(left_type, right_type):
                    analysis['precision_issues'].append({
                        'operation': node.value,
                        'left_type': left_type.value,
                        'right_type': right_type.value,
                        'line': node.line_number
                    })

        # 递归分析子节点
        for child in node.children:
            child_analysis = self.analyze_tree_types(child)
            analysis['children_analysis'].append(child_analysis)

            # 合并子节点的类型转换和精度问题
            analysis['type_conversions'].extend(child_analysis['type_conversions'])
            analysis['precision_issues'].extend(child_analysis['precision_issues'])

        return analysis

    def _is_mixed_precision(self, type1: DataType, type2: DataType) -> bool:
        """检查是否是混合精度运算"""
        float_types = {DataType.FLOAT, DataType.FLOAT2, DataType.FLOAT3, DataType.FLOAT4}
        half_types = {DataType.HALF, DataType.HALF2, DataType.HALF3, DataType.HALF4}

        return (type1 in float_types and type2 in half_types) or \
               (type1 in half_types and type2 in float_types)

    def get_type_summary(self, trees: List[ASTNode]) -> Dict[str, Any]:
        """获取所有语法树的类型摘要"""
        summary = {
            'total_trees': len(trees),
            'variable_declarations': 0,
            'assignments': 0,
            'function_calls': 0,
            'type_conversions': 0,
            'precision_issues': 0,
            'declared_variables': dict(self.type_table.variables),
            'type_conversion_details': [],
            'precision_issue_details': []
        }

        for tree in trees:
            analysis = self.analyze_tree_types(tree)

            # 统计节点类型
            if tree.node_type == NodeType.DECLARATION:
                summary['variable_declarations'] += 1
            elif tree.node_type == NodeType.ASSIGNMENT:
                summary['assignments'] += 1
            elif tree.node_type == NodeType.FUNCTION:
                summary['function_calls'] += 1

            # 统计类型转换和精度问题
            summary['type_conversions'] += len(analysis['type_conversions'])
            summary['precision_issues'] += len(analysis['precision_issues'])
            summary['type_conversion_details'].extend(analysis['type_conversions'])
            summary['precision_issue_details'].extend(analysis['precision_issues'])

        return summary


class SyntaxTreeAnalyzer:
    """语法树分析器 - 整合代码行分析和语法树构建"""

    def __init__(self):
        self.tree_builder = SyntaxTreeBuilder()

    def analyze_shader_with_syntax_trees(self, shader_content: str) -> Dict[str, Any]:
        """分析着色器代码并构建语法树"""
        from .code_line_analyzer import CodeLineAnalyzer

        # 首先使用代码行分析器获取有效代码行
        line_analyzer = CodeLineAnalyzer()
        code_lines = line_analyzer.analyze_shader_code(shader_content)

        # 提取代码行内容
        code_line_contents = [line.content for line in code_lines]

        # 构建语法树
        syntax_trees = self.tree_builder.build_syntax_trees(code_line_contents)

        # 分析类型信息
        type_summary = self.tree_builder.get_type_summary(syntax_trees)

        # 整合结果
        result = {
            'code_lines': code_lines,
            'syntax_trees': syntax_trees,
            'type_summary': type_summary,
            'tree_builder': self.tree_builder  # 保留构建器以便后续使用
        }

        return result

    def format_analysis_result(self, analysis_result: Dict[str, Any]) -> str:
        """格式化分析结果"""
        result = []
        result.append("🌳 语法树分析结果")
        result.append("=" * 60)

        code_lines = analysis_result['code_lines']
        syntax_trees = analysis_result['syntax_trees']
        type_summary = analysis_result['type_summary']
        tree_builder = analysis_result['tree_builder']

        # 显示代码行和对应的语法树
        result.append(f"识别到 {len(code_lines)} 行有效代码:")
        result.append("-" * 40)

        for i, (code_line, tree) in enumerate(zip(code_lines, syntax_trees)):
            result.append(f"\n第{i+1}行: {code_line.content}")
            result.append("语法树:")
            result.append(tree_builder.print_tree(tree))

        # 显示类型摘要
        result.append("📊 类型分析摘要:")
        result.append("-" * 40)
        result.append(f"变量声明: {type_summary['variable_declarations']}")
        result.append(f"赋值语句: {type_summary['assignments']}")
        result.append(f"函数调用: {type_summary['function_calls']}")
        result.append(f"类型转换: {type_summary['type_conversions']}")
        result.append(f"精度问题: {type_summary['precision_issues']}")

        # 显示声明的变量
        if type_summary['declared_variables']:
            result.append(f"\n📝 声明的变量:")
            for var_name, var_type in type_summary['declared_variables'].items():
                result.append(f"  {var_name}: {var_type.value}")

        # 显示类型转换详情
        if type_summary['type_conversion_details']:
            result.append(f"\n🔄 类型转换详情:")
            for conversion in type_summary['type_conversion_details']:
                result.append(f"  行{conversion['line']}: {conversion['from']} → {conversion['to']}")

        # 显示精度问题详情
        if type_summary['precision_issue_details']:
            result.append(f"\n⚠️  混合精度问题:")
            for issue in type_summary['precision_issue_details']:
                result.append(f"  行{issue['line']}: {issue['left_type']} {issue['operation']} {issue['right_type']}")

        return "\n".join(result)
