#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试着色器分析弹窗
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PyQt5.QtCore import QTimer

def test_analysis_dialog():
    """测试分析弹窗"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = QMainWindow()
    window.setWindowTitle("着色器分析弹窗测试")
    window.setGeometry(100, 100, 400, 200)
    
    # 创建中央组件
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加测试按钮
    test_btn = QPushButton("🔍 打开着色器分析弹窗")
    test_btn.setStyleSheet("""
        QPushButton {
            background-color: #0078d4;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #106ebe;
        }
    """)
    layout.addWidget(test_btn)
    
    # 测试着色器内容
    test_shader = """
#include <metal_stdlib>
using namespace metal;

struct VertexOut {
    float4 position [[position]];
    half2 texCoord;
    half3 normal;
};

fragment half4 main0(VertexOut in [[stage_in]], 
                     texture2d<half> baseTexture [[texture(0)]],
                     sampler baseSampler [[sampler(0)]]) {
    
    half4 baseColor = baseTexture.sample(baseSampler, float2(in.texCoord));
    half3 normalizedNormal = normalize(in.normal);
    
    float lightIntensity = 0.8;
    half3 finalColor = baseColor.rgb * half(lightIntensity);
    
    // 混合精度运算示例
    float3 worldPos = float3(1.0, 2.0, 3.0);
    half3 lightDir = half3(normalize(worldPos));
    
    half dotProduct = dot(normalizedNormal, lightDir);
    half3 diffuse = finalColor * max(dotProduct, half(0.0));
    
    return half4(diffuse, baseColor.a);
}
"""
    
    def open_dialog():
        print("打开着色器分析弹窗...")
        from ui.shader_analysis_dialog import ShaderAnalysisDialog
        dialog = ShaderAnalysisDialog(window, test_shader)
        dialog.exec_()  # 模态显示
        print("弹窗已关闭")
    
    test_btn.clicked.connect(open_dialog)
    
    # 显示主窗口
    window.show()
    
    print("弹窗测试程序已启动")
    print("点击按钮打开着色器分析弹窗")
    print("弹窗特点:")
    print("✅ 模态对话框，阻塞主窗口")
    print("✅ 直接包含完整的ShaderAnalysisWidget")
    print("✅ 自动开始分析")
    print("✅ 透明背景样式")
    print("✅ 合适的窗口大小(800x600)")
    
    sys.exit(app.exec_())

def main():
    """主函数"""
    print("🔍 着色器分析弹窗测试")
    print("=" * 50)
    
    try:
        test_analysis_dialog()
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
