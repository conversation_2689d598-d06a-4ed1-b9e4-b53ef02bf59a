#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI启动和基本功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ui_launch():
    """测试UI启动"""
    print("🚀 测试UI启动")
    print("=" * 50)
    
    try:
        # 导入PyQt5
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        
        # 创建应用程序
        app = QApplication(sys.argv)
        print("   ✅ QApplication 创建成功")
        
        # 导入主窗口
        from ui.main_window import MainWindow
        print("   ✅ MainWindow 导入成功")
        
        # 创建主窗口
        window = MainWindow()
        print("   ✅ MainWindow 创建成功")
        
        # 显示窗口
        window.show()
        print("   ✅ MainWindow 显示成功")
        
        # 测试统一分析组件
        from ui.unified_shader_analysis_widget import UnifiedShaderAnalysisWidget
        analysis_widget = UnifiedShaderAnalysisWidget()
        print("   ✅ UnifiedShaderAnalysisWidget 创建成功")
        
        # 设置定时器自动关闭
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(2000)  # 2秒后自动关闭
        
        print("   🎯 UI启动成功，2秒后自动关闭...")
        
        # 运行应用程序
        app.exec_()
        
        print("   ✅ UI测试完成")
        return True
        
    except Exception as e:
        print(f"   ❌ UI启动失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_analysis_workflow():
    """测试分析工作流程"""
    print("\n🔄 测试分析工作流程")
    print("=" * 50)
    
    try:
        # 测试分析处理器
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        processor = ShaderAnalysisProcessor()
        print("   ✅ ShaderAnalysisProcessor 创建成功")
        
        # 测试着色器代码
        test_shader = """
float3 worldPos = transform * localPos;
half4 baseColor = texture.sample(sampler, uv);
float3 normal = normalize(normalMatrix * localNormal);
float dotNL = dot(normal, lightDir);
half3 diffuse = baseColor.rgb * half(max(dotNL, 0.0));
float3 specular = pow(max(dot(reflect(-lightDir, normal), viewDir), 0.0), shininess);
half3 finalColor = diffuse + half3(specular);
"""
        
        print("   开始分析测试着色器...")
        result = processor.analyze_shader(
            test_shader,
            save_reports=True,
            base_filename="ui_test_shader"
        )
        
        print("   ✅ 着色器分析完成")
        
        # 获取关键指标
        metrics = processor.get_key_metrics(result)
        print(f"   📊 关键指标:")
        print(f"      总节点数: {metrics.get('total_nodes', 0)}")
        print(f"      变量声明: {metrics.get('total_variables', 0)}")
        print(f"      类型转换: {metrics.get('total_type_conversions', 0)}")
        print(f"      准确性评分: {metrics.get('precision_accuracy_score', 0):.1f}%")
        
        # 获取优化建议
        suggestions = processor.get_optimization_suggestions(result)
        print(f"   💡 优化建议 ({len(suggestions)} 条):")
        for i, suggestion in enumerate(suggestions[:3], 1):
            print(f"      {i}. {suggestion}")
        
        # 检查生成的文件
        if 'files' in result:
            print(f"   📄 生成的文件:")
            for file_type, file_path in result['files'].items():
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"      {file_type}: {file_path} ({file_size:,} 字节)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 分析工作流程测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_integration():
    """测试集成功能"""
    print("\n🔗 测试集成功能")
    print("=" * 50)
    
    try:
        # 测试UI组件与分析器的集成
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        
        app = QApplication([])
        
        # 创建统一分析组件
        from ui.unified_shader_analysis_widget import UnifiedShaderAnalysisWidget
        widget = UnifiedShaderAnalysisWidget()
        print("   ✅ 统一分析组件创建成功")
        
        # 测试组件的方法
        methods_to_test = [
            'init_ui',
            'create_overview_tab',
            'create_details_tab',
            'create_suggestions_tab'
        ]
        
        for method in methods_to_test:
            if hasattr(widget, method):
                print(f"   ✅ {method} 方法存在")
            else:
                print(f"   ❌ {method} 方法缺失")
                return False
        
        # 测试分析工作线程
        from ui.unified_shader_analysis_widget import UnifiedAnalysisWorker
        worker = UnifiedAnalysisWorker("float a = b + c;")
        print("   ✅ UnifiedAnalysisWorker 创建成功")
        
        # 测试指标卡片
        from ui.unified_shader_analysis_widget import MetricCard
        card = MetricCard("测试", "100", "测试描述")
        print("   ✅ MetricCard 创建成功")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"   ❌ 集成功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎨 UI系统启动测试")
    print("=" * 60)
    print("测试内容:")
    print("• 🚀 UI启动测试")
    print("• 🔄 分析工作流程测试")
    print("• 🔗 集成功能测试")
    print()
    
    # 执行测试
    ui_ok = test_ui_launch()
    workflow_ok = test_analysis_workflow()
    integration_ok = test_integration()
    
    print(f"\n🎉 测试结果总结")
    print("=" * 60)
    
    if ui_ok:
        print("✅ UI启动测试通过")
        print("   • 主窗口创建和显示正常")
        print("   • 统一分析组件创建正常")
    else:
        print("❌ UI启动测试失败")
    
    if workflow_ok:
        print("✅ 分析工作流程测试通过")
        print("   • 着色器分析正常")
        print("   • 关键指标获取正常")
        print("   • 优化建议生成正常")
        print("   • 报告文件生成正常")
    else:
        print("❌ 分析工作流程测试失败")
    
    if integration_ok:
        print("✅ 集成功能测试通过")
        print("   • UI组件集成正常")
        print("   • 所有必要方法存在")
        print("   • 工作线程创建正常")
    else:
        print("❌ 集成功能测试失败")
    
    if all([ui_ok, workflow_ok, integration_ok]):
        print(f"\n🎊 UI系统完全正常！")
        print("现在可以启动完整的应用程序:")
        print("• 🎯 统一的着色器分析界面")
        print("• 📊 完整的分析结果展示")
        print("• 🌐 集成的HTML报告功能")
        print("• ⚡ 异步分析处理")
        print("• 🎨 现代化的UI设计")
        print("\n启动命令: python ui/main_window.py")
    else:
        print(f"\n❌ UI系统存在问题，请检查失败的测试项")

if __name__ == "__main__":
    main()
