#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试着色器分析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_analysis_core():
    """测试分析核心功能"""
    print("测试着色器分析核心功能...")
    
    # 读取测试着色器文件
    try:
        with open('metal_shader_ps', 'r', encoding='utf-8') as f:
            shader_content = f.read()
    except FileNotFoundError:
        print("错误: 找不到测试着色器文件 'metal_shader_ps'")
        return False
    
    # 测试分析处理器
    try:
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        processor = ShaderAnalysisProcessor()
        result = processor.analyze_shader(shader_content, save_reports=True)
        
        print("✅ 分析完成!")
        print(f"总运算操作: {result['analysis']['statistics']['total_operations']}")
        print(f"精度转换: {result['analysis']['statistics']['precision_conversions']}")
        print(f"混合精度运算: {result['analysis']['statistics']['mixed_precision_ops']}")
        print(f"高影响操作: {result['analysis']['statistics']['high_impact_ops']}")
        
        # 检查文件是否生成
        files = result.get('files', {})
        for file_type, file_path in files.items():
            if os.path.exists(file_path):
                print(f"✅ {file_type.upper()}报告已生成: {file_path}")
            else:
                print(f"❌ {file_type.upper()}报告生成失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_components():
    """测试UI组件"""
    print("\n测试UI组件...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from ui.shader_analysis_widget import ShaderAnalysisWidget
        
        app = QApplication(sys.argv)
        
        # 创建分析组件
        widget = ShaderAnalysisWidget()
        widget.show()
        
        print("✅ UI组件创建成功")
        
        # 不运行事件循环，只是测试创建
        widget.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ UI组件测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔍 开始测试着色器分析功能")
    print("=" * 50)
    
    # 测试核心功能
    core_success = test_analysis_core()
    
    # 测试UI组件
    ui_success = test_ui_components()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"核心功能: {'✅ 通过' if core_success else '❌ 失败'}")
    print(f"UI组件: {'✅ 通过' if ui_success else '❌ 失败'}")
    
    if core_success and ui_success:
        print("\n🎉 所有测试通过! 着色器分析功能已准备就绪")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
