#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试基于语法树的精确类型分析器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_precise_type_analysis():
    """测试精确类型分析"""
    print("🎯 精确类型分析测试")
    print("=" * 60)
    
    from Process.Analysis.tree_based_type_analyzer import TreeBasedTypeAnalyzer
    
    # 测试用例：a = b + c 应该识别4个类型变量
    test_shader = """
float a = b + c;
half d = half(intensity);
float3 result = normalize(pos) * scale;
"""
    
    analyzer = TreeBasedTypeAnalyzer()
    result = analyzer.analyze_shader_with_precise_types(test_shader)
    
    print("精确类型分析结果:")
    print(analyzer.format_precise_analysis(result))
    
    return result

def test_intermediate_results_detection():
    """测试中间结果检测"""
    print("\n" + "=" * 60)
    print("🔸 中间结果检测测试")
    print("=" * 60)
    
    from Process.Analysis.tree_based_type_analyzer import TreeBasedTypeAnalyzer
    
    # 复杂表达式测试
    test_shader = """
float a = b + c;
float d = (a * 2.0) + (b / c);
half result = half(max(a, d) * intensity);
"""
    
    analyzer = TreeBasedTypeAnalyzer()
    result = analyzer.analyze_shader_with_precise_types(test_shader)
    
    print("中间结果检测:")
    print("-" * 30)
    
    for i, analysis in enumerate(result['precise_analyses']):
        code_line = result['code_lines'][i]
        print(f"\n第{i+1}行: {code_line.content}")
        
        print("所有节点:")
        for typed_node in analysis.typed_nodes:
            node_type = typed_node.node.node_type.value
            node_value = typed_node.node.value
            inferred_type = typed_node.inferred_type.value
            is_intermediate = "中间结果" if typed_node.is_intermediate else "直接节点"
            
            print(f"  • {node_type}({node_value}) → {inferred_type} [{is_intermediate}]")
        
        print(f"中间结果数量: {len(analysis.intermediate_results)}")

def test_comparison_with_original():
    """与原始分析方法对比测试"""
    print("\n" + "=" * 60)
    print("📊 与原始方法对比测试")
    print("=" * 60)
    
    from Process.Analysis.tree_based_type_analyzer import TreeBasedTypeAnalyzer
    from Process.Analysis.code_line_analyzer import CodeLineAnalyzer
    
    # 测试用例
    test_cases = [
        "float a = b + c;",           # 应该是4个节点：a, b, c, (b+c)
        "half d = half(intensity);",  # 应该是3个节点：d, intensity, half(intensity)
        "float result = max(a, b);",  # 应该是4个节点：result, a, b, max(a,b)
        "pos = normalize(worldPos) * scale;", # 应该是5个节点：pos, worldPos, normalize(worldPos), scale, normalize(worldPos)*scale
    ]
    
    tree_analyzer = TreeBasedTypeAnalyzer()
    line_analyzer = CodeLineAnalyzer()
    
    print("对比分析结果:")
    print("-" * 40)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试{i}: {test_case}")
        
        # 原始方法
        original_lines = line_analyzer.analyze_shader_code(test_case)
        original_count = original_lines[0].variables if original_lines else 0
        
        # 精确方法
        precise_result = tree_analyzer.analyze_shader_with_precise_types(test_case)
        if precise_result['precise_analyses']:
            precise_count = precise_result['precise_analyses'][0].statistics['total_nodes']
            intermediate_count = precise_result['precise_analyses'][0].statistics['intermediate_results']
        else:
            precise_count = 0
            intermediate_count = 0
        
        print(f"  原始方法识别变量: {original_count}")
        print(f"  精确方法识别节点: {precise_count} (其中中间结果: {intermediate_count})")
        print(f"  改进程度: +{precise_count - original_count} 个节点")

def test_detailed_node_analysis():
    """测试详细节点分析"""
    print("\n" + "=" * 60)
    print("🔍 详细节点分析测试")
    print("=" * 60)
    
    from Process.Analysis.tree_based_type_analyzer import TreeBasedTypeAnalyzer
    
    test_shader = """
float3 pos = worldMatrix * localPos;
half4 color = texture.sample(sampler, uv);
float intensity = dot(normal, lightDir);
half3 result = color.rgb * half(intensity);
"""
    
    analyzer = TreeBasedTypeAnalyzer()
    result = analyzer.analyze_shader_with_precise_types(test_shader)
    
    print("详细节点分析:")
    print("-" * 30)
    
    for i in range(len(result['code_lines'])):
        detailed = analyzer.get_detailed_node_analysis(result, i)
        
        print(f"\n第{i+1}行详细分析:")
        print(f"代码: {detailed['line_info']['content']}")
        print(f"原始统计: 变量{detailed['line_info']['original_stats']['variables']}")
        print(f"精确统计: 节点{detailed['precise_stats']['total_nodes']}, 中间结果{detailed['precise_stats']['intermediate_results']}")
        
        print("节点详情:")
        for node_info in detailed['typed_nodes']:
            marker = "🔸" if node_info['is_intermediate'] else "🔹"
            print(f"  {marker} {node_info['type']}({node_info['value']}) → {node_info['inferred_type']} (置信度: {node_info['confidence']:.1f})")

def test_type_inference_accuracy():
    """测试类型推断准确性"""
    print("\n" + "=" * 60)
    print("🎯 类型推断准确性测试")
    print("=" * 60)
    
    from Process.Analysis.tree_based_type_analyzer import TreeBasedTypeAnalyzer
    
    # 包含各种类型推断场景的测试
    test_shader = """
float a = 1.0;
half b = 0.5;
float c = a + b;
half d = half(c);
float3 pos = normalize(float3(1.0, 2.0, 3.0));
half dotResult = dot(pos, pos);
float maxResult = max(a, c);
"""
    
    analyzer = TreeBasedTypeAnalyzer()
    result = analyzer.analyze_shader_with_precise_types(test_shader)
    
    print("类型推断准确性分析:")
    print("-" * 30)
    
    overall_stats = result['overall_statistics']
    print(f"精度准确性评分: {overall_stats['precision_accuracy_score']:.1f}%")
    print(f"总节点数: {overall_stats['total_nodes']}")
    print(f"类型转换: {overall_stats['total_type_conversions']}")
    print(f"精度问题: {overall_stats['total_precision_issues']}")
    
    print(f"\n类型分布:")
    for type_name, count in sorted(overall_stats['type_distribution'].items()):
        percentage = count / overall_stats['total_nodes'] * 100
        print(f"  {type_name}: {count} ({percentage:.1f}%)")
    
    print(f"\n全局类型表:")
    for var_name, var_type in sorted(result['global_type_table'].items()):
        print(f"  {var_name}: {var_type.value}")

def test_performance_comparison():
    """性能对比测试"""
    print("\n" + "=" * 60)
    print("⚡ 性能对比测试")
    print("=" * 60)
    
    import time
    from Process.Analysis.tree_based_type_analyzer import TreeBasedTypeAnalyzer
    from Process.Analysis.code_line_analyzer import CodeLineAnalyzer
    
    # 大型测试用例
    large_shader = """
float3 worldPos = transform * localPos;
half4 baseColor = texture.sample(sampler, uv);
float3 normal = normalize(normalMatrix * localNormal);
float3 lightDir = normalize(lightPos - worldPos);
float dotNL = dot(normal, lightDir);
half3 diffuse = baseColor.rgb * half(max(dotNL, 0.0));
float3 viewDir = normalize(cameraPos - worldPos);
float3 halfDir = normalize(lightDir + viewDir);
float dotNH = dot(normal, halfDir);
half specular = half(pow(max(dotNH, 0.0), shininess));
half3 finalColor = diffuse + half3(specular);
""" * 5  # 重复5次增加复杂度
    
    print("性能测试 (重复分析大型着色器):")
    print("-" * 30)
    
    # 测试原始方法
    line_analyzer = CodeLineAnalyzer()
    start_time = time.time()
    for _ in range(10):
        original_result = line_analyzer.analyze_shader_code(large_shader)
    original_time = time.time() - start_time
    
    # 测试精确方法
    tree_analyzer = TreeBasedTypeAnalyzer()
    start_time = time.time()
    for _ in range(10):
        precise_result = tree_analyzer.analyze_shader_with_precise_types(large_shader)
    precise_time = time.time() - start_time
    
    print(f"原始方法耗时: {original_time:.3f}s")
    print(f"精确方法耗时: {precise_time:.3f}s")
    print(f"性能比率: {precise_time/original_time:.2f}x")
    
    # 分析结果对比
    original_vars = sum(line.variables for line in original_result)
    precise_nodes = precise_result['overall_statistics']['total_nodes']
    
    print(f"\n结果对比:")
    print(f"原始方法识别变量: {original_vars}")
    print(f"精确方法识别节点: {precise_nodes}")
    print(f"精度提升: {(precise_nodes - original_vars) / original_vars * 100:.1f}%")

def main():
    """主函数"""
    print("🚀 基于语法树的精确类型分析器测试")
    print("=" * 70)
    print("核心改进:")
    print("• 🎯 基于语法树的精确节点类型分析")
    print("• 🔸 识别中间结果节点 (如 a+b 的结果)")
    print("• 📊 更准确的类型统计 (a=b+c 识别4个节点而非3个)")
    print("• 🔍 详细的类型推断置信度")
    print("• ⚡ 高性能的递归分析算法")
    print()
    
    try:
        test_precise_type_analysis()
        test_intermediate_results_detection()
        test_comparison_with_original()
        test_detailed_node_analysis()
        test_type_inference_accuracy()
        test_performance_comparison()
        
        print("\n" + "=" * 70)
        print("🎉 所有测试完成！")
        print("基于语法树的精确类型分析器已准备就绪。")
        print("\n主要优势:")
        print("✅ 更精确的节点识别 (包括中间结果)")
        print("✅ 智能的类型推断和置信度评估")
        print("✅ 详细的类型转换和精度问题检测")
        print("✅ 完整的全局类型表维护")
        print("✅ 高性能的分析算法")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
