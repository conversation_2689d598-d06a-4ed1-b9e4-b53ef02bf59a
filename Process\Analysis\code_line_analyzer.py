#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码行分析器 - 识别和分析着色器代码中的有效代码行
"""

import re
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass

@dataclass
class CodeLineInfo:
    """代码行信息"""
    line_number: int          # 原始行号
    content: str             # 代码内容
    variables: int           # 变量数量
    float_vars: int          # float变量数量
    half_vars: int           # half变量数量
    conversions: int         # 类型转换数量
    has_conversion: bool     # 是否包含类型转换
    all_variables: List[str] # 所有变量列表
    conversion_details: List[Dict] # 转换详情

class CodeLineAnalyzer:
    """代码行分析器"""
    
    def __init__(self):
        # 需要跳过的行模式
        self.skip_patterns = [
            r'^\s*$',                    # 空行
            r'^\s*//.*$',                # 注释行
            r'^\s*#.*$',                 # 预处理指令
            r'^\s*\{?\s*$',              # 只有大括号的行
            r'^\s*\}?\s*$',              # 只有大括号的行
            # r'^\s*(if|else|for|while|do)\s*\(',  # 控制结构开始 - 注释掉，让if语句也被识别
            r'^\s*(else)\s*$',           # else关键字
            r'^\s*(struct|class|enum)\s+\w+\s*\{?\s*$',  # 结构体声明
            r'^\s*(vertex|fragment|kernel)\s+\w+.*\[\[.*\]\].*\{?\s*$',  # 函数声明
        ]
        
        # 编译正则表达式以提高性能
        self.compiled_skip_patterns = [re.compile(pattern) for pattern in self.skip_patterns]
    
    def is_code_line(self, line: str) -> bool:
        """判断是否是有效的代码行"""
        line = line.strip()
        
        # 空行直接跳过
        if not line:
            return False
        
        # 检查跳过模式
        for pattern in self.compiled_skip_patterns:
            if pattern.match(line):
                return False
        
        # 检查是否包含赋值、函数调用或return语句
        code_indicators = [
            r'=',                        # 赋值
            r'\w+\s*\(',                # 函数调用
            r'return\s+',               # return语句
            r'\+\+|--',                 # 自增自减
            r'[+\-*/]',                 # 算术运算
        ]
        
        for indicator in code_indicators:
            if re.search(indicator, line):
                return True
        
        return False
    
    def analyze_line_variables(self, line: str) -> Dict:
        """分析单行代码的变量使用情况"""
        analysis = {
            'total_variables': 0,
            'float_variables': 0,
            'half_variables': 0,
            'type_conversions': 0,
            'variables_by_type': {},
            'type_conversion_details': [],
            'all_variables': []
        }
        
        # 移除注释
        clean_line = re.sub(r'//.*$', '', line)
        
        # 1. 统计类型声明变量
        type_declarations = re.findall(
            r'\b(half|float|half2|half3|half4|float2|float3|float4|float3x3|float4x4)\s+([a-zA-Z_][a-zA-Z0-9_]*)', 
            clean_line
        )
        
        for type_name, var_name in type_declarations:
            analysis['total_variables'] += 1
            analysis['all_variables'].append(var_name)
            
            if type_name.startswith('float'):
                analysis['float_variables'] += 1
            elif type_name.startswith('half'):
                analysis['half_variables'] += 1
            
            if type_name not in analysis['variables_by_type']:
                analysis['variables_by_type'][type_name] = []
            analysis['variables_by_type'][type_name].append(var_name)
        
        # 2. 统计所有变量使用（包括成员访问）
        variable_matches = re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)', clean_line)
        
        # 过滤掉关键字和函数名
        keywords = {
            'half', 'float', 'half2', 'half3', 'half4', 'float2', 'float3', 'float4',
            'float3x3', 'float4x4', 'return', 'if', 'else', 'for', 'while', 'do',
            'true', 'false', 'void', 'const', 'static', 'inline'
        }
        
        # 函数名模式（通常后面跟括号）
        function_pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\('
        function_names = set(re.findall(function_pattern, clean_line))
        
        unique_variables = set()
        for var in variable_matches:
            var_base = var.split('.')[0]
            if var_base not in keywords and var_base not in function_names:
                unique_variables.add(var)
        
        # 如果没有类型声明，统计所有变量使用
        if not type_declarations:
            analysis['total_variables'] = len(unique_variables)
            analysis['all_variables'] = list(unique_variables)
        else:
            # 如果有类型声明，添加其他使用的变量
            for var in unique_variables:
                if var not in analysis['all_variables']:
                    analysis['total_variables'] += 1
                    analysis['all_variables'].append(var)
        
        # 3. 查找类型转换
        conversion_patterns = [
            r'\b(half|float|half2|half3|half4|float2|float3|float4)\s*\(\s*([^)]+)\s*\)',
        ]
        
        for pattern in conversion_patterns:
            conversions = re.findall(pattern, clean_line)
            for target_type, source_expr in conversions:
                source_expr = source_expr.strip()
                # 检查是否真的是类型转换（不是构造函数调用）
                if not re.match(r'^[\d\.\,\s]+$', source_expr):
                    analysis['type_conversions'] += 1
                    analysis['type_conversion_details'].append({
                        'target_type': target_type,
                        'source_expression': source_expr,
                        'line_position': clean_line.find(f"{target_type}({source_expr})")
                    })
        
        return analysis
    
    def analyze_shader_code(self, shader_content: str) -> List[CodeLineInfo]:
        """分析着色器代码，返回所有有效代码行的分析结果"""
        lines = shader_content.split('\n')
        code_lines = []
        
        for i, line in enumerate(lines, 1):
            if self.is_code_line(line):
                # 分析变量使用情况
                var_analysis = self.analyze_line_variables(line)
                
                # 创建代码行信息
                code_line = CodeLineInfo(
                    line_number=i,
                    content=line.strip(),
                    variables=var_analysis['total_variables'],
                    float_vars=var_analysis['float_variables'],
                    half_vars=var_analysis['half_variables'],
                    conversions=var_analysis['type_conversions'],
                    has_conversion=var_analysis['type_conversions'] > 0,
                    all_variables=var_analysis['all_variables'],
                    conversion_details=var_analysis['type_conversion_details']
                )
                
                code_lines.append(code_line)
        
        return code_lines
    
    def format_analysis_result(self, code_lines: List[CodeLineInfo]) -> str:
        """格式化分析结果为可读文本"""
        result = []
        result.append(f"识别到 {len(code_lines)} 行有效代码:")
        result.append("=" * 60)
        
        for code_line in code_lines:
            # 构建标签
            tags = []
            if code_line.has_conversion:
                tags.append("[类型转换]")
            
            # 构建变量统计信息
            var_info = f"[变量: {code_line.variables} | float: {code_line.float_vars} | half: {code_line.half_vars} | 转换: {code_line.conversions}]"
            
            # 格式化输出
            tags_str = " ".join(tags) + " " if tags else ""
            result.append(f"{code_line.content} {tags_str}{var_info}")
        
        return "\n".join(result)
    
    def get_summary_statistics(self, code_lines: List[CodeLineInfo]) -> Dict:
        """获取总体统计信息"""
        total_lines = len(code_lines)
        total_variables = sum(line.variables for line in code_lines)
        total_float_vars = sum(line.float_vars for line in code_lines)
        total_half_vars = sum(line.half_vars for line in code_lines)
        total_conversions = sum(line.conversions for line in code_lines)
        lines_with_conversions = sum(1 for line in code_lines if line.has_conversion)
        
        return {
            'total_code_lines': total_lines,
            'total_variables': total_variables,
            'total_float_variables': total_float_vars,
            'total_half_variables': total_half_vars,
            'total_conversions': total_conversions,
            'lines_with_conversions': lines_with_conversions,
            'avg_variables_per_line': total_variables / total_lines if total_lines > 0 else 0,
            'conversion_rate': lines_with_conversions / total_lines if total_lines > 0 else 0
        }


# 测试函数
def test_code_line_analyzer():
    """测试代码行分析器"""
    test_shader = """
float3 _12378 = _12345.xyz;
float _12380 = _12378.x;
float _19350 = _12380 * 2.0;
if (_12381 > 0.5)
{
    float _inner1 = _19350 + 1.0;
    half _inner2 = half(_inner1);
    _result = _inner2 * 0.5;
}
else
{
    _result = 0.0;
}
"""
    
    analyzer = CodeLineAnalyzer()
    code_lines = analyzer.analyze_shader_code(test_shader)
    
    print("🔍 代码行分析器测试")
    print("=" * 50)
    print(analyzer.format_analysis_result(code_lines))
    
    print("\n📊 统计信息:")
    stats = analyzer.get_summary_statistics(code_lines)
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.2f}")
        else:
            print(f"  {key}: {value}")

if __name__ == "__main__":
    test_code_line_analyzer()
