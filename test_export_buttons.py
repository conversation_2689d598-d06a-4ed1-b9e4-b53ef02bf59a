#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导出按钮功能
"""

import sys
import os
import tempfile
import json
from datetime import datetime

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_html_generation():
    """测试HTML生成功能"""
    print("🌐 测试HTML报告生成")
    print("=" * 50)
    
    try:
        # 添加UI路径
        ui_path = os.path.join(os.path.dirname(__file__), 'UI')
        if ui_path not in sys.path:
            sys.path.append(ui_path)

        from precise_type_analysis_widget import PreciseTypeAnalysisWidget
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        # 创建组件
        widget = PreciseTypeAnalysisWidget()
        processor = ShaderAnalysisProcessor()
        
        # 模拟分析结果
        test_shader = """
float3 worldPos = transform * localPos;
half4 baseColor = texture.sample(sampler, uv);
float dotNL = dot(normal, lightDir);
"""
        
        print("   正在进行分析...")
        result = processor.analyze_shader(test_shader, save_reports=False)
        
        # 设置分析结果
        widget.analysis_result = result
        
        # 测试HTML生成
        print("   测试HTML报告生成...")
        
        # 检查是否有分析数据
        analysis_data = result.get('analysis', {})
        precise_data = analysis_data.get('precise_analysis', {})
        
        if precise_data:
            print("   ✅ 有精确分析数据")
            
            # 测试HTML内容生成
            html_content = widget._generate_html_report(precise_data)
            
            if html_content and len(html_content) > 1000:
                print("   ✅ HTML内容生成成功")
                
                # 测试保存到临时文件
                temp_dir = tempfile.gettempdir()
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                html_filename = f"test_shader_analysis_{timestamp}.html"
                html_path = os.path.join(temp_dir, html_filename)
                
                with open(html_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                
                if os.path.exists(html_path):
                    print(f"   ✅ HTML文件保存成功: {html_filename}")
                    print(f"   📁 文件位置: {html_path}")
                    
                    # 检查文件大小
                    file_size = os.path.getsize(html_path)
                    print(f"   📊 文件大小: {file_size} 字节")
                    
                    return True
                else:
                    print("   ❌ HTML文件保存失败")
                    return False
            else:
                print("   ❌ HTML内容生成失败")
                return False
        else:
            print("   ❌ 没有精确分析数据")
            return False
            
    except Exception as e:
        print(f"   ❌ HTML生成测试失败: {str(e)}")
        return False

def test_json_export_data():
    """测试JSON导出数据准备"""
    print("\n📄 测试JSON导出数据准备")
    print("=" * 50)
    
    try:
        from UI.precise_type_analysis_widget import PreciseTypeAnalysisWidget
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        # 创建组件
        widget = PreciseTypeAnalysisWidget()
        processor = ShaderAnalysisProcessor()
        
        # 模拟分析结果
        test_shader = """
float4 vertex_main(float3 position : POSITION) : SV_POSITION {
    float4x4 mvp = mul(model, view_proj);
    return mul(float4(position, 1.0), mvp);
}
"""
        
        print("   正在进行分析...")
        result = processor.analyze_shader(test_shader, save_reports=False)
        
        # 设置分析结果
        widget.analysis_result = result
        
        # 准备导出数据（模拟export_json_data中的逻辑）
        export_data = {
            'timestamp': datetime.now().isoformat(),
            'analysis_method': result.get('analysis_method', 'precise_tree_based'),
            'analysis': result.get('analysis', {}),
            'metadata': {
                'shader_length': len(result.get('shader_content', '')),
                'export_version': '1.0'
            }
        }
        
        # 验证导出数据结构
        required_keys = ['timestamp', 'analysis_method', 'analysis', 'metadata']
        for key in required_keys:
            if key in export_data:
                print(f"   ✅ 包含 {key}")
            else:
                print(f"   ❌ 缺少 {key}")
                return False
        
        # 检查分析数据
        if 'precise_analysis' in export_data['analysis']:
            print("   ✅ 包含精确分析数据")
        else:
            print("   ❌ 缺少精确分析数据")
            return False
        
        # 测试JSON序列化
        try:
            json_str = json.dumps(export_data, ensure_ascii=False, indent=2)
            print(f"   ✅ JSON序列化成功 ({len(json_str)} 字符)")
            
            # 测试保存到临时文件
            temp_dir = tempfile.gettempdir()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            json_filename = f"test_shader_analysis_{timestamp}.json"
            json_path = os.path.join(temp_dir, json_filename)
            
            with open(json_path, 'w', encoding='utf-8') as f:
                f.write(json_str)
            
            if os.path.exists(json_path):
                print(f"   ✅ JSON文件保存成功: {json_filename}")
                print(f"   📁 文件位置: {json_path}")
                
                # 验证文件内容
                with open(json_path, 'r', encoding='utf-8') as f:
                    loaded_data = json.load(f)
                
                if loaded_data == export_data:
                    print("   ✅ JSON文件内容验证成功")
                    return True
                else:
                    print("   ❌ JSON文件内容验证失败")
                    return False
            else:
                print("   ❌ JSON文件保存失败")
                return False
                
        except Exception as e:
            print(f"   ❌ JSON序列化失败: {str(e)}")
            return False
            
    except Exception as e:
        print(f"   ❌ JSON导出测试失败: {str(e)}")
        return False

def test_button_functionality():
    """测试按钮功能（模拟）"""
    print("\n🔘 测试按钮功能")
    print("=" * 50)
    
    try:
        from UI.precise_type_analysis_widget import PreciseTypeAnalysisWidget
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        # 创建组件
        widget = PreciseTypeAnalysisWidget()
        processor = ShaderAnalysisProcessor()
        
        # 模拟分析结果
        test_shader = "float a = b + c;"
        result = processor.analyze_shader(test_shader, save_reports=False)
        widget.analysis_result = result
        
        # 测试HTML按钮功能（不实际打开浏览器）
        print("   测试HTML按钮功能...")
        try:
            # 模拟open_html_report的核心逻辑
            analysis_data = widget.analysis_result.get('analysis', {})
            precise_data = analysis_data.get('precise_analysis', {})
            
            if precise_data:
                html_content = widget._generate_html_report(precise_data)
                if html_content:
                    print("   ✅ HTML按钮功能正常")
                else:
                    print("   ❌ HTML生成失败")
                    return False
            else:
                print("   ❌ 没有精确分析数据")
                return False
        except Exception as e:
            print(f"   ❌ HTML按钮测试失败: {str(e)}")
            return False
        
        # 测试JSON按钮功能（不实际弹出对话框）
        print("   测试JSON按钮功能...")
        try:
            # 模拟export_json_data的核心逻辑
            export_data = {
                'timestamp': datetime.now().isoformat(),
                'analysis_method': widget.analysis_result.get('analysis_method', 'precise_tree_based'),
                'analysis': widget.analysis_result.get('analysis', {}),
                'metadata': {
                    'shader_length': len(widget.analysis_result.get('shader_content', '')),
                    'export_version': '1.0'
                }
            }
            
            json_str = json.dumps(export_data, ensure_ascii=False, indent=2)
            if json_str:
                print("   ✅ JSON按钮功能正常")
            else:
                print("   ❌ JSON数据准备失败")
                return False
        except Exception as e:
            print(f"   ❌ JSON按钮测试失败: {str(e)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 按钮功能测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 导出按钮功能测试")
    print("=" * 60)
    print("测试内容:")
    print("• 🌐 HTML报告生成测试")
    print("• 📄 JSON导出数据准备测试")
    print("• 🔘 按钮功能测试")
    print()
    
    # 执行测试
    html_ok = test_html_generation()
    json_ok = test_json_export_data()
    button_ok = test_button_functionality()
    
    print(f"\n🎉 测试结果总结")
    print("=" * 60)
    
    if html_ok:
        print("✅ HTML报告生成测试通过")
        print("   • HTML内容生成正常")
        print("   • 保存到临时文件夹成功")
        print("   • 文件格式正确")
    else:
        print("❌ HTML报告生成测试失败")
    
    if json_ok:
        print("✅ JSON导出数据测试通过")
        print("   • 数据结构完整")
        print("   • JSON序列化正常")
        print("   • 文件保存成功")
    else:
        print("❌ JSON导出数据测试失败")
    
    if button_ok:
        print("✅ 按钮功能测试通过")
        print("   • HTML按钮逻辑正常")
        print("   • JSON按钮逻辑正常")
    else:
        print("❌ 按钮功能测试失败")
    
    if all([html_ok, json_ok, button_ok]):
        print(f"\n🎊 导出功能修复完成！")
        print("现在的功能:")
        print("• 🌐 HTML报告: 自动保存到临时文件夹并打开")
        print("• 📄 JSON导出: 弹窗选择保存路径")
        print("• 📊 数据来源: 内存中的分析结果")
        print("• 🔧 错误处理: 完善的异常处理")
        print("\n可以启动UI进行实际测试！")
    else:
        print(f"\n❌ 导出功能修复未完全完成，请检查失败的测试项")

if __name__ == "__main__":
    main()
