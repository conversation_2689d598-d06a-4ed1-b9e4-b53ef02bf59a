#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI集成 - 启动主窗口并测试着色器分析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    try:
        # 导入主窗口
        from ui.main_window import MainWindow
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        # 自动加载测试文件（如果存在）
        if os.path.exists('metal_shader_ps'):
            QTimer.singleShot(1000, lambda: auto_load_test_file(window))
        
        # 显示使用说明
        QTimer.singleShot(2000, lambda: show_usage_info(window))
        
        sys.exit(app.exec_())
        
    except Exception as e:
        QMessageBox.critical(None, "启动失败", f"无法启动应用程序:\n{str(e)}")
        sys.exit(1)

def auto_load_test_file(window):
    """自动加载测试文件"""
    try:
        # 读取测试着色器文件
        with open('metal_shader_ps', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加到代码区域
        window.code_area_widget.add_code_page("metal_shader_ps", content, "metal_shader_ps")
        
        print("✅ 自动加载测试文件成功")
        
    except Exception as e:
        print(f"❌ 自动加载测试文件失败: {str(e)}")

def show_usage_info(window):
    """显示使用说明"""
    info_text = """
🔍 着色器分析功能使用说明

1. 📁 加载着色器文件:
   - 使用 "文件" -> "导入文件" 加载着色器文件
   - 或者直接在代码区域粘贴着色器代码

2. 🔍 执行分析:
   - 使用菜单 "处理" -> "🔍 着色器分析"
   - 或者按快捷键 Ctrl+Alt+A

3. 📊 查看结果:
   - 分析结果会显示在右侧编译区域
   - 包含性能指标、优化建议等信息
   - 可以点击按钮查看详细HTML报告

4. 📄 导出报告:
   - 点击 "📊 查看详细报告" 打开HTML可视化报告
   - 点击 "📄 导出JSON数据" 查看原始分析数据

测试文件已自动加载，您可以直接按 Ctrl+Alt+A 开始分析！
"""
    
    QMessageBox.information(window, "使用说明", info_text)

if __name__ == "__main__":
    main()
