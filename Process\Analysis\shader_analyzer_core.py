#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Metal着色器分析器核心模块 - 专注于half和float数据类型运算分析
"""

import re
import json
import os
import tempfile
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
from datetime import datetime

class OperationType(Enum):
    """运算类型枚举"""
    ASSIGNMENT = "assignment"           # 赋值
    ARITHMETIC = "arithmetic"          # 算术运算 (+, -, *, /)
    COMPARISON = "comparison"          # 比较运算 (<, >, ==, !=, <=, >=)
    FUNCTION_CALL = "function_call"    # 函数调用
    TYPE_CONVERSION = "type_conversion" # 类型转换
    VECTOR_OPERATION = "vector_op"     # 向量运算
    MATRIX_OPERATION = "matrix_op"     # 矩阵运算
    TEXTURE_SAMPLE = "texture_sample"  # 纹理采样
    CONDITIONAL = "conditional"        # 条件运算
    MIXED_PRECISION = "mixed_precision" # 混合精度运算
    VARIABLE_ANALYSIS = "variable_analysis" # 变量分析

@dataclass
class DataTypeInfo:
    """数据类型信息"""
    base_type: str      # half, float
    dimension: int      # 1=标量, 2=vec2, 3=vec3, 4=vec4
    is_matrix: bool = False
    matrix_size: Optional[Tuple[int, int]] = None

@dataclass
class OperationInfo:
    """运算信息"""
    line_number: int
    operation_type: OperationType
    input_types: List[DataTypeInfo]
    output_type: Optional[DataTypeInfo]
    operation_detail: str
    precision_conversion: Optional[str] = None  # 精度转换信息
    performance_impact: str = "low"  # low, medium, high
    variable_info: Optional[dict] = None  # 变量分析信息

class ShaderAnalyzerCore:
    """着色器分析器核心类"""
    
    def __init__(self):
        # 数据类型模式
        self.type_patterns = {
            'half': r'\bhalf\b',
            'half2': r'\bhalf2\b',
            'half3': r'\bhalf3\b', 
            'half4': r'\bhalf4\b',
            'float': r'\bfloat\b',
            'float2': r'\bfloat2\b',
            'float3': r'\bfloat3\b',
            'float4': r'\bfloat4\b',
            'float3x3': r'\bfloat3x3\b',
            'float3x4': r'\bfloat3x4\b',
            'float4x4': r'\bfloat4x4\b',
        }
        
        # 运算符模式
        self.operator_patterns = {
            'arithmetic': r'[+\-*/]',
            'comparison': r'[<>=!]=?',
            'assignment': r'=(?!=)',
        }
        
        # 函数调用模式
        self.function_patterns = {
            'math_functions': r'\b(sin|cos|tan|sqrt|rsqrt|pow|powr|exp|exp2|log|log2|abs|floor|ceil|fract|clamp|mix|dot|cross|normalize|length|distance|reflect|refract|step|smoothstep|min|max|fast::|select)\b',
            'texture_functions': r'\b\w+\.sample\(',
            'type_conversion': r'\b(half|float|half2|half3|half4|float2|float3|float4)\s*\(',
        }
        
        self.operations = []
    
    def parse_data_type(self, type_str: str) -> Optional[DataTypeInfo]:
        """解析数据类型字符串"""
        type_str = type_str.strip()
        
        # 矩阵类型
        matrix_match = re.match(r'(float|half)(\d+)x(\d+)', type_str)
        if matrix_match:
            base_type = matrix_match.group(1)
            rows = int(matrix_match.group(2))
            cols = int(matrix_match.group(3))
            return DataTypeInfo(base_type, 1, True, (rows, cols))
        
        # 向量类型
        vector_match = re.match(r'(half|float)(\d+)', type_str)
        if vector_match:
            base_type = vector_match.group(1)
            dimension = int(vector_match.group(2))
            return DataTypeInfo(base_type, dimension)
        
        # 标量类型
        if type_str in ['half', 'float']:
            return DataTypeInfo(type_str, 1)
        
        return None
    
    def analyze_line(self, line: str, line_number: int) -> List[OperationInfo]:
        """分析单行代码"""
        operations = []
        original_line = line
        line = line.strip()

        if not line or line.startswith('//') or line.startswith('#'):
            return operations

        # 跳过函数入口行
        if self._is_function_entry(line):
            return operations
        
        # 检测类型转换
        type_conversion_matches = re.finditer(r'\b(half|float|half2|half3|half4|float2|float3|float4)\s*\([^)]+\)', line)
        for match in type_conversion_matches:
            target_type = self.parse_data_type(match.group(1))
            if target_type:
                operations.append(OperationInfo(
                    line_number=line_number,
                    operation_type=OperationType.TYPE_CONVERSION,
                    input_types=[],
                    output_type=target_type,
                    operation_detail=match.group(0),
                    precision_conversion=f"转换为{target_type.base_type}",
                    performance_impact="medium" if target_type.base_type != "float" else "low"
                ))
        
        # 检测纹理采样
        texture_matches = re.finditer(r'(\w+)\.sample\([^)]+\)', line)
        for match in texture_matches:
            # 纹理采样通常返回half4或float4
            output_type = DataTypeInfo("half", 4)  # 假设为half4
            operations.append(OperationInfo(
                line_number=line_number,
                operation_type=OperationType.TEXTURE_SAMPLE,
                input_types=[],
                output_type=output_type,
                operation_detail=match.group(0),
                performance_impact="high"
            ))
        
        # 检测函数调用
        func_matches = re.finditer(r'\b(fast::)?(normalize|dot|cross|mix|clamp|min|max|sqrt|rsqrt|pow|powr|abs|floor|ceil|fract|sin|cos|tan|exp|exp2|log|log2|step|smoothstep|length|distance|reflect|refract|select)\s*\([^)]+\)', line)
        for match in func_matches:
            func_name = match.group(2) if match.group(1) else match.group(1)
            operations.append(OperationInfo(
                line_number=line_number,
                operation_type=OperationType.FUNCTION_CALL,
                input_types=[],
                output_type=None,
                operation_detail=match.group(0),
                performance_impact="medium" if match.group(1) == "fast::" else "low"
            ))
        
        # 检测算术运算
        arithmetic_matches = re.finditer(r'[+\-*/]', line)
        if arithmetic_matches:
            # 检查是否涉及half和float的混合运算
            has_half = re.search(r'\bhalf\d*\b', line)
            has_float = re.search(r'\bfloat\d*\b', line)
            
            if has_half and has_float:
                operations.append(OperationInfo(
                    line_number=line_number,
                    operation_type=OperationType.MIXED_PRECISION,
                    input_types=[],
                    output_type=None,
                    operation_detail=line,
                    precision_conversion="混合精度运算",
                    performance_impact="high"
                ))
            elif has_half or has_float:
                operations.append(OperationInfo(
                    line_number=line_number,
                    operation_type=OperationType.ARITHMETIC,
                    input_types=[],
                    output_type=None,
                    operation_detail=line,
                    performance_impact="low"
                ))
        
        # 检测变量声明和赋值
        declaration_matches = re.finditer(r'\b(half|float|half2|half3|half4|float2|float3|float4|float3x3|float3x4|float4x4)\s+(\w+)\s*=', line)
        for match in declaration_matches:
            var_type = self.parse_data_type(match.group(1))
            if var_type:
                operations.append(OperationInfo(
                    line_number=line_number,
                    operation_type=OperationType.ASSIGNMENT,
                    input_types=[],
                    output_type=var_type,
                    operation_detail=f"声明{var_type.base_type}变量: {match.group(2)}",
                    performance_impact="low"
                ))
        
        # 详细分析变量使用情况
        variable_analysis = self._analyze_line_variables(original_line, line_number)
        if variable_analysis:
            operations.append(OperationInfo(
                line_number=line_number,
                operation_type=OperationType.VARIABLE_ANALYSIS,
                input_types=[],
                output_type=None,
                operation_detail=original_line,
                variable_info=variable_analysis,
                performance_impact="info"
            ))

        return operations
    
    def analyze_shader_content(self, content: str) -> Dict:
        """分析着色器内容"""
        lines = content.split('\n')
        all_operations = []
        
        for i, line in enumerate(lines, 1):
            line_operations = self.analyze_line(line, i)
            all_operations.extend(line_operations)
        
        # 统计信息
        stats = {
            'total_operations': len(all_operations),
            'operation_types': {},
            'precision_conversions': 0,
            'mixed_precision_ops': 0,
            'high_impact_ops': 0,
            'texture_samples': 0,
        }
        
        for op in all_operations:
            op_type = op.operation_type.value
            stats['operation_types'][op_type] = stats['operation_types'].get(op_type, 0) + 1
            
            if op.precision_conversion:
                stats['precision_conversions'] += 1
            
            if op.operation_type == OperationType.MIXED_PRECISION:
                stats['mixed_precision_ops'] += 1
            
            if op.performance_impact == "high":
                stats['high_impact_ops'] += 1
                
            if op.operation_type == OperationType.TEXTURE_SAMPLE:
                stats['texture_samples'] += 1
        
        return {
            'operations': [
                {
                    'line': op.line_number,
                    'type': op.operation_type.value,
                    'detail': op.operation_detail,
                    'precision_conversion': op.precision_conversion,
                    'performance_impact': op.performance_impact,
                    'output_type': f"{op.output_type.base_type}{op.output_type.dimension if op.output_type.dimension > 1 else ''}" if op.output_type else None,
                    'variable_info': getattr(op, 'variable_info', None)
                }
                for op in all_operations
            ],
            'statistics': stats,
            'metadata': {
                'analysis_time': datetime.now().isoformat(),
                'total_lines': len(lines),
                'analyzer_version': '1.0'
            }
        }

    def _is_function_entry(self, line: str) -> bool:
        """检查是否是函数入口行"""
        # 检查函数声明模式
        function_patterns = [
            r'\b(vertex|fragment|kernel)\s+\w+\s+\w+\s*\(',
            r'\b\w+\s+\w+\s*\([^)]*\)\s*\[\[',
            r'^\s*\w+\s+\w+\s*\([^)]*\)\s*{?\s*$'
        ]

        for pattern in function_patterns:
            if re.search(pattern, line):
                return True
        return False

    def _analyze_line_variables(self, line: str, line_number: int) -> dict:
        """详细分析一行代码中的变量使用情况"""
        if not line.strip() or line.strip().startswith('//'):
            return None

        analysis = {
            'total_variables': 0,
            'float_variables': 0,
            'half_variables': 0,
            'type_conversions': 0,
            'variables_by_type': {},
            'type_conversion_details': [],
            'all_variables': []
        }

        # 移除注释
        clean_line = re.sub(r'//.*$', '', line)

        # 1. 统计类型声明变量
        type_declarations = re.findall(r'\b(half|float|half2|half3|half4|float2|float3|float4|float3x3|float4x4)\s+([a-zA-Z_][a-zA-Z0-9_]*)', clean_line)
        for type_name, var_name in type_declarations:
            analysis['total_variables'] += 1
            analysis['all_variables'].append(var_name)
            if type_name.startswith('float'):
                analysis['float_variables'] += 1
            elif type_name.startswith('half'):
                analysis['half_variables'] += 1

            if type_name not in analysis['variables_by_type']:
                analysis['variables_by_type'][type_name] = []
            analysis['variables_by_type'][type_name].append(var_name)

        # 2. 统计所有变量使用（包括成员访问）
        # 匹配变量名（包括下划线开头的临时变量）
        variable_matches = re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)', clean_line)

        # 过滤掉关键字和函数名
        keywords = {'half', 'float', 'half2', 'half3', 'half4', 'float2', 'float3', 'float4',
                   'float3x3', 'float4x4', 'return', 'if', 'else', 'for', 'while', 'do',
                   'normalize', 'dot', 'cross', 'mix', 'clamp', 'min', 'max', 'sqrt'}

        unique_variables = set()
        for var in variable_matches:
            # 跳过关键字和函数名
            if var.split('.')[0] not in keywords:
                unique_variables.add(var)

        # 如果没有类型声明，统计所有变量使用
        if not type_declarations:
            analysis['total_variables'] = len(unique_variables)
            analysis['all_variables'] = list(unique_variables)
        else:
            # 如果有类型声明，添加其他使用的变量
            for var in unique_variables:
                if var not in analysis['all_variables']:
                    analysis['total_variables'] += 1
                    analysis['all_variables'].append(var)

        # 3. 查找类型转换
        conversion_patterns = [
            # 显式类型转换: type(expression)
            r'\b(half|float|half2|half3|half4|float2|float3|float4)\s*\(\s*([^)]+)\s*\)',
        ]

        for pattern in conversion_patterns:
            conversions = re.findall(pattern, clean_line)
            for target_type, source_expr in conversions:
                # 检查是否真的是类型转换（不是构造函数调用）
                source_expr = source_expr.strip()
                # 如果源表达式是纯数字字面量，不算类型转换
                if not re.match(r'^[\d\.\,\s]+$', source_expr):
                    analysis['type_conversions'] += 1
                    analysis['type_conversion_details'].append({
                        'target_type': target_type,
                        'source_expression': source_expr,
                        'line_position': clean_line.find(f"{target_type}({source_expr})")
                    })

        # 如果没有找到任何变量，返回None
        if analysis['total_variables'] == 0:
            return None

        return analysis
