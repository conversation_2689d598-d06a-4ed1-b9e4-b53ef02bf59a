#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导出功能修复
"""

import sys
import os
import tempfile
import json
from datetime import datetime

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_export_functionality():
    """测试导出功能"""
    print("🔧 测试导出功能修复")
    print("=" * 50)

    try:
        from PyQt5.QtWidgets import QApplication
        from ui.precise_type_analysis_widget import PreciseTypeAnalysisWidget
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor

        # 创建QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建组件和处理器
        widget = PreciseTypeAnalysisWidget()
        processor = ShaderAnalysisProcessor()
        
        # 测试着色器代码
        test_shader = """
float4 vertex_main(float3 position : POSITION, float2 uv : TEXCOORD0) : SV_POSITION {
    float4x4 mvp = mul(model_matrix, view_proj_matrix);
    float4 world_pos = mul(float4(position, 1.0), mvp);
    return world_pos;
}

float4 pixel_main(float2 uv : TEXCOORD0) : SV_Target {
    float4 base_color = tex2D(main_texture, uv);
    float3 normal = normalize(world_normal);
    float ndotl = dot(normal, light_direction);
    return base_color * ndotl;
}
"""
        
        print("   正在进行着色器分析...")
        result = processor.analyze_shader(test_shader, save_reports=False)
        
        # 设置分析结果到组件
        widget.analysis_result = result
        
        # 检查分析结果
        if result and 'analysis' in result:
            analysis_data = result['analysis']
            if 'precise_analysis' in analysis_data:
                precise_data = analysis_data['precise_analysis']
                print("   ✅ 获得精确分析数据")
                
                # 测试HTML生成
                print("\n🌐 测试HTML报告生成")
                try:
                    html_content = widget._generate_html_report(precise_data)
                    if html_content and len(html_content) > 1000:
                        print("   ✅ HTML内容生成成功")
                        
                        # 保存到临时文件测试
                        temp_dir = tempfile.gettempdir()
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        html_filename = f"shader_analysis_{timestamp}.html"
                        html_path = os.path.join(temp_dir, html_filename)
                        
                        with open(html_path, 'w', encoding='utf-8') as f:
                            f.write(html_content)
                        
                        if os.path.exists(html_path):
                            file_size = os.path.getsize(html_path)
                            print(f"   ✅ HTML文件保存成功: {html_filename} ({file_size} 字节)")
                            print(f"   📁 保存位置: {html_path}")
                        else:
                            print("   ❌ HTML文件保存失败")
                    else:
                        print("   ❌ HTML内容生成失败")
                except Exception as e:
                    print(f"   ❌ HTML生成测试失败: {str(e)}")
                
                # 测试JSON数据准备
                print("\n📄 测试JSON数据准备")
                try:
                    export_data = {
                        'timestamp': datetime.now().isoformat(),
                        'analysis_method': result.get('analysis_method', 'precise_tree_based'),
                        'analysis': result.get('analysis', {}),
                        'metadata': {
                            'shader_length': len(result.get('shader_content', '')),
                            'export_version': '1.0'
                        }
                    }
                    
                    # 测试JSON序列化
                    json_str = json.dumps(export_data, ensure_ascii=False, indent=2)
                    if json_str:
                        print("   ✅ JSON数据序列化成功")
                        
                        # 保存测试
                        temp_dir = tempfile.gettempdir()
                        json_filename = f"shader_analysis_{timestamp}.json"
                        json_path = os.path.join(temp_dir, json_filename)
                        
                        with open(json_path, 'w', encoding='utf-8') as f:
                            f.write(json_str)
                        
                        if os.path.exists(json_path):
                            file_size = os.path.getsize(json_path)
                            print(f"   ✅ JSON文件保存成功: {json_filename} ({file_size} 字节)")
                            print(f"   📁 保存位置: {json_path}")
                        else:
                            print("   ❌ JSON文件保存失败")
                    else:
                        print("   ❌ JSON序列化失败")
                except Exception as e:
                    print(f"   ❌ JSON数据测试失败: {str(e)}")
                
                # 显示分析统计
                print("\n📊 分析结果统计")
                overall_stats = precise_data.get('overall_statistics', {})
                type_stats = precise_data.get('type_statistics', {})
                
                print(f"   总节点数: {overall_stats.get('total_nodes', 0)}")
                print(f"   变量总数: {overall_stats.get('total_variables', 0)}")
                print(f"   函数总数: {overall_stats.get('total_functions', 0)}")
                print(f"   类型种类: {len(type_stats)}")
                
                if type_stats:
                    print("   主要类型:")
                    for type_name, count in sorted(type_stats.items(), key=lambda x: x[1], reverse=True)[:5]:
                        print(f"     • {type_name}: {count}")
                
                return True
            else:
                print("   ❌ 没有精确分析数据")
                return False
        else:
            print("   ❌ 分析失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        return False

def test_button_methods():
    """测试按钮方法"""
    print("\n🔘 测试按钮方法")
    print("=" * 50)
    
    try:
        from ui.precise_type_analysis_widget import PreciseTypeAnalysisWidget
        
        # 创建组件
        widget = PreciseTypeAnalysisWidget()
        
        # 检查方法是否存在
        methods_to_check = [
            ('open_html_report', 'HTML报告按钮方法'),
            ('export_json_data', 'JSON导出按钮方法'),
            ('_generate_html_report', 'HTML生成辅助方法')
        ]
        
        all_methods_exist = True
        for method_name, description in methods_to_check:
            if hasattr(widget, method_name):
                print(f"   ✅ {description} 存在")
            else:
                print(f"   ❌ {description} 不存在")
                all_methods_exist = False
        
        return all_methods_exist
        
    except Exception as e:
        print(f"   ❌ 按钮方法测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🎯 导出功能修复验证")
    print("=" * 60)
    print("验证内容:")
    print("• 🔧 导出功能完整性测试")
    print("• 🔘 按钮方法存在性测试")
    print("• 🌐 HTML报告生成和保存")
    print("• 📄 JSON数据导出准备")
    print()
    
    # 执行测试
    export_ok = test_export_functionality()
    methods_ok = test_button_methods()
    
    print(f"\n🎉 验证结果总结")
    print("=" * 60)
    
    if export_ok:
        print("✅ 导出功能测试通过")
        print("   • 分析数据获取正常")
        print("   • HTML报告生成成功")
        print("   • JSON数据准备完成")
        print("   • 文件保存功能正常")
    else:
        print("❌ 导出功能测试失败")
    
    if methods_ok:
        print("✅ 按钮方法测试通过")
        print("   • 所有必要方法都存在")
    else:
        print("❌ 按钮方法测试失败")
    
    if export_ok and methods_ok:
        print(f"\n🎊 导出功能修复验证通过！")
        print("修复内容:")
        print("• 🌐 HTML报告: 自动保存到临时文件夹 (AppData/Temp)")
        print("• 📄 JSON导出: 从内存数据生成，支持用户选择保存路径")
        print("• 🔧 错误处理: 完善的异常处理和状态提示")
        print("• 📊 数据来源: 直接使用内存中的分析结果")
        print("\n现在可以启动UI测试实际的按钮功能！")
        print("启动命令: python ui/main_window.py")
    else:
        print(f"\n❌ 导出功能修复验证未完全通过，请检查失败项")

if __name__ == "__main__":
    main()
