
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Metal着色器Half/Float运算分析报告</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007acc;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4fc3f7;
        }
        
        .stat-label {
            color: #cccccc;
            margin-top: 5px;
        }
        
        .chart-container {
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .bar-chart {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .bar-item {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .bar-label {
            width: 150px;
            text-align: right;
        }
        
        .bar {
            height: 25px;
            background: linear-gradient(90deg, #4fc3f7, #29b6f6);
            border-radius: 4px;
            display: flex;
            align-items: center;
            padding: 0 10px;
            color: white;
            font-weight: bold;
        }
        
        .suggestions {
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .suggestion-item {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 193, 7, 0.1);
            border-left: 4px solid #ffc107;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Metal着色器Half/Float运算分析报告</h1>
        <p>深度分析着色器中的数据类型转换和运算模式</p>
        <p>生成时间: 2025-08-01 17:23:43</p>
    </div>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">44</div>
            <div class="stat-label">总运算操作</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">7</div>
            <div class="stat-label">精度转换</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">1</div>
            <div class="stat-label">混合精度运算</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">2</div>
            <div class="stat-label">高影响操作</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">1</div>
            <div class="stat-label">纹理采样</div>
        </div>
    </div>
    
    <div class="chart-container">
        <h3>📊 运算类型分布</h3>
        <div class="bar-chart">

            <div class="bar-item">
                <div class="bar-label">arithmetic</div>
                <div class="bar" style="width: 218.18181818181816px;">
                    12 (27.3%)
                </div>
            </div>

            <div class="bar-item">
                <div class="bar-label">variable_analysis</div>
                <div class="bar" style="width: 218.18181818181816px;">
                    12 (27.3%)
                </div>
            </div>

            <div class="bar-item">
                <div class="bar-label">type_conversion</div>
                <div class="bar" style="width: 109.09090909090908px;">
                    6 (13.6%)
                </div>
            </div>

            <div class="bar-item">
                <div class="bar-label">texture_sample</div>
                <div class="bar" style="width: 18.181818181818183px;">
                    1 (2.3%)
                </div>
            </div>

            <div class="bar-item">
                <div class="bar-label">mixed_precision</div>
                <div class="bar" style="width: 18.181818181818183px;">
                    1 (2.3%)
                </div>
            </div>

            <div class="bar-item">
                <div class="bar-label">assignment</div>
                <div class="bar" style="width: 145.45454545454547px;">
                    8 (18.2%)
                </div>
            </div>

            <div class="bar-item">
                <div class="bar-label">function_call</div>
                <div class="bar" style="width: 72.72727272727273px;">
                    4 (9.1%)
                </div>
            </div>

        </div>
    </div>
    
    <div class="suggestions">
        <h3>💡 性能优化建议</h3>

    </div>

    <div class="chart-container">
        <h3>🔍 逐行代码分析 (显示 30/30 行)</h3>
        <div style="max-height: 600px; overflow-y: auto; background: #1e1e1e; border-radius: 5px; padding: 10px;">

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">1</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;"><span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">2</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">#include <metal_stdlib><span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">3</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">using namespace metal;<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">4</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;"><span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">5</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">struct VertexOut {<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">6</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">    <span style="color: #2196f3; font-weight: bold;">float4</span> <span style="color: #2196f3;">position</span> [[<span style="color: #2196f3;">position</span>]];<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"> [变量: 1 | float: 1 | half: 0 | 转换: 0]</span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">7</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">    <span style="color: #ff5722; font-weight: bold;">half2</span> <span style="color: #ff5722;">texCoord</span>;<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"> [变量: 1 | float: 0 | half: 1 | 转换: 0]</span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">8</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">    <span style="color: #ff5722; font-weight: bold;">half3</span> <span style="color: #ff5722;">normal</span>;<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"> [变量: 1 | float: 0 | half: 1 | 转换: 0]</span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">9</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">};<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">10</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;"><span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">11</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">fragment half4 main0(VertexOut in [[stage_in]],<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">12</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">                     texture2d<half> baseTexture [[texture(0)]],<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">13</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">                     sampler baseSampler [[sampler(0)]]) {<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">14</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;"><span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">15</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">    // 变量声明和类型转换测试<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; background-color: rgba(255, 87, 87, 0.2);">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">16</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">    <span style="color: #ff5722; font-weight: bold;">half4</span> <span style="color: #ff5722;">baseColor</span> = baseTexture.sample(baseSampler, <span style="background-color: rgba(255, 193, 7, 0.3); color: #2196f3; font-weight: bold;">float2(in.texCoord)</span>);<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"> [混合精度] [变量: 1 | float: 0 | half: 1 | 转换: 1]</span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">17</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">    <span style="color: #ff5722; font-weight: bold;">half3</span> <span style="color: #ff5722;">normalizedNormal</span> = normalize(in.normal);<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"> [变量: 1 | float: 0 | half: 1 | 转换: 0]</span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">18</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">    <span style="color: #4fc3f7; font-weight: bold;">float</span> <span style="color: #4fc3f7;">lightIntensity</span> = 0.8;<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"> [变量: 1 | float: 1 | half: 0 | 转换: 0]</span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; background-color: rgba(255, 193, 7, 0.2);">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">19</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">    <span style="color: #ff5722; font-weight: bold;">half3</span> <span style="color: #ff5722;">finalColor</span> = baseColor.rgb * <span style="background-color: rgba(255, 193, 7, 0.3); color: #ff9800; font-weight: bold;">half(lightIntensity)</span>;<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"> [类型转换] [变量: 1 | float: 0 | half: 1 | 转换: 1]</span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">20</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;"><span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">21</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">    // 混合精度运算<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; background-color: rgba(255, 193, 7, 0.2);">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">22</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">    <span style="color: #2196f3; font-weight: bold;">float3</span> <span style="color: #2196f3;">worldPos</span> = <span style="color: #2196f3; font-weight: bold;">float3</span>(1.0, 2.0, 3.0);<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"> [类型转换] [变量: 1 | float: 1 | half: 0 | 转换: 1]</span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; background-color: rgba(255, 193, 7, 0.2);">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">23</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">    <span style="color: #ff5722; font-weight: bold;">half3</span> <span style="color: #ff5722;">lightDir</span> = <span style="color: #ff5722; font-weight: bold;">half3</span>(normalize(worldPos));<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"> [类型转换] [变量: 1 | float: 0 | half: 1 | 转换: 1]</span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">24</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;"><span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">25</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">    <span style="color: #ff9800; font-weight: bold;">half</span> <span style="color: #ff9800;">dotProduct</span> = dot(normalizedNormal, lightDir);<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"> [变量: 1 | float: 0 | half: 1 | 转换: 0]</span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; background-color: rgba(255, 193, 7, 0.2);">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">26</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">    <span style="color: #ff5722; font-weight: bold;">half3</span> <span style="color: #ff5722;">diffuse</span> = finalColor * max(dotProduct, <span style="background-color: rgba(255, 193, 7, 0.3); color: #ff9800; font-weight: bold;">half(0.0)</span>);<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"> [类型转换] [变量: 1 | float: 0 | half: 1 | 转换: 1]</span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">27</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;"><span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; background-color: rgba(255, 193, 7, 0.2);">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">28</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">    return <span style="background-color: rgba(255, 193, 7, 0.3); color: #ff5722; font-weight: bold;">half4(diffuse, baseColor.a)</span>;<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"> [类型转换] [变量: 0 | float: 0 | half: 0 | 转换: 1]</span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">29</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;">}<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; ">
                <div style="width: 60px; text-align: right; padding-right: 15px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none;">30</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4;"><span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;"></span></div>
            </div>

        </div>

    </div>

    <div style="text-align: center; margin-top: 30px; color: #858585;">
        <p>报告由ShaderProfile工具自动生成</p>
    </div>
</body>
</html>
