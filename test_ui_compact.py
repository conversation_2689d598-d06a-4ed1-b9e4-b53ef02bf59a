#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试紧凑UI样式
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PyQt5.QtCore import QTimer

def test_compact_ui():
    """测试紧凑UI"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = QMainWindow()
    window.setWindowTitle("紧凑UI测试")
    window.setGeometry(100, 100, 600, 500)
    
    # 创建中央组件
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 创建分析组件
    from ui.shader_analysis_widget import ShaderAnalysisWidget
    analysis_widget = ShaderAnalysisWidget()
    layout.addWidget(analysis_widget)
    
    # 添加测试按钮
    test_btn = QPushButton("开始测试分析")
    test_btn.setStyleSheet("""
        QPushButton {
            background-color: #0078d4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
        }
        QPushButton:hover {
            background-color: #106ebe;
        }
    """)
    layout.addWidget(test_btn)
    
    # 测试着色器内容
    test_shader = """
#include <metal_stdlib>
using namespace metal;

struct VertexOut {
    float4 position [[position]];
    half2 texCoord;
    half3 normal;
};

fragment half4 main0(VertexOut in [[stage_in]], 
                     texture2d<half> baseTexture [[texture(0)]],
                     sampler baseSampler [[sampler(0)]]) {
    
    half4 baseColor = baseTexture.sample(baseSampler, float2(in.texCoord));
    half3 normalizedNormal = normalize(in.normal);
    
    float lightIntensity = 0.8;
    half3 finalColor = baseColor.rgb * half(lightIntensity);
    
    // 混合精度运算示例
    float3 worldPos = float3(1.0, 2.0, 3.0);
    half3 lightDir = half3(normalize(worldPos));
    
    half dotProduct = dot(normalizedNormal, lightDir);
    half3 diffuse = finalColor * max(dotProduct, half(0.0));
    
    return half4(diffuse, baseColor.a);
}
"""
    
    def start_test():
        print("开始测试紧凑UI分析...")
        analysis_widget.start_analysis(test_shader)
    
    test_btn.clicked.connect(start_test)
    
    # 显示窗口
    window.show()
    
    # 自动开始测试
    QTimer.singleShot(1000, start_test)
    
    print("紧凑UI测试窗口已启动")
    print("UI改进:")
    print("✅ 标题字体从18px减小到14px，颜色改为#d4d4d4")
    print("✅ 指标卡片从120x80减小到100x60")
    print("✅ 性能评分条高度固定为20px")
    print("✅ 建议区域高度从120px减小到80px")
    print("✅ 详情区域高度从200px减小到150px")
    print("✅ 按钮高度固定为32px，字体11px")
    print("✅ 整体间距从15px减小到8px")
    print("✅ 所有标签颜色统一为#d4d4d4，避免白色冲突")
    
    sys.exit(app.exec_())

def main():
    """主函数"""
    print("🎨 紧凑UI样式测试")
    print("=" * 50)
    
    try:
        test_compact_ui()
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
