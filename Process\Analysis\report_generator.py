#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告生成器 - 基于AST的精确分析报告
"""

import json
import os
import re
import tempfile
from typing import Dict, List, Optional
from datetime import datetime
from dataclasses import dataclass

@dataclass
class CodeLineData:
    """代码行数据结构 - 存储单行代码的分析结果"""
    line_number: int  # 代码行号（从1开始）
    content: str  # 原始代码内容
    operation_count: int = 0  # 该行代码中的运算操作总次数
    conversion_count: int = 0  # 该行代码中的类型转换操作次数
    precision_issues: int = 0  # 该行代码中的精度问题数量
    node_details: List[str] = None  # AST节点详细信息列表
    operation_types: List[str] = None  # 运算操作类型列表（如：add, mul, div等）
    has_analysis: bool = False  # 是否已进行过AST分析
    css_classes: List[str] = None  # 用于HTML显示的CSS样式类列表
    performance_level: str = "normal"  # 性能级别：normal(正常), intensive(密集), conversion(转换), issue(问题)
    
    def __post_init__(self):
        """数据类初始化后的处理 - 确保列表类型字段不为None"""
        if self.node_details is None:
            self.node_details = []  # 初始化AST节点详情列表
        if self.operation_types is None:
            self.operation_types = []  # 初始化操作类型列表
        if self.css_classes is None:
            self.css_classes = []  # 初始化CSS样式类列表
    
    def get_stats_text(self, show_details: bool = True) -> str:
        """获取统计信息文本 - 用于HTML报告中显示该行的分析结果
        
        Args:
            show_details: 是否显示详细的操作类型信息
            
        Returns:
            格式化的统计信息字符串，如："运算: 3次 | 类型转换: 1次 | 操作: add, mul"
        """
        if not self.has_analysis or self.operation_count == 0:
            return ""  # 没有分析或没有运算操作时返回空字符串
        
        stats = f"运算: {self.operation_count}次"  # 基础运算次数
        if self.conversion_count > 0:
            stats += f" | 类型转换: {self.conversion_count}次"  # 类型转换次数
        
        if show_details and self.operation_types:
            unique_ops = list(set(self.operation_types))  # 去重操作类型
            if unique_ops:
                stats += f" | 操作: {', '.join(unique_ops)}"  # 具体操作类型
        
        return stats
    
    def get_color_style(self, use_colors: bool = True) -> str:
        """获取颜色样式 - 根据分析结果返回对应的CSS样式
        
        Args:
            use_colors: 是否启用颜色标记
            
        Returns:
            CSS样式字符串，用于HTML报告中的视觉标记
        """
        if not use_colors or not self.has_analysis:
            return ""  # 不启用颜色或没有分析时返回空样式
        
        if self.conversion_count > 0:
            return "border-left: 3px solid #f44336;"  # 红色边框 - 表示类型转换（性能影响较大）
        elif self.operation_count > 5:
            return "border-left: 3px solid #ff9800;"  # 橙色边框 - 表示运算密集（需要关注）
        elif self.operation_count > 0:
            return "border-left: 3px solid #4caf50;"  # 绿色边框 - 表示正常运算（性能良好）
        
        return ""  # 无运算操作时无特殊样式

class ReportGenerator:
    """报告生成器 - 基于AST的精确分析报告"""
    
    def __init__(self):
        self.temp_dir = tempfile.gettempdir()
    
    def generate_precise_html_report(self, precise_data: Dict, code_lines_data: List[CodeLineData]) -> str:
        """生成精确分析的HTML报告，包含内置的显示选项控制"""
        overall_stats = precise_data['overall_statistics']
        
        # 计算统计数据
        total_lines = len(code_lines_data)
        analyzed_lines = sum(1 for line in code_lines_data if line.operation_count > 0)
        total_operations = sum(line.operation_count for line in code_lines_data)
        total_conversions = sum(line.conversion_count for line in code_lines_data)
        total_precision_issues = sum(line.precision_issues for line in code_lines_data)
        total_nodes = overall_stats.get('total_nodes', 0)
        precision_score = overall_stats.get('precision_accuracy_score', 0)
        analysis_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 使用模板生成HTML头部
        html_content = HTML_TEMPLATE_HEADER.format(
            total_lines=total_lines,
            analysis_time=analysis_time,
            total_nodes=total_nodes,
            analyzed_lines=analyzed_lines,
            total_operations=total_operations,
            total_conversions=total_conversions,
            total_precision_issues=total_precision_issues,
            precision_score=precision_score
        )

        # 生成所有代码行
        for line_data in code_lines_data:
            css_classes = []
            
            # 添加基础CSS类
            if line_data.css_classes:
                css_classes.extend(line_data.css_classes)
            
            # 添加性能级别类
            if line_data.has_analysis and line_data.operation_count > 0:
                css_classes.append(f"performance-{line_data.performance_level}")
            
            # 数据属性
            data_attrs = []
            data_attrs.append(f'data-has-analysis="{str(line_data.has_analysis).lower()}"')
            data_attrs.append(f'data-performance="{line_data.performance_level}"')
            data_attrs.append(f'data-operations="{line_data.operation_count}"')
            data_attrs.append(f'data-conversions="{line_data.conversion_count}"')
            
            css_class_str = ' '.join(css_classes)
            data_attr_str = ' '.join(data_attrs)
            
            html_content += f"""
            <div class="code-line {css_class_str}" {data_attr_str}>
                <div class="line-number">{line_data.line_number}</div>
                <div class="line-content">
                    {line_data.content}
                    <div class="node-info node-details">{line_data.get_stats_text(True)}</div>
                </div>
            </div>
            """

        # 添加HTML尾部
        html_content += HTML_TEMPLATE_FOOTER
        
        return html_content

# HTML模板常量 - 静态内容
HTML_TEMPLATE_HEADER = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 精确类型分析报告</title>
    <style>
        /* 浅色主题样式 */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
            transition: all 0.3s ease;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .controls {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .control-group {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        .control-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .style-selector {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .style-selector label {
            font-weight: 500;
            color: #666;
        }
        .style-selector select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            font-size: 12px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: all 0.3s ease;
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #4fc3f7;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        .code-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .code-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }
        .code-line {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            min-height: 24px;
            align-items: flex-start;
            transition: background-color 0.2s;
        }
        .code-line:hover {
            background-color: #f8f9fa;
        }
        .line-number {
            background-color: #f8f9fa;
            color: #666;
            padding: 4px 12px;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            min-width: 60px;
            text-align: right;
            border-right: 1px solid #dee2e6;
            user-select: none;
            transition: all 0.3s ease;
        }
        .line-content {
            flex: 1;
            padding: 4px 12px;
            font-family: 'Consolas', monospace;
            font-size: 13px;
            white-space: pre;
            overflow-x: auto;
            transition: all 0.3s ease;
        }
        .node-info {
            font-size: 11px;
            color: #666;
            margin-top: 2px;
            font-style: italic;
            transition: all 0.3s ease;
        }
        .type-conversion {
            background-color: #fff3e0;
        }
        .precision-issue {
            background-color: #ffebee;
        }
        .performance-normal {
            border-left: 3px solid #4caf50;
        }
        .performance-intensive {
            border-left: 3px solid #ff9800;
        }
        .performance-conversion {
            border-left: 3px solid #f44336;
        }
        .performance-issue {
            border-left: 3px solid #9c27b0;
        }
        .hidden {
            display: none;
        }
        .filter-info {
            padding: 10px;
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            margin-bottom: 10px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        /* 深色主题样式 */
        body.dark-theme {
            background-color: #1a1a1a;
            color: #e0e0e0;
        }
        .dark-theme .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }
        .dark-theme .controls {
            background: #2d2d2d;
            color: #e0e0e0;
            border: 1px solid #444;
        }
        .dark-theme .control-item label {
            color: #e0e0e0;
        }
        .dark-theme .style-selector label {
            color: #b0b0b0;
        }
        .dark-theme .style-selector select {
            background: #3a3a3a;
            color: #e0e0e0;
            border-color: #555;
        }
        .dark-theme .stat-card {
            background: #2d2d2d;
            color: #e0e0e0;
            border: 1px solid #444;
        }
        .dark-theme .stat-value {
            color: #64b5f6;
        }
        .dark-theme .stat-label {
            color: #b0b0b0;
        }
        .dark-theme .code-section {
            background: #2d2d2d;
            border: 1px solid #444;
        }
        .dark-theme .code-header {
            background: #3a3a3a;
            border-bottom-color: #555;
            color: #e0e0e0;
        }
        .dark-theme .code-line {
            border-bottom-color: #444;
        }
        .dark-theme .code-line:hover {
            background-color: #3a3a3a;
        }
        .dark-theme .line-number {
            background-color: #3a3a3a;
            color: #b0b0b0;
            border-right-color: #555;
        }
        .dark-theme .line-content {
            color: #e0e0e0;
        }
        .dark-theme .node-info {
            color: #b0b0b0;
        }
        .dark-theme .filter-info {
            background: #1e3a5f;
            border-left-color: #4a9eff;
            color: #e0e0e0;
        }
        .dark-theme .type-conversion {
            background-color: #3d2c1a;
        }
        .dark-theme .precision-issue {
            background-color: #3d1a1a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 精确类型分析报告</h1>
            <p>基于语法树的Metal着色器运算过程分析</p>
            <p>总代码行数: {total_lines} | 分析时间: {analysis_time}</p>
        </div>

        <!-- 显示控制面板 -->
        <div class="controls">
            <div class="control-group">
                <div class="control-item">
                    <input type="checkbox" id="showNodeDetails" checked>
                    <label for="showNodeDetails">显示节点详情</label>
                </div>
                <div class="control-item">
                    <input type="checkbox" id="showTypeColors" checked>
                    <label for="showTypeColors">类型颜色标记</label>
                </div>
                <div class="control-item">
                    <input type="checkbox" id="showOnlyAnalyzed">
                    <label for="showOnlyAnalyzed">只显示有分析的行</label>
                </div>
                <div class="control-item">
                    <select id="performanceFilter">
                        <option value="all">显示所有行</option>
                        <option value="conversion">只显示类型转换</option>
                        <option value="intensive">只显示运算密集</option>
                        <option value="issue">只显示精度问题</option>
                    </select>
                </div>
                <div class="style-selector">
                    <label for="themeSelector">主题样式:</label>
                    <select id="themeSelector">
                        <option value="light">浅色主题</option>
                        <option value="dark">深色主题</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{total_nodes:,}</div>
                <div class="stat-label">总节点数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{analyzed_lines:,}</div>
                <div class="stat-label">有运算的行</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{total_operations:,}</div>
                <div class="stat-label">总运算次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{total_conversions:,}</div>
                <div class="stat-label">类型转换</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{total_precision_issues:,}</div>
                <div class="stat-label">精度问题</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{precision_score:.1f}%</div>
                <div class="stat-label">准确性评分</div>
            </div>
        </div>

        <div class="code-section">
            <div class="code-header">
                <h3>📝 完整着色器代码分析</h3>
                <div class="filter-info" id="filterInfo" style="display: none;">
                    <span id="filterText"></span>
                </div>
            </div>
"""

HTML_TEMPLATE_FOOTER = """
        </div>
    </div>

    <script>
        // JavaScript控制显示选项
        document.addEventListener('DOMContentLoaded', function() {
            const showNodeDetails = document.getElementById('showNodeDetails');
            const showTypeColors = document.getElementById('showTypeColors');
            const showOnlyAnalyzed = document.getElementById('showOnlyAnalyzed');
            const performanceFilter = document.getElementById('performanceFilter');
            const themeSelector = document.getElementById('themeSelector');
            const filterInfo = document.getElementById('filterInfo');
            const filterText = document.getElementById('filterText');

            // 主题切换功能
            function updateTheme() {
                const theme = themeSelector.value;
                const body = document.body;
                
                if (theme === 'dark') {
                    body.classList.add('dark-theme');
                } else {
                    body.classList.remove('dark-theme');
                }
                
                // 保存主题偏好到本地存储
                localStorage.setItem('preferred-theme', theme);
            }

            // 加载保存的主题偏好
            function loadThemePreference() {
                const savedTheme = localStorage.getItem('preferred-theme');
                if (savedTheme) {
                    themeSelector.value = savedTheme;
                    updateTheme();
                }
            }

            function updateDisplay() {
                const codeLines = document.querySelectorAll('.code-line');
                let visibleCount = 0;
                
                codeLines.forEach(line => {
                    const hasAnalysis = line.dataset.hasAnalysis === 'true';
                    const performance = line.dataset.performance;
                    const operations = parseInt(line.dataset.operations);
                    
                    // 节点详情显示控制
                    const nodeInfo = line.querySelector('.node-details');
                    if (nodeInfo) {
                        nodeInfo.style.display = showNodeDetails.checked ? 'block' : 'none';
                    }
                    
                    // 类型颜色标记控制
                    line.classList.toggle('performance-normal', showTypeColors.checked && performance === 'normal');
                    line.classList.toggle('performance-intensive', showTypeColors.checked && performance === 'intensive');
                    line.classList.toggle('performance-conversion', showTypeColors.checked && performance === 'conversion');
                    line.classList.toggle('performance-issue', showTypeColors.checked && performance === 'issue');
                    
                    // 行过滤控制
                    let shouldShow = true;
                    
                    if (showOnlyAnalyzed.checked && !hasAnalysis) {
                        shouldShow = false;
                    }
                    
                    if (performanceFilter.value !== 'all' && performance !== performanceFilter.value) {
                        shouldShow = false;
                    }
                    
                    line.style.display = shouldShow ? 'flex' : 'none';
                    if (shouldShow) visibleCount++;
                });
                
                // 更新过滤信息
                const totalLines = codeLines.length;
                if (visibleCount < totalLines) {
                    filterInfo.style.display = 'block';
                    filterText.textContent = `显示 ${visibleCount} / ${totalLines} 行`;
                } else {
                    filterInfo.style.display = 'none';
                }
            }

            // 绑定事件监听器
            showNodeDetails.addEventListener('change', updateDisplay);
            showTypeColors.addEventListener('change', updateDisplay);
            showOnlyAnalyzed.addEventListener('change', updateDisplay);
            performanceFilter.addEventListener('change', updateDisplay);
            themeSelector.addEventListener('change', updateTheme);
            
            // 初始化显示和主题
            loadThemePreference();
            updateDisplay();
        });
    </script>
</body>
</html>
"""
