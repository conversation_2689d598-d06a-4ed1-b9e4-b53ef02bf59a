#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
着色器分析报告生成器
"""

import json
import os
import re
import tempfile
from typing import Dict, List, Optional
from datetime import datetime
from dataclasses import dataclass

@dataclass
class CodeLineData:
    """代码行数据结构"""
    line_number: int
    content: str
    operation_count: int = 0
    conversion_count: int = 0
    precision_issues: int = 0
    node_details: List[str] = None
    operation_types: List[str] = None
    has_analysis: bool = False
    css_classes: List[str] = None
    performance_level: str = "normal"  # normal, intensive, conversion, issue

    def __post_init__(self):
        if self.node_details is None:
            self.node_details = []
        if self.operation_types is None:
            self.operation_types = []
        if self.css_classes is None:
            self.css_classes = []

    def get_stats_text(self, show_details: bool = True) -> str:
        """获取统计信息文本"""
        if not self.has_analysis or self.operation_count == 0:
            return ""

        stats = f"运算: {self.operation_count}次"
        if self.conversion_count > 0:
            stats += f" | 类型转换: {self.conversion_count}次"

        if show_details and self.operation_types:
            unique_ops = list(set(self.operation_types))
            if unique_ops:
                stats += f" | 操作: {', '.join(unique_ops)}"

        return stats

    def get_color_style(self, use_colors: bool = True) -> str:
        """获取颜色样式"""
        if not use_colors or not self.has_analysis:
            return ""

        if self.conversion_count > 0:
            return "border-left: 3px solid #f44336;"  # 红色 - 类型转换
        elif self.operation_count > 5:
            return "border-left: 3px solid #ff9800;"  # 橙色 - 运算密集
        elif self.operation_count > 0:
            return "border-left: 3px solid #4caf50;"  # 绿色 - 正常运算

        return ""

class ReportGenerator:
    """报告生成器"""
    
    def __init__(self):
        self.temp_dir = tempfile.gettempdir()
    
    def generate_summary_report(self, analysis_result: Dict) -> str:
        """生成简要报告"""
        stats = analysis_result['statistics']
        
        report = []
        report.append("🔍 Metal着色器Half/Float运算分析报告")
        report.append("=" * 50)
        
        report.append(f"\n📊 统计概览:")
        report.append(f"  总运算操作数: {stats['total_operations']}")
        report.append(f"  精度转换操作: {stats['precision_conversions']}")
        report.append(f"  混合精度运算: {stats['mixed_precision_ops']}")
        report.append(f"  高性能影响操作: {stats['high_impact_ops']}")
        report.append(f"  纹理采样操作: {stats['texture_samples']}")
        
        # 运算类型分布
        report.append(f"\n🔍 运算类型分布:")
        for op_type, count in stats['operation_types'].items():
            percentage = (count / stats['total_operations']) * 100 if stats['total_operations'] > 0 else 0
            report.append(f"  {op_type:20}: {count:4} ({percentage:.1f}%)")
        
        # 关键运算行分析
        report.append(f"\n⚠️  关键运算行分析 (前10个高影响操作):")
        high_impact_ops = [op for op in analysis_result['operations'] if op['performance_impact'] == 'high'][:10]
        
        for i, op in enumerate(high_impact_ops, 1):
            report.append(f"  {i:2}. 行{op['line']:4}: {op['type']:15} - {op['detail'][:50]}...")
            if op['precision_conversion']:
                report.append(f"      转换: {op['precision_conversion']}")
        
        # 性能优化建议
        report.append(f"\n💡 性能优化建议:")
        if stats['mixed_precision_ops'] > 10:
            report.append("  ⚠️  发现大量混合精度运算，建议统一使用half或float")
        
        if stats['precision_conversions'] > 100:
            report.append("  ⚠️  频繁的精度转换可能影响性能，考虑减少不必要的转换")
        
        if stats['texture_samples'] > 20:
            report.append("  ⚠️  大量纹理采样操作，考虑优化纹理访问模式")
        
        conversion_ratio = stats['precision_conversions'] / stats['total_operations'] if stats['total_operations'] > 0 else 0
        if conversion_ratio > 0.3:
            report.append(f"  ⚠️  类型转换比例过高({conversion_ratio:.1%})，建议重构数据类型设计")
        
        return "\n".join(report)
    
    def generate_html_report(self, analysis_result: Dict, shader_content: str = "", max_lines: int = None, display_options: Dict = None) -> str:
        """生成HTML可视化报告"""
        stats = analysis_result['statistics']

        # 默认显示选项
        if display_options is None:
            display_options = {
                'enable_type_colors': True,
                'enable_conversion_highlight': True,
                'show_variable_stats': True
            }
        
        # 创建行号到操作的映射
        line_operations = {}
        for op in analysis_result['operations']:
            line_num = op['line']
            if line_num not in line_operations:
                line_operations[line_num] = []
            line_operations[line_num].append(op)
        
        shader_lines = shader_content.split('\n') if shader_content else []
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Metal着色器Half/Float运算分析报告</title>
    <style>
        body {{
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
            line-height: 1.6;
        }}
        
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }}
        
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        
        .stat-card {{
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007acc;
            text-align: center;
        }}
        
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #4fc3f7;
        }}
        
        .stat-label {{
            color: #cccccc;
            margin-top: 5px;
        }}
        
        .chart-container {{
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }}
        
        .bar-chart {{
            display: flex;
            flex-direction: column;
            gap: 10px;
        }}
        
        .bar-item {{
            display: flex;
            align-items: center;
            gap: 15px;
        }}
        
        .bar-label {{
            width: 150px;
            text-align: right;
        }}
        
        .bar {{
            height: 25px;
            background: linear-gradient(90deg, #4fc3f7, #29b6f6);
            border-radius: 4px;
            display: flex;
            align-items: center;
            padding: 0 10px;
            color: white;
            font-weight: bold;
        }}
        
        .suggestions {{
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }}
        
        .suggestion-item {{
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 193, 7, 0.1);
            border-left: 4px solid #ffc107;
            border-radius: 4px;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Metal着色器Half/Float运算分析报告</h1>
        <p>深度分析着色器中的数据类型转换和运算模式</p>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{stats['total_operations']}</div>
            <div class="stat-label">总运算操作</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{stats['precision_conversions']}</div>
            <div class="stat-label">精度转换</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{stats['mixed_precision_ops']}</div>
            <div class="stat-label">混合精度运算</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{stats['high_impact_ops']}</div>
            <div class="stat-label">高影响操作</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{stats['texture_samples']}</div>
            <div class="stat-label">纹理采样</div>
        </div>
    </div>
    
    <div class="chart-container">
        <h3>📊 运算类型分布</h3>
        <div class="bar-chart">
"""
        
        # 添加柱状图
        for op_type, count in stats['operation_types'].items():
            percentage = (count / stats['total_operations']) * 100 if stats['total_operations'] > 0 else 0
            bar_width = min(percentage * 8, 400)  # 最大宽度400px
            
            html_content += f"""
            <div class="bar-item">
                <div class="bar-label">{op_type}</div>
                <div class="bar" style="width: {bar_width}px;">
                    {count} ({percentage:.1f}%)
                </div>
            </div>
"""
        
        html_content += """
        </div>
    </div>
    
    <div class="suggestions">
        <h3>💡 性能优化建议</h3>
"""
        
        # 添加优化建议
        if stats['mixed_precision_ops'] > 10:
            html_content += '<div class="suggestion-item">⚠️ 发现大量混合精度运算，建议统一使用half或float</div>'
        
        if stats['precision_conversions'] > 100:
            html_content += '<div class="suggestion-item">⚠️ 频繁的精度转换可能影响性能，考虑减少不必要的转换</div>'
        
        if stats['texture_samples'] > 20:
            html_content += '<div class="suggestion-item">⚠️ 大量纹理采样操作，考虑优化纹理访问模式</div>'
        
        conversion_ratio = stats['precision_conversions'] / stats['total_operations'] if stats['total_operations'] > 0 else 0
        if conversion_ratio > 0.3:
            html_content += f'<div class="suggestion-item">⚠️ 类型转换比例过高({conversion_ratio:.1%})，建议重构数据类型设计</div>'
        
        html_content += """
    </div>
"""

        # 添加逐行代码分析
        if shader_lines:
            # 确定显示的行数
            if max_lines is None:
                # 如果没有指定限制，根据文件大小智能决定
                if len(shader_lines) <= 500:
                    display_lines = shader_lines  # 小文件显示全部
                elif len(shader_lines) <= 1000:
                    display_lines = shader_lines[:800]  # 中等文件显示800行
                else:
                    display_lines = shader_lines[:1000]  # 大文件显示1000行
                    max_lines = 1000
            else:
                display_lines = shader_lines[:max_lines] if max_lines > 0 else shader_lines

            total_lines = len(shader_lines)
            showing_lines = len(display_lines)

            html_content += f"""
    <div class="chart-container">
        <h3>🔍 逐行代码分析 (显示 {showing_lines}/{total_lines} 行)</h3>
        <div style="max-height: 600px; overflow-y: auto; background: #1e1e1e; border-radius: 5px; padding: 10px;">
"""

            for i, line in enumerate(display_lines, 1):
                line_clean = line.rstrip()
                css_class = ""
                operation_info = ""

                # 处理变量类型颜色显示
                if display_options.get('enable_type_colors', True):
                    colored_line = self._colorize_variables_in_line(line_clean, i, line_operations, display_options)
                else:
                    colored_line = line_clean

                if i in line_operations:
                    ops = line_operations[i]
                    op_types = [op['type'] for op in ops]

                    # 统计变量信息
                    var_info_text = ""
                    if display_options.get('show_variable_stats', True):
                        for op in ops:
                            if op.get('variable_info'):
                                var_info = op['variable_info']
                                if var_info['total_variables'] > 0 or var_info['type_conversions'] > 0:
                                    var_info_text += f" [变量: {var_info['total_variables']} | float: {var_info['float_variables']} | half: {var_info['half_variables']} | 转换: {var_info['type_conversions']}]"

                    if 'mixed_precision' in op_types:
                        css_class = "background-color: rgba(255, 87, 87, 0.2);"
                        operation_info += " [混合精度]"
                    elif 'type_conversion' in op_types:
                        css_class = "background-color: rgba(255, 193, 7, 0.2);"
                        operation_info += " [类型转换]"
                    elif 'texture_sample' in op_types:
                        css_class = "background-color: rgba(76, 175, 80, 0.2);"
                        operation_info += " [纹理采样]"
                    elif any(op['performance_impact'] == 'high' for op in ops):
                        css_class = "background-color: rgba(156, 39, 176, 0.2);"
                        operation_info += " [高影响]"

                    operation_info += var_info_text

                html_content += f"""
            <div style="display: flex; padding: 2px 0; border-bottom: 1px solid #2d2d30; {css_class}">
                <div style="min-width: 60px; width: 60px; flex-shrink: 0; text-align: right; padding-right: 10px; color: #858585; background: #252526; border-right: 1px solid #3c3c3c; user-select: none; box-sizing: border-box;">{i}</div>
                <div style="flex: 1; padding-left: 15px; white-space: pre; font-family: 'Consolas', monospace; color: #d4d4d4; overflow-x: auto;">{colored_line}<span style="font-size: 0.8em; color: #ffc107; margin-left: 10px;">{operation_info}</span></div>
            </div>
"""

            html_content += """
        </div>
"""

            # 如果有行数被截断，添加提示
            if showing_lines < total_lines:
                html_content += f"""
        <div style="text-align: center; margin-top: 10px; padding: 10px; background: rgba(255, 193, 7, 0.1); border-left: 4px solid #ffc107; border-radius: 4px;">
            <p style="margin: 0; color: #ffc107;">
                ⚠️ 为了优化显示性能，仅显示前 {showing_lines} 行代码。
                完整的分析数据包含所有 {total_lines} 行，可通过导出功能获取完整结果。
            </p>
        </div>
"""

            html_content += """
    </div>
"""

        html_content += """
    <div style="text-align: center; margin-top: 30px; color: #858585;">
        <p>报告由ShaderProfile工具自动生成</p>
    </div>
</body>
</html>
"""
        
        return html_content

    def generate_precise_html_report(self, precise_result: Dict, code_lines_data: List[CodeLineData]) -> str:
        """生成精确分析的HTML报告，包含内置的显示选项控制"""
        precise_data = precise_result['precise_analysis']
        overall_stats = precise_data['overall_statistics']

        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 精确类型分析报告</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }}
        .controls {{
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .control-group {{
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }}
        .control-item {{
            display: flex;
            align-items: center;
            gap: 8px;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }}
        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }}
        .stat-value {{
            font-size: 2em;
            font-weight: bold;
            color: #4fc3f7;
            margin-bottom: 5px;
        }}
        .stat-label {{
            color: #666;
            font-size: 0.9em;
        }}
        .code-section {{
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .code-header {{
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        .code-line {{
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            min-height: 24px;
            align-items: flex-start;
            transition: background-color 0.2s;
        }}
        .code-line:hover {{
            background-color: #f8f9fa;
        }}
        .line-number {{
            background-color: #f8f9fa;
            color: #666;
            padding: 4px 12px;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            min-width: 60px;
            text-align: right;
            border-right: 1px solid #dee2e6;
            user-select: none;
        }}
        .line-content {{
            flex: 1;
            padding: 4px 12px;
            font-family: 'Consolas', monospace;
            font-size: 13px;
            white-space: pre;
            overflow-x: auto;
        }}
        .node-info {{
            font-size: 11px;
            color: #666;
            margin-top: 2px;
            font-style: italic;
        }}
        .type-conversion {{
            background-color: #fff3e0;
        }}
        .precision-issue {{
            background-color: #ffebee;
        }}
        .performance-normal {{
            border-left: 3px solid #4caf50;
        }}
        .performance-intensive {{
            border-left: 3px solid #ff9800;
        }}
        .performance-conversion {{
            border-left: 3px solid #f44336;
        }}
        .performance-issue {{
            border-left: 3px solid #9c27b0;
        }}
        .hidden {{
            display: none;
        }}
        .filter-info {{
            padding: 10px;
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            margin-bottom: 10px;
            border-radius: 4px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 精确类型分析报告</h1>
            <p>基于语法树的Metal着色器运算过程分析</p>
            <p>总代码行数: {len(code_lines_data)} | 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>

        <!-- 显示控制面板 -->
        <div class="controls">
            <div class="control-group">
                <div class="control-item">
                    <input type="checkbox" id="showNodeDetails" checked>
                    <label for="showNodeDetails">显示节点详情</label>
                </div>
                <div class="control-item">
                    <input type="checkbox" id="showTypeColors" checked>
                    <label for="showTypeColors">类型颜色标记</label>
                </div>
                <div class="control-item">
                    <input type="checkbox" id="showOnlyAnalyzed">
                    <label for="showOnlyAnalyzed">只显示有分析的行</label>
                </div>
                <div class="control-item">
                    <select id="performanceFilter">
                        <option value="all">显示所有行</option>
                        <option value="conversion">只显示类型转换</option>
                        <option value="intensive">只显示运算密集</option>
                        <option value="issue">只显示精度问题</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{overall_stats.get('total_nodes', 0):,}</div>
                <div class="stat-label">总节点数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{sum(1 for line in code_lines_data if line.operation_count > 0):,}</div>
                <div class="stat-label">有运算的行</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{sum(line.operation_count for line in code_lines_data):,}</div>
                <div class="stat-label">总运算次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{sum(line.conversion_count for line in code_lines_data):,}</div>
                <div class="stat-label">类型转换</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{sum(line.precision_issues for line in code_lines_data):,}</div>
                <div class="stat-label">精度问题</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{overall_stats.get('precision_accuracy_score', 0):.1f}%</div>
                <div class="stat-label">准确性评分</div>
            </div>
        </div>

        <div class="code-section">
            <div class="code-header">
                <h3>📝 完整着色器代码分析</h3>
                <div class="filter-info" id="filterInfo" style="display: none;">
                    <span id="filterText"></span>
                </div>
            </div>
"""

        # 生成所有代码行
        for line_data in code_lines_data:
            css_classes = []

            # 添加基础CSS类
            if line_data.css_classes:
                css_classes.extend(line_data.css_classes)

            # 添加性能级别类
            if line_data.has_analysis and line_data.operation_count > 0:
                css_classes.append(f"performance-{line_data.performance_level}")

            # 数据属性
            data_attrs = []
            data_attrs.append(f'data-has-analysis="{str(line_data.has_analysis).lower()}"')
            data_attrs.append(f'data-performance="{line_data.performance_level}"')
            data_attrs.append(f'data-operations="{line_data.operation_count}"')
            data_attrs.append(f'data-conversions="{line_data.conversion_count}"')

            css_class_str = ' '.join(css_classes)
            data_attr_str = ' '.join(data_attrs)

            html_content += f"""
            <div class="code-line {css_class_str}" {data_attr_str}>
                <div class="line-number">{line_data.line_number}</div>
                <div class="line-content">
                    {line_data.content}
                    <div class="node-info node-details">{line_data.get_stats_text(True)}</div>
                </div>
            </div>
"""

        html_content += """
        </div>
    </div>

    <script>
        // JavaScript控制显示选项
        document.addEventListener('DOMContentLoaded', function() {
            const showNodeDetails = document.getElementById('showNodeDetails');
            const showTypeColors = document.getElementById('showTypeColors');
            const showOnlyAnalyzed = document.getElementById('showOnlyAnalyzed');
            const performanceFilter = document.getElementById('performanceFilter');
            const filterInfo = document.getElementById('filterInfo');
            const filterText = document.getElementById('filterText');

            function updateDisplay() {
                const codeLines = document.querySelectorAll('.code-line');
                let visibleCount = 0;

                codeLines.forEach(line => {
                    const hasAnalysis = line.dataset.hasAnalysis === 'true';
                    const performance = line.dataset.performance;
                    const operations = parseInt(line.dataset.operations);

                    // 节点详情显示控制
                    const nodeInfo = line.querySelector('.node-details');
                    if (nodeInfo) {
                        nodeInfo.style.display = showNodeDetails.checked ? 'block' : 'none';
                    }

                    // 类型颜色标记控制
                    line.classList.toggle('performance-normal', showTypeColors.checked && performance === 'normal');
                    line.classList.toggle('performance-intensive', showTypeColors.checked && performance === 'intensive');
                    line.classList.toggle('performance-conversion', showTypeColors.checked && performance === 'conversion');
                    line.classList.toggle('performance-issue', showTypeColors.checked && performance === 'issue');

                    // 行过滤控制
                    let shouldShow = true;

                    if (showOnlyAnalyzed.checked && !hasAnalysis) {
                        shouldShow = false;
                    }

                    if (performanceFilter.value !== 'all' && performance !== performanceFilter.value) {
                        shouldShow = false;
                    }

                    line.style.display = shouldShow ? 'flex' : 'none';
                    if (shouldShow) visibleCount++;
                });

                // 更新过滤信息
                const totalLines = codeLines.length;
                if (visibleCount < totalLines) {
                    filterInfo.style.display = 'block';
                    filterText.textContent = `显示 ${visibleCount} / ${totalLines} 行`;
                } else {
                    filterInfo.style.display = 'none';
                }
            }

            // 绑定事件监听器
            showNodeDetails.addEventListener('change', updateDisplay);
            showTypeColors.addEventListener('change', updateDisplay);
            showOnlyAnalyzed.addEventListener('change', updateDisplay);
            performanceFilter.addEventListener('change', updateDisplay);

            // 初始化显示
            updateDisplay();
        });
    </script>
</body>
</html>
"""

        return html_content

    def _colorize_variables_in_line(self, line: str, line_number: int, line_operations: dict, display_options: dict = None) -> str:
        """为代码行中的变量添加类型颜色显示"""
        if line_number not in line_operations:
            return line

        if display_options is None:
            display_options = {'enable_type_colors': True, 'enable_conversion_highlight': True}

        # 定义类型颜色
        type_colors = {
            'float': '#4fc3f7',    # 蓝色 - float类型
            'half': '#ff9800',     # 橙色 - half类型
            'float2': '#2196f3',   # 深蓝色 - float向量
            'float3': '#2196f3',
            'float4': '#2196f3',
            'half2': '#ff5722',    # 深橙色 - half向量
            'half3': '#ff5722',
            'half4': '#ff5722',
            'float3x3': '#3f51b5', # 靛蓝色 - float矩阵
            'float4x4': '#3f51b5',
        }

        colored_line = line

        # 查找该行的变量分析信息
        for op in line_operations[line_number]:
            if op.get('variable_info'):
                var_info = op['variable_info']

                # 为类型声明添加颜色
                if display_options.get('enable_type_colors', True):
                    for type_name, variables in var_info.get('variables_by_type', {}).items():
                        if type_name in type_colors:
                            color = type_colors[type_name]
                            # 替换类型名称
                            colored_line = re.sub(
                                rf'\b{re.escape(type_name)}\b',
                                f'<span style="color: {color}; font-weight: bold;">{type_name}</span>',
                                colored_line
                            )
                            # 为对应的变量名添加颜色
                            for var_name in variables:
                                colored_line = re.sub(
                                    rf'\b{re.escape(var_name)}\b',
                                    f'<span style="color: {color};">{var_name}</span>',
                                    colored_line
                                )

                # 为类型转换添加特殊标记
                if display_options.get('enable_conversion_highlight', True):
                    for conversion in var_info.get('type_conversion_details', []):
                        target_type = conversion['target_type']
                        source_expr = conversion['source_expression']

                        if target_type in type_colors:
                            color = type_colors[target_type]
                            # 高亮类型转换
                            conversion_pattern = rf'{re.escape(target_type)}\s*\(\s*{re.escape(source_expr)}\s*\)'
                            colored_line = re.sub(
                                conversion_pattern,
                                f'<span style="background-color: rgba(255, 193, 7, 0.3); color: {color}; font-weight: bold;">{target_type}({source_expr})</span>',
                                colored_line
                            )

        return colored_line
    
    def save_reports(self, analysis_result: Dict, shader_content: str = "", base_filename: str = "shader_analysis") -> Dict[str, str]:
        """保存所有报告文件并返回文件路径"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 生成文件路径
        json_path = os.path.join(self.temp_dir, f"{base_filename}_{timestamp}.json")
        txt_path = os.path.join(self.temp_dir, f"{base_filename}_{timestamp}.txt")
        html_path = os.path.join(self.temp_dir, f"{base_filename}_{timestamp}.html")
        
        # 保存JSON数据
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, ensure_ascii=False, indent=2)
        
        # 保存文本报告
        summary_report = self.generate_summary_report(analysis_result)
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write(summary_report)
        
        # 保存HTML报告
        html_report = self.generate_html_report(analysis_result, shader_content)
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_report)
        
        return {
            'json': json_path,
            'txt': txt_path,
            'html': html_path,
            'summary': summary_report
        }
