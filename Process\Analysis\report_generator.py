#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告生成器 - 基于AST的精确分析报告
"""

import json
import os
import re
import tempfile
from typing import Dict, List, Optional
from datetime import datetime
from dataclasses import dataclass

@dataclass
class CodeLineData:
    """代码行数据结构"""
    line_number: int
    content: str
    operation_count: int = 0
    conversion_count: int = 0
    precision_issues: int = 0
    node_details: List[str] = None
    operation_types: List[str] = None
    has_analysis: bool = False
    css_classes: List[str] = None
    performance_level: str = "normal"  # normal, intensive, conversion, issue
    
    def __post_init__(self):
        if self.node_details is None:
            self.node_details = []
        if self.operation_types is None:
            self.operation_types = []
        if self.css_classes is None:
            self.css_classes = []
    
    def get_stats_text(self, show_details: bool = True) -> str:
        """获取统计信息文本"""
        if not self.has_analysis or self.operation_count == 0:
            return ""
        
        stats = f"运算: {self.operation_count}次"
        if self.conversion_count > 0:
            stats += f" | 类型转换: {self.conversion_count}次"
        
        if show_details and self.operation_types:
            unique_ops = list(set(self.operation_types))
            if unique_ops:
                stats += f" | 操作: {', '.join(unique_ops)}"
        
        return stats
    
    def get_color_style(self, use_colors: bool = True) -> str:
        """获取颜色样式"""
        if not use_colors or not self.has_analysis:
            return ""
        
        if self.conversion_count > 0:
            return "border-left: 3px solid #f44336;"  # 红色 - 类型转换
        elif self.operation_count > 5:
            return "border-left: 3px solid #ff9800;"  # 橙色 - 运算密集
        elif self.operation_count > 0:
            return "border-left: 3px solid #4caf50;"  # 绿色 - 正常运算
        
        return ""

class ReportGenerator:
    """报告生成器 - 基于AST的精确分析报告"""
    
    def __init__(self):
        self.temp_dir = tempfile.gettempdir()
    
    def generate_precise_html_report(self, precise_data: Dict, code_lines_data: List[CodeLineData]) -> str:
        """生成精确分析的HTML报告，包含内置的显示选项控制"""
        overall_stats = precise_data['overall_statistics']
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 精确类型分析报告</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }}
        .controls {{
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .control-group {{
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }}
        .control-item {{
            display: flex;
            align-items: center;
            gap: 8px;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }}
        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }}
        .stat-value {{
            font-size: 2em;
            font-weight: bold;
            color: #4fc3f7;
            margin-bottom: 5px;
        }}
        .stat-label {{
            color: #666;
            font-size: 0.9em;
        }}
        .code-section {{
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .code-header {{
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        .code-line {{
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            min-height: 24px;
            align-items: flex-start;
            transition: background-color 0.2s;
        }}
        .code-line:hover {{
            background-color: #f8f9fa;
        }}
        .line-number {{
            background-color: #f8f9fa;
            color: #666;
            padding: 4px 12px;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            min-width: 60px;
            text-align: right;
            border-right: 1px solid #dee2e6;
            user-select: none;
        }}
        .line-content {{
            flex: 1;
            padding: 4px 12px;
            font-family: 'Consolas', monospace;
            font-size: 13px;
            white-space: pre;
            overflow-x: auto;
        }}
        .node-info {{
            font-size: 11px;
            color: #666;
            margin-top: 2px;
            font-style: italic;
        }}
        .type-conversion {{
            background-color: #fff3e0;
        }}
        .precision-issue {{
            background-color: #ffebee;
        }}
        .performance-normal {{
            border-left: 3px solid #4caf50;
        }}
        .performance-intensive {{
            border-left: 3px solid #ff9800;
        }}
        .performance-conversion {{
            border-left: 3px solid #f44336;
        }}
        .performance-issue {{
            border-left: 3px solid #9c27b0;
        }}
        .hidden {{
            display: none;
        }}
        .filter-info {{
            padding: 10px;
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            margin-bottom: 10px;
            border-radius: 4px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 精确类型分析报告</h1>
            <p>基于语法树的Metal着色器运算过程分析</p>
            <p>总代码行数: {len(code_lines_data)} | 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>

        <!-- 显示控制面板 -->
        <div class="controls">
            <div class="control-group">
                <div class="control-item">
                    <input type="checkbox" id="showNodeDetails" checked>
                    <label for="showNodeDetails">显示节点详情</label>
                </div>
                <div class="control-item">
                    <input type="checkbox" id="showTypeColors" checked>
                    <label for="showTypeColors">类型颜色标记</label>
                </div>
                <div class="control-item">
                    <input type="checkbox" id="showOnlyAnalyzed">
                    <label for="showOnlyAnalyzed">只显示有分析的行</label>
                </div>
                <div class="control-item">
                    <select id="performanceFilter">
                        <option value="all">显示所有行</option>
                        <option value="conversion">只显示类型转换</option>
                        <option value="intensive">只显示运算密集</option>
                        <option value="issue">只显示精度问题</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{overall_stats.get('total_nodes', 0):,}</div>
                <div class="stat-label">总节点数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{sum(1 for line in code_lines_data if line.operation_count > 0):,}</div>
                <div class="stat-label">有运算的行</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{sum(line.operation_count for line in code_lines_data):,}</div>
                <div class="stat-label">总运算次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{sum(line.conversion_count for line in code_lines_data):,}</div>
                <div class="stat-label">类型转换</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{sum(line.precision_issues for line in code_lines_data):,}</div>
                <div class="stat-label">精度问题</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{overall_stats.get('precision_accuracy_score', 0):.1f}%</div>
                <div class="stat-label">准确性评分</div>
            </div>
        </div>

        <div class="code-section">
            <div class="code-header">
                <h3>📝 完整着色器代码分析</h3>
                <div class="filter-info" id="filterInfo" style="display: none;">
                    <span id="filterText"></span>
                </div>
            </div>
"""

        # 生成所有代码行
        for line_data in code_lines_data:
            css_classes = []
            
            # 添加基础CSS类
            if line_data.css_classes:
                css_classes.extend(line_data.css_classes)
            
            # 添加性能级别类
            if line_data.has_analysis and line_data.operation_count > 0:
                css_classes.append(f"performance-{line_data.performance_level}")
            
            # 数据属性
            data_attrs = []
            data_attrs.append(f'data-has-analysis="{str(line_data.has_analysis).lower()}"')
            data_attrs.append(f'data-performance="{line_data.performance_level}"')
            data_attrs.append(f'data-operations="{line_data.operation_count}"')
            data_attrs.append(f'data-conversions="{line_data.conversion_count}"')
            
            css_class_str = ' '.join(css_classes)
            data_attr_str = ' '.join(data_attrs)
            
            html_content += f"""
            <div class="code-line {css_class_str}" {data_attr_str}>
                <div class="line-number">{line_data.line_number}</div>
                <div class="line-content">
                    {line_data.content}
                    <div class="node-info node-details">{line_data.get_stats_text(True)}</div>
                </div>
            </div>
"""

        html_content += """
        </div>
    </div>

    <script>
        // JavaScript控制显示选项
        document.addEventListener('DOMContentLoaded', function() {
            const showNodeDetails = document.getElementById('showNodeDetails');
            const showTypeColors = document.getElementById('showTypeColors');
            const showOnlyAnalyzed = document.getElementById('showOnlyAnalyzed');
            const performanceFilter = document.getElementById('performanceFilter');
            const filterInfo = document.getElementById('filterInfo');
            const filterText = document.getElementById('filterText');

            function updateDisplay() {
                const codeLines = document.querySelectorAll('.code-line');
                let visibleCount = 0;
                
                codeLines.forEach(line => {
                    const hasAnalysis = line.dataset.hasAnalysis === 'true';
                    const performance = line.dataset.performance;
                    const operations = parseInt(line.dataset.operations);
                    
                    // 节点详情显示控制
                    const nodeInfo = line.querySelector('.node-details');
                    if (nodeInfo) {
                        nodeInfo.style.display = showNodeDetails.checked ? 'block' : 'none';
                    }
                    
                    // 类型颜色标记控制
                    line.classList.toggle('performance-normal', showTypeColors.checked && performance === 'normal');
                    line.classList.toggle('performance-intensive', showTypeColors.checked && performance === 'intensive');
                    line.classList.toggle('performance-conversion', showTypeColors.checked && performance === 'conversion');
                    line.classList.toggle('performance-issue', showTypeColors.checked && performance === 'issue');
                    
                    // 行过滤控制
                    let shouldShow = true;
                    
                    if (showOnlyAnalyzed.checked && !hasAnalysis) {
                        shouldShow = false;
                    }
                    
                    if (performanceFilter.value !== 'all' && performance !== performanceFilter.value) {
                        shouldShow = false;
                    }
                    
                    line.style.display = shouldShow ? 'flex' : 'none';
                    if (shouldShow) visibleCount++;
                });
                
                // 更新过滤信息
                const totalLines = codeLines.length;
                if (visibleCount < totalLines) {
                    filterInfo.style.display = 'block';
                    filterText.textContent = `显示 ${visibleCount} / ${totalLines} 行`;
                } else {
                    filterInfo.style.display = 'none';
                }
            }

            // 绑定事件监听器
            showNodeDetails.addEventListener('change', updateDisplay);
            showTypeColors.addEventListener('change', updateDisplay);
            showOnlyAnalyzed.addEventListener('change', updateDisplay);
            performanceFilter.addEventListener('change', updateDisplay);
            
            // 初始化显示
            updateDisplay();
        });
    </script>
</body>
</html>
"""
        
        return html_content
