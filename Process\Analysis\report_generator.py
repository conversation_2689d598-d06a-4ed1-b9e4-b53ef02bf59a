#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告生成器 - 基于AST的精确分析报告
"""

import json
import os
import re
import tempfile
from typing import Dict, List, Optional
from datetime import datetime
from dataclasses import dataclass

@dataclass
class CodeLineData:
    """代码行数据结构"""
    line_number: int
    content: str
    operation_count: int = 0
    conversion_count: int = 0
    precision_issues: int = 0
    node_details: List[str] = None
    operation_types: List[str] = None
    has_analysis: bool = False
    css_classes: List[str] = None
    performance_level: str = "normal"  # normal, intensive, conversion, issue
    
    def __post_init__(self):
        if self.node_details is None:
            self.node_details = []
        if self.operation_types is None:
            self.operation_types = []
        if self.css_classes is None:
            self.css_classes = []
    
    def get_stats_text(self, show_details: bool = True) -> str:
        """获取统计信息文本"""
        if not self.has_analysis or self.operation_count == 0:
            return ""
        
        stats = f"运算: {self.operation_count}次"
        if self.conversion_count > 0:
            stats += f" | 类型转换: {self.conversion_count}次"
        
        if show_details and self.operation_types:
            unique_ops = list(set(self.operation_types))
            if unique_ops:
                stats += f" | 操作: {', '.join(unique_ops)}"
        
        return stats
    
    def get_color_style(self, use_colors: bool = True) -> str:
        """获取颜色样式"""
        if not use_colors or not self.has_analysis:
            return ""
        
        if self.conversion_count > 0:
            return "border-left: 3px solid #f44336;"  # 红色 - 类型转换
        elif self.operation_count > 5:
            return "border-left: 3px solid #ff9800;"  # 橙色 - 运算密集
        elif self.operation_count > 0:
            return "border-left: 3px solid #4caf50;"  # 绿色 - 正常运算
        
        return ""

class ReportGenerator:
    """报告生成器 - 基于AST的精确分析报告"""
    
    def __init__(self):
        self.temp_dir = tempfile.gettempdir()
    
    def generate_precise_html_report(self, precise_data: Dict, code_lines_data: List[CodeLineData]) -> str:
        """生成精确分析的HTML报告"""
        overall_stats = precise_data['overall_statistics']
        
        # 计算统计数据
        total_lines = len(code_lines_data)
        analyzed_lines = sum(1 for line in code_lines_data if line.operation_count > 0)
        total_operations = sum(line.operation_count for line in code_lines_data)
        total_conversions = sum(line.conversion_count for line in code_lines_data)
        total_precision_issues = sum(line.precision_issues for line in code_lines_data)
        total_nodes = overall_stats.get('total_nodes', 0)
        precision_score = overall_stats.get('precision_accuracy_score', 0)
        analysis_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 生成HTML内容
        html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 精确类型分析报告</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }}
        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }}
        .stat-value {{
            font-size: 2em;
            font-weight: bold;
            color: #4fc3f7;
            margin-bottom: 5px;
        }}
        .stat-label {{
            color: #666;
            font-size: 0.9em;
        }}
        .code-section {{
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .code-line {{
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            min-height: 24px;
            align-items: flex-start;
        }}
        .line-number {{
            background-color: #f8f9fa;
            color: #666;
            padding: 4px 12px;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            min-width: 60px;
            text-align: right;
            border-right: 1px solid #dee2e6;
        }}
        .line-content {{
            flex: 1;
            padding: 4px 12px;
            font-family: 'Consolas', monospace;
            font-size: 13px;
            white-space: pre;
        }}
        .node-info {{
            font-size: 11px;
            color: #666;
            margin-top: 2px;
            font-style: italic;
        }}
        .performance-normal {{
            border-left: 3px solid #4caf50;
        }}
        .performance-intensive {{
            border-left: 3px solid #ff9800;
        }}
        .performance-conversion {{
            border-left: 3px solid #f44336;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 精确类型分析报告</h1>
            <p>基于语法树的Metal着色器运算过程分析</p>
            <p>总代码行数: {total_lines} | 分析时间: {analysis_time}</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{total_nodes:,}</div>
                <div class="stat-label">总节点数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{analyzed_lines:,}</div>
                <div class="stat-label">有运算的行</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{total_operations:,}</div>
                <div class="stat-label">总运算次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{total_conversions:,}</div>
                <div class="stat-label">类型转换</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{precision_score:.1f}%</div>
                <div class="stat-label">准确性评分</div>
            </div>
        </div>

        <div class="code-section">
            <h3 style="padding: 15px 20px; margin: 0; background: #f8f9fa; border-bottom: 1px solid #dee2e6;">📝 完整着色器代码分析</h3>
"""

        # 生成所有代码行
        for line_data in code_lines_data:
            css_classes = []
            
            # 添加性能级别类
            if line_data.has_analysis and line_data.operation_count > 0:
                css_classes.append(f"performance-{line_data.performance_level}")
            
            css_class_str = ' '.join(css_classes)
            
            html_content += f"""
            <div class="code-line {css_class_str}">
                <div class="line-number">{line_data.line_number}</div>
                <div class="line-content">
                    {line_data.content}
                    <div class="node-info">{line_data.get_stats_text(True)}</div>
                </div>
            </div>
"""

        html_content += """
        </div>
    </div>
</body>
</html>
"""
        
        return html_content
