#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试弹窗着色器分析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication

def test_popup_analysis():
    """测试弹窗分析功能"""
    app = QApplication(sys.argv)
    
    # 导入主窗口
    from ui.main_window import MainWindow
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    print("🔍 弹窗着色器分析测试")
    print("=" * 50)
    print("测试步骤:")
    print("1. 主窗口已启动")
    print("2. 请打开一个着色器文件或创建新文件")
    print("3. 输入一些Metal着色器代码")
    print("4. 使用菜单 '处理' -> '着色器分析' 或按 Ctrl+Alt+A")
    print("5. 应该会弹出分析窗口")
    print()
    print("弹窗特点:")
    print("✅ 模态对话框")
    print("✅ 800x600尺寸")
    print("✅ 包含完整的ShaderAnalysisWidget")
    print("✅ 自动开始分析")
    print("✅ 透明背景样式")
    print()
    print("测试用着色器代码:")
    print("""
#include <metal_stdlib>
using namespace metal;

fragment half4 main0(VertexOut in [[stage_in]]) {
    half4 color = half4(1.0, 0.5, 0.0, 1.0);
    float intensity = 0.8;
    return color * half(intensity);
}
""")
    
    sys.exit(app.exec_())

def main():
    """主函数"""
    try:
        test_popup_analysis()
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
