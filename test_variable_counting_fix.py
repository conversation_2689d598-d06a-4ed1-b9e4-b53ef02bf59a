#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试变量统计修复和显示效果控制
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_variable_counting():
    """测试变量统计修复"""
    print("🔧 变量统计修复测试")
    print("=" * 60)
    
    try:
        from Process.Analysis.shader_analyzer_core import ShaderAnalyzerCore
        
        analyzer = ShaderAnalyzerCore()
        
        # 测试用例
        test_cases = [
            {
                'line': 'half _9270 = _9255.z;',
                'expected_vars': 2,
                'description': '应该识别 _9270 和 _9255.z'
            },
            {
                'line': 'float3 _8921 = fast::normalize(_Block1.CameraPos.xyz - in.IN_WorldPosition.xyz);',
                'expected_vars': 4,
                'description': '应该识别 _8921, _Block1.CameraPos.xyz, in.IN_WorldPosition.xyz'
            },
            {
                'line': '_18272 = _9772;',
                'expected_vars': 2,
                'description': '应该识别 _18272 和 _9772'
            },
            {
                'line': '_18237 = float4(0.0, 0.0, 0.0, 1.0);',
                'expected_vars': 1,
                'expected_conversions': 0,
                'description': '应该识别 _18237，float4(0,0,0,1)不是类型转换'
            },
            {
                'line': 'half3 result = color.rgb * half(intensity);',
                'expected_vars': 3,
                'expected_conversions': 1,
                'description': '应该识别 result, color.rgb, intensity，half(intensity)是类型转换'
            }
        ]
        
        print("测试变量统计修复:")
        print("-" * 40)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试 {i}: {test_case['line']}")
            print(f"期望: {test_case['description']}")
            
            # 分析变量
            var_info = analyzer._analyze_line_variables(test_case['line'], i)
            
            if var_info:
                actual_vars = var_info['total_variables']
                actual_conversions = var_info['type_conversions']
                
                print(f"实际变量数: {actual_vars}")
                print(f"实际转换数: {actual_conversions}")
                print(f"所有变量: {var_info.get('all_variables', [])}")
                
                # 检查变量数量
                if actual_vars == test_case['expected_vars']:
                    print("✅ 变量数量正确")
                else:
                    print(f"❌ 变量数量错误，期望 {test_case['expected_vars']}，实际 {actual_vars}")
                
                # 检查转换数量（如果有期望值）
                if 'expected_conversions' in test_case:
                    if actual_conversions == test_case['expected_conversions']:
                        print("✅ 转换数量正确")
                    else:
                        print(f"❌ 转换数量错误，期望 {test_case['expected_conversions']}，实际 {actual_conversions}")
            else:
                print("❌ 未识别到任何变量信息")
        
        print("\n" + "=" * 60)
        print("🎨 HTML显示效果控制测试")
        print("-" * 40)
        
        # 测试HTML报告生成
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        processor = ShaderAnalysisProcessor()
        
        test_shader = """
half _9270 = _9255.z;
float3 _8921 = fast::normalize(_Block1.CameraPos.xyz - in.IN_WorldPosition.xyz);
_18272 = _9772;
_18237 = float4(0.0, 0.0, 0.0, 1.0);
half3 result = color.rgb * half(intensity);
"""
        
        result = processor.analyze_shader(test_shader, save_reports=False)
        
        # 测试不同的显示选项
        display_options_tests = [
            {
                'name': '全部启用',
                'options': {
                    'enable_type_colors': True,
                    'enable_conversion_highlight': True,
                    'show_variable_stats': True
                }
            },
            {
                'name': '仅颜色显示',
                'options': {
                    'enable_type_colors': True,
                    'enable_conversion_highlight': False,
                    'show_variable_stats': False
                }
            },
            {
                'name': '全部禁用',
                'options': {
                    'enable_type_colors': False,
                    'enable_conversion_highlight': False,
                    'show_variable_stats': False
                }
            }
        ]
        
        for test in display_options_tests:
            print(f"\n测试 {test['name']}:")
            html_content = processor.generate_html_report(
                result['analysis'],
                test_shader,
                max_lines=10,
                display_options=test['options']
            )
            
            # 检查HTML内容
            has_colors = '<span style="color:' in html_content
            has_highlights = 'background-color: rgba(255, 193, 7, 0.3)' in html_content
            has_stats = '[变量:' in html_content
            
            print(f"  类型颜色: {'✅' if has_colors == test['options']['enable_type_colors'] else '❌'}")
            print(f"  转换高亮: {'✅' if has_highlights == test['options']['enable_conversion_highlight'] else '❌'}")
            print(f"  变量统计: {'✅' if has_stats == test['options']['show_variable_stats'] else '❌'}")
            
            # 保存测试报告
            filename = f"test_report_{test['name'].replace(' ', '_')}.html"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"  报告已保存: {filename}")
        
        print("\n🎉 所有测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def main():
    """主函数"""
    print("修复内容:")
    print("1. 改进变量识别算法，正确统计所有变量使用")
    print("2. 区分真正的类型转换和构造函数调用")
    print("3. 添加HTML显示效果控制选项")
    print("4. 支持独立控制类型颜色、转换高亮、变量统计")
    print()
    
    success = test_variable_counting()
    
    if success:
        print("\n✅ 修复验证成功！")
        print("现在变量统计更加准确，并且可以控制HTML显示效果。")
    else:
        print("\n❌ 修复验证失败，需要进一步检查。")

if __name__ == "__main__":
    main()
