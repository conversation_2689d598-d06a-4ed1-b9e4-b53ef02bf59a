#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码行分析器使用示例
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def example_basic_usage():
    """基本使用示例"""
    print("📖 基本使用示例")
    print("=" * 50)
    
    from Process.Analysis.code_line_analyzer import CodeLineAnalyzer
    
    # 示例着色器代码
    shader_code = """
float3 _12378 = _12345.xyz;
float _12380 = _12378.x;
float _19350 = _12380 * 2.0;
if (_12381 > 0.5)
{
    float _inner1 = _19350 + 1.0;
    half _inner2 = half(_inner1);
    _result = _inner2 * 0.5;
}
else
{
    _result = 0.0;
}
"""
    
    # 创建分析器
    analyzer = CodeLineAnalyzer()
    
    # 分析代码
    code_lines = analyzer.analyze_shader_code(shader_code)
    
    # 输出结果
    print(analyzer.format_analysis_result(code_lines))
    
    # 获取统计信息
    stats = analyzer.get_summary_statistics(code_lines)
    print(f"\n📊 统计摘要:")
    print(f"  有效代码行: {stats['total_code_lines']}")
    print(f"  总变量数: {stats['total_variables']}")
    print(f"  类型转换: {stats['total_conversions']}")
    print(f"  平均每行变量数: {stats['avg_variables_per_line']:.2f}")

def example_detailed_analysis():
    """详细分析示例"""
    print("\n" + "=" * 50)
    print("🔍 详细分析示例")
    print("=" * 50)
    
    from Process.Analysis.code_line_analyzer import CodeLineAnalyzer
    
    shader_code = """
half4 color = texture.sample(sampler, uv);
float intensity = dot(normal, lightDir);
half3 result = color.rgb * half(intensity);
"""
    
    analyzer = CodeLineAnalyzer()
    code_lines = analyzer.analyze_shader_code(shader_code)
    
    print("逐行详细分析:")
    for i, line in enumerate(code_lines, 1):
        print(f"\n第{i}行: {line.content}")
        print(f"  原始行号: {line.line_number}")
        print(f"  变量总数: {line.variables}")
        print(f"  Float变量: {line.float_vars}")
        print(f"  Half变量: {line.half_vars}")
        print(f"  类型转换: {line.conversions}")
        print(f"  所有变量: {line.all_variables}")
        
        if line.conversion_details:
            print(f"  转换详情:")
            for detail in line.conversion_details:
                print(f"    {detail['target_type']}({detail['source_expression']})")

def example_custom_filtering():
    """自定义过滤示例"""
    print("\n" + "=" * 50)
    print("🎛️ 自定义过滤示例")
    print("=" * 50)
    
    from Process.Analysis.code_line_analyzer import CodeLineAnalyzer
    
    shader_code = """
// 这是注释
#include <metal_stdlib>
float3 pos = worldPos;
{
    // 大括号内的代码
    half4 color = half4(1.0);
    result = color * 0.5;
}
if (condition) {
    value = 1.0;
}
else {
    value = 0.0;
}
"""
    
    analyzer = CodeLineAnalyzer()
    code_lines = analyzer.analyze_shader_code(shader_code)
    
    print("自动过滤结果:")
    print(f"原始代码有 {len(shader_code.split())} 行")
    print(f"识别出 {len(code_lines)} 行有效代码")
    print("\n有效代码行:")
    for line in code_lines:
        print(f"  行{line.line_number}: {line.content}")
    
    # 自定义过滤：只显示有类型转换的行
    conversion_lines = [line for line in code_lines if line.has_conversion]
    print(f"\n包含类型转换的行 ({len(conversion_lines)} 行):")
    for line in conversion_lines:
        print(f"  {line.content} [转换: {line.conversions}]")

def example_integration_with_ui():
    """与UI集成示例"""
    print("\n" + "=" * 50)
    print("🖥️ 与UI集成示例")
    print("=" * 50)
    
    from Process.Analysis.code_line_analyzer import CodeLineAnalyzer
    
    def analyze_for_ui(shader_content):
        """为UI提供分析结果的函数"""
        analyzer = CodeLineAnalyzer()
        code_lines = analyzer.analyze_shader_code(shader_content)
        stats = analyzer.get_summary_statistics(code_lines)
        
        # 返回UI需要的格式化数据
        ui_data = {
            'code_lines': [
                {
                    'line_number': line.line_number,
                    'content': line.content,
                    'variables': line.variables,
                    'float_vars': line.float_vars,
                    'half_vars': line.half_vars,
                    'conversions': line.conversions,
                    'has_conversion': line.has_conversion,
                    'display_text': f"{line.content} [变量: {line.variables} | float: {line.float_vars} | half: {line.half_vars} | 转换: {line.conversions}]"
                }
                for line in code_lines
            ],
            'summary': {
                'total_lines': stats['total_code_lines'],
                'total_variables': stats['total_variables'],
                'total_conversions': stats['total_conversions'],
                'conversion_rate': f"{stats['conversion_rate']:.1%}"
            }
        }
        
        return ui_data
    
    # 示例使用
    test_shader = """
float3 pos = transform * vertex;
half4 color = texture.sample(sampler, uv);
half3 lighting = color.rgb * half(intensity);
return half4(lighting, color.a);
"""
    
    ui_result = analyze_for_ui(test_shader)
    
    print("UI数据格式:")
    print(f"总代码行: {ui_result['summary']['total_lines']}")
    print(f"总变量数: {ui_result['summary']['total_variables']}")
    print(f"转换率: {ui_result['summary']['conversion_rate']}")
    
    print("\n代码行数据:")
    for line_data in ui_result['code_lines']:
        print(f"  {line_data['display_text']}")

def main():
    """主函数"""
    print("🚀 代码行分析器使用示例")
    print("功能特点:")
    print("• 精确识别有效代码行（跳过注释、空行、大括号等）")
    print("• 详细的变量类型统计（float/half分类）")
    print("• 类型转换检测和分析")
    print("• 灵活的数据格式，易于集成")
    print("• 高性能的正则表达式匹配")
    print()
    
    example_basic_usage()
    example_detailed_analysis()
    example_custom_filtering()
    example_integration_with_ui()
    
    print("\n" + "=" * 50)
    print("🎯 集成建议")
    print("=" * 50)
    print("1. 在着色器分析组件中使用 CodeLineAnalyzer")
    print("2. 将结果显示在UI的代码行列表中")
    print("3. 提供过滤选项（如只显示有转换的行）")
    print("4. 在HTML报告中使用格式化的分析结果")
    print("5. 利用统计信息生成性能建议")

if __name__ == "__main__":
    main()
