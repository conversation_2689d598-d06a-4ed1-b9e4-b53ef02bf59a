#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复文件编码问题
"""

import os
import re

def fix_encoding_issues():
    """修复编码问题"""
    file_path = 'ui/precise_type_analysis_widget.py'
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    try:
        # 尝试不同编码读取文件
        content = None
        for encoding in ['utf-8', 'gbk', 'latin-1']:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                print(f"   ✅ 使用 {encoding} 编码读取成功")
                break
            except UnicodeDecodeError:
                continue
        
        if content is None:
            print("   ❌ 无法读取文件")
            return False
        
        # 修复已知的编码问题
        fixes = [
            (r'未找到精确分析数�\?\)', '未找到精确分析数据"),'),
            (r'def start_precise_analysis', 'def start_analysis'),
        ]
        
        fixed_count = 0
        for pattern, replacement in fixes:
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                fixed_count += 1
                print(f"   ✅ 修复: {pattern} -> {replacement}")
        
        if fixed_count == 0:
            print("   ℹ️  未发现需要修复的问题")
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"   ✅ 文件已保存: {file_path}")
        return True
        
    except Exception as e:
        print(f"   ❌ 修复失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 修复文件编码问题")
    print("=" * 50)
    
    success = fix_encoding_issues()
    
    if success:
        print("\n🎉 修复完成！")
    else:
        print("\n❌ 修复失败")

if __name__ == "__main__":
    main()
