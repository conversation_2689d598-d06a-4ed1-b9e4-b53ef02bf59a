#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据结构修复
"""

import sys
import os
import json

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_analysis_data_structure():
    """测试分析数据结构"""
    print("🔍 测试分析数据结构")
    print("=" * 50)
    
    try:
        from Process.Analysis.shader_analysis_processor import ShaderAnalysisProcessor
        
        # 创建处理器
        processor = ShaderAnalysisProcessor()
        
        # 测试着色器代码
        test_shader = """
float4 vertex_main(float3 position : POSITION, float2 uv : TEXCOORD0) : SV_POSITION {
    float4x4 mvp = mul(model_matrix, view_proj_matrix);
    float4 world_pos = mul(float4(position, 1.0), mvp);
    return world_pos;
}

float4 pixel_main(float2 uv : TEXCOORD0) : SV_Target {
    float4 base_color = tex2D(main_texture, uv);
    float3 normal = normalize(world_normal);
    float ndotl = dot(normal, light_direction);
    return base_color * ndotl;
}
"""
        
        print("   正在进行分析...")
        result = processor.analyze_shader(test_shader, save_reports=False)
        
        # 检查顶层结构
        print("\n📊 顶层数据结构:")
        for key in result.keys():
            print(f"   • {key}: {type(result[key])}")
        
        # 检查analysis结构
        if 'analysis' in result:
            analysis = result['analysis']
            print(f"\n🔍 analysis结构:")
            for key in analysis.keys():
                print(f"   • {key}: {type(analysis[key])}")
            
            # 检查precise_analysis结构
            if 'precise_analysis' in analysis:
                precise_data = analysis['precise_analysis']
                print(f"\n🎯 precise_analysis结构:")
                for key in precise_data.keys():
                    print(f"   • {key}: {type(precise_data[key])}")
                
                # 检查overall_statistics
                if 'overall_statistics' in precise_data:
                    overall_stats = precise_data['overall_statistics']
                    print(f"\n📈 overall_statistics内容:")
                    for key, value in overall_stats.items():
                        print(f"   • {key}: {value} ({type(value)})")
                    
                    # 特别检查type_distribution
                    if 'type_distribution' in overall_stats:
                        type_dist = overall_stats['type_distribution']
                        print(f"\n🎨 type_distribution内容:")
                        if type_dist:
                            for type_name, count in sorted(type_dist.items(), key=lambda x: x[1], reverse=True):
                                print(f"   • {type_name}: {count}")
                        else:
                            print("   ⚠️  type_distribution为空")
                    else:
                        print("   ❌ 缺少type_distribution字段")
                else:
                    print("   ❌ 缺少overall_statistics字段")
            else:
                print("   ❌ 缺少precise_analysis字段")
        else:
            print("   ❌ 缺少analysis字段")
        
        return result
        
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        return None

def test_html_generation_with_real_data(result):
    """使用真实数据测试HTML生成"""
    print("\n🌐 测试HTML生成（真实数据）")
    print("=" * 50)
    
    if not result:
        print("   ❌ 没有分析结果")
        return False
    
    try:
        from PyQt5.QtWidgets import QApplication
        from ui.precise_type_analysis_widget import PreciseTypeAnalysisWidget
        
        # 创建QApplication（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建组件
        widget = PreciseTypeAnalysisWidget()
        widget.analysis_result = result
        
        # 获取精确分析数据
        analysis_data = result.get('analysis', {})
        precise_data = analysis_data.get('precise_analysis', {})
        
        if precise_data:
            print("   ✅ 获得精确分析数据")
            
            # 检查统计数据
            overall_stats = precise_data.get('overall_statistics', {})
            if overall_stats:
                print("   ✅ 获得统计数据")
                print(f"   📊 总节点数: {overall_stats.get('total_nodes', 0)}")
                print(f"   📊 变量总数: {overall_stats.get('total_variables', 0)}")
                print(f"   📊 类型转换: {overall_stats.get('total_type_conversions', 0)}")
                
                # 检查类型分布
                type_dist = overall_stats.get('type_distribution', {})
                if type_dist:
                    print(f"   ✅ 类型分布数据: {len(type_dist)} 种类型")
                    for type_name, count in list(type_dist.items())[:3]:
                        print(f"     • {type_name}: {count}")
                else:
                    print("   ⚠️  类型分布为空")
            
            # 测试HTML生成
            try:
                html_content = widget._generate_html_report(precise_data)
                if html_content and len(html_content) > 1000:
                    print("   ✅ HTML内容生成成功")
                    print(f"   📏 HTML长度: {len(html_content)} 字符")
                    
                    # 检查HTML内容是否包含数据
                    if "总节点数" in html_content and str(overall_stats.get('total_nodes', 0)) in html_content:
                        print("   ✅ HTML包含统计数据")
                    else:
                        print("   ⚠️  HTML可能缺少统计数据")
                    
                    # 检查类型表格
                    if type_dist and any(type_name in html_content for type_name in type_dist.keys()):
                        print("   ✅ HTML包含类型分布表格")
                    else:
                        print("   ⚠️  HTML可能缺少类型分布表格")
                    
                    return True
                else:
                    print("   ❌ HTML内容生成失败或内容过短")
                    return False
            except Exception as e:
                print(f"   ❌ HTML生成异常: {str(e)}")
                return False
        else:
            print("   ❌ 没有精确分析数据")
            return False
            
    except Exception as e:
        print(f"   ❌ HTML生成测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 数据结构修复验证")
    print("=" * 60)
    print("验证内容:")
    print("• 🔍 分析数据结构检查")
    print("• 🌐 HTML生成测试（真实数据）")
    print()
    
    # 执行测试
    result = test_analysis_data_structure()
    html_ok = test_html_generation_with_real_data(result)
    
    print(f"\n🎉 验证结果总结")
    print("=" * 60)
    
    if result:
        print("✅ 分析数据结构正常")
        print("   • 数据结构完整")
        print("   • 统计信息可用")
        print("   • 类型分布数据存在")
    else:
        print("❌ 分析数据结构异常")
    
    if html_ok:
        print("✅ HTML生成功能正常")
        print("   • HTML内容生成成功")
        print("   • 包含真实统计数据")
        print("   • 类型分布表格正常")
    else:
        print("❌ HTML生成功能异常")
    
    if result and html_ok:
        print(f"\n🎊 数据结构修复成功！")
        print("修复内容:")
        print("• 🔧 修正了type_statistics -> type_distribution")
        print("• 📊 HTML报告现在包含真实数据")
        print("• 📄 JSON导出包含完整分析结果")
        print("• 🎯 数据访问路径正确")
        print("\n现在导出的HTML和JSON应该包含完整的分析数据！")
    else:
        print(f"\n❌ 数据结构修复未完全成功，请检查失败项")

if __name__ == "__main__":
    main()
